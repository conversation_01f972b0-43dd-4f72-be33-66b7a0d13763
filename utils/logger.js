/**
 * Simple logger utility for OSRS Wiki tools
 */

const fs = require('fs').promises;
const path = require('path');

class Logger {
  constructor() {
    this.logLevel = process.env.LOG_LEVEL || 'info';
    this.logLevels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
  }

  _shouldLog(level) {
    return this.logLevels[level] <= this.logLevels[this.logLevel];
  }

  _formatMessage(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const formattedArgs = args.length > 0 ? ' ' + args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ') : '';
    
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${formattedArgs}`;
  }

  error(message, ...args) {
    if (this._shouldLog('error')) {
      console.error(this._formatMessage('error', message, ...args));
    }
  }

  warn(message, ...args) {
    if (this._shouldLog('warn')) {
      console.warn(this._formatMessage('warn', message, ...args));
    }
  }

  info(message, ...args) {
    if (this._shouldLog('info')) {
      console.log(this._formatMessage('info', message, ...args));
    }
  }

  debug(message, ...args) {
    if (this._shouldLog('debug')) {
      console.log(this._formatMessage('debug', message, ...args));
    }
  }
}

module.exports = new Logger();
