#!/usr/bin/env node

/**
 * Test script to verify the watcher is working correctly
 */

const OSRSWikiWatcher = require('./watcher/osrsWikiWatcher');

async function testWatcher() {
  console.log('🧪 Testing OSRS Wiki Watcher...');
  console.log('');
  
  const watcher = new OSRSWikiWatcher({
    pollInterval: 10000, // 10 seconds for testing
    batchSize: 5 // Small batch for testing
  });
  
  try {
    await watcher.initialize();
    console.log('✅ Watcher initialized');
    
    // Run a single check
    console.log('🔍 Running test check...');
    await watcher.performCheck();
    
    const status = watcher.getStatus();
    console.log('');
    console.log('📊 Test Results:');
    console.log(`   🔍 Changes detected: ${status.stats.changesDetected}`);
    console.log(`   📄 Pages updated: ${status.stats.pagesUpdated}`);
    console.log(`   📦 Pages archived: ${status.stats.pagesArchived}`);
    console.log(`   ❌ Errors: ${status.stats.errors}`);
    
    if (status.stats.pagesUpdated === 0) {
      console.log('');
      console.log('🎉 SUCCESS: No false positives detected!');
      console.log('   The watcher correctly identified that no pages need updating.');
    } else if (status.stats.pagesUpdated < 10) {
      console.log('');
      console.log('✅ GOOD: Only a few pages updated (likely legitimate changes)');
    } else {
      console.log('');
      console.log('⚠️ WARNING: Many pages updated - this might indicate an issue');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

testWatcher();
