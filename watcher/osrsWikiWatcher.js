/**
 * OSRS Wiki Watcher - Live Content Monitoring System
 * Monitors the OSRS Wiki for changes and keeps local content up to date
 */

const fs = require('fs').promises;
const path = require('path');
const WatcherLogger = require('./utils/logger');
const ContentIndex = require('./utils/contentIndex');
const ArchiveManager = require('./utils/archiveManager');
const OSRSWikiAPI = require('./utils/wikiAPI');

class OSRSWikiWatcher {
  constructor(options = {}) {
    this.workspaceRoot = process.cwd();
    this.wikiContentDir = path.join(this.workspaceRoot, 'wiki-content');
    this.watcherDir = path.join(this.workspaceRoot, 'watcher');
    this.logDir = path.join(this.watcherDir, 'logs');
    this.dataDir = path.join(this.watcherDir, 'data');
    this.archiveDir = path.join(this.watcherDir, 'archives');
    
    // Configuration
    this.config = {
      pollInterval: options.pollInterval || 5 * 60 * 1000, // 5 minutes
      batchSize: options.batchSize || 10,
      maxRetries: options.maxRetries || 3,
      ...options
    };
    
    // Initialize components
    this.logger = new WatcherLogger(this.logDir);
    this.contentIndex = new ContentIndex(path.join(this.dataDir, 'content-index.json'));
    this.archiveManager = new ArchiveManager(this.archiveDir, this.logger);
    this.wikiAPI = new OSRSWikiAPI(this.logger);
    
    // State
    this.isRunning = false;
    this.pollTimer = null;
    this.stats = {
      totalChecks: 0,
      changesDetected: 0,
      pagesUpdated: 0,
      pagesArchived: 0,
      errors: 0,
      lastCheck: null,
      startTime: null
    };
  }

  async initialize() {
    this.logger.info('🚀 Initializing OSRS Wiki Watcher...');
    
    // Create directories
    await fs.mkdir(this.watcherDir, { recursive: true });
    await fs.mkdir(this.dataDir, { recursive: true });
    
    // Initialize components
    await this.logger.initialize();
    await this.contentIndex.initialize();
    await this.archiveManager.initialize();
    
    // Load existing stats if available
    await this.loadStats();
    
    this.logger.info('✅ OSRS Wiki Watcher initialized');
    this.logger.info(`📊 Current index: ${this.contentIndex.getStats().totalPages} pages`);
  }

  async start() {
    if (this.isRunning) {
      this.logger.warn('⚠️ Watcher is already running');
      return;
    }

    this.logger.info('🎬 Starting OSRS Wiki Watcher...');
    this.isRunning = true;
    this.stats.startTime = new Date().toISOString();
    
    // Perform initial check
    await this.performCheck();
    
    // Schedule regular checks
    this.scheduleNextCheck();
    
    this.logger.info(`⏰ Watcher started with ${this.config.pollInterval / 1000}s poll interval`);
  }

  async stop() {
    if (!this.isRunning) {
      this.logger.warn('⚠️ Watcher is not running');
      return;
    }

    this.logger.info('🛑 Stopping OSRS Wiki Watcher...');
    this.isRunning = false;
    
    if (this.pollTimer) {
      clearTimeout(this.pollTimer);
      this.pollTimer = null;
    }
    
    await this.saveStats();
    this.logger.info('✅ Watcher stopped');
  }

  scheduleNextCheck() {
    if (!this.isRunning) return;
    
    this.pollTimer = setTimeout(async () => {
      try {
        await this.performCheck();
        this.scheduleNextCheck();
      } catch (error) {
        this.logger.error('Check failed, scheduling retry:', error.message);
        this.stats.errors++;
        
        // Retry with exponential backoff
        const retryDelay = Math.min(this.config.pollInterval * 2, 30 * 60 * 1000); // Max 30 minutes
        setTimeout(() => this.scheduleNextCheck(), retryDelay);
      }
    }, this.config.pollInterval);
  }

  async performCheck() {
    this.logger.info('🔍 Performing wiki change check...');
    this.stats.totalChecks++;
    this.stats.lastCheck = new Date().toISOString();
    
    try {
      // Get recent changes since last check
      const lastCheck = this.contentIndex.index.lastCheck;
      const recentChanges = await this.getRecentChanges(lastCheck);
      
      if (recentChanges.length === 0) {
        this.logger.info('📭 No changes detected');
        this.contentIndex.updateLastCheck();
        await this.contentIndex.save();
        return;
      }
      
      this.logger.info(`📬 Found ${recentChanges.length} potential changes`);
      this.stats.changesDetected += recentChanges.length;
      
      // Process changes in batches
      await this.processChanges(recentChanges);
      
      // Update last check timestamp
      this.contentIndex.updateLastCheck();
      await this.contentIndex.save();
      await this.saveStats();
      
      this.logger.info('✅ Check completed successfully');
      
    } catch (error) {
      this.logger.error('Failed to perform check:', error.message);
      throw error;
    }
  }

  async getRecentChanges(since = null) {
    const options = {
      limit: 500
    };
    
    if (since) {
      options.rcstart = since;
    }
    
    const result = await this.wikiAPI.getRecentChanges(options);
    return result.changes;
  }

  async processChanges(changes) {
    // Group changes by page title to handle multiple edits to same page
    const changesByPage = {};
    
    changes.forEach(change => {
      if (!changesByPage[change.title]) {
        changesByPage[change.title] = [];
      }
      changesByPage[change.title].push(change);
    });
    
    const pagesToCheck = Object.keys(changesByPage);
    this.logger.info(`📋 Processing ${pagesToCheck.length} unique pages`);
    
    // Process in batches
    for (let i = 0; i < pagesToCheck.length; i += this.config.batchSize) {
      const batch = pagesToCheck.slice(i, i + this.config.batchSize);
      await this.processBatch(batch, changesByPage);
      
      // Small delay between batches
      if (i + this.config.batchSize < pagesToCheck.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  async processBatch(pageTitles, changesByPage) {
    try {
      // Get current page content from API (we need content to compare hashes)
      const pageContents = await this.wikiAPI.getPageContent(pageTitles);

      for (const title of pageTitles) {
        const changes = changesByPage[title];
        const currentContent = pageContents[title];

        if (!currentContent) {
          this.logger.warn(`⚠️ No content found for page: ${title}`);
          continue;
        }

        // Generate content hash for comparison
        const currentContentHash = this.generateContentHash(currentContent.content);

        // Check if page needs updating
        if (this.contentIndex.needsUpdate(title, currentContent.revisionId, currentContentHash)) {
          await this.updatePage(title, currentContent, changes);
        } else {
          this.logger.debug(`📄 Page up to date: ${title}`);
        }
      }

    } catch (error) {
      this.logger.error('Failed to process batch:', error.message);
      throw error;
    }
  }

  async updatePage(title, pageData, changes) {
    try {
      this.logger.info(`🔄 Updating page: ${title}`);

      // Get current content from local file if it exists
      const existingPage = this.contentIndex.getPage(title);
      let currentContent = null;

      if (existingPage && existingPage.filePath) {
        try {
          const fileContent = await fs.readFile(existingPage.filePath, 'utf8');
          currentContent = JSON.parse(fileContent);
        } catch (error) {
          this.logger.warn(`Failed to read existing file for ${title}:`, error.message);
        }
      }

      // Archive current version if it exists
      if (currentContent) {
        await this.archiveManager.archivePage(title, currentContent, pageData.namespace);
        this.stats.pagesArchived++;
      }

      // Save new content (pageData already contains the content from API)
      await this.savePageContent(title, pageData);

      // Update index
      this.contentIndex.addPage(title, {
        revisionId: pageData.revisionId,
        timestamp: pageData.timestamp,
        namespace: pageData.namespace,
        filePath: this.getPageFilePath(title, pageData.namespace),
        contentHash: this.generateContentHash(pageData.content)
      });

      // Log the change
      await this.logPageChange(title, pageData, changes, currentContent ? 'updated' : 'new');

      this.stats.pagesUpdated++;
      this.logger.info(`✅ Updated page: ${title}`);

    } catch (error) {
      this.logger.error(`Failed to update page ${title}:`, error.message);
      this.stats.errors++;
    }
  }

  async savePageContent(title, pageData) {
    const namespace = pageData.namespace;
    const safeTitle = this.sanitizeFilename(title.replace(`${namespace}:`, ''));
    const filePath = path.join(this.wikiContentDir, 'pages', namespace, `${safeTitle}.json`);
    
    // Ensure directory exists
    await fs.mkdir(path.dirname(filePath), { recursive: true });
    
    const fileData = {
      title: title,
      namespace: namespace,
      content: pageData.content,
      revisionId: pageData.revisionId,
      timestamp: pageData.timestamp,
      downloadedAt: new Date().toISOString(),
      source: 'osrs_wiki_watcher',
      rawWikitext: true
    };
    
    await fs.writeFile(filePath, JSON.stringify(fileData, null, 2), 'utf8');
  }

  getPageFilePath(title, namespace) {
    const safeTitle = this.sanitizeFilename(title.replace(`${namespace}:`, ''));
    return path.join(this.wikiContentDir, 'pages', namespace, `${safeTitle}.json`);
  }

  sanitizeFilename(filename) {
    return filename
      .replace(/[<>:"/\\|?*]/g, '_')
      .replace(/\s+/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
  }

  generateContentHash(content) {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(content || '').digest('hex').substring(0, 16);
  }

  async logPageChange(title, pageData, changes, changeType) {
    const changeData = {
      type: changeType,
      title: title,
      namespace: pageData.namespace,
      revisionId: pageData.revisionId,
      timestamp: pageData.timestamp,
      changes: changes.map(c => ({
        type: c.type,
        user: c.user,
        comment: c.comment,
        timestamp: c.timestamp
      }))
    };
    
    await this.logger.logChange(changeData);
  }

  async loadStats() {
    try {
      const statsPath = path.join(this.dataDir, 'watcher-stats.json');
      const data = await fs.readFile(statsPath, 'utf8');
      this.stats = { ...this.stats, ...JSON.parse(data) };
    } catch (error) {
      // Stats file doesn't exist yet, use defaults
    }
  }

  async saveStats() {
    const statsPath = path.join(this.dataDir, 'watcher-stats.json');
    await fs.writeFile(statsPath, JSON.stringify(this.stats, null, 2));
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      stats: this.stats,
      config: this.config,
      indexStats: this.contentIndex.getStats()
    };
  }
}

module.exports = OSRSWikiWatcher;
