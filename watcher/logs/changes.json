[{"timestamp": "2025-09-06T02:35:10Z", "type": "new", "title": "<PERSON><PERSON><PERSON> Skin", "namespace": "Main", "revisionId": 14980807, "changes": [{"type": "edit", "user": "Cocoa7", "comment": "Clarify exceptions for consistency", "timestamp": "2025-09-06T02:35:10Z"}]}, {"timestamp": "2025-09-06T02:34:36Z", "type": "new", "title": "Rock Skin", "namespace": "Main", "revisionId": 14980806, "changes": [{"type": "edit", "user": "Cocoa7", "comment": "Clarify exceptions for consistency", "timestamp": "2025-09-06T02:34:36Z"}]}, {"timestamp": "2025-09-06T02:28:45Z", "type": "new", "title": "Steel Skin", "namespace": "Main", "revisionId": 14980805, "changes": [{"type": "edit", "user": "Cocoa7", "comment": "Fix typo", "timestamp": "2025-09-06T02:28:45Z"}]}, {"timestamp": "2025-09-06T02:15:56Z", "type": "new", "title": "Champions' Challenge/Strategies", "namespace": "Main", "revisionId": 14980801, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "/* Imp champion scroll */  ka<PERSON>ri", "timestamp": "2025-09-06T02:15:56Z"}]}, {"timestamp": "2025-09-06T02:06:34Z", "type": "new", "title": "Prince <PERSON> Rescue/Quick guide", "namespace": "Main", "revisionId": 14980800, "changes": [{"type": "edit", "user": "MegaMegazord", "comment": "<PERSON>'s house is east of Leela", "timestamp": "2025-09-06T02:06:34Z"}]}, {"timestamp": "2025-09-06T01:51:49Z", "type": "updated", "title": "Temple Trekking", "namespace": "Main", "revisionId": 14980798, "changes": [{"type": "edit", "user": "LesserGatsby", "comment": "Not to be rude, but whoever wrote the requirements section of this guide did not understand the meaning of \"required\".  This change separates the gear/items lists into a Required and a Recommended section to stress what gear is required for the minigame.   I have personally fallen into the trap of missing an item in this guide. These changes save future readers from the same fate.", "timestamp": "2025-09-06T01:51:49Z"}]}, {"timestamp": "2025-09-06T01:51:30Z", "type": "new", "title": "Guardians of the Rift/Strategies", "namespace": "Main", "revisionId": 14980797, "changes": [{"type": "edit", "user": "**************", "comment": "/* Recommended equipment */ Removing duplicate Graceful footnote", "timestamp": "2025-09-06T01:51:30Z"}, {"type": "edit", "user": "**************", "comment": "/* Recommended equipment */ Added Graceful cape for those without 99s", "timestamp": "2025-09-06T01:44:55Z"}]}, {"timestamp": "2025-09-06T01:50:35Z", "type": "updated", "title": "Champion of Scabaras", "namespace": "Main", "revisionId": 14980796, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "testing this on speedrunning worlds was a pain but i found out this guy has kalphite attribute", "timestamp": "2025-09-06T01:50:35Z"}]}, {"timestamp": "2025-09-06T01:46:56Z", "type": "new", "title": "<PERSON>", "namespace": "Main", "revisionId": 14980795, "changes": [{"type": "edit", "user": "Crow", "comment": "?", "timestamp": "2025-09-06T01:46:56Z"}, {"type": "edit", "user": "Takyon69", "comment": "Undid revision 14980789 by [[Special:Contributions/Takyon69|Takyon69]] ([[User talk:Takyon69|talk]])", "timestamp": "2025-09-06T01:40:19Z"}, {"type": "edit", "user": "Takyon69", "comment": "Undid revision 14980785 by [[Special:Contributions/************|************]] ([[User talk:************|talk]])", "timestamp": "2025-09-06T01:39:54Z"}, {"type": "edit", "user": "************", "comment": "", "timestamp": "2025-09-06T01:38:09Z"}, {"type": "edit", "user": "************", "comment": "", "timestamp": "2025-09-06T01:25:49Z"}, {"type": "edit", "user": "************", "comment": "Corrected gender for this NPC as they never confirmed how they present themselves.", "timestamp": "2025-09-06T01:24:44Z"}]}, {"timestamp": "2025-09-06T00:50:21Z", "type": "updated", "title": "Stealing valuables", "namespace": "Main", "revisionId": 14980782, "changes": [{"type": "edit", "user": "<PERSON><PERSON>k harpist", "comment": "update floor number to use the recommended template", "timestamp": "2025-09-06T00:50:21Z"}]}, {"timestamp": "2025-09-06T00:36:22Z", "type": "updated", "title": "Jaguar egg", "namespace": "Main", "revisionId": 14980780, "changes": [{"type": "edit", "user": "99.192.16.200", "comment": "/* Trivia */", "timestamp": "2025-09-06T00:36:22Z"}, {"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-06T00:20:19Z"}, {"type": "edit", "user": "TehAnonymous", "comment": "/* Trivia */", "timestamp": "2025-09-05T22:03:32Z"}]}, {"timestamp": "2025-09-06T00:28:45Z", "type": "updated", "title": "Pirate hat & patch", "namespace": "Main", "revisionId": 14980779, "changes": [{"type": "edit", "user": "Dig caskets", "comment": "", "timestamp": "2025-09-06T00:28:45Z"}, {"type": "edit", "user": "Dig caskets", "comment": "nevermind", "timestamp": "2025-09-06T00:27:11Z"}, {"type": "edit", "user": "Dig caskets", "comment": "", "timestamp": "2025-09-06T00:23:23Z"}, {"type": "edit", "user": "Dig caskets", "comment": "regular pirate's hat, not big pirate hat", "timestamp": "2025-09-06T00:22:17Z"}]}, {"timestamp": "2025-09-06T00:28:09Z", "type": "updated", "title": "Death Plateau", "namespace": "Main", "revisionId": 14980778, "changes": [{"type": "edit", "user": "213.165.185.211", "comment": "/* Spiked boots */ You can quickly walk around the castle on the north side", "timestamp": "2025-09-06T00:28:09Z"}]}, {"timestamp": "2025-09-06T00:20:14Z", "type": "updated", "title": "Dragon egg (Scrambled!)", "namespace": "Main", "revisionId": 14980773, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-06T00:20:14Z"}]}, {"timestamp": "2025-09-06T00:20:05Z", "type": "updated", "title": "Large egg", "namespace": "Main", "revisionId": 14980772, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-06T00:20:05Z"}]}, {"timestamp": "2025-09-06T00:18:29Z", "type": "updated", "title": "Bone bolts", "namespace": "Main", "revisionId": 14980768, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "cost per hour", "timestamp": "2025-09-06T00:18:29Z"}]}, {"timestamp": "2025-09-06T00:14:09Z", "type": "updated", "title": "A Frozen King", "namespace": "Main", "revisionId": 14980766, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "clarify what works, if we're going to single out ice spells we might as well name all of them", "timestamp": "2025-09-06T00:14:09Z"}]}, {"timestamp": "2025-09-05T23:40:00Z", "type": "new", "title": "Optimal quest guide/Free-to-play", "namespace": "Main", "revisionId": 14980764, "changes": [{"type": "edit", "user": "Telemonke", "comment": "removing iron advice since theres an f2p ironman guide, GE item list section. Will return to this later and make a more definitive item list with advice on where to gather", "timestamp": "2025-09-05T23:40:00Z"}]}, {"timestamp": "2025-09-05T23:37:50Z", "type": "new", "title": "Re<PERSON>s Chest (Fortis Colosseum)", "namespace": "Main", "revisionId": 14980763, "changes": [{"type": "edit", "user": "WillZero", "comment": "split up cumulative unique chances on separate lines for better readability", "timestamp": "2025-09-05T23:37:50Z"}, {"type": "edit", "user": "WillZero", "comment": "Added cumulative drop rate of any piece of tonalztics of ralos if you complete all 12 waves. You can see the calculation here: https://docs.google.com/spreadsheets/d/1GYvmOwkhyqnHz35nIKdoZEUoloCnG70mlHB7fpe9rG0/edit?usp=sharing", "timestamp": "2025-09-05T19:17:06Z"}, {"type": "edit", "user": "WillZero", "comment": "Added the differing rates for uniques between waves 4-6 and 7-12", "timestamp": "2025-09-05T19:10:16Z"}, {"type": "edit", "user": "WillZero", "comment": "Added cumulative drop rate of receiving at least 1 echo crystal if you complete all 12 waves. You can see the calculation here: https://docs.google.com/spreadsheets/d/1vzWuAnmoxsaLO4fld9no086xJmuxIHOEWCPDd2JluuI/edit?usp=sharing", "timestamp": "2025-09-05T19:06:18Z"}, {"type": "edit", "user": "WillZero", "comment": "Added cumulative drop rate of any piece of sunfire fanatic armour if you complete all 12 waves. You can see the calculation here: https://docs.google.com/spreadsheets/d/1EUERT4FyhXNPjjURpwM1ZFa_T1SfpYrgYtEYA846BDo/edit?usp=sharing", "timestamp": "2025-09-05T19:00:38Z"}]}, {"timestamp": "2025-09-05T23:31:00Z", "type": "updated", "title": "Chocchip crunchies", "namespace": "Main", "revisionId": 14980762, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed the required level (Only for adding the chocolate dust) and the xp gained for the whole process was wrong", "timestamp": "2025-09-05T23:31:00Z"}]}, {"timestamp": "2025-09-05T23:30:29Z", "type": "updated", "title": "Worm crunchies", "namespace": "Main", "revisionId": 14980761, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed the required level (Only for adding the gnome spice) and the xp gained for the whole process was wrong", "timestamp": "2025-09-05T23:30:29Z"}]}, {"timestamp": "2025-09-05T23:29:41Z", "type": "updated", "title": "Spicy crunchies", "namespace": "Main", "revisionId": 14980760, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed required level (Only at add gnome spice) and the total xp gained for the whole process", "timestamp": "2025-09-05T23:29:41Z"}]}, {"timestamp": "2025-09-05T23:28:55Z", "type": "updated", "title": "Toad crunchies", "namespace": "Main", "revisionId": 14980759, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed required level (Only to add equa leaves)", "timestamp": "2025-09-05T23:28:55Z"}]}, {"timestamp": "2025-09-05T23:17:34Z", "type": "new", "title": "Male tiger", "namespace": "Main", "revisionId": 14980756, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "sorry the game was not woke yet in 2015", "timestamp": "2025-09-05T23:17:34Z"}, {"type": "edit", "user": "140.177.197.237", "comment": "changed to be more inclusive", "timestamp": "2025-09-05T22:01:45Z"}]}, {"timestamp": "2025-09-05T22:52:48Z", "type": "new", "title": "<PERSON><PERSON><PERSON><PERSON>/Strategies", "namespace": "Main", "revisionId": 14980754, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "one spec lasts six scobow attacks", "timestamp": "2025-09-05T22:52:48Z"}]}, {"timestamp": "2025-09-05T22:50:33Z", "type": "updated", "title": "Raw dark crab", "namespace": "Main", "revisionId": 14980753, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "", "timestamp": "2025-09-05T22:50:33Z"}]}, {"timestamp": "2025-09-05T22:47:10Z", "type": "new", "title": "Optimal quest guide/Ironman", "namespace": "Main", "revisionId": 14980750, "changes": [{"type": "edit", "user": "Telemonke", "comment": "", "timestamp": "2025-09-05T22:47:10Z"}, {"type": "edit", "user": "Telemonke", "comment": "", "timestamp": "2025-09-05T22:38:16Z"}, {"type": "edit", "user": "Telemonke", "comment": "", "timestamp": "2025-09-05T22:34:41Z"}, {"type": "edit", "user": "Telemonke", "comment": "", "timestamp": "2025-09-05T22:31:52Z"}, {"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:50:03Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "linked directly to page", "timestamp": "2025-09-05T05:52:14Z"}, {"type": "edit", "user": "LostMage", "comment": "changed from \"taverly\" to \"taverley\"", "timestamp": "2025-09-05T05:45:33Z"}]}, {"timestamp": "2025-09-05T22:15:38Z", "type": "updated", "title": "Watchtower", "namespace": "Main", "revisionId": 14980740, "changes": [{"type": "edit", "user": "213.165.185.211", "comment": "/* Shaman slaying */ New Paragraph.", "timestamp": "2025-09-05T22:15:38Z"}, {"type": "edit", "user": "213.165.185.211", "comment": "/* Shaman slaying */ Remember to complete the dialog emphasized.", "timestamp": "2025-09-05T22:15:04Z"}, {"type": "edit", "user": "213.165.185.211", "comment": "remember 'use' and 'eat' are swapped around", "timestamp": "2025-09-05T22:12:04Z"}, {"type": "edit", "user": "213.165.185.211", "comment": "Add additional comments in case guam potion (unf) and ground bat bones were already brought by player.", "timestamp": "2025-09-05T22:06:55Z"}, {"type": "edit", "user": "213.165.185.211", "comment": "The Ogre Enclave - Moved Careful warning upfront to prevent eating Cave Nightshade.", "timestamp": "2025-09-05T21:43:45Z"}]}, {"timestamp": "2025-09-05T21:36:56Z", "type": "new", "title": "<PERSON>", "namespace": "Main", "revisionId": 14980725, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "i don't think it's a coincidence", "timestamp": "2025-09-05T21:36:56Z"}]}, {"timestamp": "2025-09-05T21:09:35Z", "type": "updated", "title": "Echo crystal", "namespace": "Main", "revisionId": 14980717, "changes": [{"type": "edit", "user": "WillZero", "comment": "added missing \"approximately\"", "timestamp": "2025-09-05T21:09:35Z"}, {"type": "edit", "user": "WillZero", "comment": "fixed/clarified cumulative drop rates & expected crystal amounts for completing all 12 waves. you can see the calculations here: https://docs.google.com/spreadsheets/d/1Ua3nUwskxyuPJqVKFnNeRrys3wFzYjE8xDXkEMQEtMY/edit?usp=sharing", "timestamp": "2025-09-05T19:38:40Z"}]}, {"timestamp": "2025-09-05T20:55:16Z", "type": "new", "title": "Money making guide/Farming hops", "namespace": "Main", "revisionId": 14980716, "changes": [{"type": "edit", "user": "Crow", "comment": "", "timestamp": "2025-09-05T20:55:16Z"}, {"type": "edit", "user": "Crow", "comment": "", "timestamp": "2025-09-05T20:40:50Z"}, {"type": "edit", "user": "Psionia", "comment": "Adjusted the activity to be generic.", "timestamp": "2025-09-05T12:20:40Z"}, {"type": "edit", "user": "Psionia", "comment": "Converted table elements so they no longer show trailing 0s.", "timestamp": "2025-09-04T14:33:58Z"}, {"type": "new", "user": "Psionia", "comment": "Introducing new MMG to consolidate all hops farming methods. This is currently split up into various MMGs but it is only possible to do one type of hop farming at a time.", "timestamp": "2025-09-04T14:07:56Z"}]}, {"timestamp": "2025-09-05T20:55:16Z", "type": "new", "title": "Money making guide/Farming herbs", "namespace": "Main", "revisionId": 14980715, "changes": [{"type": "edit", "user": "Crow", "comment": "", "timestamp": "2025-09-05T20:55:16Z"}, {"type": "edit", "user": "Crow", "comment": "Undid revision 14980705 by [[Special:Contributions/Crow|Crow]] ([[User talk:Crow|talk]]) dunno how I undid myself", "timestamp": "2025-09-05T20:45:19Z"}, {"type": "edit", "user": "Crow", "comment": "Undid revision 14980690 by [[Special:Contributions/Crow|Crow]] ([[User talk:Crow|talk]])", "timestamp": "2025-09-05T20:40:43Z"}, {"type": "edit", "user": "Crow", "comment": "", "timestamp": "2025-09-05T20:39:18Z"}, {"type": "edit", "user": "Psionia", "comment": "Using decimal to remove trailing 0s in table.", "timestamp": "2025-09-04T14:40:02Z"}]}, {"timestamp": "2025-09-05T20:55:15Z", "type": "new", "title": "Money making guide/Picking fruit trees", "namespace": "Main", "revisionId": 14980714, "changes": [{"type": "edit", "user": "Crow", "comment": "fucking dumb case-sensitive template params", "timestamp": "2025-09-05T20:55:15Z"}, {"type": "edit", "user": "Crow", "comment": "this shouldn't be merged imo", "timestamp": "2025-09-05T20:38:39Z"}, {"type": "edit", "user": "Psionia", "comment": "Added switch to the other section so that it updates alongside all other switches.", "timestamp": "2025-09-05T15:35:31Z"}, {"type": "new", "user": "Psionia", "comment": "New MMG to consolidate various existing picking MMGs.", "timestamp": "2025-09-05T15:22:05Z"}]}, {"timestamp": "2025-09-05T20:51:47Z", "type": "updated", "title": "Quests/Requirements by skill", "namespace": "Main", "revisionId": 14980712, "changes": [{"type": "edit", "user": "Starshadowx2", "comment": "", "timestamp": "2025-09-05T20:51:47Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "/* See also */", "timestamp": "2025-09-05T19:39:24Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "agility & combat header changes", "timestamp": "2025-09-05T19:35:13Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "quest point icon header changed", "timestamp": "2025-09-05T19:24:06Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "header changes", "timestamp": "2025-09-05T19:21:35Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Added sortable columns, remove highlighted boostable cells, aligned center", "timestamp": "2025-09-05T19:12:31Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "/* x40px|link=Ranged Ranged */", "timestamp": "2025-09-05T18:21:41Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "/* x40px|link=Quest points Quest points */", "timestamp": "2025-09-05T18:21:08Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "/* x40px|link=Mining Mining */", "timestamp": "2025-09-05T18:20:29Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "/* x40px|link=Magic Magic */", "timestamp": "2025-09-05T18:20:14Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "Fix tears of guthix wikisync row", "timestamp": "2025-09-05T18:19:37Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "the yes of glouphrie -> the eyes of glou<PERSON>rie", "timestamp": "2025-09-05T18:18:33Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Added lighttable and reworked columns", "timestamp": "2025-09-05T18:15:22Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Removal of extra characters", "timestamp": "2025-09-04T23:37:27Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Added two-column table formatting with numerical ordering and replaced boostable template syntax with plain \"Yes\" text for better readability and Runelite compatibility.", "timestamp": "2025-09-04T23:34:51Z"}]}, {"timestamp": "2025-09-05T20:46:09Z", "type": "new", "title": "Money making guide/Making prayer potions", "namespace": "Main", "revisionId": 14980710, "changes": [{"type": "new", "user": "Cocoa7", "comment": "Created page with \"{{#invoke:MmgZahurInputs|main |Activity = Making [[prayer potion]]s |Image = [[File:Prayer potion(4) detail.png|128px]] |Skill = {{SCP|Herblore|38}} (60 recommended) |Item =  * [[Alchemist's amulet]] recommended * [[Prescription goggles]] recommended |Quest = [[Druidic Ritual]] |Other = [[Hard Desert Diary]] recommended |Experience1 = Herblore |Experience1num = 87.5 |isperkill = y |kph = 2500 |kph name = Potions made per hour |Input1 = Snape grass |Input1num = 0.9 |Input...\"", "timestamp": "2025-09-05T20:46:09Z"}]}, {"timestamp": "2025-09-05T20:40:42Z", "type": "new", "title": "Money making guide/Farming Yanillian hops", "namespace": "Main", "revisionId": 14980704, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14979814 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:40:42Z"}, {"type": "edit", "user": "Psionia", "comment": "Excluded from MMG as it is being consolidated into Money Making Guide/Farming hops", "timestamp": "2025-09-04T14:10:54Z"}]}, {"timestamp": "2025-09-05T20:40:42Z", "type": "new", "title": "Money making guide/Farming jute fibre", "namespace": "Main", "revisionId": 14980703, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14979813 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:40:42Z"}, {"type": "edit", "user": "Psionia", "comment": "Excluded from MMG as it is being consolidated into Money Making Guide/Farming hops", "timestamp": "2025-09-04T14:09:16Z"}]}, {"timestamp": "2025-09-05T20:39:49Z", "type": "new", "title": "Money making guide/Farming irit leaves", "namespace": "Main", "revisionId": 14980702, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968490 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:49Z"}]}, {"timestamp": "2025-09-05T20:39:48Z", "type": "new", "title": "Money making guide/Farming lantadyme", "namespace": "Main", "revisionId": 14980701, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968489 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:48Z"}]}, {"timestamp": "2025-09-05T20:39:46Z", "type": "new", "title": "Money making guide/Farming dwarf weed", "namespace": "Main", "revisionId": 14980700, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968488 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:46Z"}]}, {"timestamp": "2025-09-05T20:39:46Z", "type": "new", "title": "Money making guide/Farming torstol", "namespace": "Main", "revisionId": 14980699, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968486 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:46Z"}]}, {"timestamp": "2025-09-05T20:39:45Z", "type": "new", "title": "Money making guide/Farming cadantine", "namespace": "Main", "revisionId": 14980698, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968485 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:45Z"}]}, {"timestamp": "2025-09-05T20:39:44Z", "type": "new", "title": "Money making guide/Farming avantoe", "namespace": "Main", "revisionId": 14980697, "changes": [{"type": "edit", "user": "Crow", "comment": "", "timestamp": "2025-09-05T20:39:44Z"}]}, {"timestamp": "2025-09-05T20:39:41Z", "type": "new", "title": "Money making guide/Farming toadflax", "namespace": "Main", "revisionId": 14980696, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968483 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:41Z"}]}, {"timestamp": "2025-09-05T20:39:40Z", "type": "new", "title": "Money making guide/Farming kwuarm", "namespace": "Main", "revisionId": 14980695, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968482 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:40Z"}]}, {"timestamp": "2025-09-05T20:39:40Z", "type": "new", "title": "Money making guide/Farming snapdragon", "namespace": "Main", "revisionId": 14980694, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968481 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:40Z"}]}, {"timestamp": "2025-09-05T20:39:40Z", "type": "new", "title": "Money making guide/Farming ranarr weed", "namespace": "Main", "revisionId": 14980693, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968479 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:40Z"}]}, {"timestamp": "2025-09-05T20:39:39Z", "type": "new", "title": "Money making guide/Farming huasca", "namespace": "Main", "revisionId": 14980692, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968478 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:39Z"}]}, {"timestamp": "2025-09-05T20:38:37Z", "type": "new", "title": "Money making guide/Picking papayas", "namespace": "Main", "revisionId": 14980688, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14980536 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]]) I don't agree with consolidating these, it's easier to compare these guides on the main pages if they're split.", "timestamp": "2025-09-05T20:38:37Z"}, {"type": "edit", "user": "Psionia", "comment": "Excluded as being incorporated into Money Making Guide/Picking Fruit Trees", "timestamp": "2025-09-05T15:23:04Z"}]}, {"timestamp": "2025-09-05T20:38:36Z", "type": "new", "title": "Money making guide/Picking coconuts", "namespace": "Main", "revisionId": 14980686, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14980537 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]]) I don't agree with consolidating these, it's easier to compare these guides on the main pages if they're split.", "timestamp": "2025-09-05T20:38:36Z"}, {"type": "edit", "user": "Psionia", "comment": "Excluded as being incorporated into Money Making Guide/Picking Fruit Trees", "timestamp": "2025-09-05T15:23:41Z"}]}, {"timestamp": "2025-09-05T20:37:33Z", "type": "updated", "title": "Cooked meat", "namespace": "Main", "revisionId": 14980684, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "wrong", "timestamp": "2025-09-05T20:37:33Z"}]}, {"timestamp": "2025-09-05T20:37:21Z", "type": "updated", "title": "Raw yak meat", "namespace": "Main", "revisionId": 14980683, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "wrong", "timestamp": "2025-09-05T20:37:21Z"}]}, {"timestamp": "2025-09-05T01:08:01Z", "type": "new", "title": "SA", "namespace": "Main", "revisionId": 14980239, "changes": [{"type": "new", "user": "<PERSON>ee", "comment": "Redirected page to [[Special attacks]]", "timestamp": "2025-09-05T01:08:01Z"}]}, {"timestamp": null, "type": "new", "title": "Nothing else", "namespace": "Main", "revisionId": null, "changes": [{"type": "log", "user": "Legaia 2 Pla", "comment": "bad redirect", "timestamp": "2025-09-05T00:43:22Z"}]}, {"timestamp": null, "type": "new", "title": "For a", "namespace": "Main", "revisionId": null, "changes": [{"type": "log", "user": "Legaia 2 Pla", "comment": "bad redirect", "timestamp": "2025-09-05T00:42:37Z"}]}, {"timestamp": "2025-09-04T20:17:38Z", "type": "new", "title": "B.j.r", "namespace": "Main", "revisionId": 14980018, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Fairy_rings#BJR]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:17:38Z"}]}, {"timestamp": "2025-09-04T20:07:42Z", "type": "new", "title": "Void Knights' Reward Options", "namespace": "Main", "revisionId": 14980010, "changes": [{"type": "edit", "user": "Source link", "comment": "/* Items */ Void seal does not require these skill levels to buy", "timestamp": "2025-09-04T20:07:42Z"}]}, {"timestamp": "2025-09-04T20:07:32Z", "type": "new", "title": "B-road bolts", "namespace": "Main", "revisionId": 14980009, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Broad bolts]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:07:32Z"}]}, {"timestamp": "2025-09-04T20:07:22Z", "type": "new", "title": "B loodveld", "namespace": "Main", "revisionId": 14980008, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Bloodveld]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:07:22Z"}]}, {"timestamp": "2025-09-04T20:07:13Z", "type": "new", "title": "B oss", "namespace": "Main", "revisionId": 14980007, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:07:13Z"}]}, {"timestamp": "2025-09-04T20:06:29Z", "type": "new", "title": "B andos", "namespace": "Main", "revisionId": 14980006, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Bandos]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:06:29Z"}]}, {"timestamp": "2025-09-04T20:03:58Z", "type": "new", "title": "Yama/Strategies", "namespace": "Main", "revisionId": 14980004, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "", "timestamp": "2025-09-04T20:03:58Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "/* Equipment */ add in some clauses about the usecase of stuff below purging", "timestamp": "2025-09-04T18:40:05Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "/* Equipment */ add in some context for void", "timestamp": "2025-09-04T17:55:21Z"}, {"type": "edit", "user": "AtelierLogic2", "comment": "removed glory to show better than str ammy str is more important than fury or glory accuracy", "timestamp": "2025-09-04T17:38:18Z"}]}, {"timestamp": "2025-09-04T19:42:22Z", "type": "new", "title": "Lizardman shaman/Strategies", "namespace": "Main", "revisionId": 14979981, "changes": [{"type": "edit", "user": "************", "comment": "/* Equipment */", "timestamp": "2025-09-04T19:42:22Z"}]}, {"timestamp": "2025-09-04T18:43:33Z", "type": "new", "title": "Falx", "namespace": "Main", "revisionId": 14979960, "changes": [{"type": "edit", "user": "RealEvanem", "comment": "as cool as falxes are, they aren't in game and misspelling flax led me to sickles :/", "timestamp": "2025-09-04T18:43:33Z"}]}, {"timestamp": "2025-09-04T18:40:13Z", "type": "new", "title": "<PERSON><PERSON>", "namespace": "Main", "revisionId": 14979958, "changes": [{"type": "edit", "user": "**************", "comment": "/* Task Changing */ removed typo in <PERSON><PERSON>'s name", "timestamp": "2025-09-04T18:40:13Z"}]}, {"timestamp": "2025-09-04T18:27:51Z", "type": "new", "title": "Sailing (disambiguation)", "namespace": "Main", "revisionId": 14979952, "changes": [{"type": "edit", "user": "BigDiesel2m", "comment": "", "timestamp": "2025-09-04T18:27:51Z"}, {"type": "new", "user": "BigDiesel2m", "comment": "Created page with \"[[Sailing]] is the upcoming 24th [[skill]] to be added to ''Old School RuneScape'', scheduled for release some time in late [[2025]]. It may also refer to:  ===Sailing Development=== * [[March 2025 Sailing Alpha]] * [[June 2025 Sailing Beta]] * [[August 2025 Sailing Stress Test]]  ===Historical=== * [[2014 April Fools]] * [[Sailing (2015 proposed skill)]]  {{Disambig}}\"", "timestamp": "2025-09-04T18:26:02Z"}]}, {"timestamp": "2025-09-04T18:27:30Z", "type": "new", "title": "Abyssal lantern", "namespace": "Main", "revisionId": 14979951, "changes": [{"type": "edit", "user": "Microbrews", "comment": "", "timestamp": "2025-09-04T18:27:30Z"}, {"type": "edit", "user": "Microbrews", "comment": "", "timestamp": "2025-09-04T18:01:56Z"}, {"type": "edit", "user": "Microbrews", "comment": "test", "timestamp": "2025-09-04T18:01:37Z"}, {"type": "edit", "user": "Microbrews", "comment": "default version", "timestamp": "2025-09-04T18:00:53Z"}]}, {"timestamp": "2025-09-04T18:22:49Z", "type": "new", "title": "Sailing/Development", "namespace": "Main", "revisionId": 14979946, "changes": [{"type": "new", "user": "BigDiesel2m", "comment": "Created page with \"==Polling== '''Sailing''' was proposed to players as a new [[skills|skill]] on [[27 March]] [[2023]] in a [[Update:Adding A New Skill: Introducing Sailing, Taming and Shamanism|blog]] alongside two other potential skills, [[Tam<PERSON>]] and [[Shamanism]]. This was followed by a poll to determine which of the three skills to further develop into a full proposal. Of the three proposed skills, the Sailing pitch received a plurality of votes in the Poll:Skill Pitches Poll|Skil...\"", "timestamp": "2025-09-04T18:22:49Z"}]}, {"timestamp": "2025-09-04T18:07:23Z", "type": "new", "title": "Bone blessed shards", "namespace": "Main", "revisionId": 14979941, "changes": [{"type": "new", "user": "<PERSON><PERSON><PERSON>", "comment": "Redirected to [[Blessed bone shards]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T18:07:23Z"}]}, {"timestamp": "2025-09-04T17:22:22Z", "type": "new", "title": "Chambers of Xeric/Challenge Mode/Strategies", "namespace": "Main", "revisionId": 14979897, "changes": [{"type": "edit", "user": "2605:59C0:283E:A208:3C0E:22E3:6069:DFDD", "comment": "shadow is better than harm and tbow", "timestamp": "2025-09-04T17:22:22Z"}]}, {"timestamp": "2025-09-04T17:17:46Z", "type": "new", "title": "Kalphite Queen/Strategies", "namespace": "Main", "revisionId": 14979896, "changes": [{"type": "edit", "user": "AtelierLogic2", "comment": "deleted ahrim mager + inappropriate ring switches", "timestamp": "2025-09-04T17:17:46Z"}]}, {"timestamp": "2025-09-04T17:13:20Z", "type": "new", "title": "<PERSON><PERSON>", "namespace": "Main", "revisionId": 14979894, "changes": [{"type": "new", "user": "2600:4040:2080:8E00:1CA:69A1:F613:6C4", "comment": "Redirected to [[<PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T17:13:20Z"}]}, {"timestamp": "2025-09-04T17:03:48Z", "type": "new", "title": "March 2025 Sailing Alpha", "namespace": "Main", "revisionId": 14979888, "changes": [{"type": "edit", "user": "BigDiesel2m", "comment": "/* Docking points */", "timestamp": "2025-09-04T17:03:48Z"}]}, {"timestamp": "2025-09-04T16:07:31Z", "type": "new", "title": "Yrsa", "namespace": "Main", "revisionId": 14979874, "changes": [{"type": "edit", "user": "Anarchaon", "comment": "", "timestamp": "2025-09-04T16:07:31Z"}, {"type": "edit", "user": "Anarchaon", "comment": "moved mention of shoe store to trivia", "timestamp": "2025-09-04T16:04:49Z"}]}, {"timestamp": "2025-09-04T15:24:05Z", "type": "new", "title": "Compost Bin", "namespace": "Main", "revisionId": 14979863, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Locations */ fix hosidius map", "timestamp": "2025-09-04T15:24:05Z"}]}, {"timestamp": "2025-09-04T15:19:53Z", "type": "new", "title": "Ent (Vale Totems)", "namespace": "Main", "revisionId": 14979862, "changes": [{"type": "edit", "user": "2A02:6B6F:F276:A800:F0CF:2703:4C8C:13E1", "comment": "Changed the nest type that can be received, based on the information on the ent seed page https://oldschool.runescape.wiki/w/Ent_seed", "timestamp": "2025-09-04T15:19:53Z"}]}, {"timestamp": "2025-09-04T15:16:59Z", "type": "new", "title": "<PERSON><PERSON><PERSON>/Quick guide", "namespace": "Main", "revisionId": 14979860, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Walkthrough */", "timestamp": "2025-09-04T15:16:59Z"}]}, {"timestamp": "2025-09-04T15:16:01Z", "type": "new", "title": "Hunter training", "namespace": "Main", "revisionId": 14979858, "changes": [{"type": "edit", "user": "Aivot-on", "comment": "", "timestamp": "2025-09-04T15:16:01Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "/* Level 75-99: Moonlight Moths */ epic redlink", "timestamp": "2025-09-04T15:02:40Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "slightly cleanup latest addition and expand more on reasoning to do more niche methods", "timestamp": "2025-09-04T14:49:27Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "2nd undo", "timestamp": "2025-09-04T12:57:39Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Undid revision 14979653 by [[Special:Contributions/2001:999:704:CFD1:F861:C6FF:6309:87F|2001:999:704:CFD1:F861:C6FF:6309:87F]] ([[User talk:2001:999:704:CFD1:F861:C6FF:6309:87F|talk]]) please consult with other people before deleting entire methods, they each have their own benefits and disadvantages such as quest reqs, intensity, or personal preference", "timestamp": "2025-09-04T12:57:23Z"}]}, {"timestamp": "2025-09-04T15:05:22Z", "type": "new", "title": "Free-to-play Crafting training", "namespace": "Main", "revisionId": 14979851, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Undid revision 14979850 by [[Special:Contributions/2600:4041:5F51:7800:DD34:9959:1C52:5942|2600:4041:5F51:7800:DD34:9959:1C52:5942]] ([[User talk:2600:4041:5F51:7800:DD34:9959:1C52:5942|talk]])", "timestamp": "2025-09-04T15:05:22Z"}, {"type": "edit", "user": "2600:4041:5F51:7800:DD34:9959:1C52:5942", "comment": "/* In-Place Training Methods */", "timestamp": "2025-09-04T15:04:29Z"}]}, {"timestamp": "2025-09-04T14:46:51Z", "type": "new", "title": "Arno", "namespace": "Main", "revisionId": 14979839, "changes": [{"type": "edit", "user": "Gazu", "comment": "added clarification", "timestamp": "2025-09-04T14:46:51Z"}]}, {"timestamp": "2025-09-04T14:44:35Z", "type": "new", "title": "Large geyser (God Wars Dungeon)", "namespace": "Main", "revisionId": 14979838, "changes": [{"type": "edit", "user": "Pavocado", "comment": "Added external", "timestamp": "2025-09-04T14:44:35Z"}]}, {"timestamp": "2025-09-04T14:32:27Z", "type": "new", "title": "Free-to-play Fishing training", "namespace": "Main", "revisionId": 14979834, "changes": [{"type": "edit", "user": "2001:1C00:8C04:5300:5D73:794:4C96:58E6", "comment": "/* Profitable methods */", "timestamp": "2025-09-04T14:32:27Z"}]}, {"timestamp": "2025-09-04T14:29:15Z", "type": "new", "title": "Slayer task/Smoke devils", "namespace": "Main", "revisionId": 14979831, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "", "timestamp": "2025-09-04T14:29:15Z"}, {"type": "edit", "user": "Funds", "comment": "", "timestamp": "2025-09-04T14:23:18Z"}]}, {"timestamp": "2025-09-04T14:27:59Z", "type": "new", "title": "Slayer task/Wyrms", "namespace": "Main", "revisionId": 14979830, "changes": [{"type": "edit", "user": "Funds", "comment": "", "timestamp": "2025-09-04T14:27:59Z"}]}, {"timestamp": "2025-09-04T13:53:23Z", "type": "new", "title": "Fossegrimen", "namespace": "Main", "revisionId": 14979804, "changes": [{"type": "edit", "user": "128.234.108.180", "comment": "", "timestamp": "2025-09-04T13:53:23Z"}]}, {"timestamp": "2025-09-04T13:33:33Z", "type": "new", "title": "Ultimate Ironman Guide/Hunter", "namespace": "Main", "revisionId": 14979802, "changes": [{"type": "edit", "user": "Caseynyx", "comment": "<PERSON><PERSON><PERSON> weasels mentioned fairy ring aks but didnt use the proper fairy ring formatting.", "timestamp": "2025-09-04T13:33:33Z"}]}, {"timestamp": "2025-09-04T13:32:51Z", "type": "new", "title": "Money making guide/Making super combat potions", "namespace": "Main", "revisionId": 14979801, "changes": [{"type": "edit", "user": "Falterfire", "comment": "Added a note about using 3-dose potions", "timestamp": "2025-09-04T13:32:51Z"}]}, {"timestamp": "2025-09-04T10:30:00Z", "type": "new", "title": "Demonic (attribute)", "namespace": "Main", "revisionId": 14979766, "changes": [{"type": "new", "user": "I am bri ish", "comment": "Redirected page to [[Demon (attribute)]]", "timestamp": "2025-09-04T10:30:00Z"}]}, {"timestamp": "2025-09-04T09:02:56Z", "type": "new", "title": "Temporary skill boost", "namespace": "Main", "revisionId": 14979754, "changes": [{"type": "edit", "user": "Birdie0", "comment": "/* Invisible boost list */", "timestamp": "2025-09-04T09:02:56Z"}, {"type": "edit", "user": "Birdie0", "comment": "/* Invisible boost list */", "timestamp": "2025-09-04T09:01:57Z"}]}, {"timestamp": "2025-09-04T08:25:00Z", "type": "new", "title": "Agility training", "namespace": "Main", "revisionId": 14979749, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "the rockfall obstacle has been patched and you stop recieving experience after a few jumps.", "timestamp": "2025-09-04T08:25:00Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Undid revision 14979740 by [[Special:Contributions/Gaddelito|Gaddelito]] ([[User talk:<PERSON><PERSON><PERSON><PERSON>|talk]])", "timestamp": "2025-09-04T08:22:33Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "", "timestamp": "2025-09-04T08:20:54Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "the rockfall obstacle is patched and recieving experience stops after a few jumps.", "timestamp": "2025-09-04T08:15:45Z"}]}, {"timestamp": "2025-09-04T08:24:40Z", "type": "new", "title": "Fences", "namespace": "Main", "revisionId": 14979748, "changes": [{"type": "new", "user": "<PERSON><PERSON>", "comment": "Redirected page to [[<PERSON><PERSON>]]", "timestamp": "2025-09-04T08:24:40Z"}]}, {"timestamp": "2025-09-04T08:18:43Z", "type": "new", "title": "Farming training", "namespace": "Main", "revisionId": 14979742, "changes": [{"type": "edit", "user": "2601:601:1000:6AD0:A55E:A562:5D6D:77D4", "comment": "", "timestamp": "2025-09-04T08:18:43Z"}]}, {"timestamp": "2025-09-06T04:12:15Z", "type": "new", "title": "Ore seller.", "namespace": "Main", "revisionId": 14980826, "changes": [{"type": "edit", "user": "Cocoa7", "comment": "Link to relevant mmg", "timestamp": "2025-09-06T04:12:15Z"}]}, {"timestamp": "2025-09-04T07:59:17Z", "type": "new", "title": "Grand Gold Chest", "namespace": "Main", "revisionId": 14979738, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "/* Trivia */ reword", "timestamp": "2025-09-04T07:59:17Z"}]}, {"timestamp": "2025-09-06T04:17:35Z", "type": "new", "title": "Bronze Chest (red)", "namespace": "Main", "revisionId": 14980834, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:17:35Z"}]}, {"timestamp": null, "type": "new", "title": "Grid points", "namespace": "Main", "revisionId": null, "changes": [{"type": "log", "user": "Legaia 2 Pla", "comment": "", "timestamp": "2025-09-06T04:13:39Z"}]}, {"timestamp": "2025-09-06T04:12:48Z", "type": "new", "title": "Ordan", "namespace": "Main", "revisionId": 14980827, "changes": [{"type": "edit", "user": "Cocoa7", "comment": "Link to relevant mmg", "timestamp": "2025-09-06T04:12:48Z"}]}, {"timestamp": "2025-09-06T04:18:58Z", "type": "new", "title": "Bronze Chest (purple)", "namespace": "Main", "revisionId": 14980838, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:18:58Z"}]}, {"timestamp": "2025-09-06T04:18:40Z", "type": "new", "title": "Bronze Chest (black)", "namespace": "Main", "revisionId": 14980837, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:18:40Z"}]}, {"timestamp": "2025-09-06T04:18:28Z", "type": "new", "title": "Bronze Chest (crimson)", "namespace": "Main", "revisionId": 14980836, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-06T04:18:28Z"}]}, {"timestamp": "2025-09-06T04:18:00Z", "type": "new", "title": "Bronze Chest (brown)", "namespace": "Main", "revisionId": 14980835, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:18:00Z"}]}, {"timestamp": "2025-09-06T04:28:46Z", "type": "new", "title": "Black Chest (purple)", "namespace": "Main", "revisionId": 14980860, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:28:46Z"}]}, {"timestamp": "2025-09-06T04:28:28Z", "type": "new", "title": "Black Chest (black)", "namespace": "Main", "revisionId": 14980859, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:28:28Z"}]}, {"timestamp": "2025-09-06T04:28:13Z", "type": "new", "title": "Black Chest (crimson)", "namespace": "Main", "revisionId": 14980858, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:28:13Z"}]}, {"timestamp": "2025-09-06T04:27:53Z", "type": "new", "title": "Black Chest (brown)", "namespace": "Main", "revisionId": 14980857, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:27:53Z"}]}, {"timestamp": "2025-09-06T04:27:39Z", "type": "new", "title": "Black Chest (red)", "namespace": "Main", "revisionId": 14980856, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:27:39Z"}]}, {"timestamp": "2025-09-06T04:24:36Z", "type": "new", "title": "Steel Chest (purple)", "namespace": "Main", "revisionId": 14980849, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:24:36Z"}]}, {"timestamp": "2025-09-06T04:24:23Z", "type": "new", "title": "Steel Chest (black)", "namespace": "Main", "revisionId": 14980848, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:24:23Z"}]}, {"timestamp": "2025-09-06T04:24:06Z", "type": "new", "title": "Steel Chest (crimson)", "namespace": "Main", "revisionId": 14980847, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:24:06Z"}]}, {"timestamp": "2025-09-06T04:23:51Z", "type": "new", "title": "Steel Chest (brown)", "namespace": "Main", "revisionId": 14980846, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:23:51Z"}]}, {"timestamp": "2025-09-06T04:23:38Z", "type": "new", "title": "Steel Chest (red)", "namespace": "Main", "revisionId": 14980845, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:23:38Z"}]}, {"timestamp": "2025-09-06T04:30:41Z", "type": "updated", "title": "<PERSON><PERSON> partisan of amascut", "namespace": "Main", "revisionId": 14980861, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "", "timestamp": "2025-09-05T17:52:11Z"}]}, {"timestamp": "2025-09-06T04:32:25Z", "type": "new", "title": "Silver Chest (purple)", "namespace": "Main", "revisionId": 14980872, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:32:25Z"}]}, {"timestamp": "2025-09-06T04:32:15Z", "type": "new", "title": "Silver Chest (black)", "namespace": "Main", "revisionId": 14980871, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:32:15Z"}]}, {"timestamp": "2025-09-06T04:32:01Z", "type": "new", "title": "Silver Chest (crimson)", "namespace": "Main", "revisionId": 14980870, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:32:01Z"}]}, {"timestamp": "2025-09-06T04:31:45Z", "type": "new", "title": "Silver Chest (brown)", "namespace": "Main", "revisionId": 14980869, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:31:45Z"}]}, {"timestamp": "2025-09-06T04:31:33Z", "type": "new", "title": "Silver Chest (red)", "namespace": "Main", "revisionId": 14980868, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-06T04:31:33Z"}]}, {"timestamp": "2025-09-06T04:40:28Z", "type": "updated", "title": "Treasure Trails", "namespace": "Main", "revisionId": 14980889, "changes": [{"type": "edit", "user": "Data", "comment": "/* STASH units */", "timestamp": "2025-09-06T04:40:28Z"}]}, {"timestamp": "2025-09-06T04:40:08Z", "type": "updated", "title": "Woodcutting", "namespace": "Main", "revisionId": 14980888, "changes": [{"type": "edit", "user": "Data", "comment": "/* Types of trees */", "timestamp": "2025-09-06T04:40:08Z"}]}, {"timestamp": "2025-09-06T04:39:17Z", "type": "new", "title": "Ramarno", "namespace": "Main", "revisionId": 14980887, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T04:39:17Z"}]}, {"timestamp": "2025-09-06T04:36:49Z", "type": "new", "title": "Gold Chest (purple)", "namespace": "Main", "revisionId": 14980884, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-06T04:36:49Z"}]}, {"timestamp": "2025-09-06T04:36:32Z", "type": "new", "title": "Gold Chest (black)", "namespace": "Main", "revisionId": 14980883, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-06T04:36:32Z"}]}, {"timestamp": "2025-09-06T04:36:09Z", "type": "new", "title": "Gold Chest (crimson)", "namespace": "Main", "revisionId": 14980881, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-06T04:36:09Z"}]}, {"timestamp": "2025-09-06T04:35:40Z", "type": "new", "title": "Gold Chest (brown)", "namespace": "Main", "revisionId": 14980880, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-06T04:35:40Z"}]}, {"timestamp": "2025-09-06T04:35:30Z", "type": "new", "title": "Gold Chest (red)", "namespace": "Main", "revisionId": 14980879, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-06T04:35:30Z"}]}, {"timestamp": "2025-09-06T04:42:36Z", "type": "updated", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980895, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:23:08Z"}]}, {"timestamp": "2025-09-06T04:46:14Z", "type": "updated", "title": "Pharmakos berries", "namespace": "Main", "revisionId": 14980905, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-06T04:46:14Z"}]}, {"timestamp": "2025-09-06T04:46:00Z", "type": "new", "title": "Cadarn Clan", "namespace": "Main", "revisionId": 14980904, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T04:46:00Z"}]}, {"timestamp": "2025-09-06T04:45:23Z", "type": "updated", "title": "King's Ransom", "namespace": "Main", "revisionId": 14980903, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T04:45:23Z"}]}, {"timestamp": "2025-09-06T04:45:13Z", "type": "new", "title": "Spria", "namespace": "Main", "revisionId": 14980902, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T04:45:13Z"}]}, {"timestamp": "2025-09-06T04:45:04Z", "type": "updated", "title": "<PERSON>", "namespace": "Main", "revisionId": 14980901, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T04:45:04Z"}]}, {"timestamp": "2025-09-06T04:44:39Z", "type": "updated", "title": "Sins of the Father", "namespace": "Main", "revisionId": 14980900, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T04:44:39Z"}]}, {"timestamp": "2025-09-06T04:43:56Z", "type": "updated", "title": "Mahogany Homes", "namespace": "Main", "revisionId": 14980899, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T04:43:56Z"}]}, {"timestamp": "2025-09-06T04:43:38Z", "type": "updated", "title": "<PERSON>", "namespace": "Main", "revisionId": 14980898, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T04:43:38Z"}]}, {"timestamp": "2025-09-06T04:43:23Z", "type": "updated", "title": "Desert Treasure II - The Fallen Empire", "namespace": "Main", "revisionId": 14980897, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T04:43:23Z"}]}, {"timestamp": "2025-09-06T04:40:50Z", "type": "updated", "title": "<PERSON> of <PERSON><PERSON><PERSON><PERSON><PERSON> (c)", "namespace": "Main", "revisionId": 14980891, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "nobody calls this fbow", "timestamp": "2025-09-06T04:40:50Z"}]}, {"timestamp": "2025-09-06T04:40:44Z", "type": "new", "title": "Air Guitar", "namespace": "Main", "revisionId": 14980890, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T04:40:44Z"}]}, {"timestamp": "2025-09-06T04:47:17Z", "type": "updated", "title": "<PERSON><PERSON><PERSON><PERSON> (deadman)", "namespace": "Main", "revisionId": 14980911, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Undid revision 14924248 by [[Special:Contributions/Spineweilder|Spineweilder]] reverted as it's all relevant for the aforementioned weapon. - Shouldn't need to go to main variant to see basic information.", "timestamp": "2025-09-05T15:18:08Z"}]}, {"timestamp": "2025-09-06T04:55:07Z", "type": "new", "title": "Fairy Mafia", "namespace": "Main", "revisionId": 14980916, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T04:47:39Z"}]}, {"timestamp": "2025-09-06T04:47:25Z", "type": "new", "title": "Edicts of Guthix", "namespace": "Main", "revisionId": 14980912, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-06T04:47:25Z"}]}, {"timestamp": "2025-09-06T04:47:12Z", "type": "new", "title": "Wahisietel", "namespace": "Main", "revisionId": 14980910, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-06T04:47:12Z"}]}, {"timestamp": "2025-09-06T04:47:05Z", "type": "new", "title": "<PERSON><PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980909, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-06T04:47:05Z"}]}, {"timestamp": "2025-09-06T04:46:42Z", "type": "updated", "title": "Icyene Graveyard", "namespace": "Main", "revisionId": 14980908, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-06T04:46:42Z"}]}, {"timestamp": "2025-09-06T04:46:37Z", "type": "updated", "title": "<PERSON><PERSON><PERSON> godsword (deadman)", "namespace": "Main", "revisionId": 14980907, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-06T04:46:37Z"}]}, {"timestamp": "2025-09-06T04:54:26Z", "type": "updated", "title": "Fairy Resistance Hideout", "namespace": "Main", "revisionId": 14980915, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-06T04:54:26Z"}]}, {"timestamp": "2025-09-06T04:53:54Z", "type": "new", "title": "Money making guide/Buying iron ore from Ordan", "namespace": "Main", "revisionId": 14980914, "changes": [{"type": "edit", "user": "Cocoa7", "comment": "Use prices for 4-dose stam pots as they're more reliable and can be decanted anyway", "timestamp": "2025-09-06T04:53:54Z"}]}, {"timestamp": "2025-09-06T05:02:56Z", "type": "updated", "title": "Everlight", "namespace": "Main", "revisionId": 14980918, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-06T05:02:56Z"}]}, {"timestamp": "2025-09-06T05:19:25Z", "type": "new", "title": "Logout button", "namespace": "Main", "revisionId": 14980921, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */ logout tab changes on 5 mar 2018 (thumbs up/down buttons) and 10 dec 2024 (1-5 star buttons)", "timestamp": "2025-09-06T05:19:25Z"}]}, {"timestamp": "2025-09-06T05:18:13Z", "type": "new", "title": "Money making guide/Buying Relicym's balm", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "Cocoa7", "comment": "The math appeared to be incorrect. Balms (3) are sold for 200 each, but buying 2 inventories on a world makes them 296c on average taking into account trip + restock time. Therefore, each balm (4) should be around 394 GP each after decanting", "timestamp": "2025-09-06T05:18:13Z"}]}, {"timestamp": "2025-09-06T05:26:33Z", "type": "updated", "title": "Money making guide/Buying Relicym's balm", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "Cocoa7", "comment": "The math appeared to be incorrect. Balms (3) are sold for 200 each, but buying 2 inventories on a world makes them 296c on average taking into account trip + restock time. Therefore, each balm (4) should be around 394 GP each after decanting", "timestamp": "2025-09-06T05:18:13Z"}]}, {"timestamp": "2025-09-06T05:32:03Z", "type": "updated", "title": "Money making guide/Buying Relicym's balm", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "Cocoa7", "comment": "", "timestamp": "2025-09-06T05:26:33Z"}, {"type": "edit", "user": "Cocoa7", "comment": "The math appeared to be incorrect. Balms (3) are sold for 200 each, but buying 2 inventories on a world makes them 296c on average taking into account trip + restock time. Therefore, each balm (4) should be around 394 GP each after decanting", "timestamp": "2025-09-06T05:18:13Z"}]}, {"timestamp": "2025-09-06T05:42:24Z", "type": "updated", "title": "Money making guide/Buying Relicym's balm", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "Cocoa7", "comment": "Add rings of dueling cost", "timestamp": "2025-09-06T05:32:03Z"}, {"type": "edit", "user": "Cocoa7", "comment": "", "timestamp": "2025-09-06T05:26:33Z"}, {"type": "edit", "user": "Cocoa7", "comment": "The math appeared to be incorrect. Balms (3) are sold for 200 each, but buying 2 inventories on a world makes them 296c on average taking into account trip + restock time. Therefore, each balm (4) should be around 394 GP each after decanting", "timestamp": "2025-09-06T05:18:13Z"}]}, {"timestamp": "2025-09-06T05:46:22Z", "type": "new", "title": "Guardians of the Rift/Strategies", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "**************", "comment": "/* Recommended equipment */ Removing duplicate Graceful footnote", "timestamp": "2025-09-06T01:51:30Z"}, {"type": "edit", "user": "**************", "comment": "/* Recommended equipment */ Added Graceful cape for those without 99s", "timestamp": "2025-09-06T01:44:55Z"}]}, {"timestamp": "2025-09-06T05:51:02Z", "type": "updated", "title": "Money making guide/Buying Relicym's balm", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "Cocoa7", "comment": "", "timestamp": "2025-09-06T05:42:24Z"}, {"type": "edit", "user": "Cocoa7", "comment": "Add rings of dueling cost", "timestamp": "2025-09-06T05:32:03Z"}, {"type": "edit", "user": "Cocoa7", "comment": "", "timestamp": "2025-09-06T05:26:33Z"}, {"type": "edit", "user": "Cocoa7", "comment": "The math appeared to be incorrect. Balms (3) are sold for 200 each, but buying 2 inventories on a world makes them 296c on average taking into account trip + restock time. Therefore, each balm (4) should be around 394 GP each after decanting", "timestamp": "2025-09-06T05:18:13Z"}]}, {"timestamp": "2025-09-06T06:11:05Z", "type": "updated", "title": "Amethyst dart", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "ranged str comparison", "timestamp": "2025-09-06T06:11:05Z"}]}, {"timestamp": "2025-09-06T06:53:17Z", "type": "new", "title": "Kalphite Queen/Strategies", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Equipment */", "timestamp": "2025-09-06T06:53:17Z"}]}, {"timestamp": "2025-09-06T07:08:14Z", "type": "updated", "title": "Cheese+tom batta", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed the required level (Only for adding the equa leaves) and the xp gained for the whole process was wrong", "timestamp": "2025-09-06T07:08:14Z"}]}, {"timestamp": "2025-09-06T07:07:43Z", "type": "updated", "title": "Vegetable batta", "namespace": "Main", "revisionId": 14980953, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed the required level (Only for adding the equa leaves) and the xp gained for the whole process was wrong", "timestamp": "2025-09-06T07:07:43Z"}]}, {"timestamp": "2025-09-06T07:06:42Z", "type": "updated", "title": "W<PERSON> batta", "namespace": "Main", "revisionId": 14980952, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed the required level (Only for adding the equa leaves)", "timestamp": "2025-09-06T07:06:42Z"}]}, {"timestamp": "2025-09-06T07:04:26Z", "type": "updated", "title": "Soulflame horn", "namespace": "Main", "revisionId": 14980951, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Special attack */", "timestamp": "2025-09-06T07:04:26Z"}]}, {"timestamp": "2025-09-06T07:04:21Z", "type": "new", "title": "Tithe Farm/Strategies", "namespace": "Main", "revisionId": 14980950, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "/* Basic strategy tile markers */", "timestamp": "2025-09-06T07:04:21Z"}]}, {"timestamp": "2025-09-06T07:09:29Z", "type": "updated", "title": "Chocchip crunchies", "namespace": "Main", "revisionId": 14980957, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed the required level (Only for adding the chocolate dust) and the xp gained for the whole process was wrong", "timestamp": "2025-09-05T23:31:00Z"}]}, {"timestamp": "2025-09-06T07:09:35Z", "type": "updated", "title": "Worm crunchies", "namespace": "Main", "revisionId": 14980958, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed the required level (Only for adding the gnome spice) and the xp gained for the whole process was wrong", "timestamp": "2025-09-05T23:30:29Z"}]}, {"timestamp": "2025-09-06T07:09:17Z", "type": "updated", "title": "Spicy crunchies", "namespace": "Main", "revisionId": 14980956, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed required level (Only at add gnome spice) and the total xp gained for the whole process", "timestamp": "2025-09-05T23:29:41Z"}]}, {"timestamp": "2025-09-06T07:08:55Z", "type": "updated", "title": "Toad crunchies", "namespace": "Main", "revisionId": 14980955, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed required level (Only to add equa leaves)", "timestamp": "2025-09-05T23:28:55Z"}]}, {"timestamp": "2025-09-06T07:13:52Z", "type": "updated", "title": "Chocolate bomb", "namespace": "Main", "revisionId": 14980961, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed the required level (Only for adding the chocolate dust and pot of cream) - and removed the boostable level variable", "timestamp": "2025-09-06T07:13:52Z"}]}, {"timestamp": "2025-09-06T07:12:38Z", "type": "updated", "title": "Veg ball", "namespace": "Main", "revisionId": 14980960, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed the required level (Only for adding the equa leaves) - and removed the boostable level variable", "timestamp": "2025-09-06T07:12:38Z"}]}, {"timestamp": "2025-09-06T07:11:31Z", "type": "updated", "title": "Worm hole", "namespace": "Main", "revisionId": 14980959, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Changed the required level (Only for adding the equa leaves) - and removed the boostable level variable", "timestamp": "2025-09-06T07:11:31Z"}]}, {"timestamp": "2025-09-06T07:35:37Z", "type": "updated", "title": "Tithe Farm/Strategies", "namespace": "Main", "revisionId": 14980968, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "/* Basic strategy tile markers */", "timestamp": "2025-09-06T07:04:21Z"}]}, {"timestamp": "2025-09-06T07:33:16Z", "type": "new", "title": "Pay-to-play Smithing training", "namespace": "Main", "revisionId": 14980965, "changes": [{"type": "edit", "user": "2001:4479:2605:3200:457D:B7C5:9216:1D62", "comment": "/* Levels 40–99: Blast Furnace gold bars */ Rewording so that you dont accidentally Equip the ice gloves", "timestamp": "2025-09-06T07:33:16Z"}]}, {"timestamp": "2025-09-06T07:52:34Z", "type": "updated", "title": "Temple Trekking", "namespace": "Main", "revisionId": 14980971, "changes": [{"type": "edit", "user": "LesserGatsby", "comment": "Not to be rude, but whoever wrote the requirements section of this guide did not understand the meaning of \"required\".  This change separates the gear/items lists into a Required and a Recommended section to stress what gear is required for the minigame.   I have personally fallen into the trap of missing an item in this guide. These changes save future readers from the same fate.", "timestamp": "2025-09-06T01:51:49Z"}]}, {"timestamp": "2025-09-06T08:02:42Z", "type": "updated", "title": "Pirate's hook", "namespace": "Main", "revisionId": 14980975, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Fixed time conversion", "timestamp": "2025-09-06T08:02:42Z"}]}, {"timestamp": "2025-09-06T08:15:51Z", "type": "updated", "title": "Rum Deal", "namespace": "Main", "revisionId": 14980987, "changes": [{"type": "edit", "user": "***************", "comment": "Obtaining Sluglings through Fishing is optional. You can finish the quest level 1 Fishing.", "timestamp": "2025-09-06T08:15:51Z"}]}, {"timestamp": "2025-09-06T08:56:54Z", "type": "new", "title": "The Leviathan/Strategies", "namespace": "Main", "revisionId": 14980994, "changes": [{"type": "edit", "user": "Fettism", "comment": "/* Equipment */ Added accursed sceptre as it boosts shadow dps", "timestamp": "2025-09-06T08:56:54Z"}]}, {"timestamp": "2025-09-06T09:37:24Z", "type": "updated", "title": "Prayer potion", "namespace": "Main", "revisionId": 14981000, "changes": [{"type": "edit", "user": "<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON>", "comment": "Adding MMG section", "timestamp": "2025-09-06T09:37:24Z"}]}, {"timestamp": "2025-09-06T10:07:28Z", "type": "updated", "title": "Frost Nagua", "namespace": "Main", "revisionId": 14981001, "changes": [{"type": "edit", "user": "***************", "comment": "/* Runes and ammunition */ I've gotten a 13 chaos rune drop and a 18 drop, so they would drop under the quoted threshold of 20-40", "timestamp": "2025-09-06T10:07:28Z"}]}, {"timestamp": "2025-09-06T10:28:40Z", "type": "new", "title": "Maniacal monkey/Strategies", "namespace": "Main", "revisionId": 14981003, "changes": [{"type": "edit", "user": "************", "comment": "", "timestamp": "2025-09-06T10:28:40Z"}]}, {"timestamp": "2025-09-06T10:55:54Z", "type": "updated", "title": "Armour/Highest bonuses", "namespace": "Main", "revisionId": 14981008, "changes": [{"type": "edit", "user": "Mike-south", "comment": "/* Ranged Defence */ Added echo boots to Range defence", "timestamp": "2025-09-06T10:55:54Z"}]}, {"timestamp": "2025-09-06T11:06:15Z", "type": "updated", "title": "Prayer potion", "namespace": "Main", "revisionId": 14981009, "changes": [{"type": "edit", "user": "<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON>", "comment": "Adding MMG section", "timestamp": "2025-09-06T09:37:24Z"}]}, {"timestamp": "2025-09-06T11:11:27Z", "type": "updated", "title": "Prayer potion", "namespace": "Main", "revisionId": 14981010, "changes": [{"type": "edit", "user": "178.43.183.12", "comment": "Undid revision 14981000 by [[Special:Contributions/Dr. C-Dawg|Dr. <PERSON>-Dawg]] ([[User talk:Dr. <PERSON><PERSON>Dawg|talk]])", "timestamp": "2025-09-06T11:06:15Z"}, {"type": "edit", "user": "<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON>", "comment": "Adding MMG section", "timestamp": "2025-09-06T09:37:24Z"}]}]