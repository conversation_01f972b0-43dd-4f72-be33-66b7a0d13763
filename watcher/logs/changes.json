[{"timestamp": "2025-09-05T20:36:07Z", "type": "new", "title": "<PERSON> (disambiguation)", "namespace": "Main", "revisionId": 14980680, "changes": [{"type": "edit", "user": "BigDiesel2m", "comment": "", "timestamp": "2025-09-05T20:36:07Z"}]}, {"timestamp": "2025-09-05T20:29:59Z", "type": "updated", "title": "<PERSON><PERSON>'s <PERSON><PERSON>", "namespace": "Main", "revisionId": 14980675, "changes": [{"type": "edit", "user": "BigDiesel2m", "comment": "", "timestamp": "2025-09-05T20:29:59Z"}]}, {"timestamp": "2025-09-05T20:28:56Z", "type": "updated", "title": "<PERSON><PERSON><PERSON><PERSON>' <PERSON><PERSON> (monster)", "namespace": "Main", "revisionId": 14980673, "changes": [{"type": "edit", "user": "BigDiesel2m", "comment": "", "timestamp": "2025-09-05T20:28:56Z"}]}, {"timestamp": "2025-09-05T20:17:07Z", "type": "new", "title": "Airplane", "namespace": "Main", "revisionId": 14980667, "changes": [{"type": "new", "user": "205.220.129.249", "comment": "Redirected page to [[Gnome glider]]", "timestamp": "2025-09-05T20:17:07Z"}]}, {"timestamp": "2025-09-05T20:15:27Z", "type": "new", "title": "Aeroplane", "namespace": "Main", "revisionId": 14980665, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "Redirected page to [[Gnome glider]]", "timestamp": "2025-09-05T20:15:27Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Removed redirect to [[Nonexistence]]", "timestamp": "2025-09-05T17:13:04Z"}, {"type": "new", "user": "***************", "comment": "Currently on business flight wifi in the middle of the pacific ocean. I wonder what country this IP shows up as", "timestamp": "2025-09-05T14:43:24Z"}]}, {"timestamp": "2025-09-05T20:01:02Z", "type": "new", "title": "<PERSON><PERSON>", "namespace": "Main", "revisionId": 14980658, "changes": [{"type": "edit", "user": "************", "comment": "Added citations", "timestamp": "2025-09-05T20:01:02Z"}, {"type": "edit", "user": "************", "comment": "Expanded section on types of macro to include details on advanced bots, human-like mouse movements, and conversational abilities.", "timestamp": "2025-09-04T20:11:13Z"}]}, {"timestamp": "2025-09-05T20:00:13Z", "type": "updated", "title": "Imp", "namespace": "Main", "revisionId": 14980656, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "", "timestamp": "2025-09-05T20:00:13Z"}]}, {"timestamp": "2025-09-05T19:58:05Z", "type": "new", "title": "Wealthy citizen", "namespace": "Main", "revisionId": 14980654, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "", "timestamp": "2025-09-05T19:58:05Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Undid revision 14979846 by [[Special:Contributions/Admblack|Admblack]] ([[User talk:Admblack|talk]]) - This was fixed in the last update.", "timestamp": "2025-09-05T19:50:51Z"}, {"type": "edit", "user": "Admblack", "comment": "Fixed examine text.", "timestamp": "2025-09-04T14:59:05Z"}]}, {"timestamp": "2025-09-05T19:56:11Z", "type": "new", "title": "<PERSON><PERSON> Master", "namespace": "Main", "revisionId": 14980653, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "", "timestamp": "2025-09-05T19:56:11Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "\"Grid Points\" capitalised in the news post", "timestamp": "2025-09-05T19:55:42Z"}, {"type": "edit", "user": "Tenshibeat", "comment": "", "timestamp": "2025-09-04T06:25:50Z"}]}, {"timestamp": "2025-09-05T19:51:04Z", "type": "new", "title": "Awowogei", "namespace": "Main", "revisionId": 14980649, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-05T19:51:04Z"}]}, {"timestamp": "2025-09-05T19:46:25Z", "type": "new", "title": "Monkey (magic carpet)", "namespace": "Main", "revisionId": 14980645, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-05T19:46:25Z"}]}, {"timestamp": "2025-09-05T19:39:24Z", "type": "updated", "title": "Quests/Requirements by skill", "namespace": "Main", "revisionId": 14980644, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "/* See also */", "timestamp": "2025-09-05T19:39:24Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "agility & combat header changes", "timestamp": "2025-09-05T19:35:13Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "quest point icon header changed", "timestamp": "2025-09-05T19:24:06Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "header changes", "timestamp": "2025-09-05T19:21:35Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Added sortable columns, remove highlighted boostable cells, aligned center", "timestamp": "2025-09-05T19:12:31Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "/* x40px|link=Ranged Ranged */", "timestamp": "2025-09-05T18:21:41Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "/* x40px|link=Quest points Quest points */", "timestamp": "2025-09-05T18:21:08Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "/* x40px|link=Mining Mining */", "timestamp": "2025-09-05T18:20:29Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "/* x40px|link=Magic Magic */", "timestamp": "2025-09-05T18:20:14Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "Fix tears of guthix wikisync row", "timestamp": "2025-09-05T18:19:37Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>of<PERSON>", "comment": "the yes of glouphrie -> the eyes of glou<PERSON>rie", "timestamp": "2025-09-05T18:18:33Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Added lighttable and reworked columns", "timestamp": "2025-09-05T18:15:22Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Removal of extra characters", "timestamp": "2025-09-04T23:37:27Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Added two-column table formatting with numerical ordering and replaced boostable template syntax with plain \"Yes\" text for better readability and Runelite compatibility.", "timestamp": "2025-09-04T23:34:51Z"}]}, {"timestamp": "2025-09-05T19:38:40Z", "type": "updated", "title": "Echo crystal", "namespace": "Main", "revisionId": 14980643, "changes": [{"type": "edit", "user": "WillZero", "comment": "fixed/clarified cumulative drop rates & expected crystal amounts for completing all 12 waves. you can see the calculations here: https://docs.google.com/spreadsheets/d/1Ua3nUwskxyuPJqVKFnNeRrys3wFzYjE8xDXkEMQEtMY/edit?usp=sharing", "timestamp": "2025-09-05T19:38:40Z"}]}, {"timestamp": "2025-09-05T19:20:58Z", "type": "new", "title": "Vale Totems/Strategies", "namespace": "Main", "revisionId": 14980633, "changes": [{"type": "edit", "user": "Ultimatedra1", "comment": "Added link to Fletching page", "timestamp": "2025-09-05T19:20:58Z"}]}, {"timestamp": "2025-09-05T19:17:06Z", "type": "new", "title": "Re<PERSON>s Chest (Fortis Colosseum)", "namespace": "Main", "revisionId": 14980632, "changes": [{"type": "edit", "user": "WillZero", "comment": "Added cumulative drop rate of any piece of tonalztics of ralos if you complete all 12 waves. You can see the calculation here: https://docs.google.com/spreadsheets/d/1GYvmOwkhyqnHz35nIKdoZEUoloCnG70mlHB7fpe9rG0/edit?usp=sharing", "timestamp": "2025-09-05T19:17:06Z"}, {"type": "edit", "user": "WillZero", "comment": "Added the differing rates for uniques between waves 4-6 and 7-12", "timestamp": "2025-09-05T19:10:16Z"}, {"type": "edit", "user": "WillZero", "comment": "Added cumulative drop rate of receiving at least 1 echo crystal if you complete all 12 waves. You can see the calculation here: https://docs.google.com/spreadsheets/d/1vzWuAnmoxsaLO4fld9no086xJmuxIHOEWCPDd2JluuI/edit?usp=sharing", "timestamp": "2025-09-05T19:06:18Z"}, {"type": "edit", "user": "WillZero", "comment": "Added cumulative drop rate of any piece of sunfire fanatic armour if you complete all 12 waves. You can see the calculation here: https://docs.google.com/spreadsheets/d/1EUERT4FyhXNPjjURpwM1ZFa_T1SfpYrgYtEYA846BDo/edit?usp=sharing", "timestamp": "2025-09-05T19:00:38Z"}]}, {"timestamp": "2025-09-05T19:14:29Z", "type": "updated", "title": "Tonalztics of ralos", "namespace": "Main", "revisionId": 14980629, "changes": [{"type": "edit", "user": "WillZero", "comment": "Fixed incorrect cumulative drop rate. See https://docs.google.com/spreadsheets/d/1GYvmOwkhyqnHz35nIKdoZEUoloCnG70mlHB7fpe9rG0/edit?usp=sharing for calculation", "timestamp": "2025-09-05T19:14:29Z"}]}, {"timestamp": "2025-09-05T18:53:42Z", "type": "updated", "title": "Sunfire fanatic chausses", "namespace": "Main", "revisionId": 14980611, "changes": [{"type": "edit", "user": "WillZero", "comment": "Fixed incorrect cumulative sunfire drop rate. See https://docs.google.com/spreadsheets/d/1MyRpo03uKFPHO-mP8-S0PsvcChYdUAcieC4P9Y_XZP4/edit?usp=sharing for calculation", "timestamp": "2025-09-05T18:53:42Z"}]}, {"timestamp": "2025-09-05T18:53:30Z", "type": "updated", "title": "Sunfire fanatic cuirass", "namespace": "Main", "revisionId": 14980610, "changes": [{"type": "edit", "user": "WillZero", "comment": "Fixed incorrect cumulative sunfire drop rate. See https://docs.google.com/spreadsheets/d/1MyRpo03uKFPHO-mP8-S0PsvcChYdUAcieC4P9Y_XZP4/edit?usp=sharing for calculation", "timestamp": "2025-09-05T18:53:30Z"}]}, {"timestamp": "2025-09-05T18:53:11Z", "type": "updated", "title": "Sunfire fanatic helm", "namespace": "Main", "revisionId": 14980609, "changes": [{"type": "edit", "user": "WillZero", "comment": "Fixed incorrect cumulative sunfire drop rate. See https://docs.google.com/spreadsheets/d/1MyRpo03uKFPHO-mP8-S0PsvcChYdUAcieC4P9Y_XZP4/edit?usp=sharing for calculation", "timestamp": "2025-09-05T18:53:11Z"}]}, {"timestamp": "2025-09-05T18:35:50Z", "type": "new", "title": "Money making guide/Mining gemstones", "namespace": "Main", "revisionId": 14980604, "changes": [{"type": "edit", "user": "CthulhuInACan", "comment": "", "timestamp": "2025-09-05T18:35:50Z"}]}, {"timestamp": "2025-09-05T18:16:45Z", "type": "new", "title": "Temporary skill drain", "namespace": "Main", "revisionId": 14980594, "changes": [{"type": "edit", "user": "Microbrews", "comment": "/* Items */ kebab", "timestamp": "2025-09-05T18:16:45Z"}]}, {"timestamp": "2025-09-05T18:06:48Z", "type": "updated", "title": "Overload (-)", "namespace": "Main", "revisionId": 14980589, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-05T18:06:48Z"}]}, {"timestamp": "2025-09-05T18:06:37Z", "type": "updated", "title": "Overload (Chambers of Xeric)", "namespace": "Main", "revisionId": 14980588, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-05T18:06:37Z"}]}, {"timestamp": "2025-09-05T18:04:34Z", "type": "updated", "title": "<PERSON><PERSON>th", "namespace": "Main", "revisionId": 14980586, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-05T18:04:34Z"}]}, {"timestamp": "2025-09-05T18:02:21Z", "type": "updated", "title": "Sunlight antler bolts", "namespace": "Main", "revisionId": 14980583, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-05T18:02:21Z"}]}, {"timestamp": "2025-09-05T17:52:11Z", "type": "updated", "title": "<PERSON><PERSON> partisan of amascut", "namespace": "Main", "revisionId": 14980579, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "", "timestamp": "2025-09-05T17:52:11Z"}]}, {"timestamp": "2025-09-05T17:49:07Z", "type": "updated", "title": "Thread of elidinis", "namespace": "Main", "revisionId": 14980578, "changes": [{"type": "edit", "user": "Microbrews", "comment": "", "timestamp": "2025-09-05T17:49:07Z"}]}, {"timestamp": "2025-09-05T17:33:44Z", "type": "new", "title": "Ironman guide", "namespace": "Main", "revisionId": 14980573, "changes": [{"type": "edit", "user": "143.105.92.35", "comment": "/* Early-game */", "timestamp": "2025-09-05T17:33:44Z"}, {"type": "edit", "user": "82.42.131.126", "comment": "Undid revision 14980555 by [[Special:Contributions/Fig52|Fig52]] ([[User talk:Fig52|talk]])", "timestamp": "2025-09-05T17:18:13Z"}, {"type": "edit", "user": "Fig52", "comment": "/* Obtaining useful items */", "timestamp": "2025-09-05T16:39:25Z"}, {"type": "edit", "user": "Fig52", "comment": "Moved the \"food\" and \"useful items\" sections into the early game section so they could be useful to new players. Removed a section on obtaining Amulets of power and strength early because their requirements are already low. Added a section on obtaining Amulets of glory. Removed a complicated section about midgame food because karambwans are simple and easy to obtain. Removed a section advising players to use pvp worlds and blighted food", "timestamp": "2025-09-05T16:36:09Z"}, {"type": "edit", "user": "Fig52", "comment": "/* Moneymaking */ Removed a section advising players to train from 60 to 99 at Agility Pyramid, since that won't be aligned with most accounts' goals. Changed the section on Moneymaking to be more broad, since the Moneymaking guide page gets pretty specific.", "timestamp": "2025-09-05T15:27:56Z"}, {"type": "edit", "user": "Fig52", "comment": "/* Overview */", "timestamp": "2025-09-05T15:21:04Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "/* Food and supplies */ stonghold", "timestamp": "2025-09-05T15:19:23Z"}, {"type": "edit", "user": "Fig52", "comment": "", "timestamp": "2025-09-05T15:13:26Z"}, {"type": "edit", "user": "Fig52", "comment": "/* Food and supplies */", "timestamp": "2025-09-05T02:39:44Z"}, {"type": "edit", "user": "Telemonke", "comment": "/* Limitations */Fix typo", "timestamp": "2025-09-04T16:37:54Z"}, {"type": "edit", "user": "Fig52", "comment": "/* Transportation */ Added a section on obtaining early food and supplies which I hope to expand in the future. Removed some transportation methods which are redundant or which an early iron won't have access to, including a jewelry box and a portal nexus. Removed the section advising players to hard-craft Law runes for teleport spells.", "timestamp": "2025-09-04T16:22:27Z"}, {"type": "edit", "user": "98.36.125.219", "comment": "rephrased the opening paragraph", "timestamp": "2025-09-04T14:58:12Z"}]}, {"timestamp": "2025-09-05T17:25:44Z", "type": "updated", "title": "Dragon Slayer II", "namespace": "Main", "revisionId": 14980571, "changes": [{"type": "edit", "user": "************", "comment": "/* <PERSON><PERSON>s */", "timestamp": "2025-09-05T17:25:44Z"}]}, {"timestamp": "2025-09-05T17:22:14Z", "type": "new", "title": "Disease (Farming)", "namespace": "Main", "revisionId": 14980570, "changes": [{"type": "edit", "user": "2600:1700:DB45:B410:38CC:C4EC:D94D:F223", "comment": "See feedback. The team can fix it if need be", "timestamp": "2025-09-05T17:22:14Z"}]}, {"timestamp": "2025-09-05T17:12:08Z", "type": "new", "title": "<PERSON>", "namespace": "Main", "revisionId": 14980565, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "switching to use otheruses template", "timestamp": "2025-09-05T17:12:08Z"}, {"type": "edit", "user": "AUniqueAlias", "comment": "", "timestamp": "2025-09-05T16:55:42Z"}]}, {"timestamp": "2025-09-05T17:02:10Z", "type": "new", "title": "Money making guide/Casting plank make", "namespace": "Main", "revisionId": 14980559, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "reword this table header", "timestamp": "2025-09-05T17:02:10Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Removing vouchers from profit table and changing how they are described, since they just make this method faster but less profitable", "timestamp": "2025-09-05T16:57:28Z"}, {"type": "edit", "user": "<PERSON><PERSON>", "comment": "Remove sawmill vouchers as they make this about 200k/hr worse", "timestamp": "2025-09-05T06:39:01Z"}]}, {"timestamp": "2025-09-05T16:34:42Z", "type": "new", "title": "Money making guide/Delivering food in Gnome Restaurant", "namespace": "Main", "revisionId": 14980553, "changes": [{"type": "edit", "user": "LunarfusionX", "comment": "added an alternate travel method to Captain <PERSON><PERSON> via minecart", "timestamp": "2025-09-05T16:34:42Z"}]}, {"timestamp": "2025-09-05T16:32:31Z", "type": "updated", "title": "Deranged archaeologist", "namespace": "Main", "revisionId": 14980552, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "Fix link", "timestamp": "2025-09-05T16:32:31Z"}, {"type": "edit", "user": "Lemurmoder", "comment": "", "timestamp": "2025-09-05T16:28:04Z"}, {"type": "edit", "user": "2600:1700:3580:1FF0:E172:9731:A472:D1D5", "comment": "small grammar error", "timestamp": "2025-09-05T01:41:13Z"}]}, {"timestamp": "2025-09-05T16:28:39Z", "type": "updated", "title": "Fishing", "namespace": "Main", "revisionId": 14980550, "changes": [{"type": "edit", "user": "98.173.254.30", "comment": "Changed phrasing so the sentence read easier", "timestamp": "2025-09-05T16:28:39Z"}]}, {"timestamp": "2025-09-05T16:28:35Z", "type": "new", "title": "Grid points", "namespace": "Main", "revisionId": 14980549, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "Replaced content with \"{{D}}\"", "timestamp": "2025-09-05T16:28:35Z"}, {"type": "new", "user": "209.152.151.134", "comment": "PLEASE ADD LEAGUES 6", "timestamp": "2025-09-05T16:22:54Z"}]}, {"timestamp": "2025-09-05T16:18:38Z", "type": "new", "title": "Slayer task/Dark beast", "namespace": "Main", "revisionId": 14980546, "changes": [{"type": "edit", "user": "Funds", "comment": "", "timestamp": "2025-09-05T16:18:38Z"}]}, {"timestamp": "2025-09-05T16:05:47Z", "type": "new", "title": "Ironman Mode", "namespace": "Main", "revisionId": 14980544, "changes": [{"type": "edit", "user": "RealEvanem", "comment": "/* Distinctions */ OXFORD COMMA DETECTED: DESTROY WITH PREJUDICE", "timestamp": "2025-09-05T16:05:47Z"}]}, {"timestamp": "2025-09-05T16:04:06Z", "type": "updated", "title": "Xamphur", "namespace": "Main", "revisionId": 14980543, "changes": [{"type": "edit", "user": "Funds", "comment": "", "timestamp": "2025-09-05T16:04:06Z"}]}, {"timestamp": "2025-09-05T16:01:00Z", "type": "new", "title": "Lunar spellbook", "namespace": "Main", "revisionId": 14980542, "changes": [{"type": "edit", "user": "*************", "comment": "/* Switching spellbooks */ added POH occult altar information about switching books", "timestamp": "2025-09-05T16:01:00Z"}]}, {"timestamp": "2025-09-05T15:35:31Z", "type": "new", "title": "Money making guide/Picking fruit trees", "namespace": "Main", "revisionId": 14980539, "changes": [{"type": "edit", "user": "Psionia", "comment": "Added switch to the other section so that it updates alongside all other switches.", "timestamp": "2025-09-05T15:35:31Z"}, {"type": "new", "user": "Psionia", "comment": "New MMG to consolidate various existing picking MMGs.", "timestamp": "2025-09-05T15:22:05Z"}]}, {"timestamp": "2025-09-05T15:23:41Z", "type": "new", "title": "Money making guide/Picking coconuts", "namespace": "Main", "revisionId": 14980537, "changes": [{"type": "edit", "user": "Psionia", "comment": "Excluded as being incorporated into Money Making Guide/Picking Fruit Trees", "timestamp": "2025-09-05T15:23:41Z"}]}, {"timestamp": "2025-09-05T15:23:04Z", "type": "new", "title": "Money making guide/Picking papayas", "namespace": "Main", "revisionId": 14980536, "changes": [{"type": "edit", "user": "Psionia", "comment": "Excluded as being incorporated into Money Making Guide/Picking Fruit Trees", "timestamp": "2025-09-05T15:23:04Z"}]}, {"timestamp": "2025-09-05T15:18:08Z", "type": "updated", "title": "<PERSON><PERSON><PERSON><PERSON> (deadman)", "namespace": "Main", "revisionId": 14980531, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Undid revision 14924248 by [[Special:Contributions/Spineweilder|Spineweilder]] reverted as it's all relevant for the aforementioned weapon. - Shouldn't need to go to main variant to see basic information.", "timestamp": "2025-09-05T15:18:08Z"}]}, {"timestamp": "2025-09-05T15:12:18Z", "type": "new", "title": "Estate agent", "namespace": "Main", "revisionId": 14980528, "changes": [{"type": "edit", "user": "*************", "comment": "/* Trivia */", "timestamp": "2025-09-05T15:12:18Z"}, {"type": "edit", "user": "*************", "comment": "moved \"The Construction cape is the only cape that may be bought from multiple NPCs.\" to trivia", "timestamp": "2025-09-05T15:11:39Z"}]}, {"timestamp": "2025-09-05T15:11:43Z", "type": "updated", "title": "Gemstone Crab", "namespace": "Main", "revisionId": 14980527, "changes": [{"type": "edit", "user": "Funds", "comment": "/* Trivia */", "timestamp": "2025-09-05T15:11:43Z"}]}, {"timestamp": "2025-09-05T15:06:04Z", "type": "updated", "title": "Rogues' Den", "namespace": "Main", "revisionId": 14980525, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "/* Reward */ Added expected time to get the full kit", "timestamp": "2025-09-05T15:06:04Z"}]}, {"timestamp": "2025-09-05T15:03:39Z", "type": "updated", "title": "V<PERSON><PERSON><PERSON> spiderling", "namespace": "Main", "revisionId": 14980523, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "config name calls it legacy", "timestamp": "2025-09-05T15:03:39Z"}]}, {"timestamp": "2025-09-05T15:01:16Z", "type": "new", "title": "Metamorphosis", "namespace": "Main", "revisionId": 14980510, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "", "timestamp": "2025-09-05T15:01:16Z"}]}, {"timestamp": "2025-09-05T15:00:25Z", "type": "updated", "title": "Callisto cub", "namespace": "Main", "revisionId": 14980505, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "config name calls it legacy", "timestamp": "2025-09-05T15:00:25Z"}]}, {"timestamp": "2025-09-05T14:59:18Z", "type": "updated", "title": "<PERSON><PERSON><PERSON><PERSON> jr.", "namespace": "Main", "revisionId": 14980504, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "", "timestamp": "2025-09-05T14:59:18Z"}]}, {"timestamp": "2025-09-05T14:51:00Z", "type": "new", "title": "Treasure Trails/Guide/Hot Cold/Master", "namespace": "Main", "revisionId": 14980499, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "/* Varlamore */", "timestamp": "2025-09-05T14:51:00Z"}]}, {"timestamp": "2025-09-05T14:16:19Z", "type": "new", "title": "List of music release dates", "namespace": "Main", "revisionId": 14980492, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "custom toc with horizontal list", "timestamp": "2025-09-05T14:16:19Z"}]}, {"timestamp": "2025-09-05T13:59:17Z", "type": "new", "title": "Black warlock", "namespace": "Main", "revisionId": 14980485, "changes": [{"type": "edit", "user": "2600:1001:B017:2298:514F:F5CA:1045:FA3", "comment": "Tested EXP per hour using a foot pedal to click in the same spot and got roughly 35,000 exp per hour.", "timestamp": "2025-09-05T13:59:17Z"}, {"type": "edit", "user": "2600:1001:B017:2298:514F:F5CA:1045:FA3", "comment": "Added an AFK training method that involved Black Warlock butterflies in the high-level area of the Farming guild.", "timestamp": "2025-09-05T13:22:44Z"}]}, {"timestamp": "2025-09-05T12:50:26Z", "type": "new", "title": "Fortis Colosseum/Strategies", "namespace": "Main", "revisionId": 14980462, "changes": [{"type": "edit", "user": "LilyUIM", "comment": "/* Modifiers */ thick separators", "timestamp": "2025-09-05T12:50:26Z"}]}, {"timestamp": "2025-09-05T12:20:40Z", "type": "new", "title": "Money making guide/Farming hops", "namespace": "Main", "revisionId": 14980454, "changes": [{"type": "edit", "user": "Psionia", "comment": "Adjusted the activity to be generic.", "timestamp": "2025-09-05T12:20:40Z"}, {"type": "edit", "user": "Psionia", "comment": "Converted table elements so they no longer show trailing 0s.", "timestamp": "2025-09-04T14:33:58Z"}, {"type": "new", "user": "Psionia", "comment": "Introducing new MMG to consolidate all hops farming methods. This is currently split up into various MMGs but it is only possible to do one type of hop farming at a time.", "timestamp": "2025-09-04T14:07:56Z"}]}, {"timestamp": "2025-09-05T12:03:34Z", "type": "new", "title": "Forester's Campfire", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "Myveryownaccount", "comment": "/* Permanent fire conversions */  improper use of a table by WAI standards", "timestamp": "2025-09-05T12:03:34Z"}]}, {"timestamp": "2025-09-05T12:00:50Z", "type": "updated", "title": "Stamina potion", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "Gazu", "comment": "move ring of endurance info down to utility section", "timestamp": "2025-09-05T12:00:50Z"}, {"type": "edit", "user": "Gazu", "comment": "add missing link", "timestamp": "2025-09-05T11:59:07Z"}, {"type": "edit", "user": "Gazu", "comment": "/* Ironmen access */ added common uses after run energy update from discord responses, and separated ironman access to its own heading from subheading under utility", "timestamp": "2025-09-05T11:58:37Z"}]}, {"timestamp": "2025-09-05T11:47:41Z", "type": "new", "title": "Grotesque Guardians/Strategies", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "Spaghet", "comment": "/* Equipment */ Granite hammer was shown in two positions; removed one of them", "timestamp": "2025-09-05T11:47:41Z"}, {"type": "edit", "user": "2601:152:4601:84C0:B8D2:D15B:5C2F:7A18", "comment": "/* Equipment */", "timestamp": "2025-09-05T06:00:12Z"}, {"type": "edit", "user": "Pobratym", "comment": "/* Equipment */Granite hammer is not competitive with SRA", "timestamp": "2025-09-05T05:21:25Z"}]}, {"timestamp": "2025-09-05T11:31:27Z", "type": "new", "title": "Razmire Builders Merchants.", "namespace": "Main", "revisionId": 14980440, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "added a note describing shop functionality", "timestamp": "2025-09-05T11:31:27Z"}]}, {"timestamp": "2025-09-05T10:55:52Z", "type": "new", "title": "Durgok", "namespace": "Main", "revisionId": 14980428, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "/* Dialogue */ npc", "timestamp": "2025-09-05T10:55:52Z"}]}, {"timestamp": "2025-09-05T10:48:59Z", "type": "new", "title": "Caique", "namespace": "Main", "revisionId": 14980427, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "added [[Category:Birds]] using [[Help:Gadget-HotCat|HotCat]]", "timestamp": "2025-09-05T10:48:59Z"}]}, {"timestamp": "2025-09-05T10:48:41Z", "type": "new", "title": "<PERSON><PERSON>", "namespace": "Main", "revisionId": 14980426, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "added [[Category:Birds]] using [[Help:Gadget-HotCat|HotCat]]", "timestamp": "2025-09-05T10:48:41Z"}]}, {"timestamp": "2025-09-05T10:48:19Z", "type": "new", "title": "Toucan", "namespace": "Main", "revisionId": 14980425, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "added [[Category:Birds]] using [[Help:Gadget-HotCat|HotCat]]", "timestamp": "2025-09-05T10:48:19Z"}]}, {"timestamp": "2025-09-05T10:47:59Z", "type": "new", "title": "Harpy eagle", "namespace": "Main", "revisionId": 14980424, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "added [[Category:Birds]] using [[Help:Gadget-HotCat|HotCat]]", "timestamp": "2025-09-05T10:47:59Z"}]}, {"timestamp": "2025-09-05T10:36:36Z", "type": "new", "title": "Modifiers", "namespace": "Main", "revisionId": 14980420, "changes": [{"type": "new", "user": "LilyUIM", "comment": "Redirected page to [[Fortis Colosseum/Strategies#Modifiers]]", "timestamp": "2025-09-05T10:36:36Z"}]}, {"timestamp": "2025-09-05T10:10:35Z", "type": "new", "title": "Loinur", "namespace": "Main", "revisionId": 14980412, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "/* Dialogue */ npc", "timestamp": "2025-09-05T10:10:35Z"}]}, {"timestamp": "2025-09-05T10:04:39Z", "type": "new", "title": "<PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980410, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "/* Dialogue */ npc", "timestamp": "2025-09-05T10:04:39Z"}]}, {"timestamp": "2025-09-05T09:33:53Z", "type": "new", "title": "Combat only pure/Early-game", "namespace": "Main", "revisionId": 14980407, "changes": [{"type": "edit", "user": "2A02:A472:323E:0:52F:328A:D54E:1AEE", "comment": "/*  Early-game Bossing */", "timestamp": "2025-09-05T09:33:53Z"}, {"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T09:06:18Z"}]}, {"timestamp": "2025-09-05T09:07:24Z", "type": "new", "title": "Treasure Trails/Guide/Cryptic clues/Medium", "namespace": "Main", "revisionId": 14980405, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T09:07:24Z"}]}, {"timestamp": "2025-09-05T09:06:45Z", "type": "new", "title": "Counter", "namespace": "Main", "revisionId": 14980403, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T09:06:45Z"}, {"type": "edit", "user": "Below Ice Mountain", "comment": "", "timestamp": "2025-09-04T01:18:12Z"}, {"type": "edit", "user": "Below Ice Mountain", "comment": "Otheruses only shows the first two but oh well.", "timestamp": "2025-09-04T01:17:26Z"}]}, {"timestamp": "2025-09-05T09:06:28Z", "type": "new", "title": "Counter (The Toad and Chicken)", "namespace": "Main", "revisionId": 14980402, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T09:06:28Z"}]}, {"timestamp": "2025-09-05T09:05:26Z", "type": "new", "title": "Treasure Trails/Guide/Cryptic clues/Easy", "namespace": "Main", "revisionId": 14980399, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T09:05:26Z"}]}, {"timestamp": "2025-09-05T09:00:31Z", "type": "new", "title": "Combat only pure", "namespace": "Main", "revisionId": 14980397, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T09:00:31Z"}]}, {"timestamp": "2025-09-05T08:59:56Z", "type": "new", "title": "Ironman Guide/Farming", "namespace": "Main", "revisionId": 14980396, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:59:56Z"}]}, {"timestamp": "2025-09-05T08:59:51Z", "type": "new", "title": "Well", "namespace": "Main", "revisionId": 14980395, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:59:51Z"}]}, {"timestamp": "2025-09-05T08:59:03Z", "type": "updated", "title": "<PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980394, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:59:03Z"}]}, {"timestamp": "2025-09-05T08:50:35Z", "type": "new", "title": "Ultimate Ironman Walkthrough Guide To <PERSON><PERSON><PERSON><PERSON>as Old Route Backup", "namespace": "Main", "revisionId": 14980390, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:50:35Z"}]}, {"timestamp": "2025-09-05T08:50:31Z", "type": "new", "title": "Flowerbed", "namespace": "Main", "revisionId": 14980389, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:50:31Z"}]}, {"timestamp": "2025-09-05T08:50:25Z", "type": "new", "title": "Money making guide/Creating crystal keys", "namespace": "Main", "revisionId": 14980388, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:50:25Z"}]}, {"timestamp": "2025-09-05T08:50:22Z", "type": "new", "title": "Grandfather Clock", "namespace": "Main", "revisionId": 14980387, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:50:22Z"}]}, {"timestamp": "2025-09-05T08:50:17Z", "type": "new", "title": "Scor<PERSON> Catcher/Quick guide", "namespace": "Main", "revisionId": 14980386, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:50:17Z"}]}, {"timestamp": "2025-09-05T08:50:13Z", "type": "new", "title": "<PERSON><PERSON> and <PERSON> Man I/Quick guide", "namespace": "Main", "revisionId": 14980385, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:50:13Z"}]}, {"timestamp": "2025-09-05T08:50:06Z", "type": "new", "title": "One Small Favour/Quick guide", "namespace": "Main", "revisionId": 14980382, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:50:06Z"}]}, {"timestamp": "2025-09-05T08:50:03Z", "type": "new", "title": "Optimal quest guide/Ironman", "namespace": "Main", "revisionId": 14980381, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:50:03Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "linked directly to page", "timestamp": "2025-09-05T05:52:14Z"}, {"type": "edit", "user": "LostMage", "comment": "changed from \"taverly\" to \"taverley\"", "timestamp": "2025-09-05T05:45:33Z"}]}, {"timestamp": "2025-09-05T08:49:18Z", "type": "new", "title": "A Tail of Two Cats", "namespace": "Main", "revisionId": 14980380, "changes": [{"type": "edit", "user": "<PERSON>", "comment": "", "timestamp": "2025-09-05T08:49:18Z"}]}, {"timestamp": "2025-09-05T08:40:56Z", "type": "new", "title": "Cave (Morytania Spider Cave)", "namespace": "Main", "revisionId": 14980379, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "rm irrelevant nav", "timestamp": "2025-09-05T08:40:56Z"}]}, {"timestamp": "2025-09-05T08:34:02Z", "type": "updated", "title": "River Varla", "namespace": "Main", "revisionId": 14980378, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "more", "timestamp": "2025-09-05T08:34:02Z"}]}, {"timestamp": "2025-09-05T08:16:46Z", "type": "new", "title": "Corporeal Beast's lair", "namespace": "Main", "revisionId": 14980372, "changes": [{"type": "new", "user": "<PERSON><PERSON>", "comment": "Redirected page to [[Corporeal Beast's Lair]]", "timestamp": "2025-09-05T08:16:46Z"}]}, {"timestamp": "2025-09-05T08:03:21Z", "type": "new", "title": "Stalactite (Taverley Dungeon)", "namespace": "Main", "revisionId": 14980370, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "bad otheruses, file move, remove less relevant navboxes", "timestamp": "2025-09-05T08:03:21Z"}]}, {"timestamp": "2025-09-05T07:58:39Z", "type": "new", "title": "Stalactites", "namespace": "Main", "revisionId": 14980365, "changes": [{"type": "new", "user": "<PERSON><PERSON>", "comment": "Redirected page to [[Stalactite and stalagmite]]", "timestamp": "2025-09-05T07:58:39Z"}]}, {"timestamp": "2025-09-05T07:58:18Z", "type": "new", "title": "Stalactite", "namespace": "Main", "revisionId": 14980364, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "Changed redirect target from [[Stalagmites (Lunar Isle)]] to [[Stalactite and stalagmite]]", "timestamp": "2025-09-05T07:58:18Z"}]}, {"timestamp": "2025-09-05T07:58:15Z", "type": "new", "title": "Stalagmites", "namespace": "Main", "revisionId": 14980363, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "Changed redirect target from [[Stalagmites (Lunar Isle)]] to [[Stalactite and stalagmite]]", "timestamp": "2025-09-05T07:58:15Z"}]}, {"timestamp": "2025-09-05T07:58:05Z", "type": "new", "title": "Stalagmite", "namespace": "Main", "revisionId": 14980362, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "Changed redirect target from [[Stalagmites (Lunar Isle)]] to [[Stalactite and stalagmite]]", "timestamp": "2025-09-05T07:58:05Z"}]}, {"timestamp": "2025-09-05T07:57:50Z", "type": "new", "title": "Stalactite and stalagmite", "namespace": "Main", "revisionId": 14980361, "changes": [{"type": "new", "user": "<PERSON><PERSON>", "comment": "very efficient use of title", "timestamp": "2025-09-05T07:57:50Z"}]}, {"timestamp": "2025-09-05T07:32:15Z", "type": "new", "title": "Stalagmites (Lunar Isle)", "namespace": "Main", "revisionId": 14980360, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "variants", "timestamp": "2025-09-05T07:32:15Z"}]}, {"timestamp": "2025-09-05T07:29:30Z", "type": "new", "title": "Free-to-play Ironman guide", "namespace": "Main", "revisionId": 14980357, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "replace bolds with subheaders again, fix refs and update tables", "timestamp": "2025-09-05T07:29:30Z"}, {"type": "edit", "user": "<PERSON><PERSON> <PERSON><PERSON>", "comment": "I gave major edits and updates to the early moneymaking methods, providing a bunch of alternatives that do not require very high stats (if at all), including more quests that reward coins, steel platebodies, and a couple combat alternatives that are safe on an early account. I also included a guide for ruby rings collection, although not really an \"early\" moneymaker. For hardcores, there isnt another highly profitable early method as steel platelegs are too risky. So, I included it in the list.", "timestamp": "2025-09-05T05:00:00Z"}, {"type": "edit", "user": "<PERSON><PERSON> <PERSON><PERSON>", "comment": "I did a few grammatical edits of the magic training section in continuation of the major edits I did yesterday. My major edit though was adding a list of max hits relative to your arrow type in the \"equipment\" section for ranged. I happened to have this information on a sticky note on my desk for my hardcore, so I thought to share it. I also removed some unintended information about fletching in the equipment section that was intended to only be in the woodcutting section.", "timestamp": "2025-09-05T03:40:41Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "remove <big>, replace bolds with subheaders (and shortened titles), replace images with plinks and add more plinkp's to subheaders to stand out more, as most later sections of the page do. fix a bunch of bad visedit piped links and some dupe links. some other minor cleanup", "timestamp": "2025-09-04T13:32:14Z"}, {"type": "edit", "user": "<PERSON><PERSON> <PERSON><PERSON>", "comment": "added icons for the melee, ranged, magic, and prayer training guides as it was very easy to scroll by and not see them without the image.", "timestamp": "2025-09-04T04:23:33Z"}, {"type": "edit", "user": "<PERSON><PERSON> <PERSON><PERSON>", "comment": "Made a small clarification edit in the magic training section for moss giants.", "timestamp": "2025-09-04T04:03:54Z"}, {"type": "edit", "user": "<PERSON><PERSON> <PERSON><PERSON>", "comment": "I gave a full makeover of the magic training guide to account for the new elemental weaknesses from May 2024 and 2025. I added 3 subcategories; Giants, Money making, and splashing, complete with a cost chart for runes. Each section also has additional information explaining why each of these methods are all valid and efficient. I removed Crumble undead on <PERSON><PERSON><PERSON> as a training method as there are now far better methods thanks to project rebalance. I did not make footnotes, as I do not know how.", "timestamp": "2025-09-04T04:00:00Z"}]}, {"timestamp": "2025-09-05T07:14:02Z", "type": "updated", "title": "Crypt of Tonali", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "Dungeons nav", "timestamp": "2025-09-05T07:14:02Z"}]}, {"timestamp": "2025-09-05T07:13:18Z", "type": "updated", "title": "Dragon Nest", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "Dungeons nav, left align map", "timestamp": "2025-09-05T07:13:18Z"}]}, {"timestamp": "2025-09-05T07:11:46Z", "type": "updated", "title": "<PERSON><PERSON><PERSON> of Mokhaiotl", "namespace": "Main", "revisionId": 14980353, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "Dungeons nav", "timestamp": "2025-09-05T07:11:46Z"}]}, {"timestamp": "2025-09-05T07:10:28Z", "type": "updated", "title": "<PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980352, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "type = dungeon", "timestamp": "2025-09-05T07:10:28Z"}]}, {"timestamp": "2025-09-05T07:05:34Z", "type": "updated", "title": "<PERSON><PERSON><PERSON>'s quiver", "namespace": "Main", "revisionId": 14980351, "changes": [{"type": "edit", "user": "172.97.140.237", "comment": "/* Wilderness */", "timestamp": "2025-09-05T07:05:34Z"}, {"type": "edit", "user": "172.97.140.237", "comment": "/* Wilderness */", "timestamp": "2025-09-05T07:04:24Z"}]}, {"timestamp": "2025-09-05T07:03:46Z", "type": "new", "title": "Prison Pete", "namespace": "Main", "revisionId": 14980349, "changes": [{"type": "edit", "user": "<PERSON><PERSON>", "comment": "", "timestamp": "2025-09-05T07:03:46Z"}, {"type": "edit", "user": "2601:240:4E01:A280:9115:D5BC:5ED0:B2AD", "comment": "/* Strategy */ Correcting bullet point", "timestamp": "2025-09-04T20:44:47Z"}]}, {"timestamp": "2025-09-05T06:26:45Z", "type": "new", "title": "Attribute affinities", "namespace": "Main", "revisionId": 14980336, "changes": [{"type": "edit", "user": "I am bri ish", "comment": "/* Tombs of Amascut */ added Tumekens shadow and info, minor edit for capitalisation error.", "timestamp": "2025-09-05T06:26:45Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Boss specific */", "timestamp": "2025-09-05T06:25:22Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Weapons */ Added a lot more information for the weapon notes. Some boxes were empty. Attack speed is missing in some still but demonbane is finished i think", "timestamp": "2025-09-05T05:01:06Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Weapons */", "timestamp": "2025-09-05T04:57:50Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Weapons */", "timestamp": "2025-09-05T04:45:26Z"}, {"type": "edit", "user": "I am bri ish", "comment": "Fixed bad info", "timestamp": "2025-09-04T10:35:41Z"}, {"type": "edit", "user": "I am bri ish", "comment": "Wondered how I could include the list of monsters without it being ugly and expandable tables is what I settled on. I played around with the min-max widths to try make it so it would be visible on both mobile and pc without breaking. Feel free to give feedback on this and/or remove if you think it looks bad. I think that it looks good enough for its functionality, and being collapsed helps -- people wont see it unless theyre seeking out the information.", "timestamp": "2025-09-04T10:32:41Z"}, {"type": "edit", "user": "I am bri ish", "comment": "Included price differences for everything in leafy, and several prose changes throughout over the last few edits. Sorry for spamming edits i kept spotting small mistakes, reading through it.", "timestamp": "2025-09-04T02:12:33Z"}, {"type": "edit", "user": "Data", "comment": "/* Weapons */", "timestamp": "2025-09-04T02:08:51Z"}, {"type": "edit", "user": "Data", "comment": "/* Spells */", "timestamp": "2025-09-04T02:08:23Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Magic */", "timestamp": "2025-09-04T01:57:48Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Ranged */", "timestamp": "2025-09-04T01:57:32Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Magic */", "timestamp": "2025-09-04T01:56:44Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Other */ added ranged/magic and added more info for magic dart. also updated the magic dart page.", "timestamp": "2025-09-04T01:56:04Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Other */", "timestamp": "2025-09-04T01:33:11Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Leafy enemies */", "timestamp": "2025-09-04T01:31:26Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Other */ I'm going to save this quick so I can edit only the leafy section, to make it easier. Ignore the error, it will be gone in one minute. Also: Added a table with leafy ammunition, I might make it collapsible", "timestamp": "2025-09-04T01:24:06Z"}, {"type": "edit", "user": "I am bri ish", "comment": "/* Weapons */ Improved the keris section and prose, made it more informative including quests and important NPCs for replacing. Added that the stat increase is unique to the raid, before it wasn't super clear.  Also:/* Special attacks */ Added some more examples. I'll add more later. I want to try and think of super unique examples, but it's not so easy.", "timestamp": "2025-09-04T00:34:51Z"}]}, {"timestamp": "2025-09-05T03:40:35Z", "type": "updated", "title": "Sturdy harness", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "Purple prsn", "comment": "", "timestamp": "2025-09-05T03:40:35Z"}, {"type": "edit", "user": "Purple prsn", "comment": "Updated to match new banking capabilities, aligning with Log/Forestry Basket.", "timestamp": "2025-09-05T03:30:11Z"}]}, {"timestamp": "2025-09-05T03:15:30Z", "type": "new", "title": "Hat stand", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "Below Ice Mountain", "comment": "Update dwarven release date/location", "timestamp": "2025-09-05T03:15:30Z"}, {"type": "edit", "user": "Pavocado", "comment": "Dwarven variant has no exclamation mark", "timestamp": "2025-09-05T01:00:47Z"}]}, {"timestamp": "2025-09-05T03:11:14Z", "type": "updated", "title": "Mastering Mixology", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "35.146.70.84", "comment": "/* Depositing */ Removed outdated (or simply incorrect) information and misleading advice.", "timestamp": "2025-09-05T03:11:14Z"}]}, {"timestamp": "2025-09-05T03:09:54Z", "type": "updated", "title": "Chronicle", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "217.28.12.10", "comment": "Higlighting useful info", "timestamp": "2025-09-05T03:09:54Z"}]}, {"timestamp": "2025-09-05T03:06:50Z", "type": "updated", "title": "Sailing", "namespace": "Main", "revisionId": 14980277, "changes": [{"type": "edit", "user": "Fixer", "comment": "update the interface stuff here cause since the beta the old ones werent used", "timestamp": "2025-09-05T03:06:50Z"}, {"type": "edit", "user": "BigDiesel2m", "comment": "Rewriting this to focus on content shown in the alpha/beta, with most the prose for the content being taken from the June 2025 Sailing Beta page. Moved Development info to a subpage, linked to in the See also section.", "timestamp": "2025-09-04T18:27:04Z"}]}, {"timestamp": "2025-09-05T03:05:13Z", "type": "new", "title": "June 2025 Sailing Beta", "namespace": "Main", "revisionId": 14980276, "changes": [{"type": "edit", "user": "Fixer", "comment": "replace those pics cause they werent used in the beta", "timestamp": "2025-09-05T03:05:13Z"}]}, {"timestamp": "2025-09-05T02:51:17Z", "type": "updated", "title": "Albatross", "namespace": "Main", "revisionId": 14980269, "changes": [{"type": "edit", "user": "Tenshibeat", "comment": "", "timestamp": "2025-09-05T02:51:17Z"}]}, {"timestamp": "2025-09-05T02:40:55Z", "type": "new", "title": "<PERSON><PERSON><PERSON><PERSON> (rock)", "namespace": "Main", "revisionId": 14980267, "changes": [{"type": "edit", "user": "Microbrews", "comment": "needs success chart", "timestamp": "2025-09-05T02:40:55Z"}]}, {"timestamp": "2025-09-05T02:14:21Z", "type": "new", "title": "Chest", "namespace": "Main", "revisionId": 14980258, "changes": [{"type": "edit", "user": "*************", "comment": "Added The Gauntlet chest", "timestamp": "2025-09-05T02:14:21Z"}]}, {"timestamp": "2025-09-05T01:58:44Z", "type": "updated", "title": "Rogue's equipment crate", "namespace": "Main", "revisionId": 14980256, "changes": [{"type": "edit", "user": "************", "comment": "", "timestamp": "2025-09-05T01:58:44Z"}]}, {"timestamp": "2025-09-05T01:16:01Z", "type": "updated", "title": "Motherlode Mine", "namespace": "Main", "revisionId": 14980247, "changes": [{"type": "edit", "user": "DriftArray", "comment": "/* Cleaning ores */ Removed leveling list (added it to talk page) based on feedback from <PERSON> on Discord", "timestamp": "2025-09-05T01:16:01Z"}]}, {"timestamp": "2025-09-05T01:09:20Z", "type": "new", "title": "Tormented Demon/Strategies", "namespace": "Main", "revisionId": 14980241, "changes": [{"type": "edit", "user": "Saelyk", "comment": "/* Fight overview */  Added Tzhaar-ket-om to example weapons list for 7-tick speed.", "timestamp": "2025-09-05T01:09:20Z"}]}, {"timestamp": "2025-09-05T01:08:01Z", "type": "new", "title": "SA", "namespace": "Main", "revisionId": 14980239, "changes": [{"type": "new", "user": "<PERSON>ee", "comment": "Redirected page to [[Special attacks]]", "timestamp": "2025-09-05T01:08:01Z"}]}, {"timestamp": "2025-09-05T00:43:42Z", "type": "new", "title": "Sa", "namespace": "Main", "revisionId": 14980230, "changes": [{"type": "edit", "user": "Legaia 2 Pla", "comment": "", "timestamp": "2025-09-05T00:43:42Z"}, {"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-04T17:28:44Z"}, {"type": "new", "user": "2600:4040:2080:8E00:1CA:69A1:F613:6C4", "comment": "Redirected to [[Salve_amulet]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T17:09:55Z"}]}, {"timestamp": "2025-09-05T00:26:06Z", "type": "new", "title": "<PERSON>", "namespace": "Main", "revisionId": 14980222, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-05T00:26:06Z"}]}, {"timestamp": "2025-09-05T00:24:26Z", "type": "updated", "title": "Sandstone", "namespace": "Main", "revisionId": 14980221, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14980219 by [[Special:Contributions/Vosk|Vosk]] ([[User talk:Vosk|talk]]) not a typo", "timestamp": "2025-09-05T00:24:26Z"}, {"type": "edit", "user": "Vosk", "comment": "<PERSON><PERSON>.", "timestamp": "2025-09-05T00:22:15Z"}, {"type": "edit", "user": "Sadsaddads", "comment": "added wikilink", "timestamp": "2025-09-04T17:29:43Z"}]}, {"timestamp": "2025-09-05T00:17:39Z", "type": "updated", "title": "Ash sanctifier", "namespace": "Main", "revisionId": 14980215, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-05T00:17:39Z"}]}, {"timestamp": "2025-09-05T00:10:06Z", "type": "new", "title": "Mine's Better", "namespace": "Main", "revisionId": 14980211, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "entering Doom arena with it equipped did not trigger a fail message", "timestamp": "2025-09-05T00:10:06Z"}, {"type": "edit", "user": "*************", "comment": "Add elysian spirit shield to list of bugs", "timestamp": "2025-09-04T17:52:56Z"}, {"type": "edit", "user": "Vroomstick", "comment": "/* Strategy */ redundant", "timestamp": "2025-09-04T04:54:14Z"}]}, {"timestamp": "2025-09-05T00:09:48Z", "type": "updated", "title": "Eclipse red", "namespace": "Main", "revisionId": 14980210, "changes": [{"type": "edit", "user": "Crow", "comment": "respawn time is variable, removing from prose, I presume the infobox is the base rate but idk for sure", "timestamp": "2025-09-05T00:09:48Z"}]}, {"timestamp": "2025-09-04T23:49:38Z", "type": "new", "title": "Triple eat", "namespace": "Main", "revisionId": 14980204, "changes": [{"type": "edit", "user": "Data", "comment": "Changed redirect target from [[Food#Combo Eating]] to [[Food/Fast foods#Combo eating]]", "timestamp": "2025-09-04T23:49:38Z"}]}, {"timestamp": "2025-09-04T23:37:55Z", "type": "new", "title": "Phantom Muspah/Strategies", "namespace": "Main", "revisionId": 14980196, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Equipment */", "timestamp": "2025-09-04T23:37:55Z"}]}, {"timestamp": "2025-09-04T23:36:55Z", "type": "updated", "title": "Amulet of blood fury", "namespace": "Main", "revisionId": 14980194, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Reverted edits by [[Special:Contributions/2603:6010:86F0:84A0:34FE:E942:CBDE:54C7|2603:6010:86F0:84A0:34FE:E942:CBDE:54C7]] ([[User talk:2603:6010:86F0:84A0:34FE:E942:CBDE:54C7|talk]] | [[Special:Block/2603:6010:86F0:84A0:34FE:E942:CBDE:54C7|block]]) to last version by [[Special:Contributions/Keplare|Ke<PERSON>lare]]", "timestamp": "2025-09-04T23:36:55Z"}, {"type": "edit", "user": "2603:6010:86F0:84A0:34FE:E942:CBDE:54C7", "comment": "/* Creation */", "timestamp": "2025-09-04T23:36:46Z"}]}, {"timestamp": "2025-09-04T23:34:07Z", "type": "updated", "title": "Magic mix", "namespace": "Main", "revisionId": 14980187, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Changes */", "timestamp": "2025-09-04T23:34:07Z"}]}, {"timestamp": "2025-09-04T23:08:28Z", "type": "updated", "title": "Broken bark snelm", "namespace": "Main", "revisionId": 14980181, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "", "timestamp": "2025-09-04T23:08:28Z"}]}, {"timestamp": "2025-09-04T23:05:27Z", "type": "updated", "title": "Blood'n'tar snelm", "namespace": "Main", "revisionId": 14980179, "changes": [{"type": "edit", "user": "2600:1702:3270:88C0:0:0:0:48", "comment": "bruise blue -> blood'n'tar in \"this article is about...\"", "timestamp": "2025-09-04T23:05:27Z"}]}, {"timestamp": "2025-09-04T23:04:02Z", "type": "new", "title": "Achievement Diary", "namespace": "Main", "revisionId": 14980177, "changes": [{"type": "edit", "user": "************", "comment": "/* All requirements */", "timestamp": "2025-09-04T23:04:02Z"}]}, {"timestamp": "2025-09-04T23:00:56Z", "type": "new", "title": "Kree'arra/Strategies", "namespace": "Main", "revisionId": 14980174, "changes": [{"type": "edit", "user": "Frafabowa", "comment": "replace occult with robe top - virtus robe top is actually just strictly better than occult but even if you have ahrim's or something the accuracy is almost assuredly better. also added slaughter bracelet (over grapple, which should be quivered)", "timestamp": "2025-09-04T23:00:56Z"}, {"type": "edit", "user": "AtelierLogic2", "comment": "removed a loooooooot of gear that is not acceptable to be bringing into the god wars dungeon (didnt touch the group tabs as much tho)", "timestamp": "2025-09-04T10:56:51Z"}]}, {"timestamp": "2025-09-04T23:00:11Z", "type": "updated", "title": "Category:Items with no default version", "namespace": "Category", "revisionId": 14980173, "changes": [{"type": "new", "user": "Microbrews", "comment": "Created page with \"{{Categoryheader|items|with all <code>default_version</code> set to <code>false</code> in [[Bucket:Infobox item]]|yes|[[Module:Infobox Item]]|related=no}} {{Hidden category}} [[Category:Items]] [[Category:Maintenance categories]]\"", "timestamp": "2025-09-04T23:00:11Z"}]}, {"timestamp": "2025-09-04T22:59:14Z", "type": "updated", "title": "Category:Items with different stats between versions", "namespace": "Category", "revisionId": 14980170, "changes": [{"type": "edit", "user": "Microbrews", "comment": "", "timestamp": "2025-09-04T22:59:14Z"}, {"type": "edit", "user": "Microbrews", "comment": "", "timestamp": "2025-09-04T16:30:19Z"}, {"type": "new", "user": "Microbrews", "comment": "Created page with \"{{Categoryheader|items|mismatched stats between versions|yes|[[Module:Infobox Bonuses]]}} {{Hidden category}} [[Category:Maintenance categories]]\"", "timestamp": "2025-09-04T16:26:25Z"}]}, {"timestamp": "2025-09-04T22:20:50Z", "type": "new", "title": "Priff shop", "namespace": "Main", "revisionId": 14980167, "changes": [{"type": "edit", "user": "Crow", "comment": "Changed redirect target from [[Prifddinas shops]] to [[List of shops in Prifddinas]]", "timestamp": "2025-09-04T22:20:50Z"}, {"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Prifddinas shops]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:52:12Z"}]}, {"timestamp": "2025-09-04T22:20:50Z", "type": "new", "title": "Priffddinas shop", "namespace": "Main", "revisionId": 14980166, "changes": [{"type": "edit", "user": "Crow", "comment": "Changed redirect target from [[Prifddinas shops]] to [[List of shops in Prifddinas]]", "timestamp": "2025-09-04T22:20:50Z"}, {"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Prifddinas shops]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:49:37Z"}]}, {"timestamp": "2025-09-04T22:20:49Z", "type": "new", "title": "Prifddinas stores", "namespace": "Main", "revisionId": 14980165, "changes": [{"type": "edit", "user": "Crow", "comment": "Changed redirect target from [[Prifddinas shops]] to [[List of shops in Prifddinas]]", "timestamp": "2025-09-04T22:20:49Z"}, {"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Prifddinas shops]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:49:21Z"}]}, {"timestamp": "2025-09-04T22:20:48Z", "type": "new", "title": "Priffdinas shops", "namespace": "Main", "revisionId": 14980164, "changes": [{"type": "edit", "user": "Crow", "comment": "Changed redirect target from [[Prifddinas shops]] to [[List of shops in Prifddinas]]", "timestamp": "2025-09-04T22:20:48Z"}, {"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Prifddinas shops]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:47:39Z"}]}, {"timestamp": "2025-09-04T22:20:48Z", "type": "new", "title": "Priffddinas shops", "namespace": "Main", "revisionId": 14980163, "changes": [{"type": "edit", "user": "Crow", "comment": "Changed redirect target from [[Prifddinas shops]] to [[List of shops in Prifddinas]]", "timestamp": "2025-09-04T22:20:48Z"}, {"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Prifddinas shops]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:47:25Z"}]}, {"timestamp": "2025-09-04T22:20:47Z", "type": "new", "title": "Prifddinas store", "namespace": "Main", "revisionId": 14980162, "changes": [{"type": "edit", "user": "Crow", "comment": "Changed redirect target from [[Prifddinas shops]] to [[List of shops in Prifddinas]]", "timestamp": "2025-09-04T22:20:47Z"}, {"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Prifddinas shops]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:45:17Z"}]}, {"timestamp": "2025-09-04T22:20:46Z", "type": "new", "title": "Priff shops", "namespace": "Main", "revisionId": 14980161, "changes": [{"type": "edit", "user": "Crow", "comment": "Changed redirect target from [[Prifddinas shops]] to [[List of shops in Prifddinas]]", "timestamp": "2025-09-04T22:20:46Z"}, {"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Prifddinas shops]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:45:04Z"}]}, {"timestamp": "2025-09-04T22:20:46Z", "type": "new", "title": "Prif magic shop", "namespace": "Main", "revisionId": 14980160, "changes": [{"type": "edit", "user": "Crow", "comment": "Changed redirect target from [[Prifddinas shops]] to [[List of shops in Prifddinas]]", "timestamp": "2025-09-04T22:20:46Z"}, {"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Prifddinas shops]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:44:52Z"}]}, {"timestamp": "2025-09-04T22:20:46Z", "type": "new", "title": "Prif shops", "namespace": "Main", "revisionId": 14980159, "changes": [{"type": "edit", "user": "Crow", "comment": "Changed redirect target from [[Prifddinas shops]] to [[List of shops in Prifddinas]]", "timestamp": "2025-09-04T22:20:46Z"}, {"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Prifddinas shops]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:44:45Z"}]}, {"timestamp": "2025-09-04T22:20:45Z", "type": "new", "title": "Prifddinas shop", "namespace": "Main", "revisionId": 14980158, "changes": [{"type": "edit", "user": "Crow", "comment": "Changed redirect target from [[Prifddinas shops]] to [[List of shops in Prifddinas]]", "timestamp": "2025-09-04T22:20:45Z"}, {"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Prifddinas shops]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:44:29Z"}]}, {"timestamp": "2025-09-04T22:19:26Z", "type": "new", "title": "Prifddinas shops", "namespace": "Main", "revisionId": 14980157, "changes": [{"type": "log", "user": "Crow", "comment": "", "timestamp": "2025-09-04T22:19:26Z"}, {"type": "edit", "user": "Crow", "comment": "displaytitle should not be used, changing page title is what moving the page is for", "timestamp": "2025-09-04T22:12:32Z"}, {"type": "edit", "user": "Sadsaddads", "comment": "Changed title", "timestamp": "2025-09-04T21:42:28Z"}, {"type": "new", "user": "Sadsaddads", "comment": "Created page with \"The following is a list of shops and stalls in [[Prifddinas]]: {{List| * [[Amlodd's Magical Supplies]] * [[Aneirin's Armour]] * [[Branwen's Farming Shop]] * [[Construction supplies]] * [[Elgan's Exceptional Staffs!]] * [[Guinevere's Dyes]] * [[Gwyn's Mining Emporium]] * [[Hefin Inn]] * [[Iorwerth's Arms]] * [[Iwan's Maces]] * [[<PERSON>lian<PERSON>'s Wares]] * [[Prifddinas Foodstuffs]] * [[Prifddinas Gem Stall]] * [[Prifddinas General Store]] * [[Prifddinas Herbal Supplies]] * Prifd...\"", "timestamp": "2025-09-04T21:41:20Z"}]}, {"timestamp": "2025-09-04T22:16:08Z", "type": "new", "title": "Shattered Relics League/Tasks/Medium", "namespace": "Main", "revisionId": 14980154, "changes": [{"type": "edit", "user": "Crow", "comment": "??", "timestamp": "2025-09-04T22:16:08Z"}]}, {"timestamp": "2025-09-04T22:14:59Z", "type": "new", "title": "Brimhaven Agility Arena Ticket Exchange", "namespace": "Main", "revisionId": 14980153, "changes": [{"type": "edit", "user": "Crow", "comment": "", "timestamp": "2025-09-04T22:14:59Z"}]}, {"timestamp": "2025-09-04T22:13:28Z", "type": "new", "title": "Slayer task/Suqah", "namespace": "Main", "revisionId": 14980152, "changes": [{"type": "edit", "user": "Crow", "comment": "?", "timestamp": "2025-09-04T22:13:28Z"}]}, {"timestamp": "2025-09-04T21:52:34Z", "type": "new", "title": "Prifidnas", "namespace": "Main", "revisionId": 14980146, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:52:34Z"}]}, {"timestamp": "2025-09-04T21:52:26Z", "type": "new", "title": "Prif magic", "namespace": "Main", "revisionId": 14980145, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON>'s Magical Supplies]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:52:26Z"}]}, {"timestamp": "2025-09-04T21:52:19Z", "type": "new", "title": "<PERSON><PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980144, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:52:19Z"}]}, {"timestamp": "2025-09-04T21:52:02Z", "type": "new", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "namespace": "Main", "revisionId": 14980142, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:52:02Z"}]}, {"timestamp": "2025-09-04T21:51:54Z", "type": "new", "title": "<PERSON><PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980141, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:51:54Z"}]}, {"timestamp": "2025-09-04T21:51:45Z", "type": "new", "title": "<PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980140, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:51:45Z"}]}, {"timestamp": "2025-09-04T21:51:34Z", "type": "new", "title": "Prifdinnas rune shop", "namespace": "Main", "revisionId": 14980139, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON>'s Magical Supplies]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:51:34Z"}]}, {"timestamp": "2025-09-04T21:51:24Z", "type": "new", "title": "Prif rune", "namespace": "Main", "revisionId": 14980138, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON>'s Magical Supplies]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:51:24Z"}]}, {"timestamp": "2025-09-04T21:51:18Z", "type": "new", "title": "<PERSON><PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980137, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:51:18Z"}]}, {"timestamp": "2025-09-04T21:51:00Z", "type": "new", "title": "Priff sawmill", "namespace": "Main", "revisionId": 14980136, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Sawmill#<PERSON><PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:51:00Z"}]}, {"timestamp": "2025-09-04T21:50:59Z", "type": "new", "title": "<PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980135, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:50:59Z"}]}, {"timestamp": "2025-09-04T21:49:27Z", "type": "new", "title": "Prif mage shop", "namespace": "Main", "revisionId": 14980133, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON>'s Magical Supplies]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:49:27Z"}]}, {"timestamp": "2025-09-04T21:48:54Z", "type": "new", "title": "Prif rune store", "namespace": "Main", "revisionId": 14980131, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON>'s Magical Supplies]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:48:54Z"}]}, {"timestamp": "2025-09-04T21:48:27Z", "type": "new", "title": "Priff<PERSON>", "namespace": "Main", "revisionId": 14980129, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:48:27Z"}]}, {"timestamp": "2025-09-04T21:48:13Z", "type": "new", "title": "Priffd", "namespace": "Main", "revisionId": 14980128, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:48:13Z"}]}, {"timestamp": "2025-09-04T21:48:03Z", "type": "new", "title": "<PERSON><PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980127, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:48:03Z"}]}, {"timestamp": "2025-09-04T21:47:58Z", "type": "new", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980126, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:47:58Z"}]}, {"timestamp": "2025-09-04T21:47:47Z", "type": "new", "title": "Priff rune store", "namespace": "Main", "revisionId": 14980125, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON>'s Magical Supplies]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:47:47Z"}]}, {"timestamp": "2025-09-04T21:47:09Z", "type": "new", "title": "Prifddinas rune store", "namespace": "Main", "revisionId": 14980122, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON>'s Magical Supplies]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:47:09Z"}]}, {"timestamp": "2025-09-04T21:47:03Z", "type": "new", "title": "Prifddinas magic", "namespace": "Main", "revisionId": 14980121, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON>'s Magical Supplies]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:47:03Z"}]}, {"timestamp": "2025-09-04T21:46:48Z", "type": "new", "title": "Priffddinas rune shop", "namespace": "Main", "revisionId": 14980120, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON>'s Magical Supplies]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:46:48Z"}]}, {"timestamp": "2025-09-04T21:46:36Z", "type": "new", "title": "Priffdinas rune shop", "namespace": "Main", "revisionId": 14980119, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON>'s Magical Supplies]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:46:36Z"}]}, {"timestamp": "2025-09-04T21:46:00Z", "type": "new", "title": "Priff magic shop", "namespace": "Main", "revisionId": 14980118, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON><PERSON><PERSON>'s Magical Supplies]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T21:46:00Z"}]}, {"timestamp": "2025-09-04T21:45:35Z", "type": "new", "title": "<PERSON>", "namespace": "Main", "revisionId": 14980117, "changes": [{"type": "edit", "user": "Data", "comment": "Undid revision 14980116 by [[Special:Contributions/Data|Data]] ([[User talk:Data|talk]]) accidentally reverted", "timestamp": "2025-09-04T21:45:35Z"}, {"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:45:18Z"}, {"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:42:25Z"}]}, {"timestamp": "2025-09-04T21:45:01Z", "type": "updated", "title": "Rabbit", "namespace": "Main", "revisionId": 14980113, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:45:01Z"}, {"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:42:43Z"}]}, {"timestamp": "2025-09-04T21:43:17Z", "type": "new", "title": "White rabbit", "namespace": "Main", "revisionId": 14980108, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-04T21:43:17Z"}]}, {"timestamp": "2025-09-04T21:40:39Z", "type": "new", "title": "<PERSON>", "namespace": "Main", "revisionId": 14980103, "changes": [{"type": "new", "user": "Data", "comment": "Redirected page to [[<PERSON> Bunny]]", "timestamp": "2025-09-04T21:40:39Z"}]}, {"timestamp": "2025-09-04T21:40:05Z", "type": "new", "title": "<PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980101, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:40:05Z"}, {"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:39:17Z"}]}, {"timestamp": "2025-09-04T21:34:45Z", "type": "new", "title": "Darkness is your ally", "namespace": "Main", "revisionId": 14980098, "changes": [{"type": "new", "user": "Peace love h", "comment": "Redirected page to [[Darkness Is Your Ally?]]", "timestamp": "2025-09-04T21:34:45Z"}]}, {"timestamp": "2025-09-04T21:32:32Z", "type": "new", "title": "Portal (Player-owned house)", "namespace": "Main", "revisionId": 14980096, "changes": [{"type": "edit", "user": "Prydwen13", "comment": "Added Agility as skill for <PERSON><PERSON><PERSON>. Changed hyperlink for Pollnivneach Rooftop Course in skill column of POHs. Alphabetized skills for all house locations", "timestamp": "2025-09-04T21:32:32Z"}]}, {"timestamp": "2025-09-04T21:25:14Z", "type": "new", "title": "Charter ship", "namespace": "Main", "revisionId": 14980093, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:25:14Z"}, {"type": "edit", "user": "Data", "comment": "/* Getting to the charter ships */", "timestamp": "2025-09-04T21:24:54Z"}, {"type": "edit", "user": "Data", "comment": "/* Quest requirements */", "timestamp": "2025-09-04T21:22:53Z"}, {"type": "edit", "user": "Data", "comment": "/* <PERSON><PERSON> */", "timestamp": "2025-09-04T20:50:35Z"}]}, {"timestamp": "2025-09-04T21:24:42Z", "type": "updated", "title": "Sunset Coast", "namespace": "Main", "revisionId": 14980091, "changes": [{"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T21:24:42Z"}]}, {"timestamp": "2025-09-04T21:24:17Z", "type": "updated", "title": "Ship Yard", "namespace": "Main", "revisionId": 14980090, "changes": [{"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T21:24:17Z"}]}, {"timestamp": "2025-09-04T21:23:08Z", "type": "updated", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980089, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:23:08Z"}]}, {"timestamp": "2025-09-04T21:20:41Z", "type": "updated", "title": "Port Tyras", "namespace": "Main", "revisionId": 14980087, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:20:41Z"}]}, {"timestamp": "2025-09-04T21:19:08Z", "type": "updated", "title": "Port Sarim", "namespace": "Main", "revisionId": 14980086, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:19:08Z"}]}, {"timestamp": "2025-09-04T21:18:04Z", "type": "updated", "title": "Port Piscarilius", "namespace": "Main", "revisionId": 14980085, "changes": [{"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T21:18:04Z"}]}, {"timestamp": "2025-09-04T21:16:51Z", "type": "updated", "title": "Port Phasmatys", "namespace": "Main", "revisionId": 14980083, "changes": [{"type": "edit", "user": "Data", "comment": "/* Locations */", "timestamp": "2025-09-04T21:16:51Z"}, {"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:14:29Z"}]}, {"timestamp": "2025-09-04T21:13:08Z", "type": "updated", "title": "Port Khazard", "namespace": "Main", "revisionId": 14980078, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:13:08Z"}, {"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T21:12:04Z"}]}, {"timestamp": "2025-09-04T21:10:37Z", "type": "updated", "title": "Edgeville Dungeon", "namespace": "Main", "revisionId": 14980075, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:10:37Z"}]}, {"timestamp": "2025-09-04T21:10:08Z", "type": "updated", "title": "Musa Point", "namespace": "Main", "revisionId": 14980074, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:10:08Z"}]}, {"timestamp": "2025-09-04T21:09:11Z", "type": "updated", "title": "<PERSON><PERSON>", "namespace": "Main", "revisionId": 14980073, "changes": [{"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T21:09:11Z"}]}, {"timestamp": "2025-09-04T21:07:38Z", "type": "updated", "title": "Land's End", "namespace": "Main", "revisionId": 14980072, "changes": [{"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T21:07:38Z"}]}, {"timestamp": "2025-09-04T21:06:28Z", "type": "updated", "title": "Corsair Cove", "namespace": "Main", "revisionId": 14980071, "changes": [{"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T21:06:28Z"}]}, {"timestamp": "2025-09-04T21:05:18Z", "type": "updated", "title": "Brimhaven", "namespace": "Main", "revisionId": 14980070, "changes": [{"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T21:05:18Z"}, {"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T21:04:23Z"}, {"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T20:56:55Z"}, {"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T20:56:31Z"}, {"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T20:54:22Z"}]}, {"timestamp": "2025-09-04T21:04:45Z", "type": "updated", "title": "<PERSON><PERSON><PERSON>", "namespace": "Main", "revisionId": 14980069, "changes": [{"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T21:04:45Z"}, {"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T20:58:04Z"}, {"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T20:57:38Z"}]}, {"timestamp": "2025-09-04T21:04:37Z", "type": "updated", "title": "Aldarin", "namespace": "Main", "revisionId": 14980068, "changes": [{"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T21:04:37Z"}, {"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T20:56:16Z"}, {"type": "edit", "user": "Data", "comment": "/* Charter ship */", "timestamp": "2025-09-04T20:51:28Z"}]}, {"timestamp": "2025-09-04T21:04:02Z", "type": "updated", "title": "Civitas illa Fortis", "namespace": "Main", "revisionId": 14980066, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:04:02Z"}]}, {"timestamp": "2025-09-04T21:03:11Z", "type": "updated", "title": "<PERSON><PERSON>", "namespace": "Main", "revisionId": 14980064, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:03:11Z"}, {"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T21:02:01Z"}]}, {"timestamp": "2025-09-04T20:53:14Z", "type": "new", "title": "Recruitment Drive/Quick guide", "namespace": "Main", "revisionId": 14980053, "changes": [{"type": "edit", "user": "*************", "comment": "Again fixed an erroneous statement.", "timestamp": "2025-09-04T20:53:14Z"}, {"type": "edit", "user": "*************", "comment": "Changed an erroneous statement that said Sir <PERSON><PERSON> was the one fought rather than Sir <PERSON>.", "timestamp": "2025-09-04T20:46:21Z"}]}, {"timestamp": "2025-09-04T20:52:07Z", "type": "new", "title": "Duke Su<PERSON>us/Strategies", "namespace": "Main", "revisionId": 14980052, "changes": [{"type": "edit", "user": "KodakKid3", "comment": "added true tile IDs for gas vents & orb of darkness", "timestamp": "2025-09-04T20:52:07Z"}]}, {"timestamp": "2025-09-04T20:50:11Z", "type": "updated", "title": "ScapeRune", "namespace": "Main", "revisionId": 14980047, "changes": [{"type": "edit", "user": "Data", "comment": "", "timestamp": "2025-09-04T20:50:11Z"}, {"type": "edit", "user": "Data", "comment": "/* Prison */", "timestamp": "2025-09-04T20:49:56Z"}]}, {"timestamp": "2025-09-04T20:43:26Z", "type": "new", "title": "Player character", "namespace": "Main", "revisionId": 14980037, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-04T20:43:26Z"}, {"type": "edit", "user": "Data", "comment": "/* Adventures */", "timestamp": "2025-09-04T20:42:15Z"}]}, {"timestamp": "2025-09-04T20:40:10Z", "type": "updated", "title": "Keg of beer", "namespace": "Main", "revisionId": ********, "changes": [{"type": "edit", "user": "Data", "comment": "/* Trivia */", "timestamp": "2025-09-04T20:40:10Z"}]}, {"timestamp": "2025-09-04T20:36:13Z", "type": "new", "title": "Pure pk", "namespace": "Main", "revisionId": ********, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Player_killing#Account_Builds]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:36:13Z"}]}, {"timestamp": "2025-09-04T20:35:21Z", "type": "new", "title": "Pk guide", "namespace": "Main", "revisionId": ********, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Player_killing/Mechanics#Player_killing_techniques]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:35:21Z"}]}, {"timestamp": "2025-09-04T20:34:41Z", "type": "new", "title": "Pk setup", "namespace": "Main", "revisionId": ********, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Player_killing/Mechanics#Player_killing_techniques]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:34:41Z"}]}, {"timestamp": "2025-09-04T20:31:45Z", "type": "new", "title": "Shilo village river", "namespace": "Main", "revisionId": ********, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Shilo_Village_(location)#Fishing_spots]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:31:45Z"}]}, {"timestamp": "2025-09-04T20:24:43Z", "type": "new", "title": "Ba salt", "namespace": "Main", "revisionId": 14980021, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Basal<PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:24:43Z"}]}, {"timestamp": "2025-09-04T20:23:26Z", "type": "new", "title": "B;ue dye", "namespace": "Main", "revisionId": 14980020, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Blue dye]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:23:26Z"}]}, {"timestamp": "2025-09-04T20:23:18Z", "type": "new", "title": "B;ack demons", "namespace": "Main", "revisionId": 14980019, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Black demon]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:23:18Z"}]}, {"timestamp": "2025-09-05T20:40:50Z", "type": "updated", "title": "Money making guide/Farming hops", "namespace": "Main", "revisionId": 14980706, "changes": [{"type": "edit", "user": "Crow", "comment": "", "timestamp": "2025-09-05T20:40:50Z"}, {"type": "edit", "user": "Psionia", "comment": "Adjusted the activity to be generic.", "timestamp": "2025-09-05T12:20:40Z"}, {"type": "edit", "user": "Psionia", "comment": "Converted table elements so they no longer show trailing 0s.", "timestamp": "2025-09-04T14:33:58Z"}, {"type": "new", "user": "Psionia", "comment": "Introducing new MMG to consolidate all hops farming methods. This is currently split up into various MMGs but it is only possible to do one type of hop farming at a time.", "timestamp": "2025-09-04T14:07:56Z"}]}, {"timestamp": "2025-09-05T20:40:43Z", "type": "new", "title": "Money making guide/Farming herbs", "namespace": "Main", "revisionId": 14980705, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14980690 by [[Special:Contributions/Crow|Crow]] ([[User talk:Crow|talk]])", "timestamp": "2025-09-05T20:40:43Z"}, {"type": "edit", "user": "Crow", "comment": "", "timestamp": "2025-09-05T20:39:18Z"}, {"type": "edit", "user": "Psionia", "comment": "Using decimal to remove trailing 0s in table.", "timestamp": "2025-09-04T14:40:02Z"}]}, {"timestamp": "2025-09-05T20:40:42Z", "type": "new", "title": "Money making guide/Farming Yanillian hops", "namespace": "Main", "revisionId": 14980704, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14979814 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:40:42Z"}, {"type": "edit", "user": "Psionia", "comment": "Excluded from MMG as it is being consolidated into Money Making Guide/Farming hops", "timestamp": "2025-09-04T14:10:54Z"}]}, {"timestamp": "2025-09-05T20:40:42Z", "type": "new", "title": "Money making guide/Farming jute fibre", "namespace": "Main", "revisionId": 14980703, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14979813 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:40:42Z"}, {"type": "edit", "user": "Psionia", "comment": "Excluded from MMG as it is being consolidated into Money Making Guide/Farming hops", "timestamp": "2025-09-04T14:09:16Z"}]}, {"timestamp": "2025-09-05T20:39:49Z", "type": "new", "title": "Money making guide/Farming irit leaves", "namespace": "Main", "revisionId": 14980702, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968490 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:49Z"}]}, {"timestamp": "2025-09-05T20:39:48Z", "type": "new", "title": "Money making guide/Farming lantadyme", "namespace": "Main", "revisionId": 14980701, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968489 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:48Z"}]}, {"timestamp": "2025-09-05T20:39:46Z", "type": "new", "title": "Money making guide/Farming dwarf weed", "namespace": "Main", "revisionId": 14980700, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968488 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:46Z"}]}, {"timestamp": "2025-09-05T20:39:46Z", "type": "new", "title": "Money making guide/Farming torstol", "namespace": "Main", "revisionId": 14980699, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968486 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:46Z"}]}, {"timestamp": "2025-09-05T20:39:45Z", "type": "new", "title": "Money making guide/Farming cadantine", "namespace": "Main", "revisionId": 14980698, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968485 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:45Z"}]}, {"timestamp": "2025-09-05T20:39:44Z", "type": "new", "title": "Money making guide/Farming avantoe", "namespace": "Main", "revisionId": 14980697, "changes": [{"type": "edit", "user": "Crow", "comment": "", "timestamp": "2025-09-05T20:39:44Z"}]}, {"timestamp": "2025-09-05T20:39:41Z", "type": "new", "title": "Money making guide/Farming toadflax", "namespace": "Main", "revisionId": 14980696, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968483 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:41Z"}]}, {"timestamp": "2025-09-05T20:39:40Z", "type": "new", "title": "Money making guide/Farming kwuarm", "namespace": "Main", "revisionId": 14980695, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968482 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:40Z"}]}, {"timestamp": "2025-09-05T20:39:40Z", "type": "new", "title": "Money making guide/Farming snapdragon", "namespace": "Main", "revisionId": 14980694, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968481 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:40Z"}]}, {"timestamp": "2025-09-05T20:39:40Z", "type": "new", "title": "Money making guide/Farming ranarr weed", "namespace": "Main", "revisionId": 14980693, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968479 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:40Z"}]}, {"timestamp": "2025-09-05T20:39:39Z", "type": "new", "title": "Money making guide/Farming huasca", "namespace": "Main", "revisionId": 14980692, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14968478 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]])", "timestamp": "2025-09-05T20:39:39Z"}]}, {"timestamp": "2025-09-05T20:38:39Z", "type": "updated", "title": "Money making guide/Picking fruit trees", "namespace": "Main", "revisionId": 14980689, "changes": [{"type": "edit", "user": "Crow", "comment": "this shouldn't be merged imo", "timestamp": "2025-09-05T20:38:39Z"}, {"type": "edit", "user": "Psionia", "comment": "Added switch to the other section so that it updates alongside all other switches.", "timestamp": "2025-09-05T15:35:31Z"}, {"type": "new", "user": "Psionia", "comment": "New MMG to consolidate various existing picking MMGs.", "timestamp": "2025-09-05T15:22:05Z"}]}, {"timestamp": "2025-09-05T20:38:37Z", "type": "updated", "title": "Money making guide/Picking papayas", "namespace": "Main", "revisionId": 14980688, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14980536 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]]) I don't agree with consolidating these, it's easier to compare these guides on the main pages if they're split.", "timestamp": "2025-09-05T20:38:37Z"}, {"type": "edit", "user": "Psionia", "comment": "Excluded as being incorporated into Money Making Guide/Picking Fruit Trees", "timestamp": "2025-09-05T15:23:04Z"}]}, {"timestamp": "2025-09-05T20:38:36Z", "type": "updated", "title": "Money making guide/Picking coconuts", "namespace": "Main", "revisionId": 14980686, "changes": [{"type": "edit", "user": "Crow", "comment": "Undid revision 14980537 by [[Special:Contributions/Psionia|Psionia]] ([[User talk:Psionia|talk]]) I don't agree with consolidating these, it's easier to compare these guides on the main pages if they're split.", "timestamp": "2025-09-05T20:38:36Z"}, {"type": "edit", "user": "Psionia", "comment": "Excluded as being incorporated into Money Making Guide/Picking Fruit Trees", "timestamp": "2025-09-05T15:23:41Z"}]}, {"timestamp": "2025-09-05T20:37:33Z", "type": "updated", "title": "Cooked meat", "namespace": "Main", "revisionId": 14980684, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "wrong", "timestamp": "2025-09-05T20:37:33Z"}]}, {"timestamp": "2025-09-05T20:37:21Z", "type": "updated", "title": "Raw yak meat", "namespace": "Main", "revisionId": 14980683, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "wrong", "timestamp": "2025-09-05T20:37:21Z"}]}, {"timestamp": "2025-09-05T01:08:01Z", "type": "new", "title": "SA", "namespace": "Main", "revisionId": 14980239, "changes": [{"type": "new", "user": "<PERSON>ee", "comment": "Redirected page to [[Special attacks]]", "timestamp": "2025-09-05T01:08:01Z"}]}, {"timestamp": null, "type": "new", "title": "Nothing else", "namespace": "Main", "revisionId": null, "changes": [{"type": "log", "user": "Legaia 2 Pla", "comment": "bad redirect", "timestamp": "2025-09-05T00:43:22Z"}]}, {"timestamp": null, "type": "new", "title": "For a", "namespace": "Main", "revisionId": null, "changes": [{"type": "log", "user": "Legaia 2 Pla", "comment": "bad redirect", "timestamp": "2025-09-05T00:42:37Z"}]}, {"timestamp": "2025-09-04T20:17:38Z", "type": "new", "title": "B.j.r", "namespace": "Main", "revisionId": 14980018, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Fairy_rings#BJR]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:17:38Z"}]}, {"timestamp": "2025-09-04T20:07:42Z", "type": "new", "title": "Void Knights' Reward Options", "namespace": "Main", "revisionId": 14980010, "changes": [{"type": "edit", "user": "Source link", "comment": "/* Items */ Void seal does not require these skill levels to buy", "timestamp": "2025-09-04T20:07:42Z"}]}, {"timestamp": "2025-09-04T20:07:32Z", "type": "new", "title": "B-road bolts", "namespace": "Main", "revisionId": 14980009, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Broad bolts]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:07:32Z"}]}, {"timestamp": "2025-09-04T20:07:22Z", "type": "new", "title": "B loodveld", "namespace": "Main", "revisionId": 14980008, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Bloodveld]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:07:22Z"}]}, {"timestamp": "2025-09-04T20:07:13Z", "type": "new", "title": "B oss", "namespace": "Main", "revisionId": 14980007, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[<PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:07:13Z"}]}, {"timestamp": "2025-09-04T20:06:29Z", "type": "new", "title": "B andos", "namespace": "Main", "revisionId": 14980006, "changes": [{"type": "new", "user": "Sadsaddads", "comment": "Redirected to [[Bandos]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T20:06:29Z"}]}, {"timestamp": "2025-09-04T20:03:58Z", "type": "new", "title": "Yama/Strategies", "namespace": "Main", "revisionId": 14980004, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON><PERSON>", "comment": "", "timestamp": "2025-09-04T20:03:58Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "/* Equipment */ add in some clauses about the usecase of stuff below purging", "timestamp": "2025-09-04T18:40:05Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "/* Equipment */ add in some context for void", "timestamp": "2025-09-04T17:55:21Z"}, {"type": "edit", "user": "AtelierLogic2", "comment": "removed glory to show better than str ammy str is more important than fury or glory accuracy", "timestamp": "2025-09-04T17:38:18Z"}]}, {"timestamp": "2025-09-04T19:42:22Z", "type": "new", "title": "Lizardman shaman/Strategies", "namespace": "Main", "revisionId": 14979981, "changes": [{"type": "edit", "user": "************", "comment": "/* Equipment */", "timestamp": "2025-09-04T19:42:22Z"}]}, {"timestamp": "2025-09-04T18:43:33Z", "type": "new", "title": "Falx", "namespace": "Main", "revisionId": 14979960, "changes": [{"type": "edit", "user": "RealEvanem", "comment": "as cool as falxes are, they aren't in game and misspelling flax led me to sickles :/", "timestamp": "2025-09-04T18:43:33Z"}]}, {"timestamp": "2025-09-04T18:40:13Z", "type": "new", "title": "<PERSON><PERSON>", "namespace": "Main", "revisionId": 14979958, "changes": [{"type": "edit", "user": "**************", "comment": "/* Task Changing */ removed typo in <PERSON><PERSON>'s name", "timestamp": "2025-09-04T18:40:13Z"}]}, {"timestamp": "2025-09-04T18:27:51Z", "type": "new", "title": "Sailing (disambiguation)", "namespace": "Main", "revisionId": 14979952, "changes": [{"type": "edit", "user": "BigDiesel2m", "comment": "", "timestamp": "2025-09-04T18:27:51Z"}, {"type": "new", "user": "BigDiesel2m", "comment": "Created page with \"[[Sailing]] is the upcoming 24th [[skill]] to be added to ''Old School RuneScape'', scheduled for release some time in late [[2025]]. It may also refer to:  ===Sailing Development=== * [[March 2025 Sailing Alpha]] * [[June 2025 Sailing Beta]] * [[August 2025 Sailing Stress Test]]  ===Historical=== * [[2014 April Fools]] * [[Sailing (2015 proposed skill)]]  {{Disambig}}\"", "timestamp": "2025-09-04T18:26:02Z"}]}, {"timestamp": "2025-09-04T18:27:30Z", "type": "new", "title": "Abyssal lantern", "namespace": "Main", "revisionId": 14979951, "changes": [{"type": "edit", "user": "Microbrews", "comment": "", "timestamp": "2025-09-04T18:27:30Z"}, {"type": "edit", "user": "Microbrews", "comment": "", "timestamp": "2025-09-04T18:01:56Z"}, {"type": "edit", "user": "Microbrews", "comment": "test", "timestamp": "2025-09-04T18:01:37Z"}, {"type": "edit", "user": "Microbrews", "comment": "default version", "timestamp": "2025-09-04T18:00:53Z"}]}, {"timestamp": "2025-09-04T18:22:49Z", "type": "new", "title": "Sailing/Development", "namespace": "Main", "revisionId": 14979946, "changes": [{"type": "new", "user": "BigDiesel2m", "comment": "Created page with \"==Polling== '''Sailing''' was proposed to players as a new [[skills|skill]] on [[27 March]] [[2023]] in a [[Update:Adding A New Skill: Introducing Sailing, Taming and Shamanism|blog]] alongside two other potential skills, [[Tam<PERSON>]] and [[Shamanism]]. This was followed by a poll to determine which of the three skills to further develop into a full proposal. Of the three proposed skills, the Sailing pitch received a plurality of votes in the Poll:Skill Pitches Poll|Skil...\"", "timestamp": "2025-09-04T18:22:49Z"}]}, {"timestamp": "2025-09-04T18:07:23Z", "type": "new", "title": "Bone blessed shards", "namespace": "Main", "revisionId": 14979941, "changes": [{"type": "new", "user": "<PERSON><PERSON><PERSON>", "comment": "Redirected to [[Blessed bone shards]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T18:07:23Z"}]}, {"timestamp": "2025-09-04T17:22:22Z", "type": "new", "title": "Chambers of Xeric/Challenge Mode/Strategies", "namespace": "Main", "revisionId": 14979897, "changes": [{"type": "edit", "user": "2605:59C0:283E:A208:3C0E:22E3:6069:DFDD", "comment": "shadow is better than harm and tbow", "timestamp": "2025-09-04T17:22:22Z"}]}, {"timestamp": "2025-09-04T17:17:46Z", "type": "new", "title": "Kalphite Queen/Strategies", "namespace": "Main", "revisionId": 14979896, "changes": [{"type": "edit", "user": "AtelierLogic2", "comment": "deleted ahrim mager + inappropriate ring switches", "timestamp": "2025-09-04T17:17:46Z"}]}, {"timestamp": "2025-09-04T17:13:20Z", "type": "new", "title": "<PERSON><PERSON>", "namespace": "Main", "revisionId": 14979894, "changes": [{"type": "new", "user": "2600:4040:2080:8E00:1CA:69A1:F613:6C4", "comment": "Redirected to [[<PERSON><PERSON><PERSON>]] via [[Special:SearchDigest]]", "timestamp": "2025-09-04T17:13:20Z"}]}, {"timestamp": "2025-09-04T17:03:48Z", "type": "new", "title": "March 2025 Sailing Alpha", "namespace": "Main", "revisionId": 14979888, "changes": [{"type": "edit", "user": "BigDiesel2m", "comment": "/* Docking points */", "timestamp": "2025-09-04T17:03:48Z"}]}, {"timestamp": "2025-09-04T16:07:31Z", "type": "new", "title": "Yrsa", "namespace": "Main", "revisionId": 14979874, "changes": [{"type": "edit", "user": "Anarchaon", "comment": "", "timestamp": "2025-09-04T16:07:31Z"}, {"type": "edit", "user": "Anarchaon", "comment": "moved mention of shoe store to trivia", "timestamp": "2025-09-04T16:04:49Z"}]}, {"timestamp": "2025-09-04T15:24:05Z", "type": "new", "title": "Compost Bin", "namespace": "Main", "revisionId": 14979863, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Locations */ fix hosidius map", "timestamp": "2025-09-04T15:24:05Z"}]}, {"timestamp": "2025-09-04T15:19:53Z", "type": "new", "title": "Ent (Vale Totems)", "namespace": "Main", "revisionId": 14979862, "changes": [{"type": "edit", "user": "2A02:6B6F:F276:A800:F0CF:2703:4C8C:13E1", "comment": "Changed the nest type that can be received, based on the information on the ent seed page https://oldschool.runescape.wiki/w/Ent_seed", "timestamp": "2025-09-04T15:19:53Z"}]}, {"timestamp": "2025-09-04T15:16:59Z", "type": "new", "title": "<PERSON><PERSON><PERSON>/Quick guide", "namespace": "Main", "revisionId": 14979860, "changes": [{"type": "edit", "user": "YoshiFan12", "comment": "/* Walkthrough */", "timestamp": "2025-09-04T15:16:59Z"}]}, {"timestamp": "2025-09-04T15:16:01Z", "type": "new", "title": "Hunter training", "namespace": "Main", "revisionId": 14979858, "changes": [{"type": "edit", "user": "Aivot-on", "comment": "", "timestamp": "2025-09-04T15:16:01Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "/* Level 75-99: Moonlight Moths */ epic redlink", "timestamp": "2025-09-04T15:02:40Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "slightly cleanup latest addition and expand more on reasoning to do more niche methods", "timestamp": "2025-09-04T14:49:27Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "2nd undo", "timestamp": "2025-09-04T12:57:39Z"}, {"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Undid revision 14979653 by [[Special:Contributions/2001:999:704:CFD1:F861:C6FF:6309:87F|2001:999:704:CFD1:F861:C6FF:6309:87F]] ([[User talk:2001:999:704:CFD1:F861:C6FF:6309:87F|talk]]) please consult with other people before deleting entire methods, they each have their own benefits and disadvantages such as quest reqs, intensity, or personal preference", "timestamp": "2025-09-04T12:57:23Z"}, {"type": "edit", "user": "2001:999:704:CFD1:F861:C6FF:6309:87F", "comment": "/* Other methods */ remove aerial fisfing. very bad rates, we shouldnt mislead people to think this is actually viable alternatuve to drift nets", "timestamp": "2025-09-04T03:59:05Z"}, {"type": "edit", "user": "2001:999:704:CFD1:F861:C6FF:6309:87F", "comment": "movinf drift net fishing to other methods and remove sallies and butterflies. sallies can be replaced with razorkebs and butterflies as well. typically the other methds have a reason to b included like better profits or split rates. this shouldnt be list of all hunting methods", "timestamp": "2025-09-04T03:54:28Z"}]}, {"timestamp": "2025-09-04T15:05:22Z", "type": "new", "title": "Free-to-play Crafting training", "namespace": "Main", "revisionId": 14979851, "changes": [{"type": "edit", "user": "<PERSON><PERSON><PERSON>", "comment": "Undid revision 14979850 by [[Special:Contributions/2600:4041:5F51:7800:DD34:9959:1C52:5942|2600:4041:5F51:7800:DD34:9959:1C52:5942]] ([[User talk:2600:4041:5F51:7800:DD34:9959:1C52:5942|talk]])", "timestamp": "2025-09-04T15:05:22Z"}, {"type": "edit", "user": "2600:4041:5F51:7800:DD34:9959:1C52:5942", "comment": "/* In-Place Training Methods */", "timestamp": "2025-09-04T15:04:29Z"}]}, {"timestamp": "2025-09-04T14:46:51Z", "type": "new", "title": "Arno", "namespace": "Main", "revisionId": 14979839, "changes": [{"type": "edit", "user": "Gazu", "comment": "added clarification", "timestamp": "2025-09-04T14:46:51Z"}]}, {"timestamp": "2025-09-04T14:44:35Z", "type": "new", "title": "Large geyser (God Wars Dungeon)", "namespace": "Main", "revisionId": 14979838, "changes": [{"type": "edit", "user": "Pavocado", "comment": "Added external", "timestamp": "2025-09-04T14:44:35Z"}]}, {"timestamp": "2025-09-04T14:32:27Z", "type": "new", "title": "Free-to-play Fishing training", "namespace": "Main", "revisionId": 14979834, "changes": [{"type": "edit", "user": "2001:1C00:8C04:5300:5D73:794:4C96:58E6", "comment": "/* Profitable methods */", "timestamp": "2025-09-04T14:32:27Z"}]}]