{"name": "osrs-wiki-watcher", "version": "1.0.0", "description": "Live monitoring system for Old School RuneScape Wiki content changes", "main": "index.js", "bin": {"osrs-watcher": "./index.js"}, "scripts": {"start": "node index.js start", "init": "node index.js init", "status": "node index.js status", "check": "node index.js check"}, "keywords": ["osrs", "runescape", "wiki", "watcher", "monitoring", "content"], "author": "OSRS Wiki Watcher", "license": "MIT", "dependencies": {"axios": "^1.4.0"}, "engines": {"node": ">=14.0.0"}}