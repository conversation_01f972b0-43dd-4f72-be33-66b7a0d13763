/**
 * OSRS Wiki API Client for the Watcher System
 * Handles all API interactions with the Old School RuneScape Wiki
 */

const axios = require('axios');

class OSRSWikiAPI {
  constructor(logger) {
    this.apiUrl = 'https://oldschool.runescape.wiki/api.php';
    this.logger = logger;
    this.userAgent = 'OSRS-Wiki-Watcher/1.0 (Educational Research)';
    
    // OSRS-specific filtering - same as original system
    this.osrsCategories = [
      'Category:Items',
      'Category:NPCs', 
      'Category:Locations',
      'Category:Quests',
      'Category:Skills',
      'Category:Equipment',
      'Category:Monsters',
      'Category:Activities',
      'Category:Minigames',
      'Category:Weapons',
      'Category:Armour',
      'Category:Food',
      'Category:Potions',
      'Category:Runes',
      'Category:Tools',
      'Category:Jewellery'
    ];
  }

  /**
   * Get recent changes from the wiki
   */
  async getRecentChanges(options = {}) {
    const params = {
      action: 'query',
      list: 'recentchanges',
      rcprop: 'title|ids|sizes|flags|user|timestamp|comment',
      rcnamespace: '0|14', // Main and Category namespaces
      rctype: 'edit|new|log',
      rclimit: options.limit || 500,
      format: 'json',
      maxlag: 5
    };

    if (options.rcstart) {
      params.rcstart = options.rcstart;
    }
    if (options.rcend) {
      params.rcend = options.rcend;
    }
    if (options.rccontinue) {
      params.rccontinue = options.rccontinue;
    }

    try {
      const response = await axios.get(this.apiUrl, {
        params: params,
        headers: {
          'User-Agent': this.userAgent,
          'Accept-Encoding': 'gzip'
        },
        timeout: 30000
      });

      const changes = response.data.query?.recentchanges || [];
      const filteredChanges = changes.filter(change => this.isOSRSPage(change.title));

      return {
        changes: filteredChanges,
        continue: response.data.continue?.rccontinue || null
      };

    } catch (error) {
      this.logger.error('Failed to get recent changes:', error.message);
      throw error;
    }
  }

  /**
   * Get page content with revision information
   */
  async getPageContent(titles) {
    if (!Array.isArray(titles)) {
      titles = [titles];
    }

    const params = {
      action: 'query',
      prop: 'revisions|info',
      rvprop: 'content|ids|timestamp',
      inprop: 'url',
      titles: titles.join('|'),
      format: 'json',
      maxlag: 5
    };

    try {
      const response = await axios.get(this.apiUrl, {
        params: params,
        headers: {
          'User-Agent': this.userAgent,
          'Accept-Encoding': 'gzip'
        },
        timeout: 30000
      });

      const pages = response.data.query?.pages || {};
      const results = {};

      for (const pageId in pages) {
        const page = pages[pageId];
        const title = page.title;

        if (page.revisions && page.revisions[0]) {
          const revision = page.revisions[0];
          results[title] = {
            content: revision['*'] || '',
            revisionId: revision.revid,
            timestamp: revision.timestamp,
            namespace: this.getNamespaceFromTitle(title),
            url: page.fullurl || null
          };
        } else {
          // Page exists but no content (might be deleted or empty)
          results[title] = {
            content: '',
            revisionId: null,
            timestamp: null,
            namespace: this.getNamespaceFromTitle(title),
            url: page.fullurl || null
          };
        }
      }

      return results;

    } catch (error) {
      this.logger.error('Failed to get page content:', error.message);
      throw error;
    }
  }

  /**
   * Get all pages from specific categories
   */
  async getCategoryMembers(categoryTitle, options = {}) {
    const pages = [];
    let cmcontinue = null;

    do {
      const params = {
        action: 'query',
        list: 'categorymembers',
        cmtitle: categoryTitle,
        cmlimit: options.limit || 500,
        format: 'json',
        maxlag: 5
      };

      if (cmcontinue) {
        params.cmcontinue = cmcontinue;
      }

      try {
        const response = await axios.get(this.apiUrl, {
          params: params,
          headers: {
            'User-Agent': this.userAgent,
            'Accept-Encoding': 'gzip'
          },
          timeout: 30000
        });

        if (response.data.query && response.data.query.categorymembers) {
          const members = response.data.query.categorymembers;
          pages.push(...members.map(member => ({
            title: member.title,
            namespace: member.ns,
            pageid: member.pageid
          })));

          cmcontinue = response.data.continue?.cmcontinue;
        } else {
          break;
        }

        // Delay between requests
        if (cmcontinue) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error) {
        this.logger.error(`Error fetching category members for ${categoryTitle}:`, error.message);
        break;
      }
    } while (cmcontinue && (!options.maxPages || pages.length < options.maxPages));

    return pages;
  }

  /**
   * Check if a page title is OSRS-related (same logic as original system)
   */
  isOSRSPage(title) {
    // Always include category pages
    if (title.startsWith('Category:')) {
      return true;
    }

    // Include template pages
    if (title.startsWith('Template:')) {
      return true;
    }

    // Exclude other namespaces with colons (User:, File:, etc.)
    if (title.includes(':') && !title.startsWith('Category:') && !title.startsWith('Template:')) {
      return false;
    }

    // For main namespace pages, be conservative
    return !title.includes(':') || title.startsWith('Category:') || title.startsWith('Template:');
  }

  /**
   * Get namespace from page title
   */
  getNamespaceFromTitle(title) {
    if (title.startsWith('Category:')) return 'Category';
    if (title.startsWith('Template:')) return 'Template';
    if (title.startsWith('User:')) return 'User';
    if (title.startsWith('File:')) return 'File';
    if (title.startsWith('Help:')) return 'Help';
    if (title.startsWith('Project:')) return 'Project';
    return 'Main';
  }

  /**
   * Get page info (revision ID, timestamp) without full content
   */
  async getPageInfo(titles) {
    if (!Array.isArray(titles)) {
      titles = [titles];
    }

    const params = {
      action: 'query',
      prop: 'revisions|info',
      rvprop: 'ids|timestamp',
      titles: titles.join('|'),
      format: 'json',
      maxlag: 5
    };

    try {
      const response = await axios.get(this.apiUrl, {
        params: params,
        headers: {
          'User-Agent': this.userAgent,
          'Accept-Encoding': 'gzip'
        },
        timeout: 30000
      });

      const pages = response.data.query?.pages || {};
      const results = {};

      for (const pageId in pages) {
        const page = pages[pageId];
        const title = page.title;

        if (page.revisions && page.revisions[0]) {
          const revision = page.revisions[0];
          results[title] = {
            revisionId: revision.revid,
            timestamp: revision.timestamp,
            namespace: this.getNamespaceFromTitle(title)
          };
        }
      }

      return results;

    } catch (error) {
      this.logger.error('Failed to get page info:', error.message);
      throw error;
    }
  }
}

module.exports = OSRSWikiAPI;
