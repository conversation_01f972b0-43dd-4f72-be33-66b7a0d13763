/**
 * Logger utility for OSRS Wiki Watcher
 */

const fs = require('fs').promises;
const path = require('path');

class WatcherLogger {
  constructor(logDir) {
    this.logDir = logDir;
    this.logLevel = process.env.LOG_LEVEL || 'info';
    this.logLevels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
  }

  async initialize() {
    await fs.mkdir(this.logDir, { recursive: true });
  }

  _shouldLog(level) {
    return this.logLevels[level] <= this.logLevels[this.logLevel];
  }

  _formatMessage(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const formattedArgs = args.length > 0 ? ' ' + args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ') : '';
    
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${formattedArgs}`;
  }

  async _writeToFile(filename, message) {
    try {
      const logFile = path.join(this.logDir, filename);
      await fs.appendFile(logFile, message + '\n');
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    }
  }

  error(message, ...args) {
    const formatted = this._formatMessage('error', message, ...args);
    if (this._shouldLog('error')) {
      console.error(formatted);
    }
    this._writeToFile('watcher-error.log', formatted);
  }

  warn(message, ...args) {
    const formatted = this._formatMessage('warn', message, ...args);
    if (this._shouldLog('warn')) {
      console.warn(formatted);
    }
    this._writeToFile('watcher-activity.log', formatted);
  }

  info(message, ...args) {
    const formatted = this._formatMessage('info', message, ...args);
    if (this._shouldLog('info')) {
      console.log(formatted);
    }
    this._writeToFile('watcher-activity.log', formatted);
  }

  debug(message, ...args) {
    const formatted = this._formatMessage('debug', message, ...args);
    if (this._shouldLog('debug')) {
      console.log(formatted);
    }
    this._writeToFile('watcher-debug.log', formatted);
  }

  async logChange(changeData) {
    const changeEntry = {
      timestamp: new Date().toISOString(),
      ...changeData
    };
    
    const changeLogFile = path.join(this.logDir, 'changes.json');
    
    try {
      let changes = [];
      try {
        const existing = await fs.readFile(changeLogFile, 'utf8');
        changes = JSON.parse(existing);
      } catch (error) {
        // File doesn't exist yet, start with empty array
      }
      
      changes.push(changeEntry);
      await fs.writeFile(changeLogFile, JSON.stringify(changes, null, 2));
      
      this.info(`📝 Change logged: ${changeData.type} - ${changeData.title}`);
    } catch (error) {
      this.error('Failed to log change:', error.message);
    }
  }
}

module.exports = WatcherLogger;
