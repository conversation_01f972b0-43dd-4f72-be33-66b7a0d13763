/**
 * Content Index Manager for OSRS Wiki Watcher
 * Manages the index of known pages and their revision information
 */

const fs = require('fs').promises;
const path = require('path');

class ContentIndex {
  constructor(indexPath) {
    this.indexPath = indexPath;
    this.index = {
      pages: {},
      lastFullScan: null,
      lastCheck: null,
      totalPages: 0,
      version: '1.0'
    };
  }

  async initialize() {
    await fs.mkdir(path.dirname(this.indexPath), { recursive: true });
    await this.load();
  }

  async load() {
    try {
      const data = await fs.readFile(this.indexPath, 'utf8');
      this.index = JSON.parse(data);
      
      // Ensure all required fields exist
      if (!this.index.pages) this.index.pages = {};
      if (!this.index.version) this.index.version = '1.0';
      if (!this.index.totalPages) this.index.totalPages = 0;
      
    } catch (error) {
      // Index doesn't exist yet, use defaults
      this.index = {
        pages: {},
        lastFullScan: null,
        lastCheck: null,
        totalPages: 0,
        version: '1.0'
      };
    }
  }

  async save() {
    await fs.writeFile(this.indexPath, JSON.stringify(this.index, null, 2));
  }

  addPage(title, pageData) {
    this.index.pages[title] = {
      revisionId: pageData.revisionId || null,
      timestamp: pageData.timestamp || new Date().toISOString(),
      namespace: pageData.namespace || 'Main',
      lastChecked: new Date().toISOString(),
      filePath: pageData.filePath || null,
      contentHash: pageData.contentHash || null
    };
    this.index.totalPages = Object.keys(this.index.pages).length;
  }

  getPage(title) {
    return this.index.pages[title] || null;
  }

  hasPage(title) {
    return title in this.index.pages;
  }

  removePage(title) {
    if (this.index.pages[title]) {
      delete this.index.pages[title];
      this.index.totalPages = Object.keys(this.index.pages).length;
    }
  }

  getAllPages() {
    return Object.keys(this.index.pages);
  }

  getPagesByNamespace(namespace) {
    return Object.entries(this.index.pages)
      .filter(([title, data]) => data.namespace === namespace)
      .map(([title, data]) => title);
  }

  updateLastCheck(timestamp = null) {
    this.index.lastCheck = timestamp || new Date().toISOString();
  }

  updateLastFullScan(timestamp = null) {
    this.index.lastFullScan = timestamp || new Date().toISOString();
  }

  getStats() {
    const namespaces = {};
    Object.values(this.index.pages).forEach(page => {
      namespaces[page.namespace] = (namespaces[page.namespace] || 0) + 1;
    });

    return {
      totalPages: this.index.totalPages,
      lastFullScan: this.index.lastFullScan,
      lastCheck: this.index.lastCheck,
      namespaces: namespaces
    };
  }

  // Check if a page needs updating based on revision ID or content hash
  needsUpdate(title, currentRevisionId, currentContentHash = null) {
    const page = this.getPage(title);
    if (!page) return true; // New page

    // If we have real revision IDs for both, compare them
    if (page.revisionId && currentRevisionId) {
      const pageRevStr = page.revisionId.toString();
      const currentRevStr = currentRevisionId.toString();

      // Check if both are real revision IDs (not fake hashes)
      const pageRevIsFake = /^[a-f0-9]{8}$/.test(pageRevStr);
      const currentRevIsFake = /^[a-f0-9]{8}$/.test(currentRevStr);

      if (!pageRevIsFake && !currentRevIsFake) {
        return pageRevStr !== currentRevStr;
      }
    }

    // Otherwise, compare content hashes if available
    if (currentContentHash && page.contentHash) {
      return page.contentHash !== currentContentHash;
    }

    // If we can't compare properly, assume it needs update
    return true;
  }

  // Get pages that haven't been checked recently
  getStalePages(maxAge = 24 * 60 * 60 * 1000) { // 24 hours default
    const cutoff = new Date(Date.now() - maxAge).toISOString();
    
    return Object.entries(this.index.pages)
      .filter(([title, data]) => !data.lastChecked || data.lastChecked < cutoff)
      .map(([title, data]) => title);
  }
}

module.exports = ContentIndex;
