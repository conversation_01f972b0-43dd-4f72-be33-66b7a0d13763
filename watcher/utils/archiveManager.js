/**
 * Archive Manager for OSRS Wiki Watcher
 * Handles archiving of old page versions with timestamps
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class ArchiveManager {
  constructor(archiveDir, logger) {
    this.archiveDir = archiveDir;
    this.logger = logger;
  }

  async initialize() {
    await fs.mkdir(this.archiveDir, { recursive: true });
    await fs.mkdir(path.join(this.archiveDir, 'Main'), { recursive: true });
    await fs.mkdir(path.join(this.archiveDir, 'Category'), { recursive: true });
  }

  /**
   * Archive an old version of a page before updating it
   */
  async archivePage(pageTitle, currentContent, namespace = 'Main') {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const safeTitle = this.sanitizeFilename(pageTitle);
      
      // Create archive filename with timestamp
      const archiveFilename = `${safeTitle}_${timestamp}.json`;
      const archivePath = path.join(this.archiveDir, namespace, archiveFilename);
      
      // Create archive entry with metadata
      const archiveEntry = {
        title: pageTitle,
        namespace: namespace,
        archivedAt: new Date().toISOString(),
        originalContent: currentContent,
        contentHash: this.generateContentHash(currentContent),
        reason: 'page_updated'
      };
      
      await fs.writeFile(archivePath, JSON.stringify(archiveEntry, null, 2));
      
      this.logger.info(`📦 Archived page: ${pageTitle} -> ${archiveFilename}`);
      
      return {
        archivePath: archivePath,
        archiveFilename: archiveFilename,
        timestamp: timestamp
      };
      
    } catch (error) {
      this.logger.error(`Failed to archive page ${pageTitle}:`, error.message);
      throw error;
    }
  }

  /**
   * Get archive history for a specific page
   */
  async getPageHistory(pageTitle, namespace = 'Main') {
    try {
      const safeTitle = this.sanitizeFilename(pageTitle);
      const archiveDir = path.join(this.archiveDir, namespace);
      
      const files = await fs.readdir(archiveDir);
      const pageArchives = files.filter(file => 
        file.startsWith(safeTitle + '_') && file.endsWith('.json')
      );
      
      const history = [];
      for (const file of pageArchives) {
        try {
          const filePath = path.join(archiveDir, file);
          const content = await fs.readFile(filePath, 'utf8');
          const archiveData = JSON.parse(content);
          
          history.push({
            filename: file,
            archivedAt: archiveData.archivedAt,
            contentHash: archiveData.contentHash,
            reason: archiveData.reason || 'unknown'
          });
        } catch (error) {
          this.logger.warn(`Failed to read archive file ${file}:`, error.message);
        }
      }
      
      // Sort by archive date, newest first
      history.sort((a, b) => new Date(b.archivedAt) - new Date(a.archivedAt));
      
      return history;
      
    } catch (error) {
      this.logger.error(`Failed to get history for ${pageTitle}:`, error.message);
      return [];
    }
  }

  /**
   * Clean up old archives (keep only last N versions)
   */
  async cleanupOldArchives(pageTitle, namespace = 'Main', keepVersions = 10) {
    try {
      const history = await this.getPageHistory(pageTitle, namespace);
      
      if (history.length <= keepVersions) {
        return; // Nothing to clean up
      }
      
      const toDelete = history.slice(keepVersions);
      let deletedCount = 0;
      
      for (const archive of toDelete) {
        try {
          const filePath = path.join(this.archiveDir, namespace, archive.filename);
          await fs.unlink(filePath);
          deletedCount++;
        } catch (error) {
          this.logger.warn(`Failed to delete archive ${archive.filename}:`, error.message);
        }
      }
      
      if (deletedCount > 0) {
        this.logger.info(`🧹 Cleaned up ${deletedCount} old archives for ${pageTitle}`);
      }
      
    } catch (error) {
      this.logger.error(`Failed to cleanup archives for ${pageTitle}:`, error.message);
    }
  }

  /**
   * Get archive statistics
   */
  async getArchiveStats() {
    try {
      const stats = {
        totalArchives: 0,
        namespaces: {},
        oldestArchive: null,
        newestArchive: null
      };
      
      const namespaces = ['Main', 'Category'];
      
      for (const namespace of namespaces) {
        const namespaceDir = path.join(this.archiveDir, namespace);
        
        try {
          const files = await fs.readdir(namespaceDir);
          const archiveFiles = files.filter(file => file.endsWith('.json'));
          
          stats.namespaces[namespace] = archiveFiles.length;
          stats.totalArchives += archiveFiles.length;
          
          // Get date info from filenames
          for (const file of archiveFiles) {
            const match = file.match(/_(\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z)\.json$/);
            if (match) {
              const dateStr = match[1].replace(/-/g, ':').replace(/T(\d{2}):(\d{2}):(\d{2}):(\d{3})Z/, 'T$1:$2:$3.$4Z');
              const date = new Date(dateStr);
              
              if (!stats.oldestArchive || date < new Date(stats.oldestArchive)) {
                stats.oldestArchive = date.toISOString();
              }
              if (!stats.newestArchive || date > new Date(stats.newestArchive)) {
                stats.newestArchive = date.toISOString();
              }
            }
          }
        } catch (error) {
          stats.namespaces[namespace] = 0;
        }
      }
      
      return stats;
      
    } catch (error) {
      this.logger.error('Failed to get archive stats:', error.message);
      return { totalArchives: 0, namespaces: {}, oldestArchive: null, newestArchive: null };
    }
  }

  sanitizeFilename(filename) {
    return filename
      .replace(/[<>:"/\\|?*]/g, '_')
      .replace(/\s+/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
  }

  generateContentHash(content) {
    return crypto.createHash('sha256').update(JSON.stringify(content)).digest('hex').substring(0, 16);
  }
}

module.exports = ArchiveManager;
