/**
 * Initial Scan Script for OSRS Wiki Watcher
 * Scans existing wiki-content folder to build initial content index
 */

const fs = require('fs').promises;
const path = require('path');
const WatcherLogger = require('./utils/logger');
const ContentIndex = require('./utils/contentIndex');

class InitialScanner {
  constructor() {
    this.workspaceRoot = process.cwd();
    this.wikiContentDir = path.join(this.workspaceRoot, 'wiki-content');
    this.watcherDir = path.join(this.workspaceRoot, 'watcher');
    this.logDir = path.join(this.watcherDir, 'logs');
    this.indexPath = path.join(this.watcherDir, 'data', 'content-index.json');
    
    this.logger = new WatcherLogger(this.logDir);
    this.contentIndex = new ContentIndex(this.indexPath);
    
    this.stats = {
      totalFiles: 0,
      processedFiles: 0,
      errorFiles: 0,
      namespaces: {}
    };
  }

  async initialize() {
    await fs.mkdir(this.watcherDir, { recursive: true });
    await fs.mkdir(path.join(this.watcherDir, 'data'), { recursive: true });
    await fs.mkdir(path.join(this.watcherDir, 'archives'), { recursive: true });
    
    await this.logger.initialize();
    await this.contentIndex.initialize();
    
    this.logger.info('🔍 Initial scanner initialized');
  }

  async scanExistingContent() {
    this.logger.info('🚀 Starting initial scan of existing wiki content...');
    
    const pagesDir = path.join(this.wikiContentDir, 'pages');
    
    try {
      // Check if wiki-content directory exists
      await fs.access(pagesDir);
    } catch (error) {
      this.logger.error('Wiki content directory not found:', pagesDir);
      throw new Error('Wiki content directory not found. Please run the initial download first.');
    }

    // Scan Main namespace
    await this.scanNamespace('Main');
    
    // Scan Category namespace
    await this.scanNamespace('Category');
    
    // Update index metadata
    this.contentIndex.updateLastFullScan();
    await this.contentIndex.save();
    
    // Generate report
    await this.generateScanReport();
    
    this.logger.info('✅ Initial scan completed successfully!');
    this.logger.info(`📊 Processed ${this.stats.processedFiles}/${this.stats.totalFiles} files`);
    
    return this.stats;
  }

  async scanNamespace(namespace) {
    this.logger.info(`📂 Scanning ${namespace} namespace...`);
    
    const namespaceDir = path.join(this.wikiContentDir, 'pages', namespace);
    
    try {
      const files = await fs.readdir(namespaceDir);
      const jsonFiles = files.filter(file => file.endsWith('.json'));
      
      this.stats.totalFiles += jsonFiles.length;
      this.stats.namespaces[namespace] = {
        total: jsonFiles.length,
        processed: 0,
        errors: 0
      };
      
      this.logger.info(`📄 Found ${jsonFiles.length} files in ${namespace} namespace`);
      
      // Process files in batches
      const batchSize = 100;
      for (let i = 0; i < jsonFiles.length; i += batchSize) {
        const batch = jsonFiles.slice(i, i + batchSize);
        await this.processBatch(batch, namespace, namespaceDir);
        
        // Progress update
        const progress = ((i + batch.length) / jsonFiles.length * 100).toFixed(1);
        this.logger.info(`📊 ${namespace} progress: ${progress}% (${i + batch.length}/${jsonFiles.length})`);
      }
      
      this.logger.info(`✅ ${namespace} namespace scan complete: ${this.stats.namespaces[namespace].processed} processed`);
      
    } catch (error) {
      this.logger.error(`Failed to scan ${namespace} namespace:`, error.message);
      throw error;
    }
  }

  async processBatch(files, namespace, namespaceDir) {
    const promises = files.map(file => this.processFile(file, namespace, namespaceDir));
    await Promise.allSettled(promises);
  }

  async processFile(filename, namespace, namespaceDir) {
    try {
      const filePath = path.join(namespaceDir, filename);
      const content = await fs.readFile(filePath, 'utf8');
      const pageData = JSON.parse(content);
      
      // Extract page information
      const title = pageData.title;
      if (!title) {
        this.logger.warn(`No title found in file: ${filename}`);
        return;
      }
      
      // Try to extract revision ID from content if available
      let revisionId = null;
      if (pageData.revisionId) {
        revisionId = pageData.revisionId;
      } else if (pageData.content && typeof pageData.content === 'string') {
        // For now, we'll use a hash of the content as a pseudo-revision ID
        const crypto = require('crypto');
        revisionId = crypto.createHash('md5').update(pageData.content).digest('hex').substring(0, 8);
      }
      
      // Add to content index
      this.contentIndex.addPage(title, {
        revisionId: revisionId,
        timestamp: pageData.downloadedAt || pageData.timestamp || new Date().toISOString(),
        namespace: namespace,
        filePath: filePath,
        contentHash: this.generateContentHash(pageData.content || '')
      });
      
      this.stats.processedFiles++;
      this.stats.namespaces[namespace].processed++;
      
    } catch (error) {
      this.logger.error(`Failed to process file ${filename}:`, error.message);
      this.stats.errorFiles++;
      this.stats.namespaces[namespace].errors++;
    }
  }

  generateContentHash(content) {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
  }

  async generateScanReport() {
    const report = {
      scanCompletedAt: new Date().toISOString(),
      stats: this.stats,
      indexStats: this.contentIndex.getStats(),
      summary: {
        totalPagesIndexed: this.contentIndex.index.totalPages,
        successRate: ((this.stats.processedFiles / this.stats.totalFiles) * 100).toFixed(2) + '%',
        namespaceBreakdown: this.stats.namespaces
      }
    };
    
    const reportPath = path.join(this.watcherDir, 'logs', 'initial-scan-report.json');
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    
    this.logger.info('📋 Scan report generated:', reportPath);
    
    return report;
  }
}

// Main execution
async function main() {
  const scanner = new InitialScanner();
  
  try {
    await scanner.initialize();
    const results = await scanner.scanExistingContent();
    
    console.log('\n🎉 Initial scan completed!');
    console.log(`📊 Results:`);
    console.log(`   📄 Total files: ${results.totalFiles}`);
    console.log(`   ✅ Processed: ${results.processedFiles}`);
    console.log(`   ❌ Errors: ${results.errorFiles}`);
    console.log(`   📂 Namespaces:`);
    
    Object.entries(results.namespaces).forEach(([ns, stats]) => {
      console.log(`      ${ns}: ${stats.processed}/${stats.total} (${stats.errors} errors)`);
    });
    
    console.log(`\n📁 Index saved to: watcher/data/content-index.json`);
    console.log(`📋 Report saved to: watcher/logs/initial-scan-report.json`);
    
  } catch (error) {
    console.error('💥 Initial scan failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = InitialScanner;
