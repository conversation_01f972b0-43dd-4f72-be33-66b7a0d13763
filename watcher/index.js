#!/usr/bin/env node

/**
 * OSRS Wiki Watcher - Main Entry Point
 * Command-line interface for the wiki watcher system
 */

const OSRSWikiWatcher = require('./osrsWikiWatcher');
const InitialScanner = require('./initialScan');

class WatcherCLI {
  constructor() {
    this.watcher = null;
  }

  async run() {
    const args = process.argv.slice(2);
    const command = args[0];

    try {
      switch (command) {
        case 'init':
        case 'scan':
          await this.runInitialScan();
          break;
          
        case 'start':
          await this.startWatcher(args.slice(1));
          break;
          
        case 'status':
          await this.showStatus();
          break;
          
        case 'check':
          await this.runSingleCheck();
          break;
          
        case 'help':
        case '--help':
        case '-h':
          this.showHelp();
          break;
          
        default:
          console.log('❌ Unknown command:', command);
          this.showHelp();
          process.exit(1);
      }
    } catch (error) {
      console.error('💥 Command failed:', error.message);
      process.exit(1);
    }
  }

  async runInitialScan() {
    console.log('🔍 Running initial scan of existing wiki content...');
    console.log('');
    
    const scanner = new InitialScanner();
    await scanner.initialize();
    const results = await scanner.scanExistingContent();
    
    console.log('');
    console.log('🎉 Initial scan completed!');
    console.log(`📊 Results:`);
    console.log(`   📄 Total files: ${results.totalFiles}`);
    console.log(`   ✅ Processed: ${results.processedFiles}`);
    console.log(`   ❌ Errors: ${results.errorFiles}`);
    console.log(`   📂 Namespaces:`);
    
    Object.entries(results.namespaces).forEach(([ns, stats]) => {
      console.log(`      ${ns}: ${stats.processed}/${stats.total} (${stats.errors} errors)`);
    });
    
    console.log('');
    console.log('✅ Ready to start watching! Run: node watcher/index.js start');
  }

  async startWatcher(args) {
    console.log('🚀 Starting OSRS Wiki Watcher...');
    
    // Parse options
    const options = this.parseOptions(args);
    
    this.watcher = new OSRSWikiWatcher(options);
    await this.watcher.initialize();
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n🛑 Received shutdown signal...');
      if (this.watcher) {
        await this.watcher.stop();
      }
      process.exit(0);
    });
    
    process.on('SIGTERM', async () => {
      console.log('\n🛑 Received termination signal...');
      if (this.watcher) {
        await this.watcher.stop();
      }
      process.exit(0);
    });
    
    await this.watcher.start();
    
    // Keep process alive
    console.log('');
    console.log('🎬 Watcher is now running...');
    console.log('📊 Press Ctrl+C to stop');
    console.log('');
    
    // Show periodic status updates
    setInterval(() => {
      const status = this.watcher.getStatus();
      console.log(`📊 Status: ${status.stats.totalChecks} checks, ${status.stats.changesDetected} changes, ${status.stats.pagesUpdated} updates`);
    }, 60000); // Every minute
  }

  async showStatus() {
    try {
      const watcher = new OSRSWikiWatcher();
      await watcher.initialize();
      const status = watcher.getStatus();
      
      console.log('📊 OSRS Wiki Watcher Status');
      console.log('============================');
      console.log('');
      console.log(`🔄 Running: ${status.isRunning ? '✅ Yes' : '❌ No'}`);
      console.log(`📈 Total Checks: ${status.stats.totalChecks}`);
      console.log(`🔍 Changes Detected: ${status.stats.changesDetected}`);
      console.log(`📄 Pages Updated: ${status.stats.pagesUpdated}`);
      console.log(`📦 Pages Archived: ${status.stats.pagesArchived}`);
      console.log(`❌ Errors: ${status.stats.errors}`);
      console.log(`⏰ Last Check: ${status.stats.lastCheck || 'Never'}`);
      console.log(`🚀 Started: ${status.stats.startTime || 'Not started'}`);
      console.log('');
      console.log('📚 Content Index:');
      console.log(`   📄 Total Pages: ${status.indexStats.totalPages}`);
      console.log(`   📂 Namespaces: ${Object.entries(status.indexStats.namespaces).map(([ns, count]) => `${ns}(${count})`).join(', ')}`);
      console.log(`   🔍 Last Full Scan: ${status.indexStats.lastFullScan || 'Never'}`);
      console.log(`   ⏰ Last Check: ${status.indexStats.lastCheck || 'Never'}`);
      
    } catch (error) {
      console.log('❌ Could not get status:', error.message);
      console.log('💡 Try running initial scan first: node watcher/index.js init');
    }
  }

  async runSingleCheck() {
    console.log('🔍 Running single check...');
    
    const watcher = new OSRSWikiWatcher();
    await watcher.initialize();
    await watcher.performCheck();
    
    const status = watcher.getStatus();
    console.log('✅ Check completed');
    console.log(`📊 Changes detected: ${status.stats.changesDetected}`);
    console.log(`📄 Pages updated: ${status.stats.pagesUpdated}`);
  }

  parseOptions(args) {
    const options = {};
    
    for (let i = 0; i < args.length; i += 2) {
      const key = args[i];
      const value = args[i + 1];
      
      switch (key) {
        case '--interval':
          options.pollInterval = parseInt(value) * 1000; // Convert to milliseconds
          break;
        case '--batch-size':
          options.batchSize = parseInt(value);
          break;
        case '--max-retries':
          options.maxRetries = parseInt(value);
          break;
      }
    }
    
    return options;
  }

  showHelp() {
    console.log('🔍 OSRS Wiki Watcher - Live Content Monitoring');
    console.log('==============================================');
    console.log('');
    console.log('Commands:');
    console.log('  init, scan           Run initial scan of existing wiki content');
    console.log('  start [options]      Start the live watcher');
    console.log('  status               Show current watcher status');
    console.log('  check                Run a single check for changes');
    console.log('  help                 Show this help message');
    console.log('');
    console.log('Start Options:');
    console.log('  --interval <seconds> Poll interval in seconds (default: 300)');
    console.log('  --batch-size <num>   Batch size for processing (default: 10)');
    console.log('  --max-retries <num>  Max retries for failed requests (default: 3)');
    console.log('');
    console.log('Examples:');
    console.log('  node watcher/index.js init');
    console.log('  node watcher/index.js start');
    console.log('  node watcher/index.js start --interval 600 --batch-size 20');
    console.log('  node watcher/index.js status');
    console.log('  node watcher/index.js check');
    console.log('');
    console.log('First time setup:');
    console.log('  1. Run "node watcher/index.js init" to scan existing content');
    console.log('  2. Run "node watcher/index.js start" to begin monitoring');
    console.log('');
  }
}

// Run CLI if called directly
if (require.main === module) {
  const cli = new WatcherCLI();
  cli.run().catch(error => {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  });
}

module.exports = WatcherCLI;
