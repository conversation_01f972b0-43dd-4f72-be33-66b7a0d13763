{"title": "ScapeRune", "namespace": "Main", "archivedAt": "2025-09-05T20:38:26.441Z", "originalContent": {"title": "ScapeRune", "namespace": "Main", "content": "{{External|rs}}\n{{Infobox Location\n|version1 = Evil Bob's island\n|version2 = Prison\n|name1 = Evil Bob's island\n|name2 = Prison\n|image1 = [[File:Evil Bob random event.png|300px]]\n|image2 = [[File:Prison.png|300px]]\n|release1 = [[9 August]] [[2005]]\n|release2 = [[10 January]] [[2006]]\n|update1 = Tai Bwo Wannai Clean-Up\n|update2 = The Hand in the Sand\n|members = No\n|location = \n|music1 = [[Evil Bob's Island]]\n|music2 = [[In the Clink]]\n|map1 = {{Map|name=ScapeRune|x:2528,y:4775|plane=0|mapID=10057}}\n|map2 = {{Map|name=ScapeRune|x:2085,y:4465|plane=0|mapID=10127}}\n|type = realm\n|leagueRegion = No\n}}\n'''ScapeRune''' is a realm parallel to [[Gielinor]]. It appears to be an opposite world, where many concepts are reversed. For example, fish are caught already cooked, and must be uncooked at an uncooking pot. The only areas of ScapeRune accessible to the player are an island and a prison, either of which players may be taken to by the \"[[Evil Bob]]\" [[random event]].\n\n==Transportation==\nThe player is accosted by [[Evil Bob]], the evil twin of [[<PERSON> the Jagex Cat|<PERSON>]] the [[Jagex]] cat. With a \"meow\" from Evil Bob and an \"No... what? Noooooooo!\" from the player, you will arrive at Evil <PERSON>'s island. If you shout \"Aaarrrrgh!\" instead, you arrive in the prison. When you return, a message appears in the chatbox that says \"Welcome back to Old School Runescape.\"\n\n==Island==\nAfter declaring you to be his [[Servant (ScapeRune)|slave]], Bob will demand fish. Talk to his other slaves to be shown a video clip zooming to the required [[fishing]] area. There is one at each compass point, marked by different statues.\n[[File:Uncooking pot.png|thumb|200px|The uncooking pot.]]\nA [[small fishing net]] may be collected from the beach between the fishing areas, but it will not be kept on leaving the island. Catch a \"[[fishlike thing]]\" and use the uncooking pot to make it raw. Talk to Bob, and he will eat it if it was caught from the correct area.\n\nIf the fish was caught from an incorrect area, Bob will eat it, and then say it is disgusting. Each time you feed Bob an incorrect fish, it will take one more fish to get him to fall asleep, and you must keep going to the slave and catching fish to uncook and feed to Bob. For example, if you feed Bob one incorrect fish, it will take 2 correct fish to get him to fall asleep. Making 2 mistakes will require 3 fish to lull him to sleep, and so on.\n\nWhen the fish has been eaten, Bob falls asleep and the player may escape through the portal, receiving some Fishing [[experience]], or in some cases, [[Magic]] experience depending on the action being performed.\n\nOn reaching the portal, the player is now reminded to collect any dropped items before leaving.\n\nWhen you leave, you make a raspberry [[emote]] at the slave, \"Be seeing you!\", and then teleport back to where you were before the event.\n\n==Prison==\nEvil Bob can also teleport players to a prison, where they must speak to [[Prison Pete]] in order to discover how to escape. To escape, you must pull a lever, which will tell you which type of [[balloon animal]] to pop in order to find a [[Prison key (Prison Pete)|key]]. The animals are randomly selected and the same one may be picked twice.\n\nEvery balloon animal contains a prison key, but the right one must be popped in order to open the first barrier. After getting three correct keys, players are free to escape with Prison Pete and will be returned to where they were, awarded with a small prize.\n\nThe varying characteristics between each balloon animal can be easily distinguished from their horns, body width, and tail length.\n\n<gallery position=\"center\" widths=\"100px\">\nBalloon Animal (cat).png|Cat\nBalloon Animal (dog).png|Dog\nBalloon Animal (goat).png|Goat\nBalloon Animal (sheep).png|Sheep\n</gallery>\n\nThe \"return key\" option, the default action for the key, walks directly toward Prison Pete, with no attempt to navigate around obstructions.\n\nIt should be noted that the prison bears a striking resemblance to the [[Party Room|party hall]] in [[Falador]], perhaps implying that every location in Gielinor may also exist in ScapeRune.\n[[Category:Jails]]\n\n==Inhabitants==\n*[[Evil Bob]]\n*[[Prison Pete]]\n*[[Servant (ScapeRune)|Servant]]\n*[[Balloon animal]]\n*[[Anti-Santa]] (formerly, no longer present after the [[2019 Christmas event]])\n\n==Historical==\nOriginally, on getting to the island, the player was only able to speak cat and required an \"Amulet of Humanspeak\" from a chest in order to talk to the slaves. While this was an example of [[Jagex]] humour based on the [[Amulet of Catspeak]], it wasn't obvious to players who knew nothing about cats. Now, if you ask Evil Bob how he can talk to you, he'll tell you that he's wearing an Amulet of Humanspeak, quite contrary to what it was like then, where you had to get it. This also adds in another piece of Jagex humour on the Amulet of Catspeak in ''RuneScape''. In ScapeRune, Evil Bob is wearing an amulet to talk to humans, attempting to teach the human slaves catspeak, while in ''RuneScape'', humans use the Amulet of Catspeak to talk to cats.\n\nOriginally, four fish were required to leave, later two, now only one.\n\n== Trivia ==\n* A [[Duke of Bridgelum|scrapped random event]] involving ScapeRune had players visit an alternative version of Lumbridge named \"Bridgelum\" where they would help the Duke reassemble dead cows from a pile of dismembered parts. It was removed due to being too grotesque. \n\n{{Realms}}\n{{Random events}}", "downloadedAt": "2025-09-05T09:50:04.701Z", "source": "osrs_wiki_bulk_download", "rawWikitext": true}, "contentHash": "c414d1ffbb41ef6b", "reason": "page_updated"}