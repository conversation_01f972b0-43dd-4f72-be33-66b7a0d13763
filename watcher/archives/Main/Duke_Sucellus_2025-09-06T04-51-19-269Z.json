{"title": "<PERSON>", "namespace": "Main", "archivedAt": "2025-09-06T04:51:19.269Z", "originalContent": {"title": "<PERSON>", "namespace": "Main", "content": "{{redirect|Duke|other uses|<PERSON> (disambiguation)}}\n{{External|rs}}\n{{Instance}}\n{{HasStrategy}}\n{{Multi Infobox\n|text1 = Post-quest\n|item1 = \n{{Infobox Monster\n|version1 = Awake\n|version2 = Asleep\n|version3 = Defeated\n|smwname1 = Post-quest, Awake\n|smwname2 = Post-quest, Asleep\n|smwname3 = Post-quest, Defeated\n|name = <PERSON>\n|image1 = [[File:<PERSON>.png|280px]]\n|image2 = [[File:<PERSON> (asleep).png|280px]]\n|image3 = [[File:<PERSON> (defeated).png|280px]]\n|release = [[26 July]] [[2023]]\n|update = Desert Treasure II - The Fallen Empire\n|members = Yes\n|combat = 758\n|size = 7\n|max hit = 56 ([[<PERSON><PERSON>]]), 48 ([[Magic]]), 101 (Gaze)\n|aggressive = Yes \n|poisonous = No\n|attack style = [[Magic]], [[Crush]]\n|attack speed = 5\n|slayxp = 310\n|cat = Bosses\n|assignedby = konar,nieve,duradel\n|hitpoints = 485\n|respawn = 20\n|att = 300\n|str = 345\n|def = 275\n|mage = 310\n|range = 0\n|attbns = 200\n|strbns = 38\n|amagic = 150\n|mbns = 32\n|arange = 0\n|rngbns = 0\n|dstab = 255\n|dslash = 45\n|dcrush = 190\n|dmagic = 440\n|dlight = 320\n|dstandard = 320\n|dheavy = 320\n|immunepoison = No\n|immunevenom = Poisons instead\n|immunecannon = Yes\n|immunethrall = No\n|examine1 = That's a lot of eyes...\n|examine2 = He seems to be asleep.\n|examine3 = Is he dead?\n|attributes = Demon\n|xpbonus = 52.5\n|id1 = 12191\n|id2 = 12166,12167\n|id3 = 12192,12196 <!-- Probably just 12192 but needs verification-->\n}}\n|text2 = Awakened\n|item2 = \n{{Infobox Monster\n|version1 = Awake\n|version2 = Asleep\n|version3 = Defeated\n|smwname1 = Awakened, Awake\n|smwname2 = Awakened, Asleep\n|smwname3 = Awakened, Defeated\n|name = Duke Sucellus\n|image1 = [[File:Duke Sucellus.png|280px]]\n|image2 = [[File:Duke Sucellus (asleep).png|280px]]\n|image3 = [[File:Duke Sucellus (defeated).png|280px]]\n|release = [[26 July]] [[2023]]\n|update = Desert Treasure II - The Fallen Empire\n|members = Yes\n|combat = 1099\n|slayxp = 1540\n|cat = Bosses\n|assignedby = konar,nieve,duradel\n|size = 7\n|max hit = 81 ([[Melee]]), 71 ([[Magic]]), 121 (Gaze)\n|aggressive = Yes \n|poisonous = No\n|attack style = [[Magic]], [[Crush]]\n|attack speed = 5\n|hitpoints = 1540\n|respawn = 20\n|att = 435\n|str = 500\n|def = 316\n|mage = 465\n|range = 0\n|attbns = 200\n|strbns = 38\n|amagic = 150\n|mbns = 32\n|arange = 0\n|rngbns = 0\n|dstab = 255\n|dslash = 65\n|dcrush = 190\n|dmagic = 440\n|dlight = 320\n|dstandard = 320\n|dheavy = 320\n|immunepoison = No\n|immunevenom = Poisons instead\n|immunecannon = Yes\n|examine1 = That's a lot of eyes...\n|examine2 = He seems to be asleep.\n|examine3 = Is he dead?\n|attributes = Demon\n|xpbonus = 52.5\n|id1 = 12191\n|id2 = 12166,12167\n|id3 = 12192,12196 <!-- Probably just 12192 but needs verification-->\n}}\n|text3 = Quest\n|item3 =\n{{Infobox Monster\n|version1 = Awake\n|version2 = Asleep\n|version3 = Defeated\n|smwname1 = Quest, Awake\n|smwname2 = Quest, Asleep\n|smwname3 = Quest, Defeated\n|name = Duke Sucellus\n|image1 = [[File:Duke Sucellus.png|280px]]\n|image2 = [[File:Duke Sucellus (asleep).png|280px]]\n|image3 = [[File:Duke Sucellus (defeated).png|280px]]\n|release = [[26 July]] [[2023]]\n|update = Desert Treasure II - The Fallen Empire\n|members = Yes\n|combat = 538\n|slayxp = 360\n|cat = Bosses\n|assignedby = konar,nieve,duradel\n|size = 7\n|max hit = 36 ([[Melee]]), 28 ([[Magic]]), 99 (Gaze)\n|aggressive = Yes\n|poisonous = No\n|attack style = [[Magic]], [[Crush]]\n|attack speed = 6\n|hitpoints = 330\n|respawn = 20\n|att = 250\n|str = 280\n|def = 215\n|mage = 230\n|range = 0\n|attbns = 200\n|strbns = 16\n|amagic = 150\n|mbns = 10\n|arange = 0\n|rngbns = 0\n|dstab = 210\n|dslash = 30\n|dcrush = 160\n|dmagic = 400\n|dlight = 300\n|dstandard = 300\n|dheavy = 300\n|immunepoison = No\n|immunevenom = Poisons instead\n|immunecannon = Yes\n|immunethrall = No\n|examine1 = That's a lot of eyes...\n|examine2 = He seems to be asleep.\n|examine3 = Is he dead?\n|attributes = Demon\n|xpbonus = 45\n|id1 = 12195\n|id2 = 12193,12194\n|id3 = 12196 <!--Most likely this rather than 12192-->\n}}\n}}\n'''Duke Sucellus''' (pronounced {{IPAc-en|s|uː|ˈ|k|ɛ|l|ə|s}} {{respell|soo-KELL-əs}}) is a chthonian demon sealed in [[Ghorrock Prison]], who is encountered during [[Desert Treasure II - The Fallen Empire]]. A high-ranking commander of the [[Zarosian Empire]], Sucellus took informal command of the remaining forces of the Empire after the [[Betrayal of Zaros]]. A stronger variant of him can be fought again after the completion of the quest.\n==Location==\n{{LocTableHead|league=yes}}\n{{LocLine\n|name = Duke Sucellus\n|location = [[Ghorrock Prison]] - Asylum\n|levels = 538, 758, 1,099\n|members = Yes\n|mapID = -1\n|plane = 0\n|3039,6454\n|mtype = pin\n|leagueRegion = Fremennik\n}}\n{{LocTableBottom}}\n\n[[Ghorrock Prison]] is accessible from [[Ghorrock Dungeon]], with the dungeon itself being accessible from [[Weiss]]'s [[Salt Mine]]. Players do not have to traverse the entirety of Ghorrock Prison after the completion of the quest, as they will instead directly reach the asylum when going through the main prison entrance.\n\nA [[Scoreboard (Duke Sucellus)|scoreboard]] by the gates leading to the asylum show statistics related to Duke Sucellus.\n\n==History==\n[[File:Statue (Duke Sucellus).png|thumb|left|180px|The statue of Duke Sucellus in the Ancient Vault.]]\nDuke Sucellus was the leader of [[Zaros]]' Sixth Legion, one of twelve demonic legions from [[Infernus]] sent to [[Gielinor]] at the beginning of the [[Second Age]], and held the rank of Legatus within the [[Zarosian Empire]], which bestowed command of a whole legion. He was also one of four individuals entrusted with a medallion - the means to open the [[Ancient Vault]] where the [[Elder Horn]] was sealed - alongside [[Perseriya]], [[Vardorvis]], and [[the Whisperer]], all of whom were high-ranking personnel in the Empire.\n\nAt some point during the [[Third Age]], after the [[Betrayal of Zaros]], Sucellus was sent to [[Ghorrock]], where he became Acting Legatus Maximus (the rank previously held by [[Zamorak]]) and took informal command over what little forces of the [[Zarosian Empire]] remained. During his time in Ghorrock, he took in many refugees from the Empire who managed to make their way there. As the [[God Wars]] dragged on, the fortress was close to running out of room,{{CiteText|text=Refugees note|quote=Another hundred refugees arrived today. This war has already lasted multiple lifetimes for the lesser races, and there is no end in sight. I ask once again that we consider our capacity here. I do not wish to turn away those who are still faithful, but we are running out of room.}} with some humans having been born there and never leaving the fortress.{{CiteText|text=Request note|quote=I understand the need for security, but some of the humans have spent their entire lives in this place. Giving them a glimpse of the outside could be a much needed morale boost for everyone.}}\n\nBy the end of the [[God Wars]], Sucellus moved to the prison asylum and began ordering preparations to return to the surface.{{CiteText|text=Prisoner's letter|quote=Something isn't right. The war is over and the gods are gone. We should be leaving this place, but instead we remain. Apparently, the Duke requires us to make preparations before we return to the surface, but there is something we are not being told.}} However, suspicions were raised after more and more people who were taken to the asylum never returned.{{CiteText|text=Prisoner's letter|quote=The Duke hasn't left the asylum in days, and every day, more people are taken down there... never to return. I feel something terrible is fast approaching. We must leave before it does.}} In reality, Sucellus believed that he was the one destined to restore the Empire to its former glory, and ate everyone who was brought to the asylum in order to absorb their essence and gain more power.{{CiteText|text=Warning letter|quote=If you're reading this, you've no doubt already seen the horrifying truth for yourself. The Duke thinks he's destined to restore the empire, at the small cost of all our lives!}}{{CiteText|text=Evacuation note|quote=He ate them... He ate them all!}} This would seem to be the explanation for the tremendous difference in appearance between statues of the Duke seen in Ghorrock's Prison and the Ancient Vault, and Sucellus' current form.\n\nRealising the threat Sucellus posed, [[Jhallan]] and [[Mizzarch]] began to make preparations to seal the prison, handing out evacuation orders to leave within the hour.{{CiteText|text=Evacuation note|quote=Ghorrock is forsaken and must be evacuated at once. Round up all who are left and tell them they have an hour to leave. Jhallan and Mizzarch are preparing to have this place sealed.}} The two [[Mahjarrat]] would each take one half of the Ghorrock Crest used to seal the prison, though it is likely Mizzarch's crest came into [[Hazeel]]'s possession after his sacrifice at one of their rituals, where-after it fell into the hands of the [[Carnillean family]], though it is unlikely they were ever aware of its significance.{{CiteNPC|npc=Hazeel|quote=Half of the crest used to do so came into my possession, but it was seemingly taken by the Carnilleans after my defeat.|loc=Secrets of the North}} The prison was successfully sealed shut, leaving anyone unlucky enough to have not evacuated to die cold, abandoned deaths, or else be eaten by their despotic host. Sucellus remained in the asylum and, eventually running out of food, fell into a very long, deep slumber.\n\nDuring the [[Fifth Age]], the prison would be unsealed by an [[assassin]] later revealed to be [[Sliske]]. [[player character|An adventurer]] who sought his medallion ventured into the prison, discovering the slumbering Sucellus. After the adventurer woke him up with poison, Sucellus attempted to consume their essence, but was swiftly subdued. He fell back into slumber, while the adventurer discovered his medallion in a locked chest that required a [[Odd key|key]] he had on him.\n\n==Fight overview==\n{{Main|Duke Sucellus/Strategies}}\nUnlike the other members of [[the Forgotten Four]], Duke Sucellus is not available to fight immediately; rather, he is in a deep slumber and must be weakened with mushrooms or [[Arder-musca poison]] before he is forcefully woken up to fight. During this preparation phase, the player must gather materials from the hallways to his side, avoiding both his ice magic and [[extremity|extremities]], which will bind the player, dealing damage, draining prayer and binding them if caught in one. Vents in the middle of the arena will expel poisonous gas in a predictable manner, which deals damage in a 3x3 from the vent and can be reduced with face protection.\n\nAfter Sucellus' meter is full, he will awaken and initiate combat with the player. While Sucellus is considered [[Demon (attribute)|demonic]], he has 30% demonbane resistance. This means an [[emberlight]] would only have 49% increased damage and accuracy against him rather than 70%.\n\n[[File:Fighting Duke Sucellus.png|left|thumb|A player fighting Duke Sucellus.]]\nWhen in melee range (including diagonally across) and a few other select tiles, Sucellus will raise his arms, causing icicles to spike up from the ground and follow up with a slam attack. The slam attack is significantly more powerful than the icicles and will deal heavy damage. [[Protect from Melee]] reduces the icicles' damage by 50% and the slam by ~26%. If the player is outside of melee range and not on those other select tiles, Sucellus will spit a magical cluster of energy that also deals high damage (but less than the slam) even through [[Protect from Magic]].\n\nEvery five auto-attacks, Sucellus will gaze at the player, revealing the massive eye in his mouth. The player must hide behind a pillar to avoid his gaze; failing to do so will result in extreme amounts of damage being taken, often enough to kill them in a single hit. After the first sequence of attacks, Sucellus will begin to spit gas from his mouth at the vent closest to the player. He will do this once every five auto-attacks above 75% of his hitpoints, decreasing to four auto-attacks between 26-74% of his hitpoints.\n\nUpon reaching 25% of his health, Sucellus will enter an enrage phase, increasing his attack speed from 5 (3.0 seconds) to 4 (2.4 seconds) and the gas flare cooldown decreases to three auto-attacks. The rest of his mechanics remain unaffected.\n\nAfter the Duke is defeated, he remains 'dead' for a few seconds and his drops appear in front of him, before beginning to sleep again.\n{{clear|left}}\n\n==Money making==\n{{Mmgsection|Killing Duke Sucellus}}\n\n==Drops==\n{{DropLogProject|kills=318722}}\n\n===Average value===\nDrop values are listed by whether you include the [[Magus ring|Magus Ring]] and [[Soulreaper Axe]] components, which must be completed to be sold, as well as if your kill was credited as perfect.{{BossValue/Duke Sucellus}}\n\n===Mechanics===\nWhen Duke Sucellus is defeated, a drop will be rolled for until the roll results in a success, as described below:{{CiteNews|url=https://secure.runescape.com/m=news/desert-treasure-ii---tweaks-and-drop-rates?oldschool=1|title=Desert Treasure II - Tweaks and Drop Rates|newsdate=3 August 2023}}\n* Roll 1/90 for the tradeable unique drop table (1/30 for awakened variant).\n** If unsuccessful, roll 1/48 for the [[awakener's orb]].\n** If unsuccessful, roll 1/25 for [[frozen tablet]].\n** If unsuccessful, roll 1/200 for [[ice quartz]].\n** If unsuccessful, roll 1/5 for the supply drop table.\n* If all rolls above were unsuccessful, the standard drop table is rolled.\n* Once the player rolls the entire drop table, roll 1/2,500 for [[Baron]] and 1/40 for a clue scroll.\n\nIn addition, defeating the boss perfectly will earn 50% more loot from the standard drop table.{{CiteReddit|author=Mod Ayiza|url=https://www.reddit.com/r/2007scape/comments/15gzjui/desert_treasure_ii_tweaks_and_drop_rates/julpslb/|date=3 August 2023|archiveurl=https://archive.li/lVplt|archivedate=3 August 2023|quote=You have to do the fight while avoiding any avoidable damage. Essentially a perfect fight (chip damage does not count).|title=Desert Treasure II - Tweaks and Drop Rates}} This requires the player to kill Duke Sucellus without taking any avoidable damage, which includes:\n* Melee attacks from his slam attack (the chip damage taken from the rising icicles are considered unavoidable)\n* Standard magic attack (praying against them will still fail the achievement, even when hit for a 0)\n* Damage from the Freezing Gaze attack\n* Damage from ignited gas vents in both the preparation and battle phases\n* Damage from ice fall and [[Extremity|extremities]]\n* In the awakened fight, avoid taking poison damage and being hit by a shadow orb\n\n===Uniques===\n{{Listen\n|title = Unique drop [[sound effect]]\n|align = right\n|filename = Unique drop sound effect.ogg\n|desc = The sound that plays when receiving a unique drop.\n}}\n''There is a '''1/90''' chance of rolling the unique drop table per kill. After hitting the table, there is a 1/8 chance to get a piece of the [[Virtus robes]] (then a separate 1/3 roll to determine which piece), a 3/8 chance to get a single vestige roll (each roll will drop a gold ring until the final roll which guarantees the vestige), a 3/8 chance for a [[chromium ingot]], and 1/8 chance for the [[Eye of the duke]]. Below lists the effective drop rates.''\n\n{{DropsTableHead}}\n{{DropsLine|name=Magus vestige|quantity=1|rarity=1/720|raritynotes=<ref group=d>To receive the magus vestige, the player must roll an invisible 1/240 drop on the tradeable unique table. The first roll will cause a [[gold ring]] to appear and increment a hidden variable, while the second drops two gold rings; when the third such roll occurs, the vestige is dropped and the variable is reset.</ref>}}\n{{DropsLine|name=Chromium ingot|quantity=1|rarity=3/720}}\n{{DropsLine|name=Eye of the duke|quantity=1|rarity=1/720}}\n{{DropsLine|name=Virtus mask|quantity=1|rarity=1/2160}}\n{{DropsLine|name=Virtus robe top|quantity=1|rarity=1/2160}}\n{{DropsLine|name=Virtus robe bottom|quantity=1|rarity=1/2160}}\n{{DropsTableBottom}}\n{{reflist|group=d}}\n\n===Secondary uniques===\n''The secondary unique table is rolled if the unique drop table roll fails. This table is then rolled in the following sequence: [[awakener's orb]], [[frozen tablet]], [[ice quartz]]. These items will replace the normal loot rolls.''\n\n{{DropsTableHead}}\n{{DropsLine|name=Frozen tablet|quantity=1|rarity=1/25.8|raritynotes=<ref name=\"secondary_uniques\" group=\"d\">The frozen tablet will become more common at an unknown rate as kill count increases until the first drop is received. Afterwards, the item will no longer be dropped.</ref>}}\n{{DropsLine|name=Awakener's orb|quantity=1|rarity=1/48.5}}\n{{DropsLine|name=Ice quartz|quantity=1|rarity=1/206.6|raritynotes={{refn|group=d|If the player has not yet received their first ice quartz, its drop rate scales from 1/200 to 1/50 as the player's kill count increases, where the best rate is reached at 300 kill count. Once players obtain their first ice quartz, the drop rate is reverted back to 1/200.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1707308397186465945|date=28 September 2023|archiveurl=https://archive.ph/GHIBh{{^|https://archive.ph/vYnpZ}}|archivedate=28 September 2023|quote=If I'm reading my colleagues' code correctly, over your first 300 kills, the chance shifts from 1/200 to 1/50, where it stays thereafter.}}}}}}\n{{DropsTableBottom}}\n{{reflist|group=d}}\n\n===Supplies===\n''These supplies are all dropped together.''\n{{DropsTableHead}}\n{{DropsLine|name=Pineapple pizza|quantity=3-4|rarity=1/5.4}}\n{{DropsLine|name=Prayer potion(3)|quantity=1|rarity=1/5.4}}\n{{DropsLine|name=Super combat potion(2)|quantity=1|rarity=1/5.4}}\n{{DropsTableBottom}}\n\n===Weapons and armour===\n{{DropsTableHead}}\n{{DropsLine|name=Bronze chainbody|quantity=11,17 (noted)|rarity=1/100}}\n{{DropsLine|name=Mithril chainbody|quantity=5,7 (noted)|rarity=1/100}}\n{{DropsLine|name=Adamant chainbody|quantity=6,10 (noted)|rarity=1/100}}\n{{DropsLine|name=Dragon platelegs|quantity=5,7|rarity=1/100}}\n{{DropsTableBottom}}\n\n===Resources===\n{{DropsTableHead}}\n{{DropsLine|name=Pure essence|quantity=200,300 (noted)|rarity=1/100}}\n{{DropsLine|name=Iron ore|quantity=63,95 (noted)|rarity=1/100}}\n{{DropsLine|name=Coal|quantity=216,325 (noted)|rarity=8/100}}\n{{DropsLine|name=Mithril ore|quantity=33,50 (noted)|rarity=1/100}}\n{{DropsLine|name=Adamantite ore|quantity=75,112 (noted)|rarity=8/100}}\n{{DropsLine|name=Runite ore|quantity=30,45 (noted)|rarity=2/100}}\n\n{{DropsLine|name=Sapphire|quantity=28,42 (noted)|rarity=1/100}}\n{{DropsLine|name=Emerald|quantity=28,42 (noted)|rarity=1/100}}\n{{DropsLine|name=Ruby|quantity=28,42 (noted)|rarity=1/100}}\n{{DropsLine|name=Uncut ruby|quantity=41,62 (noted)|rarity=5/100}}\n{{DropsLine|name=Uncut diamond|quantity=41,62 (noted)|rarity=5/100}}\n\n{{DropsLine|name=Bronze bar|quantity=63,95 (noted)|rarity=1/100}}\n{{DropsLine|name=Dragon arrowtips|quantity=166,250|rarity=2/100}}\n{{DropsLine|name=Rune javelin heads|quantity=60,90|rarity=8/100}}\n{{DropsLine|name=Dragon javelin heads|quantity=60,90|rarity=8/100}}\n{{DropsLine|name=Raw sea turtle|quantity=200,300 (noted)|rarity=1/100}}\n{{DropsTableBottom}}\n\n===Runes and ammunition===\n{{DropsTableHead}}\n{{DropsLine|name=Air rune|quantity=200,300|rarity=1/100}}\n{{DropsLine|name=Mist rune|quantity=100,150|rarity=8/100}}\n{{DropsLine|name=Chaos rune|quantity=100,150|rarity=9/100}}\n{{DropsLine|name=Soul rune|quantity=666,1000|rarity=2/100}}\n{{DropsTableBottom}}\n\n===Tertiary===\n{{DropsTableHead}}\n{{DropsLine|name=Odd key|rarity=Always|quantity=1|raritynotes=<ref group=d>Only dropped by the quest variant.</ref>}}\n{{DropsLine|name=Brimstone key|quantity=1|rarity={{Brimstone rarity|758}}|raritynotes=<ref group=\"d\">Brimstone keys are only dropped while on a [[Slayer task]] given by [[Konar quo Maten]].</ref>|gemw=No|leagueRegion=Fremennik&Kourend}}\n{{DropsLineClue|type=easy|rarity=1/160}}\n{{DropsLineClue|type=medium|rarity=1/160}}\n{{DropsLineClue|type=hard|rarity=1/160}}\n{{DropsLineClue|type=elite|rarity=1/160}}\n{{DropsLine|name=Baron|quantity=1|rarity=1/2500|gemw=No}}\n{{DropsLine|name=Ancient blood ornament kit|rarity=Always|quantity=1|gemw=no|raritynotes=<ref group=d>Only when defeated in the awakened encounter as the '''last''' of the four.</ref>}}\n{{DropsTableBottom}}\n\n==Combat Achievements==\n{{Sync}}\n{{Combat Achievements list|Duke Sucellus}}\n\n==Gallery==\n<gallery mode=\"packed\" heights=\"180\" style=\"text-align:left\">\nDuke Sucellus concept art.png|Concept of Duke Sucellus, by [[Mod Jerv]].\nGhorrock Prison asylum concept art.png|Concept art of the asylum, by Mod Jerv.\nDuke Sucellus concept art (early).png|An early concept of Duke Sucellus, by Mod Jerv. The figure growing out of Sucellus' forehead that resembles a mahjarrat suggests that at one point Mizzarch or Jhallan was supposed to have been eaten by the Duke, and even be incorporated into the fight.\n</gallery>\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 20 August 2025\n|update = More Doom Tweaks, Poll 84, & Summer Sweep Up Changes\n|change = Duke Sucellus no longer remains poisoned from one fight to the next.\n}}\n{{Subject changes\n|date = 6 August 2025\n|update = Varlamore & Summer Sweep-Up Combat Tweaks\n|change = Duke mushrooms and salts now stack properly if your inventory is full and you have one held.\n}}\n{{Subject changes\n|date = 2 July 2025\n|update = Stackable Clues are here!\n|change = Repeatedly clicking \"Feed\" will no longer feed Duke more than two poisons.\n}}\n{{Subject changes\n|date = 25 June 2025\n|update = Summer Sweep Up: Combat\n|change = * Duke Sucellus' health has been increased by 45.\n* The poison-brewing phase of the encounter has been reworked:\n** Mushrooms and poisons will no longer be removed from the player's inventory after each kill.\n** Players can now pick up to eight [[arder mushroom]]s and [[musca mushroom]]s, after which the object itself will despawn, effectively allowing players to prepare for 2-4 kills in advance depending on their Herblore level.\n** To prevent lockouts, mushrooms can no longer be fed directly.\n** Duke Sucellus' has been given a \"Feed\" option when he is asleep.\n* The kill time for Duke Sucellus now start its timer upon him waking up, rather than when the player first enters the asylum or upon him \"respawning\". As such, the required kill times for all his speed-based [[Combat Achievements]] have been reduced by 45 seconds.\n* Progress towards the magus vestige is now indicated with a [[gold ring]] drop. For players who were in progress before this update, their next kill will guarantee one or two gold rings, indicating 1/3 and 2/3 vestige rolls respectively.\n}}\n{{Subject changes\n|date = 29 May 2024\n|update = Project Rebalance: Combat Changes\n|change = The [[slash]] defence of the post-quest version of Duke Sucellus has been decreased from 65 to 45.\n}}\n{{Subject changes\n|date = 31 January 2024\n|update = Scurrius & DT2 Combat Achievements\n|change = [[Combat Achievements]] were added for the boss.\n}}\n{{Subject changes\n|date = 16 August 2023\n|update = Desert Treasure II Changes & More\n|change = * Duke Sucellus' Freezing Gaze overlay will now disappear upon leaving the asylum.\n* The rendering of Duke Sucellus' vent and acid walk projectile have been updated to be more clear.\n* Personal best kill times for Duke Sucellus will now broadcast in [[clan chat]] and [[group ironman]] channels.\n}}\n{{Subject changes\n|update = Desert Treasure II - Further Changes\n|date = 9 August 2023\n|change = The vents during the preparation phase will now erupt gas in a set pattern.\n}}\n{{Subject changes\n|date = 3 August 2023\n|update = Desert Treasure II - Tweaks and Drop Rates\n|change = * The combat level for the quest variant was increased from 519 to 538.\n* The drop rate for the tradeable unique drop table was increased from 1/108 to 1/90.\n* Pathing to salt deposits within the asylum has been improved.\n* Vents within the asylum will now correctly despawn when the encounter ends.\n* An issue that could prevent the Duke Sucellus encounter from starting has been resolved.\n}}\n{{Subject changes footer}}\n\n==Trivia==\n* {{wp|Sucellus}} is the name of a Gallo-Roman god associated with agriculture and wine.\n* The design of Duke Sucellus appears to be heavily inspired by ''Runescape 3''<nowiki>'s</nowiki> [[RSW:Senecianus|Senecianus]], which both bear strong resemblances to the shoggoths of H.P. Lovecraft's Cthulhu mythos.\n* In ''RuneScape'', Sucellus was also a Zarosian duke, though his role was far different - following the destruction of the Zarosian Empire, he devised a long-term plan to steal Saradomin's crown by inserting the Temple Knights as a Saradominist organization. He also died of unknown means.\n* The room Sucellus is fought in greatly resembles a room found in the ''RuneScape'' quest \"Twilight of the Gods\",  inside the [[Senntisten]] Asylum.\n* Originally, the fight had four types of mushrooms available to be picked from - [[arder mushroom|arder]], [[musca mushroom|musca]], [[resper mushroom|resper]], and [[holos mushrooms]] - which could be used on the [[Fermentation Vat]] to create six combinations of poisons to weaken Duke Sucellus. Presumably, all but one were scrapped to make the fight simpler.\n\n==References==\n{{Reflist}}\n\n{{The Forgotten Four}}\n{{Desert Treasure II - The Fallen Empire}}\n{{Bosses}}\n{{Demons}}\n\n[[Category:Zarosians]]", "downloadedAt": "2025-09-05T09:50:16.267Z", "source": "osrs_wiki_bulk_download", "rawWikitext": true}, "contentHash": "00fea1adb7d4581d", "reason": "page_updated"}