{"title": "<PERSON> of <PERSON><PERSON><PERSON><PERSON><PERSON> (c)", "namespace": "Main", "archivedAt": "2025-09-06T04:51:20.520Z", "originalContent": {"title": "<PERSON> of <PERSON><PERSON><PERSON><PERSON><PERSON> (c)", "namespace": "Main", "content": "{{Otheruses|def=no|other variants of the bow|Bow of faerdhinen (disambiguation)}}\n{{Infobox Item\n|name = Bow of faerdhinen (c)\n|image = [[File:Bow of faerdhinen (c).png]]\n|release = [[30 June]] [[2021]]\n|update = <PERSON><PERSON><PERSON>'s Nightmare\n|aka = <PERSON><PERSON>, <PERSON><PERSON>\n|members = Yes\n|quest = No\n|tradeable = No\n|placeholder = Yes\n|equipable = Yes\n|stackable = No\n|noteable = No\n|options = Wield, Uncharge, Drop\n|examine = A magical elven bow, corrupted through the addition of too many shards.\n|value = 5000000\n|alchable = No\n|weight = 1.500\n|id = 25867\n|leagueRegion = Tirannwn\n}}\n[[File:Bow of faerdhinen (c) detail.png|left|140px]]\nThe '''bow of Faerdhinen (c)''' is a corrupted version of the [[Bow of Faerdhinen]] that does not [[Degradation|degrade]], with its appearance altered to resemble the corrupted crystal seen within the [[Corrupted Gauntlet]]. The bow's colours may be changed to other colours of the [[Elven Clans]] by buying crystals from [[<PERSON><PERSON><PERSON>'s Wares]] in [[<PERSON><PERSON><PERSON><PERSON><PERSON>]], which cost 500,000 coins each. It can also be reverted to its original colour by using the [[crystal of Hefin]] on any variant of the corrupted bow.\n\nThe bow can be upgraded by bringing it and 2,000 [[crystal shard]]s to a [[singing bowl]]. Doing so requires level 82 [[Smithing]] and [[Crafting]] (the levels can be [[Temporary skill boost|boosted]]), and grants no experience. If the player does not meet the Smithing and Crafting requirements, they can have [[Conwenna]] or [[Reese]] sing the crystal in their stead for an extra 1,000 crystal shards. Excess charges in the bow will be taken into account; for example, a newly created bow with 10,000 charges will require 1,900 crystal shards to corrupt and a fully charged bow will only cost 1,800 crystal shards to upgrade. \n\nAlternatively, the [[Bow of Faerdhinen]] can now be corrupted passively through use: each time the bow is recharged with [[crystal shard]]s, the amount contributes toward the 1,900-shard corruption requirement. Once this cumulative amount is reached, the bow becomes corrupted automatically without needing a manual upgrade at the singing bowl.\n\nUpon creating the corrupted bow, the chatbox message states \"''With the help of the crystal bowl, you sing a dark song which corrupts the crystals.''\"\n\nWhile untradeable, the bow can be uncharged, but the crystal shards used to upgrade it will '''not''' be returned.\n\nLike its regular variant, the bow requires level 80 [[Ranged]] and 70 [[Agility]] to wield, and provides the highest Ranged Attack and Ranged Strength bonus out of any weapon for its [[attack speed]]. It also benefits from damage and accuracy boosts when [[crystal armour]] is equipped. Each [[crystal armour]] piece equipped grants additional bonus accuracy and damage, adding up to a total 15% damage and 30% accuracy bonus when the full set is equipped.\n\nWhen using crystal armour, the bow provides similar damage per second to the [[twisted bow]] and [[toxic blowpipe]]; however, these weapons excel against targets with high Magic level and low Defence respectively. Unlike the twisted bow or toxic blowpipe, the corrupted bow of Faerdhinen suffers no significant drawback and provides consistent performance due to its high accuracy and damage per shot, along with its moderate attack speed, long range, and unlimited ammunition.\n\n==Combat stats==\n{{Infobox Bonuses\n|astab = 0\n|aslash = 0\n|acrush = 0\n|amagic = 0\n|arange = +128\n|dstab = 0\n|dslash = 0\n|dcrush = 0\n|dmagic = 0\n|drange = 0\n|str = 0\n|rstr = +106\n|mdmg = 0\n|prayer = 0\n|slot = 2h\n|speed = 5\n|attackrange = 10\n|combatstyle = Bow\n|image = [[File:Bow of faerdhinen (c) equipped male.png|180px]]\n|altimage = [[File:Bow of faerdhinen (c) equipped female.png|180px]]\n}}\n\n{{CombatStyles|Bow|speed=5|attackrange=10}}\n\n==Creation==\n{{#vardefine:crystalshardvalue|{{#ifexpr:{{#vardefineecho:seedvalue|{{GEP|Enhanced crystal teleport seed}} / 150}} > {{#vardefineecho:divinesupercombatvalue|({{GEP|Divine super combat potion(4)}} - {{GEP|Super combat potion(4)}}) / 0.4}} | {{#var:seedvalue}} | {{#var:divinesupercombatvalue}}}}}}\n\n===Upgrade===\n{{Recipe\n|skill1 = Smithing\n|skill1lvl = 82\n|skill1boostable = Yes\n|skill1exp = 0\n|skill2 = Crafting\n|skill2lvl = 82\n|skill2boostable = Yes\n|skill2exp = 0\n|members = Yes\n|ticks = 3\n|ticksnote = Takes 3 ticks to create an entire inventory\n|facilities = Singing bowl\n|mat1 = Bow of faerdhinen\n|mat1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|mat2 = Crystal shard\n|mat2quantity = 2000\n|mat2cost = {{#var:crystalshardvalue}}\n|mat2costnote = Value based on {{#ifexpr:{{#var:seedvalue}} > {{#var:divinesupercombatvalue}} | the cost of obtaining by exchanging [[enhanced crystal teleport seed]]s with [[Amrod]] | the potential profit of using the crystal shards to create [[divine super combat potion]]s}}\n|mat2quantitynote = Quantity is the cost for a fully inactive bow. Excess charges in the bow will reduce the cost to corrupt by the number of shards required to give that many charges at a rate of 1 shard per 100 charges.\n|output1 = Bow of faerdhinen (c)\n|output1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|output1costnote = Value based on the cost for a fully inactive bow\n|output1subtxt = Upgrade\n}}\n\n===Full process===\n{{Recipe\n|skill1 = Smithing\n|skill1lvl = 82\n|skill1boostable = Yes\n|skill1exp = 5,000\n|skill2 = Crafting\n|skill2lvl = 82\n|skill2boostable = Yes\n|skill2exp = 5,000\n|members = Yes\n|ticks = 5\n|ticksnote = Takes 5 ticks to create an entire inventory, and 3 ticks to corrupt an entire inventory\n|facilities = Singing bowl\n|mat1 = Enhanced crystal weapon seed\n|mat2 = Crystal shard\n|mat2quantity = 2000\n|mat2cost = {{#var:crystalshardvalue}}\n|mat2costnote = Value based on {{#ifexpr:{{#var:seedvalue}} > {{#var:divinesupercombatvalue}} | the cost of obtaining by exchanging [[enhanced crystal teleport seed]]s with [[Amrod]] | the potential profit of using the crystal shards to create [[divine super combat potion]]s}}\n|output1 = Bow of faerdhinen (c)\n|output1cost = {{GEP|Enhanced crystal weapon seed}}\n|output1costnote = Value based on the cost for an enhanced crystal weapon seed\n|output1subtxt = Full process\n}}\n\n===Recolour===\n{{Tabber\n|tab1 = Amlodd\n|tab1content = {{Recipe\n|members = Yes\n|ticks = 0\n|mat1 = Bow of faerdhinen (c) (Amlodd)\n|mat1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|mat2 = Crystal of hefin\n|mat2cost = 500,000\n|output1 = Bow of faerdhinen (c)\n|output1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|output1subtxt = Recolour (Amlodd)\n}}\n|tab2 = Cadarn\n|tab2content = {{Recipe\n|members = Yes\n|ticks = 0\n|mat1 = Bow of faerdhinen (c) (Cadarn)\n|mat1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|mat2 = Crystal of hefin\n|mat2cost = 500,000\n|output1 = Bow of faerdhinen (c)\n|output1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|output1subtxt = Recolour (Cadarn)\n}}\n|tab3 = Crwys\n|tab3content = {{Recipe\n|members = Yes\n|ticks = 0\n|mat1 = Bow of faerdhinen (c) (Crwys)\n|mat1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|mat2 = Crystal of hefin\n|mat2cost = 500,000\n|output1 = Bow of faerdhinen (c)\n|output1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|output1subtxt = Recolour (Crwys)\n}}\n|tab4 = Iorwerth\n|tab4content = {{Recipe\n|members = Yes\n|ticks = 0\n|mat1 = Bow of faerdhinen (c) (Iorwerth)\n|mat1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|mat2 = Crystal of hefin\n|mat2cost = 500,000\n|output1 = Bow of faerdhinen (c)\n|output1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|output1subtxt = Recolour (Iorwerth)\n}}\n|tab5 = Ithell\n|tab5content = {{Recipe\n|members = Yes\n|ticks = 0\n|mat1 = Bow of faerdhinen (c) (Ithell)\n|mat1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|mat2 = Crystal of hefin\n|mat2cost = 500,000\n|output1 = Bow of faerdhinen (c)\n|output1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|output1subtxt = Recolour (Ithell)\n}}\n|tab6 = Meilyr\n|tab6content = {{Recipe\n|members = Yes\n|ticks = 0\n|mat1 = Bow of faerdhinen (c) (Meilyr)\n|mat1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|mat2 = Crystal of hefin\n|mat2cost = 500,000\n|output1 = Bow of faerdhinen (c)\n|output1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|output1subtxt = Recolour (Meilyr)\n}}\n|tab7 = Trahaearn\n|tab7content = {{Recipe\n|members = Yes\n|ticks = 0\n|mat1 = Bow of faerdhinen (c) (Trahaearn)\n|mat1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|mat2 = Crystal of hefin\n|mat2cost = 500,000\n|output1 = Bow of faerdhinen (c)\n|output1cost = {{GEP|Bow of faerdhinen (inactive)}}\n|output1subtxt = Recolour (Trahaearn)\n}}\n}}\n\n==Products==\n{{Uses material list|Bow of faerdhinen (c)}}\n\n==Comparison between other ranged weapons==\n{{Ranged weapons comparison}}\n\n==Treasure Trails==\n{{EmoteClueHeader}}\n{{EmoteClue|tier=master|emote=Bow|location=North-west tent in the [[Iorwerth Camp]]|item1=Bow of faerdhinen (c)|cost={{GEP|Enhanced crystal weapon seed}}|variation=crystal bow}}}}\n{{EmoteClueBottom}}\n\n==Sound effects==\n{{SFXTableHead}}\n{{SFXLine|file=Equip fun.wav|desc=Wielding}}\n{{SFXLine|file=Crystal bow attack.wav|desc=Attacking}}\n{{SFXTableBottom}}\n\n==Used in recommended equipment==\n{{Used in recommended equipment|Bow of faerdhinen}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 11 May 2022\n|update = Equipment Rebalance Tier Changes & Poll 76\n|change = The bow of Faerdhinen now requires level 80 Ranged and 70 Agility to wield instead of level 75 Ranged and 70 Agility.\n}}\n{{Subject changes footer}}\n\n==Trivia==\n* This bow is currently one of only a few ranged weapons in ''Old School RuneScape'' to have unlimited ammo and durability, the others being the [[tonalztics of Ralos]] or [[Morrigan's throwing axe (bh)|Morrigan's throwing axe]].\n\n{{Ranged weapons}}\n{{Crystal items}}\n{{The Gauntlet}}\n[[Category:Lliann's Wares]]", "downloadedAt": "2025-09-05T09:48:11.696Z", "source": "osrs_wiki_bulk_download", "rawWikitext": true}, "contentHash": "1162192ab41cd2f9", "reason": "page_updated"}