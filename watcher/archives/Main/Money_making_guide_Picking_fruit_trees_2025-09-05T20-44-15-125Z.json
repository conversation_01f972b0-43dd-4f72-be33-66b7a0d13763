{"title": "Money making guide/Picking fruit trees", "namespace": "Main", "archivedAt": "2025-09-05T20:44:15.125Z", "originalContent": {"title": "Money making guide/Picking fruit trees", "namespace": "Main", "content": "{{Mmgtable recurring\n|Activity = Picking [[Fruit trees]]\n|Image = \n{{#switch:{{max|{{#var:appleprofit}}||{{#var:bananaprofit}}||{{#var:orangeprofit}}||{{#var:curryprofit}}||{{#var:pineappleprofit}}||{{#var:papayaprofit}}||{{#var:coconutprofit}}||{{#var:dragonfruitprofit}}}}\n|{{#var:appleprofit}} = [[File:Apple tree (grown).png|x215px]]\n|{{#var:bananaprofit}} = [[File:Banana tree (grown).png|x215px]]\n|{{#var:orangeprofit}} = [[File:Orange tree (grown).png|x215px]]\n|{{#var:curryprofit}} = [[File:Curry tree (grown).png|x215px]]\n|{{#var:pineappleprofit}} = [[File:Pineapple plant (grown).png|x215px]]\n|{{#var:papayaprofit}} = [[File:Papaya tree (grown).png|x215px]]\n|{{#var:coconutprofit}} = [[File:Palm tree (grown).png|x215px]]\n|{{#var:dragonfruitprofit}} = [[File:Dragonfruit tree (grown).png|x215px]] }}\n|Activity Time = 3\n|Recurrence Time = 180 minutes\n|Skill =\n{{#switch:{{max|{{#var:appleprofit}}||{{#var:bananaprofit}}||{{#var:orangeprofit}}||{{#var:curryprofit}}||{{#var:pineappleprofit}}||{{#var:papayaprofit}}||{{#var:coconutprofit}}||{{#var:dragonfruitprofit}}}}\n|{{#var:appleprofit}} = {{SCP|Farming|27}}\n|{{#var:bananaprofit}} = {{SCP|Farming|33}}\n|{{#var:orangeprofit}} = {{SCP|Farming|39}}\n|{{#var:curryprofit}} = {{SCP|Farming|42}}\n|{{#var:pineappleprofit}} = {{SCP|Farming|51}}\n|{{#var:papayaprofit}} = {{SCP|Farming|57}}\n|{{#var:coconutprofit}} = {{SCP|Farming|68}}\n|{{#var:dragonfruitprofit}} = {{SCP|Farming|81}}}} (85+ recommended)\n|Item =\n* [[Teleport crystal]] (recommended)\n* [[Pendant of ates]] (recommended)\n|Quest =\n* Partial completion of [[Twilight's Promise]] (recommended)\n* [[The Grand Tree]] (recommended)\n* Started [[Mourning's End Part I]] (recommended)\n|Other =\nFully grown {{#switch:{{max|{{#var:appleprofit}}||{{#var:bananaprofit}}||{{#var:orangeprofit}}||{{#var:curryprofit}}||{{#var:pineappleprofit}}||{{#var:papayaprofit}}||{{#var:coconutprofit}}||{{#var:dragonfruitprofit}}}}\n|{{#var:appleprofit}} = [[Apple tree]]\n|{{#var:bananaprofit}} = [[Banana tree]]\n|{{#var:orangeprofit}} = [[Orange tree]]\n|{{#var:curryprofit}} = [[Curry tree]]\n|{{#var:pineappleprofit}} = [[Pineapple plant]]\n|{{#var:papayaprofit}} = [[Papaya tree]]\n|{{#var:coconutprofit}} = [[Palm tree]]\n|{{#var:dragonfruitprofit}} = [[Dragonfruit tree]]}} in [[fruit tree patch]]es\n|Other Benefits = \n{{#switch:{{max|{{#var:appleprofit}}||{{#var:bananaprofit}}||{{#var:orangeprofit}}||{{#var:curryprofit}}||{{#var:pineappleprofit}}||{{#var:papayaprofit}}||{{#var:coconutprofit}}||{{#var:dragonfruitprofit}}}}\n|{{#var:appleprofit}} = {{SCP|Farming|{{Formatexpr|6*8.5 round 0}}}}\n|{{#var:bananaprofit}} = {{SCP|Farming|{{Formatexpr|6*10.5 round 0}}}}\n|{{#var:orangeprofit}} = {{SCP|Farming|{{Formatexpr|6*13.5 round 0}}}}\n|{{#var:curryprofit}} = {{SCP|Farming|{{Formatexpr|6*15 round 0}}}}\n|{{#var:pineappleprofit}} = {{SCP|Farming|{{Formatexpr|6*21.5 round 0}}}}\n|{{#var:papayaprofit}} = {{SCP|Farming|{{Formatexpr|6*27 round 0}}}}\n|{{#var:coconutprofit}} = {{SCP|Farming|{{Formatexpr|6*41.5 round 0}}}} \n|{{#var:dragonfruitprofit}} = {{SCP|Farming|{{Formatexpr|6*70 round 0}}}}}}\n|Output1 = \n{{#vardefineecho:bestfruit|\n{{#vardefine:appleprofit|{{#expr:({{GEP|Cooking apple}}-floor({{GEP|Cooking apple|}}/50))*42}}}}\n{{#vardefine:bananaprofit|{{#expr:({{GEP|Banana}}-floor({{GEP|Banana|}}/50))*42}}}}\n{{#vardefine:orangeprofit|{{#expr:({{GEP|Orange}}-floor({{GEP|Orange|}}/50))*42}}}}\n{{#vardefine:curryprofit|{{#expr:({{GEP|Curry leaf}}-floor({{GEP|Curry leaf|}}/50))*42}}}}\n{{#vardefine:pineappleprofit|{{#expr:({{GEP|Pineapple}}-floor({{GEP|Pineapple|}}/50))*42}}}}\n{{#vardefine:papayaprofit|{{#expr:({{GEP|Papaya fruit}}-floor({{GEP|Papaya fruit|}}/50))*42}}}}\n{{#vardefine:coconutprofit|{{#expr:({{GEP|Coconut}}-floor({{GEP|Coconut|}}/50))*42}}}}\n{{#vardefine:dragonfruitprofit|{{#expr:({{GEP|Dragonfruit}}-floor({{GEP|Dragonfruit|}}/50))*42}}}}\n{{#switch:{{max|{{#var:appleprofit}}||{{#var:bananaprofit}}||{{#var:orangeprofit}}||{{#var:curryprofit}}||{{#var:pineappleprofit}}||{{#var:papayaprofit}}||{{#var:coconutprofit}}||{{#var:dragonfruitprofit}}}}\n|{{#var:appleprofit}} = Cooking apple\n|{{#var:bananaprofit}} = Banana\n|{{#var:orangeprofit}} = Orange\n|{{#var:curryprofit}} = Curry leaf\n|{{#var:pineappleprofit}} = Pineapple\n|{{#var:papayaprofit}} = Papaya fruit\n|{{#var:coconutprofit}} = Coconut\n|{{#var:dragonfruitprofit}} = Dragonfruit}}}}\n|Output1num = 46\n|Location = [[Catherby]], [[Brimhaven]], [[Tree Gnome Stronghold]], [[Tree Gnome Village (location)|Tree Gnome Village]], [[Lletya]], [[Kastori]] and [[Farming Guild]] (85 Farming required) [[fruit tree patch]]es\n|Category = Farming\n|Intensity = Low\n|Details = Fruit trees may be harvested repeatedly, with a fruit growing back every 30 minutes on average, for a maximum of six per fruit tree.\n\nHarvesting trips are most efficient when the player has, at most, 4 items in their inventory, to minimise time spent running to the [[tool leprechaun]]s to note fruits. An efficient inventory setup includes a [[Construction cape]], [[Farming cape]], a [[Teleportation crystal]] and a [[Pendent of ates]]. This way, the player only needs to use the noting function of the tool leprechaun once.\n\n'''Recommended route:'''\n* Teleport to the [[Brimhaven]] house portal using the [[Construction cape]] and run north, harvesting all 6 fruits from the fruit tree.  \n* Teleport to [[Lletya]] using a [[Teleport crystal]] and run south-east, harvesting all 6 fruits from the fruit tree. Note 12 fruits with the [[tool leprechaun]] located 2 tiles north of the tree. \n* Teleport to the [[Farming Guild]] using the [[Farming cape]] and run north into the advanced tier of the guild, harvesting all 6 fruits from the fruit tree. \n* Teleport to [[Catherby]] using the [[Catherby Teleport]] and run east to the shore, harvesting all 6 fruits from the fruit tree. \n* Teleport to your [[player-owned house]] and use the [[Spirit tree]] to teleport to the [[Gnome Stronghold]]. Harvest all 6 fruits from the fruit tree to the east before returning back to the Spirit tree and travelling to the [[Tree Gnome Village (location)|Tree Gnome Village]]. Squeeze through the fence to the south-west and exit the maze quickly by following [[Elkoy]]. Once out of the maze, travel south-west and harvest all 6 fruits from the fruit tree. \n* Finally, teleport to [[Kastori]] using the [[Pendent of ates]] and run south-west, harvesting all 6 fruits from the fruit tree. \n}}\n\n=Picking Fruit Trees Profitability=\n\nThe below table details all pickable fruit trees, their profitability, and the expected experience gained per run.\n\nAll prices are calculated using current [[Grand Exchange]] market prices, meaning the actual profit per hour may vary from what is stated here, especially for lower volume items. If you notice a discrepancy between profits listed here and profits listed on guide pages, it is possibly a caching issue. You can force this page to update by clicking {{Purge|here|tag=span|noinplace=true}}.\n\n{| class=\"wikitable sortable\" style=\"text-align:center\"\n|+ Picking Fruit Trees Profitability\n|-\n! Farming <br> Level\n! Fruit Tree\n! Output\n! Profit <br> (includes G.E. tax)\n! Effective Profit <br> (per hour)  \n! Average Farming Experience <br> (per instance)\n|-  \n| {{SCP|Farming|27}} || [[File:Apple tree (grown).png|x30px]] <br> [[Apple tree]] || {{plink|Cooking apple}} || {{Coins|{{decimals|({{GEP|Cooking apple}}-floor({{GEP|Cooking apple}}/50))*42|0}}}} || {{Coins|{{decimals|60/3*{{#var:appleprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|6*8.5 round 0}}}}\n|-\n| {{SCP|Farming|33}} || [[File:Banana tree (grown).png|x30px]] <br> [[Banana tree]] || {{plink|Banana}} || {{Coins|{{decimals|({{GEP|Banana}}-floor({{GEP|Banana}}/50))*42|0}}}} || {{Coins|{{decimals|60/3*{{#var:bananaprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|6*10.5 round 0}}}}\n|-\n| {{SCP|Farming|39}} || [[File:Orange tree (grown).png|x30px]] <br> [[Orange tree]] || {{plink|Orange}} || {{Coins|{{decimals|({{GEP|Orange}}-floor({{GEP|Orange}}/50))*42|0}}}} || {{Coins|{{decimals|60/3*{{#var:orangeprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|6*13.5 round 0}}}}\n|-\n| {{SCP|Farming|42}} || [[File:Pineapple plant (grown).png|x30px]] <br> [[Curry tree]] || {{plink|Curry leaf}} || {{Coins|{{decimals|({{GEP|Curry leaf}}-floor({{GEP|Curry leaf}}/50))*42|0}}}} || {{Coins|{{decimals|60/3*{{#var:curryprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|6*15 round 0}}}}\n|-\n| {{SCP|Farming|51}} || [[File:Curry tree (grown).png|x30px]] <br> [[Pineapple plant]] || {{plink|Pineapple}} || {{Coins|{{decimals|({{GEP|Pineapple}}-floor({{GEP|Pineapple}}/50))*42|0}}}} || {{Coins|{{decimals|60/3*{{#var:pineappleprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|6*21.5 round 0}}}}\n|-\n| {{SCP|Farming|57}} || [[File:Papaya tree (grown).png|x30px]] <br> [[Papaya tree]] || {{plink|Papaya fruit}} || {{Coins|{{decimals|({{GEP|Papaya fruit}}-floor({{GEP|Papaya fruit}}/50))*42|0}}}} || {{Coins|{{decimals|60/3*{{#var:papayaprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|6*27 round 0}}}}\n|-\n| {{SCP|Farming|68}} || [[File:Palm tree (grown).png|x30px]] <br> [[Palm tree]] || {{plink|Coconut}} || {{Coins|{{decimals|({{GEP|Coconut}}-floor({{GEP|Coconut}}/50))*42|0}}}} || {{Coins|{{decimals|60/3*{{#var:coconutprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|6*41.5 round 0}}}}\n|-\n| {{SCP|Farming|81}} || [[File:Dragonfruit tree (grown).png|x30px]] <br> [[Dragonfruit tree]] || {{plink|Dragonfruit}} || {{Coins|{{decimals|({{GEP|Dragonfruit}}-floor({{GEP|Dragonfruit}}/50))*42|0}}}} || {{Coins|{{decimals|60/3*{{#var:dragonfruitprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|6*70 round 0}}}}\n|-\n|}\n\n{{:Fruit tree patch/Patches}}\n\n{{Money making}}\n[[Category:MMG/Skilling]]", "revisionId": 14980539, "timestamp": "2025-09-05T15:35:31Z", "downloadedAt": "2025-09-05T20:37:29.870Z", "source": "osrs_wiki_watcher", "rawWikitext": true}, "contentHash": "509c17734aa2c6cf", "reason": "page_updated"}