# OSRS Wiki Watcher

A live monitoring system for Old School RuneScape Wiki content changes. This system detects new and updated pages on the OSRS Wiki and keeps your local wiki collection up to date.

## Features

- 🔍 **Live Monitoring**: Continuously monitors the OSRS Wiki for changes using the MediaWiki API
- 📦 **Smart Archiving**: Archives old versions of pages with timestamps for historical tracking
- 📊 **Detailed Logging**: Comprehensive logs of all changes and system activity
- 🎯 **OSRS Filtering**: Only processes content relevant to Old School RuneScape
- ⚡ **Efficient Updates**: Only fetches content that has actually changed (using revision IDs)
- 🔄 **Batch Processing**: Processes multiple pages efficiently to respect API limits
- 📈 **Progress Tracking**: Real-time status and statistics

## Directory Structure

```
watcher/
├── index.js              # Main CLI entry point
├── osrsWikiWatcher.js    # Core watcher system
├── initialScan.js        # Initial content scanner
├── utils/
│   ├── logger.js         # Logging utilities
│   ├── contentIndex.js   # Content index management
│   ├── archiveManager.js # Archive management
│   └── wikiAPI.js        # OSRS Wiki API client
├── data/                 # Generated data files
│   ├── content-index.json
│   └── watcher-stats.json
├── logs/                 # Log files
│   ├── changes.json
│   ├── watcher-activity.log
│   └── watcher-error.log
└── archives/             # Archived page versions
    ├── Main/
    └── Category/
```

## Quick Start

### 1. Initial Setup

First, run the initial scan to index your existing wiki content:

```bash
node watcher/index.js init
```

This will:
- Scan your existing `wiki-content/pages/Main/` and `wiki-content/pages/Category/` folders
- Build an index of all known pages with their current revision information
- Create the necessary directory structure

### 2. Start Monitoring

Start the live watcher:

```bash
node watcher/index.js start
```

The watcher will:
- Check for changes every 5 minutes (configurable)
- Archive old versions before updating pages
- Log all changes with detailed information
- Continue running until stopped with Ctrl+C

### 3. Check Status

View current status and statistics:

```bash
node watcher/index.js status
```

## Commands

### `init` / `scan`
Run initial scan of existing wiki content to build the content index.

```bash
node watcher/index.js init
```

### `start [options]`
Start the live watcher with optional configuration.

```bash
node watcher/index.js start
node watcher/index.js start --interval 600 --batch-size 20
```

Options:
- `--interval <seconds>`: Poll interval in seconds (default: 300)
- `--batch-size <num>`: Batch size for processing pages (default: 10)
- `--max-retries <num>`: Max retries for failed requests (default: 3)

### `status`
Show current watcher status and statistics.

```bash
node watcher/index.js status
```

### `check`
Run a single check for changes (useful for testing).

```bash
node watcher/index.js check
```

## How It Works

### 1. Initial Scan
- Scans existing `wiki-content/pages/` directory
- Extracts page titles, timestamps, and content hashes
- Builds a content index for comparison

### 2. Change Detection
- Uses MediaWiki's `recentchanges` API to get recent wiki changes
- Filters changes to only OSRS-relevant content
- Compares revision IDs to detect actual content changes

### 3. Content Updates
- Archives old version with timestamp in filename
- Fetches new content from wiki API
- Updates local files with new content
- Updates content index with new revision information

### 4. Logging and Archiving
- Logs all changes to `logs/changes.json`
- Archives old versions to `archives/[namespace]/[page]_[timestamp].json`
- Maintains activity logs for monitoring and debugging

## Configuration

The watcher can be configured through command-line options or by modifying the default configuration in `osrsWikiWatcher.js`:

```javascript
this.config = {
  pollInterval: 5 * 60 * 1000,  // 5 minutes
  batchSize: 10,                // Process 10 pages at a time
  maxRetries: 3                 // Retry failed requests 3 times
};
```

## Archive Format

Archived pages are stored with timestamps in the filename:

```
archives/Main/Abyssal_whip_2025-01-15T14-30-25-123Z.json
```

Each archive contains:
```json
{
  "title": "Abyssal whip",
  "namespace": "Main",
  "archivedAt": "2025-01-15T14:30:25.123Z",
  "originalContent": { ... },
  "contentHash": "abc123...",
  "reason": "page_updated"
}
```

## Change Log Format

All changes are logged to `logs/changes.json`:

```json
{
  "timestamp": "2025-01-15T14:30:25.123Z",
  "type": "updated",
  "title": "Abyssal whip",
  "namespace": "Main",
  "revisionId": 12345,
  "changes": [
    {
      "type": "edit",
      "user": "WikiEditor123",
      "comment": "Updated drop rates",
      "timestamp": "2025-01-15T14:25:00.000Z"
    }
  ]
}
```

## Monitoring

The watcher provides several ways to monitor its operation:

1. **Console Output**: Real-time status updates
2. **Log Files**: Detailed activity and error logs
3. **Status Command**: Current statistics and health
4. **Change Log**: Complete history of all detected changes

## Error Handling

The system includes robust error handling:
- Automatic retries with exponential backoff
- Graceful handling of API errors and network issues
- Detailed error logging for debugging
- Continues operation even if individual pages fail

## Requirements

- Node.js 14.0.0 or higher
- Existing `wiki-content/` directory with OSRS wiki pages
- Internet connection for API access

## License

MIT License - See LICENSE file for details.
