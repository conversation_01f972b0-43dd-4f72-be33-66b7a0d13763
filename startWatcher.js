#!/usr/bin/env node

/**
 * Quick Start Script for OSRS Wiki Watcher
 * Provides an easy way to initialize and start the watcher system
 */

const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

async function checkWikiContent() {
  try {
    const wikiContentDir = path.join(process.cwd(), 'wiki-content', 'pages');
    await fs.access(wikiContentDir);
    
    const mainDir = path.join(wikiContentDir, 'Main');
    const categoryDir = path.join(wikiContentDir, 'Category');
    
    const mainFiles = await fs.readdir(mainDir);
    const categoryFiles = await fs.readdir(categoryDir);
    
    const mainCount = mainFiles.filter(f => f.endsWith('.json')).length;
    const categoryCount = categoryFiles.filter(f => f.endsWith('.json')).length;
    
    return { mainCount, categoryCount, total: mainCount + categoryCount };
  } catch (error) {
    return null;
  }
}

async function checkWatcherSetup() {
  try {
    const indexPath = path.join(process.cwd(), 'watcher', 'data', 'content-index.json');
    await fs.access(indexPath);
    return true;
  } catch (error) {
    return false;
  }
}

async function runCommand(command, args = []) {
  return new Promise((resolve, reject) => {
    const childProcess = spawn('node', [command, ...args], {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    childProcess.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });

    childProcess.on('error', (error) => {
      reject(error);
    });
  });
}

async function main() {
  console.log('🔍 OSRS Wiki Watcher - Quick Start');
  console.log('===================================');
  console.log('');
  
  // Check if wiki content exists
  console.log('📂 Checking for existing wiki content...');
  const wikiStats = await checkWikiContent();
  
  if (!wikiStats) {
    console.log('❌ No wiki content found!');
    console.log('');
    console.log('💡 Please run your wiki downloader first to populate the wiki-content directory.');
    console.log('   Example: node quickRestart.js');
    console.log('');
    process.exit(1);
  }
  
  console.log(`✅ Found ${wikiStats.total} wiki pages:`);
  console.log(`   📄 Main namespace: ${wikiStats.mainCount} pages`);
  console.log(`   📂 Category namespace: ${wikiStats.categoryCount} pages`);
  console.log('');
  
  // Check if watcher is already set up
  console.log('🔧 Checking watcher setup...');
  const isSetup = await checkWatcherSetup();
  
  if (!isSetup) {
    console.log('⚙️ Watcher not initialized. Running initial scan...');
    console.log('');
    
    try {
      await runCommand('watcher/index.js', ['init']);
      console.log('');
      console.log('✅ Initial scan completed!');
    } catch (error) {
      console.error('❌ Initial scan failed:', error.message);
      process.exit(1);
    }
  } else {
    console.log('✅ Watcher already initialized');
  }
  
  console.log('');
  console.log('🚀 Starting OSRS Wiki Watcher...');
  console.log('');
  console.log('📊 The watcher will:');
  console.log('   🔍 Check for changes every 5 minutes');
  console.log('   📦 Archive old versions of updated pages');
  console.log('   📝 Log all changes for your records');
  console.log('   ⚡ Only fetch pages that have actually changed');
  console.log('');
  console.log('🛑 Press Ctrl+C to stop the watcher');
  console.log('');
  
  try {
    await runCommand('watcher/index.js', ['start']);
  } catch (error) {
    console.error('❌ Watcher failed:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down...');
  process.exit(0);
});

main().catch(error => {
  console.error('💥 Fatal error:', error.message);
  process.exit(1);
});
