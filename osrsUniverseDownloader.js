const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');

class DownloadQueue {
  constructor(maxConcurrent = 3) {
    this.queue = [];
    this.processing = 0;
    this.maxConcurrent = maxConcurrent;
    this.results = { downloaded: 0, failed: 0, errors: [] };
  }

  add(batch, batchNum) {
    this.queue.push({ batch, batchNum });
    this.process();
  }

  async process() {
    if (this.processing >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }

    this.processing++;
    const { batch, batchNum } = this.queue.shift();

    try {
      await this.downloadBatch(batch, batchNum);
    } catch (error) {
      logger.error(`Queue processing error for batch ${batchNum}:`, error.message);
    } finally {
      this.processing--;
      // Process next item in queue
      if (this.queue.length > 0) {
        setImmediate(() => this.process());
      }
    }
  }

  async downloadBatch(batch, batchNum) {
    // This will be set by the parent class
    if (this.downloader) {
      return await this.downloader.processBatch(batch, batchNum, this.results);
    }
  }

  getStats() {
    return {
      queueLength: this.queue.length,
      processing: this.processing,
      downloaded: this.results.downloaded,
      failed: this.results.failed
    };
  }
}

class ErrorLogger {
  constructor(wikiContentDir) {
    this.logDir = path.join(wikiContentDir, 'logs');
    this.errorLogPath = path.join(this.logDir, 'download_errors.json');
    this.failedPagesPath = path.join(this.logDir, 'failed_pages.json');
    this.progressLogPath = path.join(this.logDir, 'download_progress.json');
    this.errors = [];
    this.failedPages = [];
  }

  async initialize() {
    await fs.mkdir(this.logDir, { recursive: true });

    // Load existing errors if resuming
    try {
      const existingErrors = await fs.readFile(this.errorLogPath, 'utf8');
      this.errors = JSON.parse(existingErrors);
    } catch (error) {
      this.errors = [];
    }

    try {
      const existingFailed = await fs.readFile(this.failedPagesPath, 'utf8');
      this.failedPages = JSON.parse(existingFailed);
    } catch (error) {
      this.failedPages = [];
    }
  }

  async logError(type, details) {
    const errorEntry = {
      timestamp: new Date().toISOString(),
      type: type,
      details: details
    };

    this.errors.push(errorEntry);

    // Write to file immediately
    await fs.writeFile(this.errorLogPath, JSON.stringify(this.errors, null, 2));

    logger.error(`🚨 ${type}: ${JSON.stringify(details)}`);
  }

  async logFailedPage(pageTitle, error, batchNum) {
    const failedEntry = {
      timestamp: new Date().toISOString(),
      pageTitle: pageTitle,
      error: error,
      batchNum: batchNum,
      canRetry: true
    };

    this.failedPages.push(failedEntry);

    // Write to file immediately
    await fs.writeFile(this.failedPagesPath, JSON.stringify(this.failedPages, null, 2));
  }

  async logProgress(stats) {
    const progressEntry = {
      timestamp: new Date().toISOString(),
      ...stats
    };

    await fs.writeFile(this.progressLogPath, JSON.stringify(progressEntry, null, 2));
  }

  getFailedPages() {
    return this.failedPages.filter(page => page.canRetry);
  }

  async generateErrorReport() {
    const report = {
      summary: {
        totalErrors: this.errors.length,
        totalFailedPages: this.failedPages.length,
        retryablePages: this.failedPages.filter(p => p.canRetry).length,
        generatedAt: new Date().toISOString()
      },
      errorsByType: {},
      failedPages: this.failedPages,
      errors: this.errors
    };

    // Group errors by type
    for (const error of this.errors) {
      if (!report.errorsByType[error.type]) {
        report.errorsByType[error.type] = 0;
      }
      report.errorsByType[error.type]++;
    }

    const reportPath = path.join(this.logDir, 'error_report.json');
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    return report;
  }
}

class OSRSUniverseDownloader {
  constructor() {
    this.osrsWikiAPI = 'https://oldschool.runescape.wiki/api.php';
    this.workspaceRoot = process.cwd();
    this.wikiContentDir = path.join(this.workspaceRoot, 'wiki-content');
    this.downloadQueue = new DownloadQueue(3); // 3 concurrent downloads
    this.downloadQueue.downloader = this; // Set reference for queue callbacks

    // Error logging system
    this.errorLogger = new ErrorLogger(this.wikiContentDir);

    // OSRS-specific filtering - using actual category names from the wiki
    this.osrsCategories = [
      'Category:Items',
      'Category:NPCs',
      'Category:Locations',
      'Category:Quests',
      'Category:Skills',
      'Category:Equipment',
      'Category:Monsters',
      'Category:Activities',
      'Category:Minigames',
      'Category:Weapons',
      'Category:Armour',
      'Category:Food',
      'Category:Potions',
      'Category:Runes',
      'Category:Tools',
      'Category:Jewellery'
    ];
  }

  // Main method to download complete OSRS universe
  async downloadCompleteUniverse() {
    const results = {
      pages: { total: 0, downloaded: 0, failed: 0 },
      images: { total: 0, downloaded: 0, failed: 0 },
      startTime: new Date(),
      errors: []
    };

    try {
      logger.info('🌍 Starting complete OSRS universe download...');

      // Initialize error logging
      await this.errorLogger.initialize();
      logger.info('📋 Error logging system initialized');

      // Create directory structure
      await this.createDirectoryStructure();
      
      // Phase 1: Download all page content
      logger.info('📄 Phase 1: Downloading all wiki pages...');
      await this.downloadAllPages(results);
      
      // Phase 2: Download all images
      logger.info('🖼️ Phase 2: Downloading all images...');
      await this.downloadAllImages(results);
      
      // Phase 3: Create indexes
      logger.info('📊 Phase 3: Creating universe indexes...');
      await this.createUniverseIndexes(results);
      
      const duration = ((new Date() - results.startTime) / 1000 / 60).toFixed(1);
      
      logger.info('🎉 Complete OSRS universe download finished!');
      logger.info(`📊 Final Results:`);
      logger.info(`   📄 Pages: ${results.pages.downloaded}/${results.pages.total} (${results.pages.failed} failed)`);
      logger.info(`   🖼️ Images: ${results.images.downloaded}/${results.images.total} (${results.images.failed} failed)`);
      logger.info(`   ⏱️ Duration: ${duration} minutes`);
      logger.info(`   📁 Location: ${this.wikiContentDir}`);
      
      return results;
      
    } catch (error) {
      logger.error('💥 Universe download failed:', error);
      throw error;
    }
  }

  // Create the complete directory structure
  async createDirectoryStructure() {
    const dirs = [
      'pages/Main',
      'pages/User', 
      'pages/Template',
      'pages/Category',
      'pages/File',
      'pages/Talk',
      'pages/Help',
      'pages/Project',
      'images/items',
      'images/npcs',
      'images/locations', 
      'images/interfaces',
      'images/misc',
      'metadata',
      'indexes'
    ];

    for (const dir of dirs) {
      await fs.mkdir(path.join(this.wikiContentDir, dir), { recursive: true });
    }
    
    logger.info(`📁 Created directory structure at: ${this.wikiContentDir}`);
  }

  // OSRS-specific concurrent queue-based download system
  async downloadAllPages(results) {
    try {
      logger.info('🚀 Starting OSRS-specific concurrent download...');
      logger.info('📊 Using 3 concurrent download workers');
      logger.info('🎯 Filtering for OSRS content only');

      // Get OSRS-specific pages instead of ALL pages
      const osrsPageTitles = await this.getOSRSPageTitles();
      logger.info(`📋 Found ${osrsPageTitles.length} OSRS-specific pages to download`);

      let totalFetched = 0;
      let batchNum = 0;

      // Start progress monitoring
      const progressInterval = setInterval(() => {
        const stats = this.downloadQueue.getStats();
        logger.info(`📊 Progress: Processing ${stats.processing} | Downloaded: ${stats.downloaded}/${osrsPageTitles.length} pages | Queue: ${stats.queueLength}`);
      }, 10000); // Every 10 seconds

      // Process OSRS pages in batches of 50
      const downloadBatchSize = 50;
      for (let i = 0; i < osrsPageTitles.length; i += downloadBatchSize) {
        const batch = osrsPageTitles.slice(i, i + downloadBatchSize);
        batchNum++;
        totalFetched += batch.length;

        logger.info(`📋 Queuing batch ${batchNum}: ${batch.length} pages (${totalFetched}/${osrsPageTitles.length} total)`);

        // Add to queue (non-blocking)
        this.downloadQueue.add(batch, batchNum);

        // Small delay between batches to avoid overwhelming the queue
        if (i + downloadBatchSize < osrsPageTitles.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      logger.info(`📋 All ${osrsPageTitles.length} OSRS pages queued for download`);
      logger.info('⏳ Waiting for download queue to complete...');

      // Wait for queue to finish
      while (this.downloadQueue.queue.length > 0 || this.downloadQueue.processing > 0) {
        const stats = this.downloadQueue.getStats();
        logger.info(`⏳ Queue: ${stats.queueLength} | Processing: ${stats.processing} | Downloaded: ${stats.downloaded}/${osrsPageTitles.length} pages`);
        await new Promise(resolve => setTimeout(resolve, 5000));
      }

      clearInterval(progressInterval);

      // Update results
      results.pages.total = osrsPageTitles.length;
      results.pages.downloaded = this.downloadQueue.results.downloaded;
      results.pages.failed = this.downloadQueue.results.failed;
      results.errors.push(...this.downloadQueue.results.errors);

      logger.info(`🎉 OSRS download complete! Downloaded: ${results.pages.downloaded}/${osrsPageTitles.length} pages`);

    } catch (error) {
      logger.error('💥 Concurrent download failed:', error);
      throw error;
    }
  }

  // Process a single batch (called by queue)
  async processBatch(batch, batchNum, queueResults) {
    logger.info(`📦 Processing batch ${batchNum} (${batch.length} pages)`);

    try {
      // Get content for batch
      const batchContent = await this.getBatchWikiContent(batch);

      // Save each page
      let batchDownloaded = 0;
      for (const [pageTitle, content] of Object.entries(batchContent)) {
        try {
          await this.savePageContent(pageTitle, content);
          queueResults.downloaded++;
          batchDownloaded++;

        } catch (fileError) {
          logger.error(`❌ Failed to save page ${pageTitle}:`, fileError.message);
          queueResults.failed++;
          queueResults.errors.push({ type: 'page', item: pageTitle, error: fileError.message });

          // Log to error system
          await this.errorLogger.logFailedPage(pageTitle, fileError.message, batchNum);
        }
      }

      logger.info(`✅ Batch ${batchNum} complete: ${batchDownloaded}/${batch.length} pages saved`);

      // Small delay to be respectful
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (batchError) {
      logger.error(`❌ Batch ${batchNum} failed:`, batchError.message);
      queueResults.failed += batch.length;
      queueResults.errors.push({ type: 'batch', batch: batchNum, pages: batch, error: batchError.message });
    }
  }

  // Process a single batch (called by queue)
  async processBatch(batch, batchNum, queueResults) {
    logger.info(`📦 Processing batch ${batchNum} (${batch.length} pages)`);

    try {
      // Get content for batch
      const batchContent = await this.getBatchWikiContent(batch);

      // Save each page
      let batchDownloaded = 0;
      for (const [pageTitle, content] of Object.entries(batchContent)) {
        try {
          await this.savePageContent(pageTitle, content);
          queueResults.downloaded++;
          batchDownloaded++;

        } catch (fileError) {
          logger.error(`❌ Failed to save page ${pageTitle}:`, fileError.message);
          queueResults.failed++;
          queueResults.errors.push({ type: 'page', item: pageTitle, error: fileError.message });
        }
      }

      logger.info(`✅ Batch ${batchNum} complete: ${batchDownloaded}/${batch.length} pages saved`);

      // Small delay to be respectful
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (batchError) {
      logger.error(`❌ Batch ${batchNum} failed:`, batchError.message);
      queueResults.failed += batch.length;
      queueResults.errors.push({ type: 'batch', batch: batchNum, pages: batch, error: batchError.message });
    }
  }

  // Get OSRS-specific page titles using category filtering
  async getOSRSPageTitles() {
    const osrsPages = new Set(); // Use Set to avoid duplicates

    logger.info('🎯 Fetching OSRS-specific pages from categories...');

    // Get pages from OSRS-specific categories
    for (const category of this.osrsCategories) {
      try {
        const categoryPages = await this.getCategoryMembers(category);
        categoryPages.forEach(page => osrsPages.add(page));
        logger.info(`📂 Found ${categoryPages.length} pages in ${category}`);
      } catch (error) {
        logger.warn(`⚠️ Failed to fetch category ${category}: ${error.message}`);
      }
    }

    // Also get main namespace pages with OSRS-specific patterns
    logger.info('🔍 Fetching main namespace pages with OSRS filtering...');
    try {
      const mainPages = await this.getMainNamespacePages();
      mainPages.forEach(page => osrsPages.add(page));
      logger.info(`📂 Found ${mainPages.length} additional pages from main namespace`);
    } catch (error) {
      logger.warn(`⚠️ Failed to fetch main namespace pages: ${error.message}`);
      logger.info('📂 Continuing with category pages only...');
    }

    const finalPages = Array.from(osrsPages);
    logger.info(`✅ Total OSRS pages found: ${finalPages.length}`);

    return finalPages;
  }

  // Get pages from a specific category
  async getCategoryMembers(categoryTitle) {
    const pages = [];
    let cmcontinue = null;

    do {
      try {
        const params = {
          action: 'query',
          list: 'categorymembers',
          cmtitle: categoryTitle,
          cmlimit: 500,
          format: 'json',
          maxlag: 5
        };

        if (cmcontinue) {
          params.cmcontinue = cmcontinue;
        }

        const response = await axios.get(this.osrsWikiAPI, {
          params: params,
          headers: {
            'User-Agent': 'OSRS-Universe-Downloader/1.0 (Educational Research)',
            'Accept-Encoding': 'gzip'
          },
          timeout: 30000
        });

        if (response.data.query && response.data.query.categorymembers) {
          const members = response.data.query.categorymembers;
          pages.push(...members.map(member => member.title));

          cmcontinue = response.data.continue?.cmcontinue;
        } else {
          break;
        }

        // Delay between requests
        if (cmcontinue) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error) {
        logger.error(`Error fetching category ${categoryTitle}:`, error.message);
        break;
      }
    } while (cmcontinue);

    return pages;
  }

  // Get main namespace pages with OSRS filtering (with timeout protection)
  async getMainNamespacePages() {
    const pages = [];
    let apcontinue = null;
    let requestCount = 0;
    const maxRequests = 10; // Limit to prevent hanging

    logger.info('🔍 Scanning main namespace for additional OSRS pages...');

    do {
      try {
        requestCount++;
        if (requestCount > maxRequests) {
          logger.info(`⏰ Reached max requests (${maxRequests}) for main namespace, stopping...`);
          break;
        }

        const params = {
          action: 'query',
          list: 'allpages',
          apnamespace: 0, // Main namespace only
          aplimit: 500,
          format: 'json',
          maxlag: 5
        };

        if (apcontinue) {
          params.apcontinue = apcontinue;
        }

        logger.info(`🔍 Main namespace request ${requestCount}/${maxRequests}...`);

        const response = await axios.get(this.osrsWikiAPI, {
          params: params,
          headers: {
            'User-Agent': 'OSRS-Universe-Downloader/1.0 (Educational Research)',
            'Accept-Encoding': 'gzip'
          },
          timeout: 15000 // Shorter timeout
        });

        if (response.data.query && response.data.query.allpages) {
          const allPages = response.data.query.allpages;

          // Filter for OSRS-specific content
          const osrsPages = allPages
            .map(page => page.title)
            .filter(title => this.isOSRSPage(title));

          pages.push(...osrsPages);

          apcontinue = response.data.continue?.apcontinue;
        } else {
          break;
        }

        // Delay between requests
        if (apcontinue) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }

      } catch (error) {
        logger.error('Error fetching main namespace pages:', error.message);
        break;
      }
    } while (apcontinue);

    return pages;
  }

  // Check if a page title is OSRS-related
  isOSRSPage(title) {
    // Skip non-OSRS content
    const nonOSRSPatterns = [
      /Call of Duty/i,
      /Modern Warfare/i,
      /Battlefield/i,
      /Apex Legends/i,
      /Fortnite/i,
      /League of Legends/i,
      /World of Warcraft/i,
      /Minecraft/i,
      /Valorant/i,
      /Counter-Strike/i,
      /Overwatch/i,
      /Destiny/i,
      /Halo/i,
      /Grand Theft Auto/i,
      /GTA/i,
      /FIFA/i,
      /NBA/i,
      /NFL/i
    ];

    // If it matches non-OSRS patterns, skip it
    if (nonOSRSPatterns.some(pattern => pattern.test(title))) {
      return false;
    }

    // OSRS-specific patterns (more inclusive)
    const osrsPatterns = [
      /RuneScape/i,
      /OSRS/i,
      /Old School/i,
      /Gielinor/i,
      /Varrock/i,
      /Lumbridge/i,
      /Falador/i,
      /Ardougne/i,
      /Camelot/i,
      /Seers/i,
      /Catherby/i,
      /Rimmington/i,
      /Draynor/i,
      /Barbarian/i,
      /Wilderness/i,
      /Grand Exchange/i,
      /Quest/i,
      /Skill/i,
      /Prayer/i,
      /Magic/i,
      /Ranged/i,
      /Combat/i,
      /Mining/i,
      /Smithing/i,
      /Fishing/i,
      /Cooking/i,
      /Firemaking/i,
      /Woodcutting/i,
      /Fletching/i,
      /Crafting/i,
      /Herblore/i,
      /Agility/i,
      /Thieving/i,
      /Slayer/i,
      /Farming/i,
      /Runecrafting/i,
      /Hunter/i,
      /Construction/i
    ];

    // If it matches OSRS patterns, include it
    if (osrsPatterns.some(pattern => pattern.test(title))) {
      return true;
    }

    // For other pages, be more conservative - only include if they don't have colons
    // (to avoid other game content like "Game: Subtitle" format)
    return !title.includes(':') || title.startsWith('Category:') || title.startsWith('Template:');
  }

  // Get content for multiple pages in one API call (batch processing)
  async getBatchWikiContent(pageTitles) {
    try {
      const titlesParam = pageTitles.join('|');
      
      const response = await axios.get(this.osrsWikiAPI, {
        params: {
          action: 'query',
          prop: 'revisions',
          rvprop: 'content',
          titles: titlesParam,
          format: 'json',
          maxlag: 5
        },
        headers: {
          'User-Agent': 'OSRS-Universe-Downloader/1.0 (https://github.com/osrs-learning-brain; <EMAIL>) axios/1.4',
          'Accept-Encoding': 'gzip'
        },
        timeout: 30000
      });

      const pages = response.data.query.pages;
      const batchContent = {};
      
      for (const pageId in pages) {
        const page = pages[pageId];
        const pageTitle = page.title;
        
        if (page.revisions && page.revisions[0] && page.revisions[0]['*']) {
          batchContent[pageTitle] = page.revisions[0]['*'];
        } else {
          // Handle missing or empty pages
          batchContent[pageTitle] = '';
          logger.warn(`⚠️ No content found for: ${pageTitle}`);
        }
      }
      
      return batchContent;
      
    } catch (error) {
      if (error.response && error.response.status === 403) {
        logger.error('403 Forbidden error in batch request', { error: error.message });
        throw new Error('403_FORBIDDEN');
      }
      
      logger.error('Error in batch wiki content request:', error.message);
      throw error;
    }
  }

  // Save page content to appropriate namespace directory
  async savePageContent(pageTitle, content) {
    const [namespace, title] = this.parseNamespace(pageTitle);
    const safeTitle = this.sanitizeFilename(title || pageTitle);
    const filePath = path.join(this.wikiContentDir, 'pages', namespace, `${safeTitle}.json`);
    
    const pageData = {
      title: pageTitle,
      namespace: namespace,
      content: content,
      downloadedAt: new Date().toISOString(),
      source: 'osrs_wiki_bulk_download',
      rawWikitext: true
    };
    
    await fs.writeFile(filePath, JSON.stringify(pageData, null, 2), 'utf8');
  }

  // Parse namespace from page title
  parseNamespace(title) {
    const colonIndex = title.indexOf(':');
    if (colonIndex === -1) {
      return ['Main', title];  // Main namespace
    }

    const namespace = title.substring(0, colonIndex);
    const pageTitle = title.substring(colonIndex + 1);

    // Valid MediaWiki namespaces for OSRS wiki
    const validNamespaces = [
      'User', 'Template', 'Category', 'File', 'Talk',
      'Help', 'Project', 'MediaWiki', 'Module', 'RuneScape'
    ];

    // Only treat as namespace if it's a valid wiki namespace
    if (validNamespaces.includes(namespace)) {
      return [namespace, pageTitle];
    } else {
      // It's just a page title with a colon (like "Kree'arra (Deadman: Apocalypse)")
      return ['Main', title];
    }
  }

  // Download main page images only (header/primary images)
  async downloadAllImages(results) {
    try {
      // Get main images from pages (not all File: namespace pages)
      logger.info('🖼️ Extracting main page images...');
      const mainImages = await this.getMainPageImages();
      results.images.total = mainImages.length;

      logger.info(`📊 Found ${mainImages.length} main page images to download`);

      // Process main images in batches
      const batchSize = 100;
      const totalBatches = Math.ceil(mainImages.length / batchSize);

      for (let i = 0; i < mainImages.length; i += batchSize) {
        const batch = mainImages.slice(i, i + batchSize);
        const batchNum = Math.floor(i / batchSize) + 1;

        logger.info(`🖼️ Processing image batch ${batchNum}/${totalBatches} (${batch.length} images)`);

        try {
          // Get image info for entire batch
          const batchImageInfo = await this.getBatchImageInfo(batch);

          // Download each image and save metadata
          for (const imageInfo of batchImageInfo) {
            try {
              await this.downloadAndSaveImage(imageInfo);
              results.images.downloaded++;

            } catch (imageError) {
              logger.error(`❌ Failed to download image ${imageInfo.title}:`, imageError.message);
              results.images.failed++;
              results.errors.push({ type: 'image', item: imageInfo.title, error: imageError.message });
            }
          }

          // Progress update
          const progress = ((results.images.downloaded / results.images.total) * 100).toFixed(1);
          logger.info(`✅ Image batch ${batchNum} complete. Progress: ${progress}% (${results.images.downloaded}/${results.images.total})`);

          // Respectful delay between batches
          if (i + batchSize < imagePages.length) {
            await new Promise(resolve => setTimeout(resolve, 3000));
          }

        } catch (batchError) {
          logger.error(`❌ Image batch ${batchNum} failed:`, batchError.message);
          results.images.failed += batch.length;
          results.errors.push({ type: 'image_batch', batch: batchNum, images: batch, error: batchError.message });
        }
      }

    } catch (error) {
      logger.error('💥 Image download failed:', error);
      throw error;
    }
  }

  // Get main page images by extracting from page content
  async getMainPageImages() {
    try {
      // Read all downloaded pages to extract main images
      const pagesDir = path.join(this.wikiContentDir, 'pages');
      const mainImages = new Set(); // Use Set to avoid duplicates

      // Check Main namespace pages (items, NPCs, locations, etc.)
      const mainPagesDir = path.join(pagesDir, 'Main');

      try {
        const pageFiles = await fs.readdir(mainPagesDir);

        logger.info(`🔍 Scanning ${pageFiles.length} main pages for images...`);

        for (const pageFile of pageFiles) {
          if (!pageFile.endsWith('.json')) continue;

          try {
            const pageContent = await fs.readFile(path.join(mainPagesDir, pageFile), 'utf8');
            const pageData = JSON.parse(pageContent);

            // Extract main image from infobox or first image reference
            const mainImage = this.extractMainImage(pageData.content, pageData.title);
            if (mainImage) {
              mainImages.add(mainImage);
            }

          } catch (error) {
            // Skip individual page errors
            continue;
          }
        }

      } catch (error) {
        logger.warn('Could not scan main pages for images, will use fallback method');
        return await this.getFallbackMainImages();
      }

      const imageList = Array.from(mainImages).map(imageName => ({
        title: `File:${imageName}`,
        name: imageName,
        pageTitle: this.guessPageFromImage(imageName)
      }));

      logger.info(`🖼️ Found ${imageList.length} unique main page images`);
      return imageList;

    } catch (error) {
      logger.error('Error extracting main page images:', error.message);
      throw error;
    }
  }

  // Extract main image from page content
  extractMainImage(wikitext, pageTitle) {
    if (!wikitext) return null;

    // Look for infobox image parameter
    const infoboxMatch = wikitext.match(/\|image\s*=\s*([^|\n}]+)/i);
    if (infoboxMatch) {
      return infoboxMatch[1].trim();
    }

    // Look for first image reference
    const imageMatch = wikitext.match(/\[\[File:([^|\]]+)/i);
    if (imageMatch) {
      return imageMatch[1].trim();
    }

    // Try to guess image name from page title
    const guessedImage = `${pageTitle.replace(/\s+/g, '_')}_detail.png`;
    if (wikitext.toLowerCase().includes(guessedImage.toLowerCase())) {
      return guessedImage;
    }

    return null;
  }

  // Guess page title from image name
  guessPageFromImage(imageName) {
    return imageName
      .replace(/_detail\.png$/i, '')
      .replace(/_inventory\.png$/i, '')
      .replace(/_equipped\.png$/i, '')
      .replace(/\.[^.]+$/, '')
      .replace(/_/g, ' ');
  }

  // Fallback method if page scanning fails
  async getFallbackMainImages() {
    // Get a curated list of common main images
    const commonImages = [
      'Abyssal_whip_detail.png',
      'Dragon_scimitar_detail.png',
      'Rune_platebody_detail.png',
      'Lobster_detail.png',
      'Shark_detail.png'
      // Add more as needed
    ];

    return commonImages.map(imageName => ({
      title: `File:${imageName}`,
      name: imageName,
      pageTitle: this.guessPageFromImage(imageName)
    }));
  }

  // Get detailed info for batch of images
  async getBatchImageInfo(imageBatch) {
    try {
      const titles = imageBatch.map(img => img.title).join('|');

      const response = await axios.get(this.osrsWikiAPI, {
        params: {
          action: 'query',
          titles: titles,
          prop: 'imageinfo|imageusage',
          iiprop: 'url|size|dimensions|mime',
          iulimit: 50,
          format: 'json',
          maxlag: 5
        },
        headers: {
          'User-Agent': 'OSRS-Universe-Downloader/1.0 (https://github.com/osrs-learning-brain; <EMAIL>) axios/1.4',
          'Accept-Encoding': 'gzip'
        },
        timeout: 30000
      });

      // Check if response has expected structure
      if (!response.data.query || !response.data.query.pages) {
        logger.warn(`⚠️ Unexpected API response structure for image batch`);
        logger.warn(`📋 API Response: ${JSON.stringify(response.data, null, 2).substring(0, 500)}...`);
        logger.warn(`📋 Requested titles: ${titles.substring(0, 200)}...`);
        return [];
      }

      const pages = response.data.query.pages;
      const imageInfoList = [];

      for (const pageId in pages) {
        const page = pages[pageId];

        if (page.imageinfo && page.imageinfo[0]) {
          const imageInfo = page.imageinfo[0];
          const usagePages = page.imageusage ? page.imageusage.map(usage => usage.title) : [];

          imageInfoList.push({
            title: page.title,
            name: page.title.replace('File:', ''),
            url: imageInfo.url,
            size: imageInfo.size,
            dimensions: {
              width: imageInfo.width,
              height: imageInfo.height
            },
            mime: imageInfo.mime,
            usagePages: usagePages,
            primaryEntity: this.identifyPrimaryEntity(page.title, usagePages),
            category: this.categorizeImage(page.title)
          });
        }
      }

      return imageInfoList;

    } catch (error) {
      logger.error('Error getting batch image info:', error.message);
      throw error;
    }
  }

  // Download and save individual image with metadata
  async downloadAndSaveImage(imageInfo) {
    try {
      // Download the actual image file
      const imageResponse = await axios.get(imageInfo.url, {
        responseType: 'arraybuffer',
        timeout: 30000,
        headers: {
          'User-Agent': 'OSRS-Universe-Downloader/1.0 (https://github.com/osrs-learning-brain; <EMAIL>) axios/1.4'
        }
      });

      // Determine file extension from mime type or URL
      const extension = this.getFileExtension(imageInfo.mime, imageInfo.url);
      const safeFilename = this.sanitizeFilename(imageInfo.name.replace(/\.[^.]+$/, ''));

      // Save image file
      const imagePath = path.join(this.wikiContentDir, 'images', imageInfo.category, `${safeFilename}${extension}`);
      await fs.writeFile(imagePath, imageResponse.data);

      // Save metadata
      const metadataPath = path.join(this.wikiContentDir, 'images', imageInfo.category, `${safeFilename}.json`);
      const metadata = {
        title: imageInfo.title,
        filename: `${safeFilename}${extension}`,
        originalUrl: imageInfo.url,
        primaryEntity: imageInfo.primaryEntity,
        relatedPages: imageInfo.usagePages,
        imageType: this.classifyImageType(imageInfo.name),
        dimensions: imageInfo.dimensions,
        fileSize: imageInfo.size,
        mimeType: imageInfo.mime,
        category: imageInfo.category,
        downloadedAt: new Date().toISOString()
      };

      await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2), 'utf8');

    } catch (error) {
      logger.error(`Failed to download/save image ${imageInfo.title}:`, error.message);
      throw error;
    }
  }

  // Identify what entity this image primarily represents
  identifyPrimaryEntity(imageTitle, usagePages) {
    const filename = imageTitle.replace('File:', '').toLowerCase();

    // Look for exact matches in usage pages
    for (const page of usagePages) {
      const pageLower = page.toLowerCase().replace(/\s+/g, '_');
      if (filename.includes(pageLower) || pageLower.includes(filename.split('_')[0])) {
        return page;
      }
    }

    // Fallback: use the most likely page or extract from filename
    if (usagePages.length > 0) {
      return usagePages[0];
    }

    // Extract entity name from filename
    return filename.split('_')[0].replace(/[^a-zA-Z\s]/g, '').trim() || 'Unknown';
  }

  // Categorize image for directory organization
  categorizeImage(filename) {
    const lower = filename.toLowerCase();

    if (lower.includes('inventory') || lower.includes('detail') || lower.includes('equipped')) return 'items';
    if (lower.includes('npc') || lower.includes('monster') || lower.includes('chathead')) return 'npcs';
    if (lower.includes('map') || lower.includes('location') || lower.includes('area')) return 'locations';
    if (lower.includes('interface') || lower.includes('chatbox') || lower.includes('screen')) return 'interfaces';

    return 'misc';
  }

  // Classify the type of image
  classifyImageType(filename) {
    const lower = filename.toLowerCase();

    if (lower.includes('detail')) return 'item_detail';
    if (lower.includes('inventory')) return 'inventory_icon';
    if (lower.includes('equipped')) return 'equipped_appearance';
    if (lower.includes('chathead')) return 'npc_chathead';
    if (lower.includes('map')) return 'location_map';
    if (lower.includes('interface')) return 'game_interface';

    return 'misc';
  }

  // Get file extension from mime type or URL
  getFileExtension(mimeType, url) {
    if (mimeType) {
      const mimeMap = {
        'image/png': '.png',
        'image/jpeg': '.jpg',
        'image/gif': '.gif',
        'image/webp': '.webp',
        'image/svg+xml': '.svg'
      };
      if (mimeMap[mimeType]) return mimeMap[mimeType];
    }

    // Fallback to URL extension
    const urlExtension = url.match(/\.([^.]+)$/);
    return urlExtension ? `.${urlExtension[1]}` : '.png';
  }

  // Create comprehensive indexes for the universe
  async createUniverseIndexes(results) {
    try {
      const indexes = {
        summary: {
          totalPages: results.pages.downloaded,
          totalImages: results.images.downloaded,
          downloadDate: new Date().toISOString(),
          duration: ((new Date() - results.startTime) / 1000 / 60).toFixed(1) + ' minutes'
        },
        namespaces: {},
        imageCategories: {},
        errors: results.errors
      };

      // Count pages by namespace
      const pagesDir = path.join(this.wikiContentDir, 'pages');
      const namespaces = await fs.readdir(pagesDir);

      for (const namespace of namespaces) {
        // Skip system files like .DS_Store
        if (namespace.startsWith('.')) continue;

        const namespacePath = path.join(pagesDir, namespace);

        try {
          const stat = await fs.stat(namespacePath);
          if (stat.isDirectory()) {
            const files = await fs.readdir(namespacePath);
            // Count only JSON files, skip system files
            const jsonFiles = files.filter(file => file.endsWith('.json'));
            indexes.namespaces[namespace] = jsonFiles.length;
          }
        } catch (error) {
          logger.warn(`⚠️ Skipping ${namespace}: ${error.message}`);
        }
      }

      // Count images by category
      const imagesDir = path.join(this.wikiContentDir, 'images');

      try {
        const categories = await fs.readdir(imagesDir);

        for (const category of categories) {
          // Skip system files like .DS_Store
          if (category.startsWith('.')) continue;

          const categoryPath = path.join(imagesDir, category);

          try {
            const stat = await fs.stat(categoryPath);
            if (stat.isDirectory()) {
              const files = await fs.readdir(categoryPath);
              // Count only image files, skip system files and metadata
              const imageFiles = files.filter(file =>
                !file.startsWith('.') &&
                !file.endsWith('.json') &&
                (file.endsWith('.png') || file.endsWith('.jpg') || file.endsWith('.jpeg') || file.endsWith('.gif') || file.endsWith('.webp'))
              );
              indexes.imageCategories[category] = imageFiles.length;
            }
          } catch (error) {
            logger.warn(`⚠️ Skipping image category ${category}: ${error.message}`);
          }
        }
      } catch (error) {
        logger.warn(`⚠️ Images directory not found or empty: ${error.message}`);
        indexes.imageCategories = {};
      }

      // Save comprehensive index
      const indexPath = path.join(this.wikiContentDir, 'indexes', 'universe_index.json');
      await fs.writeFile(indexPath, JSON.stringify(indexes, null, 2), 'utf8');

      logger.info('📊 Universe indexes created successfully');

    } catch (error) {
      logger.error('Error creating universe indexes:', error.message);
      throw error;
    }
  }

  // Sanitize filename for safe file system storage
  sanitizeFilename(title) {
    return title
      .replace(/[<>:"/\\|?*]/g, '_')  // Replace invalid characters (including colons)
      .replace(/\s+/g, '_')           // Replace spaces with underscores
      .replace(/_{2,}/g, '_')         // Replace multiple underscores with single
      .replace(/^_|_$/g, '')          // Remove leading/trailing underscores
      .substring(0, 200);             // Limit length to 200 characters
  }
}

module.exports = OSRSUniverseDownloader;
