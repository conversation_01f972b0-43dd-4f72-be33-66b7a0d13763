#!/usr/bin/env node

/**
 * Quick Restart with Better Telemetry
 * 
 * Restart the download with the fixes for hanging main namespace requests
 */

const { spawn } = require('child_process');

console.log('🔧 Quick Restart - Fixed Hanging Issue');
console.log('=====================================');
console.log('');
console.log('🎯 Fixes applied:');
console.log('   ✅ Added telemetry to main namespace fetching');
console.log('   ✅ Added timeout protection (max 10 requests)');
console.log('   ✅ Added error handling for hanging requests');
console.log('   ✅ Will continue with category pages if main namespace fails');
console.log('');
console.log('📊 Expected results:');
console.log('   - ~11,984 items from Category:Items');
console.log('   - ~1,583 monsters from Category:Monsters');
console.log('   - ~1,190 locations from Category:Locations');
console.log('   - ~231 quests from Category:Quests');
console.log('   - Plus additional categories');
console.log('   - Total: ~15,000+ OSRS pages');
console.log('');
console.log('🚀 Starting fixed download...');
console.log('');

// Start the download
const downloadProcess = spawn('node', ['scripts/downloadOSRSUniverse.js'], {
  stdio: 'inherit',
  cwd: process.cwd()
});

downloadProcess.on('close', (code) => {
  if (code === 0) {
    console.log('');
    console.log('🎉 Download completed successfully!');
    console.log('');
    console.log('📊 Check results:');
    console.log('   ls -la wiki-content/pages/Main/');
    console.log('   node scripts/checkUniverseStatus.js');
  } else {
    console.log('');
    console.log('⚠️  Download stopped or failed');
    console.log('   Check error logs in wiki-content/logs/');
  }
});

downloadProcess.on('error', (error) => {
  console.error('💥 Failed to start download:', error.message);
});
