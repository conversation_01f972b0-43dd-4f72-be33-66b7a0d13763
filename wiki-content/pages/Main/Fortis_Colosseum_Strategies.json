{"title": "Fortis Colosseum/Strategies", "namespace": "Main", "content": "{{Dangerous|instance=yes}}\n\nThe '''Fortis Colosseum''' is a very difficult solo PvM challenge, being a spiritual successor of the [[Inferno]]. Players who are successful will be rewarded with [[<PERSON><PERSON><PERSON>'s quiver]].\n\nBefore attempting it, it is highly recommended that you have the best gear that you can afford and maxed or very high combat stats. However, completions are possible with inexpensive gear, though at least a [[bow of Faerdhinen]] with full [[crystal armour]] is recommended for a first quiver. More importantly, do not expect to complete it on your first few attempts; be mentally prepared to fail a lot and learn from your mistakes. \n\n==Suggested skills==\nThe stats below are suggested as a minimum for completing the Colosseum for the first time. However, having higher stats will be very helpful, particularly with dealing with bad double-south spawns which place heavy pressure on the player to kill at least one NPC before reinforcements arrive.\n*{{SCP|Attack|95+}}\n*{{SCP|Strength|95+}}\n*{{SCP|Defence|95+}}\n*{{SCP|Prayer|85+}}\n*{{SCP|Ranged|95+}}\n**{{SCP|Ranged|97+}} is very helpful for one-shotting the [[Fremennik warband seer]] with a [[venator bow]], [[dragon arrow]]s and [[Rigour]].\n*{{SCP|Magic|94+}}\n**If not using a mage-based setup, {{SCP|Magic|99}} is very helpful for one-shotting the [[Fremennik warband berserker]] with a [[Tumeken's shadow]], [[occult necklace]], [[tormented bracelet]], and one piece of [[Ancestral robes]].\n*{{SCP|Hitpoints|95+}}\n\n==Equipment==\nDue to [[Sol Heredit|Sol Heredit's]] incredibly high ranged and magic defence, a strong slashing melee weapon (i.e., [[abyssal tentacle]] or better) is essential for completing the Colosseum. However, the waves can effectively be cleared with strong ranged or magic setups, and at least one of these options is highly recommended for learners to simplify waves. In particular, dealing with south spawns while on the NW pillar is difficult without a ranged or magic option.\n\nA [[twisted bow]] setup is the strongest option overall for a first quiver; a [[Tumeken's shadow]] setup provides significant QoL by one-shotting the melee frem, but suffers later into the wave from lower damage output against the other NPCs. Note that even with a [[Scythe of vitur]], '''a melee-only setup is extremely difficult to use on a first quiver'''. Learning proper wave-solving procedures, becoming comfortable with the prayer flicking needed for pillar stack solves, and being able to recover from mistakes quickly are more important than any given gear optimisation, '''but not bringing some kind of ranged option will make your first quiver much harder to obtain.'''\n\n<tabber>\nRanged=\nNotes on gear:\n*A [[twisted bow]] is the strongest ranged weapon for Colosseum waves, beating the [[Tumeken's shadow]] by a fair margin but losing to the [[scythe of vitur]]. Against the [[jaguar warrior]], melee weapons do better damage than the twisted bow, so melee the jaguar if it is safe to do so.\n*A [[bow of Faerdhinen]] with crystal armour is a reasonably strong budget setup, though you should avoid taking handicaps that hurt more with low DPS, such as Reentry.\n*As minimal magic switches are brought, runes for [[Death Charge]] and thralls (optional) are much more useful than runes for Ancient Magicks.\n*A 4-way shadow setup is very convenient for one-shotting the melee frem, but it eats into inventory space. For your first quiver, a single [[Sanguinesti staff]], [[Trident of the swamp]] or [[Eye of ayak]] to two-shot the melee frem will suffice. [[Sanguinesti staff]] offers some healing while [[Eye of ayak]] is 3-tick compared to 4-tick for the other two options and [[Trident of the Swamp]] is the most affordable option and easiest to obtain for [[Ironmen]]. You can bring the 4-way shadow if you find supplies are sufficient.\n*You can opt to cut melee switches down to a minimum until you learn to clear waves semi-consistently.\n*The [[Saradomin godsword]] is very useful to have to one-shot the ranged frem with minimal switches, and the special attack can restore large amounts of health and prayer to stabilise a rough wave start. \n*Dumping [[dragon claws]]/[[burning claws]] special attacks is a very effective way to finish the Sol fight once he goes below 10% HP. Claws are also very useful for finishing NPCs before reinforcements spawn. For a first quiver, dragon claws are easier to use than burning claws.\n{{Recommended equipment\n|style = Ranged\n|head1 = {{plink|Torva full helm}}  / {{plink|Masori mask (f)}}\n|head2 = {{plink|Oathplate helm}}{{efn|For a first quiver, the defence from Torva is more useful than the offensive stats from Oathplate.|name=oath}} / {{plink|Crystal helm}}\n|head3 = {{plink|Neitiznot faceguard}} / {{plink|Serpentine helm}} + {{plink|Crystal helm}}\n|neck1 = {{plink|Amulet of blood fury}} / {{plink|Amulet of rancour}}{{efn|Not worth the inventory space on a first quiver, unless you are an iron and limited on blood shards.|name=bf}} + {{plink|Necklace of anguish}}\n|neck2 = {{plink|Amulet of torture}}\n|cape1 = {{plink|Infernal cape}} + {{plink|Dizana's quiver}}\n|cape2 = {{plink|Fire cape}} + {{plink|Ava's assembler}}\n|body1 = {{plink|Torva platebody}} + {{plink|Masori body (f)}}\n|body2 = {{plink|Oathplate chest}}{{efn|For a first quiver, the defence from Torva is more useful than the offensive stats from Oathplate.|name=oath}} + {{plink|Masori body (f)}}\n|body3 = {{plink|Bandos chestplate}} + {{plink|Crystal body}}\n|body4 = {{plink|Fighter torso}} / {{plink|Blood moon chestplate}} + {{plink|Crystal body}}\n|legs1 = {{plink|Torva platelegs}} + {{plink|Masori chaps (f)}}\n|legs2 = {{plink|Oathplate legs}}{{efn|For a first quiver, the defence from Torva is more useful than the offensive stats from Oathplate.|name=oath}} + {{plink|Crystal legs}}\n|legs3 = {{plink|Bandos tassets}} / {{plink|Blood moon tassets}} + {{plink|Crystal legs}}\n|weapon1 = {{plink|Scythe of vitur}} + {{plink|Tumeken's shadow}} + {{plink|Twisted bow}} &{{plink|Venator bow}}\n|weapon2 = {{plink|Soulreaper axe}} / {{plink|Blade of saeldor}} + {{plink|Eye of ayak}} + {{plink|Bow of faerdhinen}} & {{plink|Toxic blowpipe}} \n|weapon3 = {{plink|Abyssal tentacle}} / {{plink|Noxious halberd}}{{efn|Makes the Sol fight significantly more difficult due to its range if Myopia is not taken.}} + {{plink|Sanguinesti staff}} / {{plink|Trident of the swamp}} + {{plink|Bow of faerdhinen}} & {{plink|Eclipse atlatl}}\n|shield1 = {{plink|Elysian spirit shield}} / {{plink|Avernic defender}} (if using a 1H weapon)\n|shield2 = {{plink|Crystal shield}} / {{plink|Dragon defender}} (if using a 1H weapon) \n|ammo1 = {{plink|Dragon arrow}}\n|hands1 = {{plink|Ferocious gloves}} + {{plink|Zaryte vambraces}}\n|hands2 = {{plink|Barrows gloves}}\n|feet1 = {{plink|Avernic treads (max)}}\n|feet2 = {{plink|Primordial boots}}\n|feet3 = {{plink|Aranea boots}}\n|feet4 = {{plink|Echo boots}}\n|ring1 = {{plink|Ultor ring}} / {{plink|Lightbearer}}\n|ring2 = {{plink|Bellator ring}}\n|ring3 = {{plink|Berserker ring (i)}}\n|special1 = {{plink|Saradomin godsword}} + {{plink|Dragon claws}}\n|special2 = {{plink|Burning claws}} / {{plink|Voidwaker}}{{efn|One-shotting melee frem|name=frem}} / {{plink|Elder maul}}{{efn|Sol Heredit only, not recommended for first quiver|name=sol}} / {{plink|Dragon warhammer}}{{efn|name=sol}} / {{plink|Eldritch nightmare staff}}{{efn|name=frem}}\n}}\n{{Notelist}}\n\n==Inventory==\n{| class=\"floatright\"\n|{{Equipment\n|head = Torva full helm\n|cape = Dizana's quiver\n|neck = Necklace of anguish\n|ammo = Dragon arrow\n|weapon = Twisted bow\n|torso = Masori body (f)\n|legs = Masori chaps (f)\n|gloves = Barrows gloves\n|boots = Avernic treads (max)\n|ring = Ultor ring\n}}\n|{{Inventory\n|1=Eye of ayak\n|2=Scythe of vitur\n|3=Infernal cape\n|4=Dragon claws\n|5=Venator bow\n|6=Amulet of blood fury\n|7=Oathplate chest\n|8=Divine rune pouch\n|9=Saradomin godsword\n|10=Saradomin brew(4)\n|11=Super restore(4)\n|12=Super restore(4)\n|13=Saradomin brew(4)\n|14=Saradomin brew(4)\n|15=Super restore(4)\n|16=Super restore(4)\n|17=Saradomin brew(4)\n|18=Saradomin brew(4)\n|19=Super restore(4)\n|20=Super restore(4)\n|21=Bastion potion(4)\n|22=Saradomin brew(4)\n|23=Super restore(4)\n|24=Super restore(4)\n|25=Divine bastion potion(4)\n|26=Super combat potion(4)\n|27=Super restore(4)\n|28=Sanfew serum(4)\n}}\n|-\n| colspan=\"2\" |{{Rune pouch|align=center\n|1=Blood rune\\8000\n|2=Soul rune\\8000\n|3=Death rune\\8000\n}}\n|}\n*4-way mage switch to one-shot the melee frem, or replace with a single [[Eye of ayak]] or [[sanguinesti staff]] / [[trident of the swamp]] to make room for supplies\n*If not using a [[twisted bow]], use a [[venator bow]] with [[dragon arrows]] to one-shot the mage frem, or a [[toxic blowpipe]] with [[dragon darts]]. If using a twisted bow, the venator bow can be substituted with an extra [[Saradomin brew]] or a piece of melee armour.\n*[[Rune pouch]] containing [[death rune]]s, [[blood rune]]s, and [[soul rune]]s for [[Death Charge]]\n*[[Saradomin godsword]] to one-shot ranged frem\n*[[Saradomin brew]]s and [[super restore]]s; the quantity may differ from person to person, depending on how much you [[prayer flick]]\n*[[Sanfew serum]] to remove venom when picking the Mantimayhem II invocation. Noxious halberd special attack can be used alternatively to remove venom if bringing one.\n*1 [[divine bastion potion]] and 1 [[bastion potion]]\n*1 [[divine super combat potion]]\n{{Clear}}\n|-|\nMagic=\nNotes on gear:\n*[[Tumeken's shadow]] is the only viable magic weapon for clearing waves, as enemy NPCs have high magic defences. \n*A [[saturated heart]] is convenient as it can be used to boost more than 4 times in a run, but its effects are lost until the cooldown ends if you drink a dose of [[Saradomin brew]]. \n*You may choose between Ancient Magicks and the Arceuus spellbook for [[death charge]]; blood barraging can help save supplies, though it will not help you in a high-pressure situation. It is better to one-shot the melee frem with the [[Tumeken's shadow]] than to freeze it.\n*You can opt to cut melee switches down to a minimum until you learn to clear waves semi-consistently.\n*The [[Saradomin godsword]] is very useful to have to one-shot the ranged frem with minimal switches, and the special attack can restore large amounts of health and prayer to stabilise a rough wave start. \n*Dumping [[dragon claws]]/[[burning claws]] special attacks is a very effective way to finish the Sol fight once he goes below 10% HP. Claws are also very useful for finishing NPCs before reinforcements spawn. For a first quiver, dragon claws are easier to use than burning claws.\n{{Recommended equipment\n|style = Mage\n|head1 = {{plink|Torva full helm}}  / {{plink|Ancestral hat}}\n|head2 = {{plink|Oathplate helm}}{{efn|For a first quiver, the defence from Torva is more useful than the offensive stats from Oathplate.|name=oath}} / {{plink|Virtus mask}}\n|head3 = {{plink|Neitiznot faceguard}} / {{plink|Serpentine helm}} + {{plink|Virtus mask}}\n|neck1 = {{plink|Amulet of blood fury}} / {{plink|Amulet of rancour}}{{efn|Not worth the inventory space on a first quiver, unless you are an iron and limited on blood shards.|name=bf}} + {{plink|Occult necklace}}\n|neck2 = {{plink|Amulet of torture}} + {{plink|Occult necklace}}\n|cape1 = {{plink|Infernal cape}} + {{plink|Imbued god capes|txt=Imbued god capes|pic=Imbued saradomin cape}}\n|cape2 = {{plink|Fire cape}} + {{plink|Imbued god capes|txt=Imbued god capes|pic=Imbued saradomin cape}}\n|body1 = {{plink|Torva platebody}} + {{plink|Ancestral robe top}}\n|body2 = {{plink|Oathplate chest}}{{efn|For a first quiver, the defence from Torva is more useful than the offensive stats from Oathplate.|name=oath}} + {{plink|Virtus robe top}}\n|body3 = {{plink|Bandos chestplate}} / {{plink|Blood moon chestplate}} + {{plink|Virtus robe top}}\n|body4 = {{plink|Fighter torso}} + {{plink|Virtus robe top}}\n|legs1 = {{plink|Torva platelegs}} + {{plink|Ancestral robe bottom}}\n|legs2 = {{plink|Oathplate legs}}{{efn|For a first quiver, the defence from Torva is more useful than the offensive stats from Oathplate.|name=oath}} + {{plink|Virtus robe bottom}}\n|legs3 = {{plink|Bandos tassets}} / {{plink|Blood moon tassets}} + {{plink|Virtus robe bottom}}\n|weapon1 = {{plink|Tumeken's shadow}} + {{plink|Scythe of vitur}} + {{plink|Venator bow}}\n|weapon2 = {{plink|Tumeken's shadow}} +{{plink|Soulreaper axe}} / {{plink|Blade of saeldor}} + {{plink|Toxic blowpipe}}\n|weapon3 = {{plink|Tumeken's shadow}} +{{plink|Abyssal tentacle}} / {{plink|Noxious halberd}}{{efn|Makes the Sol fight significantly more difficult due to its range if Myopia is not taken.}} +  {{plink|Eclipse atlatl}}\n|shield1 = {{plink|Elysian spirit shield}} / {{plink|Avernic defender}} (if using a 1H weapon)\n|shield2 = {{plink|Crystal shield}} / {{plink|Dragon defender}} (if using a 1H weapon) \n|ammo1 = {{plink|Dragon arrow}}\n|ammo2 = {{plink|Atlatl dart}}\n|hands1 = {{plink|Ferocious gloves}} + {{plink|Confliction gauntlets}}\n|hands2 = {{plink|Barrows gloves}} + {{plink|Tormented bracelet}}\n|feet1 = {{plink|Avernic treads (max)}}\n|feet2 = {{plink|Eternal boots}}{{efn|With a shadow setup, Eternal boots give a sorely needed damage boost to the Shadow, which is the main weapon for waves.|name=eternals}} > {{plink|Primordial boots}} \n|feet3 = {{plink|Aranea boots}}\n|ring1 = {{plink|Ultor ring}} / {{plink|Magus ring}}\n|ring2 = {{plink|Bellator ring}} / {{plink|Lightbearer}}\n|ring3 = {{plink|Berserker ring (i)}}\n|special1 = {{plink|Saradomin godsword}} + {{plink|Dragon claws}}\n|special2 = {{plink|Burning claws}} / {{plink|Elder maul}}{{efn|Sol Heredit only, not recommended for first quiver|name=sol}} / {{plink|Dragon warhammer}}{{efn|name=sol}}\n}}\n{{Notelist}}\n\n==Inventory==\n{| class=\"floatright\"\n|{{Equipment\n|head = Ancestral hat\n|cape = Imbued saradomin cape\n|neck = Occult necklace\n|ammo = Dragon arrow\n|weapon = Tumeken's shadow\n|torso = Ancestral robe top\n|legs = Ancestral robe bottom\n|gloves = Confliction gauntlets\n|boots = Avernic treads (max)\n|ring = Magus ring\n}}\n|{{Inventory\n|1=Venator bow\n|2=Scythe of vitur\n|3=Ultor ring\n|4=Amulet of blood fury\n|5=Saradomin godsword\n|6=Ferocious gloves\n|7=Torva platebody\n|8=Infernal cape\n|9=Kodai wand \n|10=Dragon claws\n|11=Super combat potion(4)\n|12=Super restore(4)\n|13=Saradomin brew(4)\n|14=Saradomin brew(4)\n|15=Super restore(4)\n|16=Super restore(4)\n|17=Saradomin brew(4)\n|18=Saradomin brew(4)\n|19=Super restore(4)\n|20=Super restore(4)\n|21=Saradomin brew(4)\n|22=Forgotten brew(4)\n|23=Super restore(4)\n|24=Super restore(4)\n|25=Saturated heart\n|26=Divine rune pouch\n|27=Super restore(4)\n|28=Sanfew serum(4)\n}}\n|-\n| colspan=\"2\" |{{Rune pouch|align=center\n|1=Soul rune\\8000\n|2=Blood rune\\8000\n|3=Death rune\\8000\n|4=Water rune\\8000\n}}\n|}\n*Melee switches as comfortable\n*[[Scythe of vitur]] or a strong slash weapon\n*[[Rune pouch]] containing runes for [[Ancient Magicks]] or [[Death Charge]]\n*If using Ancients, bring a weapon that can autocast, such as a [[blood ancient sceptre]], [[nightmare staff]], or [[kodai wand]].\n*[[Saradomin godsword]] to one-shot ranged frem\n*[[Saradomin brew]]s and [[super restore]]s; the quantity may differ from person to person, depending on how much you [[prayer flick]]\n*[[Sanfew serum]] to remove venom when picking the Mantimayhem II invocation. Noxious halberd special attack can be used alternatively to remove venom if bringing one.\n*[[Saturated heart]]\n*1 [[divine super combat potion]]\n*1 [[forgotten brew]] to re-boost if forced to drink [[Saradomin brew]]\n{{Clear}}\n|-|\nBudget=\nNotes on gear:\n*In a budget setup, your best DPS will come from your melee setup. You should try to melee where possible, but make sure to use the ranged setup for tough situations.\n*[[Osmumten's fang]] is slightly better DPS than the [[Noxious halberd]] on all NPCs except Sol Heredit; however, many players enjoy using the halberd for its reach and utility, such as trivialising Solarflare. Reaching Sol with a Noxious halberd without having taken Myopia during the waves will result in a significantly harder fight.\n*If you have a [[sanguinesti staff]], you can uncharge it for blood runes and bring a [[book of the dead]] in your offhand to use both thralls and Death Charge.\n*Without access to a [[bow of Faerdhinen]], your next-best ranged option is a [[scorching bow]] with [[Masori armour]]. The [[hunters' sunlight crossbow]] underperforms the scorching bow, and its low range can make Myopia III a serious detriment. '''You are strongly encouraged to obtain a bofa setup.'''\n*The [[Saradomin godsword]] is very useful to have to one-shot the ranged frem with minimal switches, and the special attack can restore large amounts of health and prayer to stabilise a rough wave start. \n*Dumping [[dragon claws]]/[[burning claws]] special attacks is a very effective way to finish the Sol fight once he goes below 10% HP. Claws are also very useful for finishing NPCs before reinforcements spawn. For a first quiver, dragon claws are easier to use than burning claws.\n{{Recommended equipment\n|style = Budget\n|head1 = {{plink|Serpentine helm}}{{efn|Better than the Neitiznot faceguard as the additional strength bonus from the faceguard does not give either melee weapon a max hit.}} + {{plink|Crystal helm}}\n|head2 = {{plink|Neitiznot faceguard}} + {{plink|Crystal helm}}\n|neck1 = {{plink|Amulet of blood fury}} + {{plink|Necklace of anguish}}\n|cape1 = {{plink|Infernal cape}}{{efn|Ava's assembler gives extremely minimal bonuses to the bofa and is not worth bringing.}}\n|cape2 = {{plink|Fire cape}}\n|body1 = {{plink|Fighter torso}} / {{plink|Blood moon chestplate}} + {{plink|Crystal body}}\n|legs1 = {{plink|Crystal legs}}\n|weapon1 = {{plink|Osmumten's fang}} / {{plink|Noxious halberd}}{{efn|Makes the Sol fight significantly more difficult due to its range if Myopia is not taken.}} + {{plink|Bow of faerdhinen}} + {{plink|Sanguinesti staff}} / {{plink|Trident of the swamp}} + {{plink|Venator bow}} / {{plink|Toxic blowpipe}}\n|shield1 = {{plink|Dragon defender}}\n|ammo1 = {{plink|Dragon arrow}} (if using a [[venator bow]]) / {{plink|Rada's blessing 4}} (if using a [[toxic blowpipe]])\n|hands1 = {{plink|Barrows gloves}}\n|feet1 = {{plink|Echo boots}}\n|feet2 = {{plink|Aranea boots}}\n|feet3 = {{plink|Dragon boots}}\n|ring1 = {{plink|Berserker ring (i)}} / {{plink|Lightbearer}}{{efn|If using a Voidwaker to one-shot the melee frem}}\n|special1 = {{plink|Saradomin godsword}} + {{plink|Dragon claws}}\n|special2 = {{plink|Burning claws}} / {{plink|Voidwaker}}{{efn|One-shotting melee frem|name=frem}} / {{plink|Dragon warhammer}}{{efn|Sol Heredit only, not recommended for first quiver|name=sol}}\n}}\n{{Notelist}}\n\n==Inventory==\n{| class=\"floatright\"\n|{{Equipment\n|head = Serpentine helm\n|cape = Infernal cape\n|neck = Amulet of blood fury\n|ammo = Dragon arrow\n|weapon = Trident of the swamp\n|shield = Dragon defender\n|torso = Fighter torso\n|legs = Crystal legs\n|gloves = Barrows gloves\n|boots = Echo boots\n|ring = Berserker ring (i)\n}}\n|{{Inventory\n|1=Venator bow\n|2=Osmumten's fang\n|3=Bow of faerdhinen\n|4=Necklace of anguish\n|5=Saradomin godsword\n|6=Dragon claws\n|7=Crystal body\n|8=Crystal helm\n|9=Saradomin brew(4) \n|10=Saradomin brew(4)\n|11=Super restore(4)\n|12=Super restore(4)\n|13=Saradomin brew(4)\n|14=Saradomin brew(4)\n|15=Super restore(4)\n|16=Super restore(4)\n|17=Saradomin brew(4)\n|18=Saradomin brew(4)\n|19=Super restore(4)\n|20=Super restore(4)\n|21=Super combat potion(4)\n|22=Ranging potion(4)\n|23=Super restore(4)\n|24=Super restore(4)\n|25=Divine super combat potion(4)\n|26=Divine ranging potion(4)\n|27=Abyssal tentacle\n|28=Divine rune pouch\n}}\n|-\n| colspan=\"2\" |{{Rune pouch|align=center\n|1=Soul rune\\16000\n|2=Blood rune\\16000\n|3=Death rune\\16000\n}}\n|}\n*\n*A [[venator bow]] with [[dragon arrows]], or [[toxic blowpipe]] with [[dragon darts]] for the mage frem\n*[[Saradomin godsword]] to one-shot ranged frem\n*[[Dragon claws]] or [[burning claws]] for special attacks and ending the Sol fight\n*[[Abyssal tentacle]] for Sol; you may opt to bring this even if you use a [[noxious halberd]], as not being offered Myopia in the waves will make Sol difficult to control with the halberd's reach\n*[[Rune pouch]] containing runes for [[Death Charge]]\n*[[Saradomin brew]]s and [[super restore]]s; the quantity may differ from person to person, depending on how much you [[prayer flick]]\n{{Clear}}\n|-|\nMelee only (not recommended for first quiver)=\nNotes on gear:\n*'''A melee-only setup is strongly not recommended for first quivers, as dealing with south spawns and certain pillar stacks on NW pillar are nigh-impossible on a first quiver.''' Meleeing NPCs also '''increases handicap difficulty''' by making it more troublesome to manage Solarflares, Javelin Colossus sky javelins and Reentry pools, and Volatility explosions. Excessive supplies in a melee-only setup will rarely save you from mistakes, as NPCs in the Colosseum hit extremely hard and accurately.\n*If attempting a melee-only first quiver, a [[scythe of vitur]] should be considered mandatory, and you may want to consider learning to play around the SW pillar. Prepare to face the equivalent of '''double south spawns at nearly 4x frequency.'''\n*A [[scythe of vitur]] with good strength gear is the highest-DPS option in the game for every NPC except the [[serpent shaman]]. You may wish to bring a one-handed weapon such as a [[Ghrazi rapier]] or an [[Osmumten's fang]] for the shamans.\n*A strength ring is highly recommended, as the [[scythe of vitur]] benefits immensely from increased max hits. However, a [[lightbearer]] is also an option to allow for more special attacks.\n*As a melee-only setup has excessive space for supplies, a 4-way [[Tumeken's shadow]] switch is very helpful for one-shotting the melee frem.\n*An [[amulet of blood fury]] is very useful for healing through the waves and in the Sol fight, especially with the [[scythe of vitur]]. However, it will not save you from mistakes.\n*As minimal magic switches are brought, runes for [[Death Charge]] and thralls (optional) are much more useful than runes for Ancient Magicks.\n*The [[Saradomin godsword]] is very useful to have to one-shot the ranged frem with minimal switches, and the special attack can restore large amounts of health and prayer to stabilise a rough wave start. \n*Dumping [[dragon claws]]/[[burning claws]] special attacks is a very effective way to finish the Sol fight once he goes below 10% HP. Claws are also very useful for finishing NPCs before reinforcements spawn. For a first quiver, dragon claws are easier to use than burning claws.\n{{Recommended equipment\n|style = Melee\n|head1 = {{plink|Torva full helm}} > {{plink|Oathplate helm}} \n|head2 = {{plink|Neitiznot faceguard}} / {{plink|Serpentine helm}}\n|neck1 = {{plink|Amulet of blood fury}} + {{plink|Amulet of rancour}}{{efn|Not worth the inventory space on a first quiver, unless you are an iron and limited on blood shards.|name=bf}}\n|cape1 = {{plink|Infernal cape}}\n|cape2 = {{plink|Fire cape}}\n|body1 = {{plink|Torva platebody}} \n|body2 = {{plink|Oathplate chest}}\n|body3 = {{plink|Bandos chestplate}}\n|body4 = {{plink|Fighter torso}} / {{plink|Blood moon chestplate}}\n|legs1 = {{plink|Torva platelegs}}\n|legs2 = {{plink|Oathplate legs}}\n|legs3 = {{plink|Bandos tassets}}\n|legs4 = {{plink|Blood moon tassets}}\n|weapon1 = {{plink|Scythe of vitur}} + {{plink|Tumeken's shadow}} + {{plink|Venator bow}}\n|weapon2 = {{plink|Soulreaper axe}} + {{plink|Sanguinesti staff}} + {{plink|Toxic blowpipe}}\n|weapon3 = {{plink|Osmumten's fang}}{{efn|In Bandos-tier gear, the Fang outperforms even a blade of saeldor on every NPC except Sol Heredit.}} + {{plink|Abyssal tentacle}} / {{plink|Noxious halberd}} {{efn|Makes the Sol fight significantly more difficult due to its range if Myopia is not taken.}} + {{plink|Trident of the swamp}} + {{plink|Eclipse atlatl}}\n|shield1 = {{plink|Elysian spirit shield}} / {{plink|Avernic defender}} \n|shield2 = {{plink|Crystal shield}} / {{plink|Dragon defender}} \n|ammo1 = {{plink|Dragon arrow}}\n|ammo2 = {{plink|Atlatl dart}}\n|hands1 = {{plink|Ferocious gloves}}\n|hands2 = {{plink|Barrows gloves}}\n|feet1 = {{plink|Avernic treads (max)}}\n|feet2 = {{plink|Primordial boots}}\n|feet3 = {{plink|Aranea boots}}\n|feet4 = {{plink|Dragon boots}}\n|ring1 = {{plink|Ultor ring}} / {{plink|Lightbearer}} (if not using a scythe of vitur)\n|ring2 = {{plink|Bellator ring}}\n|ring3 = {{plink|Berserker ring (i)}}\n|special1 = {{plink|Saradomin godsword}} + {{plink|Dragon claws}}\n|special2 = {{plink|Burning claws}} / {{plink|Voidwaker}}{{efn|One-shotting melee frem|name=frem}} / {{plink|Elder maul}}{{efn|Sol Heredit only, not recommended for first quiver|name=sol}} / {{plink|Dragon warhammer}}{{efn|name=sol}} / {{plink|Eldritch nightmare staff}}{{efn|name=frem}}\n}}\n{{Notelist}}\n\n==Inventory==\n{| class=\"floatright\"\n|{{Equipment\n|head = Torva full helm\n|cape = Infernal cape\n|neck = Amulet of blood fury\n|ammo = Dragon arrow\n|weapon = Scythe of vitur\n|torso = Torva platebody\n|legs = Torva platelegs\n|gloves = Ferocious gloves\n|boots = Avernic treads (max)\n|ring = Ultor ring\n}}\n|{{Inventory\n|1=Tumeken's shadow\n|2=Occult necklace\n|3=Venator bow\n|4=Dragon claws\n|5=Tormented bracelet\n|6=Ancestral hat\n|7=Saradomin godsword\n|8=Dinh's bulwark\n|9=Saradomin brew(4)\n|10=Saradomin brew(4)\n|11=Super restore(4)\n|12=Super restore(4)\n|13=Saradomin brew(4)\n|14=Saradomin brew(4)\n|15=Super restore(4)\n|16=Super restore(4)\n|17=Saradomin brew(4)\n|18=Saradomin brew(4)\n|19=Super restore(4)\n|20=Super restore(4)\n|21=Divine super combat potion(4)\n|22=Saradomin brew(4)\n|23=Super restore(4)\n|24=Super restore(4)\n|25=Divine super combat potion(4)\n|26=Super combat potion(4)\n|27=Sanfew serum(4)\n|28=Divine rune pouch\n}}\n|-\n| colspan=\"2\" |{{Rune pouch|align=center\n|1=Blood rune\\8000\n|2=Soul rune\\8000\n|3=Death rune\\8000\n}}\n|}\n*4-way mage switch to one-shot the melee frem\n*A [[venator bow]] with [[dragon arrows]] to one-shot the mage frem, or a [[toxic blowpipe]] with [[dragon darts]]\n*[[Rune pouch]] containing [[death rune]]s, [[blood rune]]s, and [[soul rune]]s for [[Death Charge]]\n*[[Saradomin godsword]] to one-shot ranged frem\n*[[Saradomin brew]]s and [[super restore]]s; the quantity may differ from person to person, depending on how much you [[prayer flick]]\n*[[Sanfew serum]] to remove venom when picking the Mantimayhem II invocation. Noxious halberd special attack can be used alternatively to remove venom if bringing one.\n*2 [[divine super combat potion]]s and 1 [[super combat potion]] (to restore boosts after brewing)\n{{Clear}}\n|-|\nHigh efficiency=\nNotes on gear:\n*This assumes you are comfortable with completing the Colosseum and are capable of, or aiming to do consistent sub-20 minute runs.\n*The [[venator bow]] should be used whenever triple bounces are possible - mainly on waves 1-6, and on 10-11. Scything large NPCs is the next-best DPS, though the [[twisted bow]] is still useful for tick-filling and tight situations.\n*The [[venator ring]] gives 3 max hits for the [[venator bow]], but can be dropped for more supplies.\n*You can opt to camp the [[amulet of blood fury]] for more supplies.\n*Instead of the 4-tick weapon, you can still opt to bring a [[Saradomin godsword]], or simply scythe the ranged frem in max melee. This will require you to free up one more inventory slot.\n*Adjust other supplies according to your preference. With some lazy flicking of offensive prayers, 7 [[super restores]] is a comfortable amount of prayer to have per KC.\n*You can bring a [[saturated heart]] instead of the [[Ancestral hat]] to cut a switch, but [[brewing|Saradomin brew]] will render you unable to one-shot the melee frem until the heart boost cooldown ends.\n*The difference between [[burning claws]] and [[dragon claws]] is very minimal for most players. To use burning claws effectively, you have to avoid staying on 100% special attack energy, and do your best to get the maximum burn time on NPCs.\n{{Recommended equipment\n|style = High efficiency\n|head1 = {{plink|Torva full helm}} + {{plink|Masori mask (f)}}\n|head2 = {{plink|Neitiznot faceguard}} + {{plink|Masori mask}}\n|neck1 = {{plink|Amulet of rancour}} + {{plink|Amulet of blood fury}} + {{plink|Necklace of anguish}}\n|neck2 = {{plink|Amulet of blood fury}} + {{plink|Necklace of anguish}}\n|cape1 = {{plink|Infernal cape}} + {{plink|Dizana's quiver}}\n|body1 = {{plink|Oathplate chest}} > {{plink|Torva platebody}} + {{plink|Masori body (f)}}\n|body2 = {{plink|Bandos chestplate}} + {{plink|Masori body}}\n|body3 = {{plink|Fighter torso}} / {{plink|Blood moon chestplate}} + {{plink|Crystal body}}\n|legs1 = {{plink|Oathplate legs}} > {{plink|Torva platelegs}} + {{plink|Masori chaps (f)}}\n|legs2 = {{plink|Bandos tassets}} + {{plink|Masori chaps}}\n|legs3 = {{plink|Blood moon tassets}} + {{plink|Crystal legs}}\n|weapon1 = {{plink|Scythe of vitur}} + {{plink|Tumeken's shadow}} + {{plink|Twisted bow}} + {{plink|Venator bow}}\n|ammo1 = {{plink|Dragon arrow}}\n|hands1 = {{plink|Ferocious gloves}} + {{plink|Zaryte vambraces}}\n|hands2 = {{plink|Barrows gloves}}\n|feet1 = {{plink|Avernic treads (max)}}\n|feet2 = {{plink|Primordial boots}}\n|feet3 = {{plink|Aranea boots}}\n|ring1 = {{plink|Ultor ring}}\n|ring2 = {{plink|Bellator ring}}\n|ring3 = {{plink|Berserker ring (i)}}\n|special1 = {{plink|Burning claws}} / {{plink|Dragon claws}} + {{plink|Elder maul}} / {{plink|Dragon warhammer}}\n}}\n{{Notelist}}\n\n==Inventory==\n{| class=\"floatright\"\n|{{Equipment\n|head = Torva full helm\n|cape = Infernal cape\n|neck = Amulet of rancour\n|ammo = Dragon arrow\n|weapon = Abyssal tentacle\n|shield = Book of the dead\n|torso = Oathplate chest\n|legs = Oathplate legs\n|gloves = Ferocious gloves\n|boots = Avernic treads (max)\n|ring = Ultor ring\n}}\n|{{Inventory\n|1=Twisted bow\n|2=Masori body (f)\n|3=Masori mask (f)\n|4=Dizana's quiver\n|5=Zaryte vambraces\n|6=Masori chaps (f)\n|7=Necklace of anguish\n|8=Dragon claws\n|9=Tumeken's shadow\n|10=Confliction gauntlets\n|11=Venator bow\n|12=Amulet of blood fury\n|13=Surge potion\n|14=Occult necklace\n|15=Scythe of vitur\n|16=Elder maul\n|17=Divine super combat potion(4)\n|18=Divine bastion potion(4)\n|19=Saradomin brew(4)\n|20=Guthix rest(4)\n|21=Sanfew serum(4)\n|22=Sanfew serum(4)\n|23=Sanfew serum(4)\n|24=Sanfew serum(4)\n|25=Sanfew serum(4)\n|26=Sanfew serum(4)\n|27=Sanfew serum(4)\n|28=Divine rune pouch\n}}\n|-\n| colspan=\"2\" |{{Rune pouch|align=center\n|1=Fire rune\\16000\n|2=Aether rune\\16000\n|3=Death rune\\16000\n|4=Blood rune\\16000\n}}\n|}\n*Strong equipment to complement a [[scythe of vitur]] or [[twisted bow]]\n*6-, 7-, or 8-way [[venator bow]] switch; a [[venator ring]] can be taken as the 8th switch if not bringing [[Avernic treads (max)]]\n*Thralls and [[Death Charge]]\n*4-tick weapon to one-shot ranged frem; a [[Saradomin godsword]] may also be used\n*[[Saradomin brew]](s) (if needed) and [[super restore]]s; the quantity may differ from person to person, depending on how much you [[prayer flick]]\n*[[Sanfew serum]] to remove venom when picking the Mantimayhem II invocation. Noxious halberd special attack can be used alternatively to remove venom if bringing one.\n*1 [[divine ranging potion]] - as your Defence level will be boosted from the super combat potion, a [[divine bastion potion]] is unnecessary\n*1 [[divine super combat potion]]\n\nIf you are pushing to go faster but aren't comfortable with these supplies, drop the surge potion, Masori mask, and maybe even consider camping the Amulet of blood fury, or running Barrows gloves.\n{{Clear}}\n</tabber>\n\n<gallery mode=\"packed\" heights=\"500\" style=\"text-align:centre\">\nFile:Colosseum DPS table.png|DPS comparison on each enemy for different setups, assuming 99s in all offensive stats, potion boosts, and usage of the highest-tier offensive prayers. Visit the [https://tools.runescape.wiki/osrs-dps/ DPS calculator] to calculate the results for your current loadout.\n</gallery>\n\n== Monsters ==\n\n===Fremennik Warband [[Fremennik warband berserker|Berserker]], [[Fremennik warband seer|Seer]], and [[Fremennik warband archer|Archer]]===\n{|class=\"wikitable floatright\" width=\"25%\"\n!{{SCP|Combat}} Max hit\n!{{SCP|Hitpoints}}\n!{{SCP|Magic}}\n!Size\n!Attack speed\n|-\n|12 (seer) <br/> 14 (ranger) <br/> 29 (berserker)\n|50 (seer) <br/> 50 (ranger) <br/> 48 (berserker)\n|110\n|1x1\n|6\n|}\n\n<gallery>\nFile:Fremennik warband berserker.png\nFile:Fremennik warband seer.png\nFile:Fremennik warband archer.png\n</gallery>\n\nThe Fremennik warband are the weakest monsters in the Colosseum, but can quickly overwhelm new players. Similarly to the [[Jal-Nib]]s from the Inferno, the warband will spawn in the centre of the arena, though unlike the nibs, they will immediately dart towards the player upon spawning. Despite the archer and seer using ranged weapons, the trio will only attack upon reaching '''melee range''' of the player. Upon reaching the player, the trio will begin attacking on different ticks, starting with melee, magic then ranged. Learners frequently sabotage themselves by sacrificing clicks to walk away from frems - it is worth standing still and taking damage from frems to kill them as fast as possible. This is especially important when enemies have line of sight on you when they spawn, where wasting time on killing frems can be fatal. Kill the '''Berserker''' first, followed by the '''Seer''', and finally the '''Archer'''.\n\nDespite their weapons, their combat stats are identical; 50 hitpoints (48 for the Berserker), with an Attack, Strength, Ranged, and Magic level of 110, and 80 Defence. The seer has a max hit of 12, the archer has a max hit of 14 and the berserker has a max hit of 29. The trio are very accurate regardless of the player's defensive gear, so it is necessary to kill them once it is safe to do so to minimise damage taken.\n\nWhen using a combat style corresponding to their weakness, the player's attacks will always hit and for a [[maximum hit]]. The only exception is [[Ice Barrage]] on the berserker; a minimum magic attack bonus of +104 and [[Augury]] is needed to guarantee a freeze on it and the other frems. The table below shows the strongest options for each NPC.\n\n{| class=\"wikitable sortable\" style=width:960px;\n! style=\"width: 20%\" |Frem\n!style=\"width: 40%\" |Weapon\n!Details\n|-\n| rowspan=\"3\" |[[File:Fremennik warband berserker.png|center|width=124x124]]\n|{{plink|Tumeken's shadow}} + {{plink|Confliction gauntlets}} + {{plink|Avernic treads (max)}} with {{plink|Saturated heart}} or {{plink|Occult necklace}}\n|This is the simplest switch capable of one-shotting the melee frem without use of a special attack, and also requiring no prayer usage. If you use a Saturated heart, drinking [[Saradomin brew]] will prevent you from reaching the one-shot threshold until the heart comes off cooldown.\n|-\n|{{plink|Eye of ayak}} x2 > </br> {{plink|Sanguinesti staff}}, {{plink|Trident of the swamp}}, {{plink|Trident of the seas}} x2\n|The two-shot threshold changes with Magic level; see each weapon's respective page. You need to hit a 24 to two-shot the melee frem. The Eye of ayak is slightly better due to its faster attack speed.\n|-\n|[[File:Ice Barrage.png]] [[Ice barrage]], [[File:Blood Barrage.png]] [[Blood barrage]], or [[File:Undead_Grasp.png]] [[Undead grasp]] x2\n|Slower than using a powered staff. [[Ice Barrage]] needs a minimum magic attack bonus of +104 and {{plink|Augury}}, or +140 without Augury to guarantee a freeze. '''Do not barrage the other frems after the melee is dead!'''\n|-\n| rowspan=\"3\" |[[File:Fremennik warband seer.png|center|width=118x118]]\n|{{plink|Venator bow}} + [[File:Dragon arrow 5.png]] [[Dragon arrows]] + {{plink|Rigour}}\n|The only weapon that can one-shot the frem with a one-way switch at base stats.\n|-\n|{{plink|Twisted bow}}+ [[File:Dragon arrow 5.png]] [[Dragon arrows]] + {{plink|Necklace of anguish}} + {{plink|Masori body}} + {{plink|Rigour}}\n|If potted, fewer ranged switches are needed. Slower than the venator bow.\n|-\n|{{plink|Toxic blowpipe}} with {{plink|Dragon dart}}s + {{plink|Necklace of anguish}} or {{plink|Masori body}} + {{plink|Rigour}} x2\n|If potted, fewer ranged switches are needed. You can use a special attack to get some extra healing, if needed.\n|-\n|[[File:Fremennik warband archer.png|center|width=134x134]]\n|{{plink|Saradomin godsword}} special attack on Aggressive + {{plink|Piety}} + {{plinkp|Barrows gloves}} {{plinkp|Berserker ring (i)}} {{plinkp|Dragon boots}} {{plinkp|Fire cape}} 28 strength bonus\n|Extremely useful even for experienced players due to the guaranteed large heal in the hardest part of the wave. If potted, fewer melee switches are needed.\n|}\n\n====Other options for the Berserker (at 99 Magic)====\n* [[Tumeken's shadow]] with an [[Occult necklace]], [[Tormented bracelet]], and either a [[Saturated heart]] boost or any piece of [[Ancestral robes]], preferably an [[Ancestral hat]] for minimal defence loss (50) - drinking [[Saradomin brew]] will remove a heart boost\n* [[Eldritch nightmare staff|Eldritch]] or [[Volatile nightmare staff]] special attack (50, 66)\n* [[Voidwaker]] special attack with 99 strength, Piety, and an additional 15 [[Strength bonus]] (48)  (can also kill Archer with super combat, Piety, and 61 additional [[Strength bonus]])\n* Any [[surge spell]] (24 x2)\n* The [[Dragon_crossbow#Special_attack|Dragon crossbow]] special attack can always one-shot both the seer and berserker at the same time if the player's normal max hit with the crossbow's regular attacks is at least 42. This is possible at a much lower ranged level than the 95 required to one shot the Seer with a venator bow or dark bow on accurate. The berserker will always line up next to the seer when it gets close, but it may go around the pillar before doing so, and may break formation if you walk next to a wall just as you attack.\n\n====Other options for the Seer (at 99 Range with [[Rigour]])====\n\n* [[Eclipse atlatl]] with [[Strength bonus|melee strength bonus]] gear (25x2)\n\n====Other options for the Archer (at 99 Strength with [[Piety]])====\n\n* An [[Abyssal whip]] or stronger in full melee gear with a super combat potion (50+)\n* [[Scythe of vitur]] in full Torva, with [[Amulet of blood fury]] (50), or [[Amulet of rancour]] / in [[Torva full helm]] + [[Oathplate chest]] + [[Oathplate legs]]\n\nIf the handicap '''Quartet''' is active, a random member will spawn alongside the trio, causing them to spawn in a small diamond-like formation.\n\n===Serpent shaman===\n{|class=\"wikitable floatright\" width=\"25%\"\n!{{SCP|Combat}} Max hit\n!{{SCP|Hitpoints}}\n!{{SCP|Magic}}\n!Size\n!Attack speed\n|-\n|27\n|125\n|220\n|1x1\n|5\n|}\n\n[[File:Serpent shaman.png|left|25px]]\n\n[[Serpent shaman]]s (also known as magers) are simple enemies in the Colosseum that use a moderately strong magic attack against the player. They have a range of 10 tiles, and no other special mechanics. However, they are particularly troublesome enemies due to their small size and high accuracy, especially when handling other monsters.\n\n===Jaguar warrior===\n{|class=\"wikitable floatright\" width=\"25%\"\n!{{SCP|Combat}} Max hit\n!{{SCP|Hitpoints}}\n!{{SCP|Magic}}\n!Size\n!Attack speed\n|-\n|47 per hit (3 hits)\n|125\n|100\n|2x2\n|5\n|}\n\n[[File:Jaguar warrior.png|left|25px]]\n\n[[Jaguar warrior]]s (also known as meleers) are enemies in the Colosseum armed with a pair of claws. These enemies only appear as reinforcements from waves 1-6.\n\nTheir claws will allow them to strike the player three times per attack. Each of these attacks makes their own independent roll on accuracy and damage like that of [[nail beast]]s. With a maximum hit of 47 and high melee stats, this means that the warrior can easily kill players, potentially dealing up to 141 damage in a single attack. Therefore, keep Protect Melee on at '''all times''' if the warrior is not in position and isolate them from other enemies to avoid such a scenario.\n\n===Javelin Colossus===\n{|class=\"wikitable floatright\" width=\"25%\"\n!{{SCP|Combat}} Max hit\n!{{SCP|Hitpoints}}\n!{{SCP|Magic}}\n!Size\n!Attack speed\n|-\n|48\n|220\n|225\n|3x3\n|5\n|}\n\n[[File:Javelin Colossus.png|left|63x63px]]\n[[Javelin Colossus|Javelin Colossi]] (also known as rangers) are one of the more dangerous enemies in the Colosseum. They attack by throwing a pair of javelins at the player, which can deal up to 48 damage. They also have an incredibly long attack range of 15 tiles, making them impossible to safespot even when other enemies are in front of it.\n\nEvery five attacks, the colossus will launch a javelin into the air, which will proceed to land on the player's position 6 ticks afterwards. ''If they do not move out of the way'', they will take up to 40 [[typeless]] damage.  \n\nThe '''Reentry 1''' modifier causes the special attack to leave behind a pool of [[Molten Sand|molten sand]] on the targeted tile, which will deal up to 15 damage every two ticks while standing on them. This is cleared at the end of the wave.\n\nThe '''Reentry 2''' modifier causes the special attack to leave behind a pool of molten sand on the targeted tile, along with the tile to its south-west (or a random tile if that tile is blocked/covered). This also makes the sand '''permanent''', making players be cautious about how they place the special attack to avoid ragging vital tiles.\n\nThe '''Reentry 3''' modifier causes the special attack to leave behind a pool of molten sand on the targeted tile, the tile to its south-west (or a random tile if that tile is blocked/covered), and then the tile to its west (or a random tile if that tile is blocked/covered). As with the previous level, the sand is permanent.\n\n===Manticore===\n{|class=\"wikitable floatright\" width=\"25%\"\n!{{SCP|Combat}} Max hit\n!{{SCP|Hitpoints}}\n!{{SCP|Magic}}\n!Size\n!Attack speed\n|-\n|34 per hit (3 hits)\n|250\n|300\n|3x3\n|10\n|}\n\n[[File:Manticore.png|left|56x56px]]\n[[Manticore]]s are another dangerous enemy in the Colosseum. Unlike other monsters, the Manticore will charge up a three-hit attack. The attack arrangement is dependent on spawn, and it will utilise a magic-ranged or ranged-magic as the first two attacks in sequence; the third attack is always melee. The magic attack is a bright blue orb, the ranged attack is a green arrowhead, and the melee attack is an orange spiky ball. These attacks are launched one tick after each other, followed by a 7-tick lull while the Manticore recharges. Each manticore orb is less damaging and accurate than an attack from a Javelin Colossus or Shockwave Colossus, but being hit by the whole salvo can kill you from full health.\n\nBecause of this, manticores can be potentially dangerous when paired with colossi, such as a magic starting manticore with a javelin colossi behind it, as this will require alternate flicking to avoid taking damage from them.\n\n'''Beware:''' Despite their visual similarity to the [[The Leviathan|The Leviathan's]] projectile attacks, '''you must pray accordingly against projectiles before they are launched''', as opposed to being able to pray against the projectiles while they are mid-flight.\n\nThe '''Mantimayhem 1''' modifier changes the Manticores damage output, causing 2 attacks per style for a total of 6, but not changing how you would handle prayer flicking them. These attacks roll separate accuracy checks and damage.\n\nThe '''Mantimayhem 2''' modifier causes the Manticores attacks to inflict [[venom]] upon an unprotected hit, though it clears at the end of the wave. With this modifier, a [[sanfew serum]] is necessary to avoid taking unnecessary damage while clearing a wave, especially if envenomed early. Other forms of venom curing are generally unhelpful due to the nature of the fight, but Noxious halberd special attack can be used if bringing one.\n\nThe '''Mantimayhem 3''' modifier will unlock any pattern of the manticore's attacks, allowing for attack patterns such as Melee-Mage-Ranged, Ranged-Melee-Mage, etc. This can adversely affect the way pillar solves work and makes them extremely dangerous due to their increased damage and venoming capabilities. \n\nIf one manticore has chosen its attack pattern and the other enters its line of sight (15 tiles), the second manticore will match the pattern of the first manticore. However, if the first manticore is killed before the second manticore can pick its attack pattern, it will choose a random attack pattern instead.\n\nWhen a manticore attacks, any other manticore that is ready to attack (i.e., finished its full 10-tick charge-up) will have its attack delayed by 5 ticks. '''Manticores can overlap attacks on the player if one of them is not yet ready to attack when the other attacks.'''\n\n===Shockwave Colossus===\n{|class=\"wikitable floatright\" width=\"25%\"\n!{{SCP|Combat}} Max hit\n!{{SCP|Hitpoints}}\n!{{SCP|Magic}}\n!Size\n!Attack speed\n|-\n|56\n|125\n|350\n|3x3\n|5\n|}\n\n[[File:Shockwave Colossus.png|left|62x62px]]\n[[Shockwave Colossus|Shockwave Colossi]] (occasionally known as shockers) are enemies in the Colosseum. They attack using magic, and much like their ranged counterparts, they have a very long attack range of 15 tiles. While they are more accurate and damaging than them, they do not have any special mechanics and are less durable.\n\nIf the handicap '''Dynamic Duo''' is active, players will have to face two of these enemies when they appear, and this second colossus will spawn around the location of the first.\n\n===Minotaur===\n{|class=\"wikitable floatright\" width=\"25%\"\n!{{SCP|Combat}} Max hit\n!{{SCP|Hitpoints}}\n!{{SCP|Magic}}\n!Size\n!Attack speed\n|-\n|74\n|225\n|250\n|3x3\n|5\n|}\n\n[[File:Minotaur (Fortis Colosseum).png|left|25px]]\n\n[[Minotaur (Fortis Colosseum)|Minotaurs]] are the final common enemy encountered in the Colosseum, starting from wave 7 and ending at 11 as reinforcements. Unlike other reinforcements, minotaurs '''immediately move''' upon spawning, and with a maximum hit of 74 and high melee stats, it is important to stay well away from the reinforcement spawn points.\n\nSimilarly to the [[Yt-MejKot]] of the Fight Caves, Minotaurs will heal other enemies if they have been damaged. However, the minotaur will continuously heal them to '''full health''' if they are within 6 tiles of them and in line of sight. Their line of sight is based on their centre tile, so their healing can be blocked by the pillars under certain circumstances. If in melee range, they will prioritise attacking the player over healing damaged NPCs.\n\nIf the handicap '''Red Flag''' is active, the minotaur will gain advanced pathing and will be capable of moving around pillars; other NPCs, such as the reinforcement serpent shamans in waves 10 and 11, will also be able to move into the minotaur's tile. These minotaurs have a different NPC ID from the regular minotaurs, so players using NPC tile markers or radius markers should be mindful of this.\n\n==Wave breakdown==\n[[File:Wave Break Down - Cheat Sheet.png|450px|right|thumb|Wave Break Down Visual]]\n\nIf the wave is not cleared within 40 seconds, the reinforcement NPCs will spawn. Realistically, only waves 1, 2, and 4 can be cleared without letting any reinforcements spawn.\n\nGenerally, the hardest waves are waves 6, 8, 10, and 11 due to the large number of NPCs. The [[serpent shaman]] reinforcements in 4, 5, 6, 10, and 11 can be challenging as there is a chance for them to spawn in front of the melee NPC. If you are busy handling a south NPC and the shaman spawns in front, you may need to manually offtick it before it arrives. If it spawns behind, the melee NPC will block the shaman until it is un-safespotted or killed.\n\n*Wave 1: Fremennik Warband, [[Serpent shaman]]\n**Reinforcement: [[Jaguar warrior]]\n*Wave 2: Fremennik Warband, Serpent Shaman, [[Javelin Colossus]]\n**Reinforcement: Jaguar Warrior\n*Wave 3: Fremennik Warband, Serpent Shaman, 2x Javelin Colossus\n**Reinforcement: Jaguar Warrior\n*Wave 4: Fremennik Warband, Serpent Shaman, [[Manticore]]\n**Reinforcements: Jaguar Warrior, Serpent Shaman\n*Wave 5: Fremennik Warband, Serpent Shaman, Javelin Colossus, Manticore\n**Reinforcements: Jaguar Warrior, Serpent Shaman\n*Wave 6: Fremennik Warband, Serpent Shaman, 2x Javelin Colossus, Manticore\n**Reinforcements: Jaguar Warrior, Serpent Shaman\n*Wave 7: Fremennik Warband, Javelin Colossus, Manticore, [[Shockwave Colossus]]\n**Reinforcement: [[Minotaur (Fortis Colosseum)|Minotaur]]\n*Wave 8: Fremennik Warband, 2x Javelin Colossus, Manticore, Shockwave Colossus\n**Reinforcement: Minotaur\n*Wave 9: Fremennik Warband, Javelin Colossus, 2x Manticore\n**Reinforcement: Minotaur\n*Wave 10: Fremennik Warband, 2x Javelin Colossus, 2x Manticore\n**Reinforcements: Minotaur, Serpent Shaman\n*Wave 11: Fremennik Warband, Javelin Colossus, 2x Manticore, Shockwave Colossus\n**Reinforcements: Minotaur, Serpent Shaman\n*Wave 12: [[Sol Heredit]]\n\n==Modifiers==\nBefore starting a run, the player will be allowed to set up where they may choose to go before speaking to Minimus, who will then offer three modifiers to choose from before the wave begins. After the first wave is cleared, the player will not be able to move when Minimus appears until they make a selection. '''You must return near to the start tile (see below) immediately after killing the last NPC in a wave, or you are likely to be caught out of position and die to the next wave.'''\n\nThe first wave will always consist of a selection of '''Relentless''', '''Blasphemy''' and '''Frailty'''. '''Red Flag''' and '''Dynamic Duo''' become available before wave 7 starts. The remaining modifiers are picked at random with a preference towards upgrades to previously picked Tier 1 and Tier 2 modifiers. Picking multiple, manageable T1 modifiers as soon as possible will reduce the chance of being forced to pick an undesirable modifier in the later waves.\n\nThe suggestions assume that the player is learning for their first quiver, have a few completion under their belt or are not going for the [[One-off]] or [[Reinforcements]] [[Combat Achievements]], which requires players to pick bad modifiers. The player should ideally have experience in more challenging content. In addition, the choices make depend on currently active modifiers, gear, skill level and preference. However, if the player is able to handle less desirable modifiers, they will be able to clear later waves or the entire Colosseum without relying on a reset in the event of very bad offerings.\n\n{| class=\"wikitable sortable\" style=width:960px;\n!colspan=\"2\" style=\"width: 20%\"|Modifier\n!style=\"width: 40%\" |Effect\n!Recommendation\n!Details\n|-\n|[[File:Fortis Colosseum - Bees!.png|centre]]\n|Bees!\n|A [[Bee Swarm]] will drift around the arena, slowly converging on the player at a speed of 12 ticks. If underneath the player, it will deal up to 10 unblockable poison damage every tick, and inflict poison starting at 1 damage.\n\nThey have 1 Hitpoint and will respawn 30 seconds after being killed.\n| {{No}}\n| rowspan=\"3\" |This is universally deemed as one of the worst modifiers - it wastes time and demands attention like Quartet, but throughout the whole wave instead of just at the start. Even worse, it can mess with your positioning if you attack the Bees in melee range, making you step out of the pillar or melee safespot. The damage Bees can do to your run far outweighs any benefit gained from using them for SGS specs or Death Charge. This is almost certain to kill you in the Sol fight.\n|-\n|[[File:Fortis Colosseum - Bees! (II).png|centre]]\n|Bees! (II)\n|The amount of bee swarms is increased to '''two'''.\n| {{No}}\n|-\n|[[File:Fortis Colosseum - Bees! (III).png|centre]]\n|Bees! (III)\n|The amount of bee swarms is increased to '''three'''.\n| {{No}}\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Blasphemy.png|centre]]\n|Blasphemy\n|The player's prayer points are drained by 20% of damage taken from enemies.\n| {{Yes}}\n| rowspan=\"3\" |This modifier is a drain on resources, but requires no conscious effort to deal with, and you are far more likely to die to a mistake that doesn't involve running out of prayer supplies. The only way this kills you is if you habitually camp low prayer.\n|-\n|[[File:Fortis Colosseum - Blasphemy (II).png|centre]]\n|Blasphemy (II)\n|Prayer drain is increased to 40% of damage taken.\n| {{Yes}}\n|-\n|[[File:Fortis Colosseum - Blasphemy (III).png|centre]]\n|Blasphemy (III)\n|Prayer drain is increased to 60% of damage taken.\n| {{Yes}}\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Doom.png|centre]]\n|Doom\n|A stack of Doom is gained whenever damage is taken, indicated by a [[hitsplat]] [[File:Doom stack hitsplat.png]]. The player is killed upon gaining 15 stacks.\n\nStacks of doom are cleared after completing a wave.\n| {{Yes}}\n| rowspan=\"3\" |This takes no conscious effort, but higher tiers can kill you on a wave that you should have lived through by spamming brews. Extra sources of damage such as Mantimayhem or Volatility make this a worse pick. Experienced players can work comfortably with Doom (II) and even (III) while playing to minimise mistakes.\n|-\n|[[File:Fortis Colosseum - Doom (II).png|centre]]\n|Doom (II)\n|The player is now killed upon gaining 10 stacks.\n| {{Maybe}}\n|-\n|[[File:Fortis Colosseum - Doom (III).png|centre]]\n|Doom (III)\n|The player is now killed upon gaining 5 stacks.\n| {{No}}\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Dynamic Duo.png|centre]]\n|Dynamic Duo\n|[[Shockwave Colossus|Shockwave Colossi]] will now spawn in pairs.\n\nThe paired Colossus spawns near the main Colossus, but not necessarily on one of the 12 default spawns.\n| {{No}}\n|This modifier has a decent chance of ruining your wave start, since it spawns two Shockwave Colossi in the same spawn point. However, this is generally preferable to Bees! or other annoying modifiers at wave 11, since it is not guaranteed to cause a bad spawn.\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Frailty.png|centre]]\n|Frailty\n|The player's base [[Hitpoints]] are reduced by 10%, and overhealing is disabled.\n| {{Yes}}\n| rowspan=\"3\" |At level 99 Hitpoints, (I) reduces health to 90 and (II) to 80. This is not a big problem especially with good sustain and damage management. It is nowhere near as dangerous as the reduced health implies as long as the player is cautious. (III) should be completely avoided at all costs, as this will bring a player with 99 Hipoints to 60. With such low health, the player can risk being one-hit or combo'ed out by a large amount of hazards while having next to no chance of survival in a bad situation. This is especially true at Heredit, where a combo attack can easily kill the player if two hits are missed and the lasers being a guaranteed death if caught in them.\n|-\n|[[File:Fortis Colosseum - Frailty (II).png|centre]]\n|Frailty (II)\n|Base Hitpoints are reduced by '''20%'''.\n| {{Yes}}\n|-\n|[[File:Fortis Colosseum - Frailty (III).png|centre]]\n|Frailty (III)\n|Base Hitpoints are reduced by '''40%'''.\n| {{No}}\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Mantimayhem.png|centre]]\n|Mantimayhem (I)\n|[[Manticore]]s now add an additional projectile per orb, effectively attacking twice per attack cycle.\n\nWill not be given as an option after wave 11 if not chosen.\n| {{Maybe}}\n| rowspan=\"3\" |If the player is able to flick manticores easily, this is an easy pick. However, if they struggle to, or make a mistake, especially with Relentless active, the player will be severely punished for a missed flick. The venom from (II) is a non-issue if you bring [[Sanfew serum]]s or a Noxious halberd and use the special attack to cure it, but you still take heavy damage from the doubled orbs. (III) has a high chance of ruining pillar stacks and should not be taken unless as a last resort.\n|-\n|[[File:Fortis Colosseum - Mantimayhem (II).png|centre]]\n|Mantimayhem (II)\n|Manticores are now [[venom]]ous, applying it if its attacks are not prayed against. Venom is cured at the end of each wave.\n| {{Yes}}\n|-\n|[[File:Fortis Colosseum - Mantimayhem (III).png|centre]]\n|Mantimayhem (III)\n|The manticore's attack patterns are now less predictable, now being set in any order of magic, range, and melee.\n| {{No}}\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Myopia.png|centre]]\n|Myopia\n|The player's attack range is reduced by two tiles. Manually casted spells are unaffected.\n| {{Yes}}\n| rowspan=\"3\" |While (I) does nothing for the most part, higher tiers can be dangerous if you carelessly get dragged out of a safe position. However, with a 10-range weapon, even (III) can be worked around with some care taken.\n|-\n|[[File:Fortis Colosseum - Myopia (II).png|centre]]\n|Myopia (II)\n|Attack range is now reduced by '''four''' tiles.\n| {{Yes}}\n|-\n|[[File:Fortis Colosseum - Myopia (III).png|centre]]\n|Myopia (III)\n|Attack range is now reduced by '''six''' tiles.\n| {{Maybe}}\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Reentry.png|centre]]\n|Reentry\n|Javelins launched into the air by [[Javelin Colossus|Javelin Colossi]] will now leave a '''temporary''' pool of molten sand where they land, disappearing after the wave ends.\n| {{Maybe}}\n| rowspan=\"3\" |With a strong ranged option this can be managed at tier (I), but as a learner, you are likely to rag important tiles while starting out. (II) can be taken at wave 10 or later, but should be avoided before that. This modifier becomes extremely bad if running a budget setup, because the Javelin Colossus will rag many more tiles before you can kill it. '''It is highly recommended to avoid Reentry if utilizing a melee-only setup.'''\n|-\n|[[File:Fortis Colosseum - Reentry (II).png|centre]]\n|Reentry (II)\n|The molten sand is now '''permanent''', and now includes the targeted tile and the tile south-west of it, if accessible.\n\nThis will also cause the sand created from the explosion of Volatility III to become permanent.\n| {{No}}\n|-\n|[[File:Fortis Colosseum - Reentry (III).png|centre]]\n|Reentry (III)\n|The tiles where molten sand is left behind now includes the tile west of the targeted tile.\n| {{Yes}}\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Red Flag.png|centre]]\n|Red Flag\n|[[Minotaur (Fortis Colosseum)|Minotaurs]] now have advanced NPC pathing, allowing it to move around obstacles and becoming impossible to safespot.\n| {{No}}\n|This is highly likely to kill learners and experienced players alike, since it ruins the reliable minotaur safespot and allows the serpent shaman reinforcements on wave 10 and 11 to pass through the minotaur. This may be preferable to Bees! on wave 11.\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Relentless.png|centre]]\n|Relentless\n|Enemy attacks will now bypass 33% of the player's Defence level and have their max hit increased by 1.\n| {{Yes}}\n| rowspan=\"3\" |This modifier takes no conscious effort to handle, but it causes you to take more damage upon missing prayers or when swapping pillars. Enemies in the Colosseum are very accurate to begin with, though. This modifier hurts less as you improve and rely less on swapping pillars or tanking hits. If you are running a budget setup and can't one-shot the Fremmies, consider picking this up in later waves rather than early.\n|-\n|[[File:Fortis Colosseum - Relentless (II).png|centre]]\n|Relentless (II)\n|Attacks now bypass '''66%''' of the player's Defence, and max hits are now increased by '''3'''.\n| {{Maybe}}\n|-\n|[[File:Fortis Colosseum - Relentless (III).png|centre]]\n|Relentless (III)\n|Enemies will fully ignore [[Damage per second/Melee#Step six: Calculate the hit chance|accuracy checks]], and max hits are now increased by '''6'''.\n| {{Maybe}}\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Solarflare.png|centre]]\n|Solarflare\n|A damaging orb circles around the pillars, moving every 2 ticks, then stopping for 7 ticks when it reaches a corner.\n\nDuring wave 12, they will circle a set pattern within the allotted arena.\n| {{Maybe}}\n| rowspan=\"3\" |Does little damage at (I) and (II), and can often be trivialised with ranged weapons. (III) can be annoying at wave start if not used to dodge timing, but the prayer-disabling effect is rarely a direct cause of death. However, learners attempting Sol are likely to be overwhelmed, especially without tile markers.\n|-\n|[[File:Fortis Colosseum - Solarflare (II).png|centre]]\n|Solarflare (II)\n|The orb now moves every two ticks without stopping, and deals more damage.\n| {{Maybe}}\n|-\n|[[File:Fortis Colosseum - Solarflare (III).png|centre]]\n|Solarflare (III)\n|The orb now moves every tick, stopping for 2 ticks when it reaches a corner. It will also now disable prayers if hit, alongside dealing even more damage.\n| {{Maybe}}\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Quartet.png|centre]]\n|Quartet\n|An extra random Fremennik Warbander spawns every wave.\n| {{No}}\n|While [[Fremennik warband|Fremmies]] can be killed quickly, this modifier robs you of valuable time in the wave opening, which is extremely important when handling a wave start. This is further exacerbated if the player is unable to kill the Frem in one attack, especially if a second berserker spawns among the group or if Volatility II or III is active. While not as bad as Bees! or Red Flag, it is not recommended to pick this modifier, especially as a learner.\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Totemic.png|centre]]\n|Totemic\n|When an enemy is reduced to 50% hitpoints or below, a healing totem will appear near them, and send healing projectiles to the target, healing them for 30% of their health every few ticks.\n\nThey have 1 Hitpoint and will respawn two minutes after being destroyed, or after the enemy dies. Additionally, it will not heal the enemy if they are destroyed before their healing projectile reaches them.\n\nDuring wave 12, after the first totem appears at 50%, an additional totem will spawn once every two minutes until the fight concludes.\n| {{No}}\n|Similarly to Bees!, this forces you to divert attention and damage away from important targets throughout the wave, and is even worse with Myopia and may cause you to break position in the Sol fight. [[Venator bow]] shots will likely bounce into the totem, and the presence of a totem does not mean immediate danger, unlike Bees!\nThis is an easy pick at wave 12, as you can pre-position yourself near Sol's SW tile to kill the totem quickly.\n|-\n!\n!\n!\n!\n!\n|-\n|[[File:Fortis Colosseum - Volatility.png|centre]]\n|Volatility\n|Upon death, the enemy will explode one tile greater than their size. For example, a [[manticore]], whose size is 3x3 tiles, will explode in a 5x5 radius.\n| {{Maybe}}\n| rowspan=\"3\" |While the explosions don't do heavy damage and can be dodged, they are easy to forget and make hard spawns more difficult to deal with. (II) significantly reduces room for error while killing the Fremmies at wave start. This modifier is worse with Quartet enabled.\nThis is an easy pick at wave 12, since it only does damage after Sol is already dead.\n|-\n|[[File:Fortis Colosseum - Volatility (II).png|centre]]\n|Volatility (II)\n|The explosion radius is now '''two''' tiles greater, ex. a 3x3 monster will now explode in a 7x7 radius.\n| {{Maybe}}\n|-\n|[[File:Fortis Colosseum - Volatility (III).png|centre]]\n|Volatility (III)\n|The tile at the centre of the explosion now leaves behind a '''temporary''' pool of molten sand, disappearing after the wave ends.\n\nReentry II or III will cause this pool of molten sand to become permanent. \n| {{Yes}}\n|}\n\n==Wave clearing procedure==\n[[File:Colosseum NW start.png|alt=Lumberjack outfit not recommended for first quivers.|thumb|Start each wave on this tile. The pillar shown is the NW pillar.]]\nFor waves 1-11, follow these steps to maximise your chances of survival:\n\n# Start by waiting on the start tile as shown. This prevents NPCs from spawning too close. Pray [[Protect from Magic]]  on waves 1, 4, 7, 8, and 11, and [[Protect from Missiles]] on all other waves.\n# On the 5th game tick after the handicap selection menu disappears, click towards either tile A or tile B as shown on the NW pillar. Using RuneLite metronome plugins, or simply walking in place, can be used to time this properly.\n# As you move towards the pillar, look to the south to see if any NPCs spawned in the two south spawn locations, and pray accordingly. The [[serpent shaman]] has a much lower maximum hit than the [[Javelin Colossus]], which in turn has a lower max hit than the [[Shockwave Colossus]]. Remember that a [[Manticore]] needs 10 ticks to charge up once it sees you, so you have some time before it fires.\n# Kill the fremmies. Start by killing the melee, followed by the mage and finally the ranger. You can use a [[Saradomin godsword]] special attack to get a large heal off the ranger if needed. It is best to kill them one by one, as fast as possible. Another good option is to Ice Barrage all three of them and stand a short distance away, thus taking no damage while finishing them off. '''If you opt to do this, use the correct weapons for the mage and range frem, as barraging them wastes precious time that you need to set up for reinforcements.''' There is a significant chance you will need to barrage seven times or more.\n# Kill south spawns. This is simple if only one NPC spawned south; if two spawned south, it is challenging but still possible to pray against their attacks. By standing on tile A or tile B, you will always be able to hit a south NPC with a 10-range weapon, even with Myopia 3.\n# (Once 40 seconds have passed) Kill reinforcements. If a reinforcement serpent shaman is going to arrive first and south NPCs are still alive, it may need to be manually offticked. If a jaguar warrior or minotaur will arrive first, make sure you safespot it.\n# Kill the pillar stack NPCs. You will need to step out of the pillar in a specific way to offtick their attacks and be able to pray against them. See the relevant section below (WIP) for examples of how to offtick various pillar stacks.\n# '''Return to the start tile''' before the next wave begins.\n[[File:Fortis Colosseum markers.png|thumb|A set of tiles useful in the Colosseum.]]\n===Tile markers===\nThis is a set of tile markers that can be used in the Colosseum. It includes:\n\n* Wave start tiles for both NW and SW pillars\n* Commonly-used A and B tiles for offticking double south spawns\n* Deadzones (tiles on the edge of the arena that you cannot enter)\n* Solarflare paths for the Sol fight\n* Destacking tiles\n* NW jaguar trap tiles\n* Tiles for offticking (NW) or stalling (SW) the reinforcement serpent shamans\n* A marker for the starting tile used to offtick a shaman + ranger stacked, in any order, on the south side of the pillar\n* A tile for dragging a shaman out if it gets caught on a triangle stack.\n{{Tile markers box|Module:Tile markers/Colosseum.json}}\n\n===Wave start===\nThe pillar recommended for first quivers is the NW pillar. This is because:\n* It minimises the risk of being attacked by two NPCs on wave start. You have a 1 in 6 chance to get a double south spawn on waves 8, 10, and 11. '''On SW, you have nearly a 2 in 3 chance for double north!'''\n* It allows the melee reinforcement NPCs to be safespotted while dealing with the south spawns on the west side.\n* It maximises opportunities to set up double ricochets with the [[venator bow]], which does better damage than even a [[scythe of vitur]] if Masori armour is equipped. This is not a consideration for a first quiver.\n* By clicking towards the pillar on the 5th tick, NPCs are prevented from spawning in the west area, minimising the number of NPCs the player has to deal with at wave start.\nFor more details on the tradeoffs of SW and why it is not recommended for a first quiver, [[Guide:Fortis Colosseum - SW pillar breakdown|you may refer to this guide.]]\n\n===Kill fremmies===\nAs fremmies have smart pathing, chase you constantly at running speed, and can do serious damage, they must be killed at the start of the wave. Failing to kill fremmies quickly results in a spiral of taking more and more damage.\n\nFremmies attack on a fixed 6-tick cycle relative to wave start. It is not worth devoting extra clicks to pray against their attacks, especially when south spawns are present. '''Kill them ASAP''' and get to dealing with south spawns. '''If and only if you can squeeze in the clicks without losing ticks,''' you can ctrl+click or turn off run to walk between hits and avoid some frem damage.\n\nNote that walking to avoid frem damage is an '''extremely common  trap''' that learners tend to internalise early, only to sabotage themselves repeatedly by continuing to walk away from frems late into the wave with no damage done to south spawns. '''It is worth standing still and taking damage from frems to kill them as fast as possible!''' If your frems tend to be alive past '''12-14 seconds into the wave''', you are sacrificing frem kill speed and will '''die more to mage first reinforcements''' as a result.\n\nKill fremmies in the order: melee, mage, range. The only exception to this is to kill the ranger with a [[Saradomin godsword]] special attack before the mager if the player is critically low on HP due to a mistake. '''Barraging the non-melee fremmies is slow, inconsistent, and may indirectly get you killed more often by reinforcements.'''\n\n===Deal with south spawns / double south===\nWhen only one NPC spawns south, it can be easily drawn in by standing on the A or B tile and killed safely from behind the pillar. However, being attacked by two south NPCs at wave start is a common cause of death for learners, as they struggle to juggle the movement and menu management of killing frems with flicking prayers between two NPCs. Learning to survive double south spawns makes a dramatic difference in the likelihood of achieving a completion.\n\nTo maximise your chances of surviving double south spawns:\n\n* '''''Practice good inventory management and awareness.''''' Do you know where your weapons used to kill the frems are? Do you use F-keys to quickly swap menus and avoid wasted clicks?\n* Avoid Quartet and Volatility if possible, since these handicaps make dealing with fremmies more difficult.\n* Watch examples of double south spawn offticks to learn them, then practice applying the solves in your own runs.\nThere are two main methods for solving double south: rotating north, and the A/B method.\n\n==== Rotating north for double south ====\nThis method has you rotate north to set up a familiar offtick with double south. In many cases, you will exploit the long 10-tick charging time a manticore needs before it fires at you.\n\nThis method has several benefits:\n\n* Since you rotate and use the manticore charge-up times, you can usually comfortably kill at least two frems before having to rotate back west and flick the south spawns.\n* The flick patterns are common two-stack patterns you would have learned elsewhere in the Colosseum.\n* You take less damage from frems, since you spend time rotating.\n* You can avoid taking explosion damage from Volatility frems easily.\n\nHowever, there are several downsides:\n\n* You need to make 2-3 more clicks than you would with the A/B method.\n* If both a ranged and magic NPC are east, you may need to tank a hit on the east side before quickly returning west, unless you can pull off a fast one-tick south peek.\n* If the east NPCs are side-by-side, you need to avoid staying on the corner on the north side, or offticking the south spawns will become more difficult.\n\nThe general procedure for this method is as follows. You may want to refer to [https://www.youtube.com/watch?v=t1qDqa_SXpk this video guide.]\n\n===== 1. Kill melee frem and rotate north =====\n\n* Stand in place on the A or B tile and kill the melee frem when it approaches you.\n* Click to go north as soon as you see the XP drop for the frem kill.\n* Switch your prayer, if necessary.\n\n===== 2. Kill mage frem and rotate west =====\n\n* Kill the mage frem.\n* Glance at the south spawns. If they start with the same style, prepare to same-tick them from the pillar edge. If they start with different styles, prepare to offtick them from the middle of the pillar.\n* There is a specific set of tiles you need to use if a [[Serpent shaman|shaman]] and [[Javelin Colossus|ranger]] are stacked on the south side -  it is included in the tile pack.\n* Click to go back west.\n\n===== 3. Flick south spawns and finish frem(s) =====\n\n* Flick the south spawns.\n* Kill the remaining frem(s).\n\nYou may adjust this as needed, such as not killing the mage frem on the north and immediately rotating back instead if there are a ranger and mager on the east side.\n\n===== Harder situations =====\nThere are a few situations where you will need to be careful with this method:\n\n'''If a shaman has made it all the way to the NW pillar'''\n\nYou cannot rotate back west safely if a shaman sees you from the corner of the NW pillar, as it will follow you back down west; you need to kill it. \n\nIf you freed a ranger from the NE pillar while rotating north, they will be offticked.\n\n'''If there are a shocker/shaman and ranger east'''\n\nYou can tank a hit from the ranger or shaman, then rotate back immediately. You may also do a 1-tick south peek, if you were fast enough.\n\nIf one of them was caught on the NE pillar, it will be offticked. \n\n'''If the east spawns are side-by-side'''\n\nThe south spawns will become harder to offtick if you let the nearest south spawn hug the pillar (you will need to do the triangle stack offtick, one tile away from the pillar). To prevent this, avoid staying on the corner of the north side when you rotate.\n\n<gallery mode=\"packed\" heights=\"200\" style=\"text-align:left\">\nSame style double south.mp4|The basic plan for same-style south spawns.\nDifferent style double south.mp4|The basic plan for different-style south spawns.\nShaman slow double south.mp4|A basic example of offticking a ranger and shaman south.\nShaman fast double south.mp4|A faster example of offticking a ranger and shaman south, since there is time pressure from the manticore.\nShaman reverse double south.mp4|The shaman offtick tiles work no matter what order the ranger and shaman are in.\nShaman manti double south.mp4|You can wiggle a south shaman out from under a manticore. Beware of the manticore's line of sight on the corner.\nShaman pillar double south.mp4|If a shaman walks all the way up to the pillar, you have to kill it on north. You can offtick any NPC that was caught on NE.\nTriple south.mp4|If you get triple south, stay on the north side until reinforcements arrive, using the Jaguar safespot.\nShocker ranger double south.mp4|If a shocker and ranger spawn south, you can kill one frem north, then rotate back.\nShocker ranger east double south.mp4|If the shocker and ranger are east, you can tank a ranged hit, or do a 1-tick south peek.\n</gallery>\n\n==== A/B offtick for double south ====\n[[File:Flick patterns.png|thumb|The prayer flick patterns for the north method and the A/B method. A/B flicks give you less free time, and you have to focus hard to start flicking early. The north method flicks are the same as those for regular pillar stacks.]]\n\nThis method has you stand still on either the A or B tile (except for the double south manticore spawn) to offtick double south. You need to stay on the tile until the rear NPC sees you.\n\nThis method has a few benefits:\n\n* You need to make 2-3 fewer clicks, since you do not rotate.\n* You never have to tank off-prayer hits, since there is no chance you rotate into a pair of ranged- and magic-using NPCs.\n\nHowever, there are several downsides:\n\n* You take more damage from frems, since you stay still for longer.\n* If a manticore spawned south, you need to flick it earlier than if you had rotated north.\n* The prayer flick patterns involving a manticore are more difficult than the standard two-stack flick patterns, and give you less breathing room to kill frems.\n*Volatility frems become much more troublesome to deal with.\n\n===== Shaman + Javelin Colossus =====\nThe serpent shaman works differently from the other 3x3 NPCs, as due to his size he does not get caught behind the pillar for several spawn locations which 'catch' the other 3x3 NPCs. You may have to manually offtick them by stepping away from the pillar as the shaman approaches. Generally, the A tile will offtick them. \n\n===== Manticore + Javelin Colossus / Shockwave Colossus =====\nWhen a manticore and another 3x3 NPC spawn south, they can be offticked by staying on the A tile if the manticore spawned close, and on the B tile if the manticore spawned far. Once both NPCs stop moving, it is safe to start moving to dodge hits from the frems. It is always possible to kill at least 2 frems before the manticore attacks you. \n\n===== Manticore + Manticore =====\nYou have two options: one, you can stay on the south corner of the pillar or walk one west of B and let them see you at the same time, then pray against their staggered attacks. Two, you can run to the A tile to cause them to attack you for 6 ticks in a row without interruption, followed by a 4-tick breathing period. If you opt for the first option, be very careful to wait for the closer manticore to attack first before dragging them in by running to the A tile or around the north side, as you may cause them to overlap their attacks by interrupting their line-of-sight.\n\n===== Javelin Colossus + Shockwave Colossus =====\nThese spawns will always be offticked by staying on the A or B tiles. The NPC that spawns close will attack first, and the rear NPC will attack 2 (if on B) or 3 (if on A) ticks later. Focus on getting the prayers right, equip your weapons, and click the frems when you have breathing room. Momentarily rounding the corner can be useful for buying time to kill a frem, but make sure to return if things get dangerous or if reinforcements are about to spawn.\n\n<gallery mode=\"packed\" heights=\"200\" style=\"text-align:left\">\nShaman Ranger south stack.mp4|If a shaman and ranger are about to see you, you can opt to pray range and go north, then offtick them as shown on the way back. Be careful if a Manticore will see you on the north side.\nManti Ranger.mp4|Since the Manticore spawned closer than the Javelin Colossus, you can offtick them on the A tile.\nManti Manti 1.mp4|You can make two Manticores see you on the same tick by walking 1 tile west of B.\nShocker Ranger.mp4|If a Javelin Colossus and Shockwave Colossus spawned south, pray against the closer one first.\n</gallery>\n\n==== How to survive mage-first reinforcements ====\nThese are your three main options.\n\nRegardless of which option you choose, the most important point is to '''plan ahead instead of being caught off-guard and panicking.''' It is very useful to enable the wave timers using the Fortis Colosseum plugin to be aware of when reinforcements are about to spawn.\n\n===== Pray against the shaman =====\nEven if you do not move and continue to hug the NW pillar, you have a decent chance (40-80%, depending on the south NPCs still alive) of having the shaman naturally offticked.\n[[File:Manual offtick.mp4|thumb|Manually offticking the shaman by running into its line of sight early.]]\nTo ensure your safety, you can step out to the offtick tile to do a manual offtick as the shaman is about to reach the middle of the pillar. While challenging, this is the only way to guarantee safety with mage-first reinforcements.\n\n===== Dump special attacks on the shaman =====\nIf you have the special attack energy and brought a strong special attack weapon such as [[dragon claws]], [[burning claws]], or a [[voidwaker]], using special attacks on the shaman can kill it quickly before it has the chance to do a lethal amount of damage. Once the shaman rounds the corner (stand on B or south of B to force it to do so), you can damage the shaman without letting any minotaurs heal it by attacking it from the A tile.\n\n===== Run pillars =====\nRunning to another pillar while praying against as many attacks as possible can sometimes give you another chance to solve the wave under low pressure. This can work remarkably well in tanky armour such as [[Torva armour]] or [[Justiciar armour]], even with a few missed prayers. However, overreliance on running pillars comes with the following issues:\n\n* You can still easily die to unlucky hits - even more so if you picked Relentless, or only rely on [[Protect from Magic]].\n* You might drag the reinforcements into the middle of the stack, leading to unsolvable stacks that require you to tank more hits or run again.\n\n=== Deal with pillar stack (see [https://imgur.com/a/colosseum-pillar-stack-solves-miRmd0i this album] for clips) ===\nKilling NPCs stacked on the pillar requires that they be offticked, or you will take massive damage. A set of summary graphics and examples are shown below. \n\nMantimayhem 3 adds three potential stacks in total that require z-solves; two of them are standard double line and triangle stacks which might now require stepping in and out of the pillar depending on the manticore's orb order. A triple line stack with a middle manticore that is melee orb first and the opposite style of the closest mob second also requires stepping back behind the pillar. There is one last unique stack: a T-stack where a manticore between a ranger and a mager has melee orb second and the opposite style of the closest mob third, which can be solved with one click of movement [https://los.colosim.com/?11112.11082.14094m.17096.#2306x2.2817.3329x5 like this]. '''Mantimayhem 3 is strongly not recommended on a first quiver.'''\n<gallery mode=\"packed\" heights=\"600\" style=\"text-align:left\">\nColosseum pillar stacks 1.png|Basic, common stacks. If not confident, the Z-stack can be destacked instead.\nColosseum pillar stacks 2.png|More complicated stacks.\nColosseum pillar stacks 3.png|More complicated stacks. If a manticore is in the front or back position and is not charged, it is recommended to run south.\n</gallery>\n\n==Sol Heredit==\n[[File:Sol Heredit chathead.png|left|100px]]\n{|class=\"wikitable floatright\" width=\"25%\"\n!{{SCP|Combat}} Max hit\n!{{SCP|Hitpoints}}\n!{{SCP|Magic}}\n!Size\n!Attack speed\n|-\n|45\n|1500\n|300\n|5x5\n|Varies\n|}\n\n[[Sol Heredit]] is the final boss of the Colosseum. When his wave starts, groups of gladiators will barricade the centre of the arena, limiting the player's movement against him to the area within the four pillars (roughly a 16x15 arena).\n\nSol is weak to slash, making the [[Scythe of vitur]] the strongest option; a [[Soulreaper axe]] or [[Abyssal tentacle]] are both acceptable alternatives. A [[Noxious halberd]] can outdamage an Abyssal tentacle, but it is very difficult to use if Myopia was not taken or offered during the waves, as you will have to manually path. It is not recommended to use magic or ranged as Heredit has massive bonuses against them with a high Magic level of 300, with +750 magic and +825 ranged defence bonuses. He does not absorb stats, so a [[dragon warhammer]] or [[elder maul]] can help make him easier to hit; however, for a first quiver, [[dragon claws]] or a similar special weapon are much more valuable to end the fight quickly once he goes below 10% HP.\n\n'''Preparation before starting wave 12:'''\n\n* If you are using NPC highlights, make sure to tag Sol Heredit. You can do this on any of the previous waves. He is sat above the north gate.\n* If you use game sounds, make sure both game sounds AND area sounds are turned up. Sol's attack sounds are area sounds. Also, if you use the Music plugin in Runelite, make sure \"Mute NPCs' area sounds\" is unchecked.\n* Make sure you are not zoomed in too much, as one of Sol's special attacks shows a cue above his head.\n\n=== Phase 1 (100% HP, 1500 HP) ===\n'''''Let's start by testing your footwork.'''''\n\nSol Heredit primarily attacks with dodgeable AOE attacks. They deal up to 45 [[Typeless|Typeless Melee]] damage which can be completely avoided if dodged correctly. Keeping your [[Protect from Melee]] prayer on should be '''avoided''', as it does not reduce his AOE damage, and having the prayer on during the start of his Triple Attack will prevent you from blocking the first hit.\n\nIt is recommended to have enough space to run at least two tiles away from Sol; while both trident attacks and the first shield slam can be avoided by walking one tile away from him, the second shield bash requires two tiles.\n\nThe attack patterns follow a rule in which if 2 of the same style, Spear attack or Shield attack, are used in a row, the second one will use a different pattern. Using a single Spear attack and following up with a Shield attack, or vice-versa, will reset the pattern back to its first one. Sol will remember his previous attack during a phase transition, but will reset if a special attack (grapple or combo) was used. '''Sol Heredit will always use a Spear attack first after phase transitions; if his previous attack was Spear 1, his next attack will be Spear 2.'''\n\nSol will be unable to move for 4 ticks starting on the tick he uses an AOE attack. Additionally, Sol must be next to the player at the start of a tick (before movement is calculated) to initiate an AOE attack. If the player walks away from Sol on a tick before Sol finishes his attack cooldown, they can delay Sol's next attack. This works on all attacks except for phase transitions, which Sol will begin regardless of player position. \n\nSol Heredit has 4 different AOE patterns:\n*'''Spear 1:''' '''Sol Heredit begins the fight with this attack.''' 5x6 (under him+ in front) with two 4x1 lines towards the player's direction. Avoided by stepping back 1 tile from his centre tile, or his corner tiles.\n* '''Spear 2:''' 5x5 (under him) with three 4x1 lines towards the player's direction. Avoided by stepping back 1 tile diagonally on either side of his centre tile.\n*'''Shield 1:''' large 15x15 AoE with a safe line at 9x9. Avoided by stepping back 1 tile from his melee range.\n*'''Shield 2:''' large 15x15 AoE with a safe line at 11x11. Avoided by stepping back 2 tiles from his melee range.\n\n<gallery mode=\"packed\" heights=\"500\" style=\"text-align:centre\">\nFile:Sol Spear.mp4|Spear attacks and safe zones\nFile:Sol Shield.mp4|Shield attacks and safe zones\nFile:Spear 2 Phase.mp4|After a phase transition, if the previous attack was Spear 1, the next attack will always be Spear 2.\n</gallery>\n\nSol Heredit's attack speed is based on the attack he previously used, with the spear stabs being a 7-tick attack and the shield slam a 6-tick attack. These attacks speed up by 1 tick each after he goes below 75% health and does his phase transition.\n\n=== How to dodge Sol's attacks ===\nThere are two main strategies to dodge Sol's regular attacks.\n\n==== The hover method ====\nBased on Sol's previous attack, there will be a tile that is '''always safe from his next regular attack.''' See the video below for demonstrations.\n{| class=\"wikitable\"\n|+\n!Previous attack\n!Hover tile\n|-\n|Any special attack\n|1 tile back, sides or middle\n|-\n|Shield 1\n|2 tiles back, sides or middle\n|-\n|Shield 2\n|1 tile back, sides or middle\n|-\n|Spear 1\n|1 tile back, off-centre tiles\n|-\n|Spear 2\n|1 tile back, sides or middle\n|}\nThis method takes a bit of practice to learn, but '''lets you anticipate every regular attack''',  gives the '''greatest control and movement options''' for maneuvering around molten sand.\n\n==== The L method ====\nBy moving in an L shape from Sol's corner, you will '''automatically dodge Spear 1, Spear 2, and Shield 2.''' This is '''always safe after a phase transition''', as Sol is guaranteed to use a Spear attack out of phasing.\n\nHowever, unlike the normal method which lets you preemptively hover your mouse over the next tile, '''you must react to Shield 1.'''\n\nAs this method forces you to react, and thus '''takes your attention away from other reactionary hazards''' like his special attacks or lasers, '''it is most helpful to master the hover method,''' with the L method as a backup for when you forget Sol's previous attack after a phase transition.\n\n<gallery mode=\"packed\" heights=\"500\" style=\"text-align:centre\">\nFile:Hover Method.mp4|Using the hover method to preempt all standard attacks. This gives the most options for maneuvering around the arena.\nFile:L Method.mp4|The L method dodges all attacks except for Shield 1, but restricts your positioning options. This is always safe after a phase change.\n</gallery>\n\n=== Phase 2 (90% HP, 1350 HP) ===\n'''''Not bad. Let's try something else...'''''\n\nSol Heredit will continue to use his primary AOE attacks like in Phase 1, but with the addition of arena obstacles and the combo special attack.\n\n==== Molten Sand ====\nDuring the phase transition, 6 random tiles will be marked with a pillar of light, leaving behind a puddle of molten sand that will remain for the rest of the encounter. One of these will always target the player's tile.\n*Stepping on them will damage the player for ~6-8 damage (and increment Doom, if applicable) per tick.\n*Additional tiles will be affected at the start of each phase.\n*Molten sand will only spawn in a 9 by 9 square centered on the player's tile.\n*If Sol attempts to spawn molten sand on a tile already covered, it will have no additional effect.\n*If the player is at an edge or corner of the arena, all 6 tiles will spawn inside of the arena, provided space exists.\n\n==== Light Beam ====\nA crystal will spawn on the outside of the arena and move a random distance. Afterwards, it will stop and project a beam of light, which after a few seconds, will shoot a ball of light which can damage the player for 70+ damage.\n*An additional beam is added at the start of each phase.\n*If your position is targeted, you have 3 ticks to react and move out of the way.\n\n==== Triple Attack ====\nSol Heredit will charge up an attack and strike 3 times. This attack must be prayed against with [[Protect from Melee]], or you will receive heavy damage. The attack requires you to activate your prayer tick-perfectly with the attack landing. The initial attack hits 3 ticks after the start of the charging animation, the second attack hits 3 ticks after that, and the final attack after another 3 ticks (delayed to 4 ticks beginning in Phase 4 at 50% health). \n*Activating any protection prayer too early will disable it and force you to take the damage. \n*On phase 1-3, each attack will deal 15, 25, and 35 damage, respectively, if not properly prayed against. In phase 4 onwards, they will deal 15, 30, and 45 damage if not properly prayed against.\n\n<gallery mode=\"packed\" heights=\"500\" style=\"text-align:centre\">\nFile:Sol Puddles.mp4|Sol drops molten sand puddles on the ground every phase transition around your current tile.\nFile:Sol Lasers.mp4|Lasers fire at semi-regular intervals. In enrage phase, they fire very often and very quickly.\nFile:Sol Triple Attack.mp4|Sol's Triple Attack must be prayed tick-perfectly. The final hit is delayed by 1 tick after the 50% HP phase transition.\n</gallery>\n\n=== Phase 3 (75% HP, 1125 HP) ===\n'''''Impressive. Let's see how you handle this...'''''\n\nPhase 3 starts with the addition of another new Light Beam, 6 more Molten Sands, and the grapple special attack. \n\nAdditionally, Sol Heredit's primary AOE attacks will have their attack speed increased by 1 tick. \n\n==== Grapple Attack ====\nSol Heredit will announce a message over his model and in the chat-box indicating one of 5 slots he will be targeting. If the player does not defend the armour slot in time, or clicks on the wrong slot, he will deal heavy damage (up to 45). Defending a gear slot will not unequip the item for the duration of this attack.\n*This attack will be used even if the player doesn't have anything worn in that slot, and will therefore be unable to defend.\n*Countering the attack within 3 ticks will result in the chat message \"''You successfully defend from Sol Heredit's grapple!\",'' blocking the damage. ''C''ountering it on the last possible tick, however,  results in the chat message ''\"You perfectly parry Sol Heredit's grapple!\",'' which will also empower your next attack (within 5 game ticks) with a max hit.\n*The chat messages indicating which armour piece to remove are as follows:\n** ''I'LL CRUSH YOUR '''BODY'''!'' indicates that you must defend the gear in the platebody slot.\n** ''I'LL BREAK YOUR '''BACK'''''<nowiki/>'''!''' indicates that you must defend the gear in the cape slot.\n** ''I'LL TWIST YOUR '''HANDS''' OFF!'' indicates that you must defend the gear in the gloves slot.\n** ''I'LL BREAK YOUR '''LEGS'''!'' indicates that you must defend the gear in the platelegs slot.\n** ''I'LL CUT YOUR '''FEET''' OFF!'' indicates that you must defend the gear in the boots slot.\n\n<gallery mode=\"packed\" heights=\"500\" style=\"text-align:centre\">\nFile:Sol Grapple.mp4|Once Sol calls out a body part, you have 4 ticks to defend the slot. Defending on the last tick will give a perfect parry.\n</gallery>\n\n=== Phase 4 (50% HP, 750 HP) ===\n'''''You can't win!'''''\n\nPhase 4 starts with the addition of another new Light Beam and 6 more Molten Sands.\n\nIn addition, Sol Heredit's Triple Attack is slightly modified. The final hit in the combo is delayed by 1 tick to catch the player off guard. If not blocked, it will inflict 45 damage.\n\n=== Phase 5 (25% HP, 375 HP) ===\n'''''Ralos guides my hand!'''''\n\nThis phase is exactly the same as Phase 4, with the addition of another new Light Beam and 6 more Molten Sands.\n\nNo more Light Beams are added from this point, for a total of 4. \n\n=== Enrage Phase (10% HP, 150 HP) ===\n'''''LET'S END THIS!'''''\n\nDuring Enrage Phase, Sol Heredit adds an initial 5 Molten Sands, and 1 extra Molten Sand every 1.8 seconds (3 game ticks), targeting a random unaffected tile in the arena. Like the phase transition sand, this sand is also in a 9 by 9 grid around the player, and can fail to spawn if Sol attempts to spawn sand on a tile already covered by it. It's important to finish off the boss here quickly before the arena is quickly covered. Special attack weapons like [[Dragon claws]] or [[Burning claws]] can help end the fight quickly, if you do not fumble the switches and special attack selection.\n\nLight beams '''fire much faster''' and more frequently, sending out an attack approximately every 7 seconds. '''You have only 2 ticks to react''' from the moment a beam targets your position.  \n\n'''Warning: Always run away after killing Sol! If Volatility is active, Sol Heredit will explode after defeating him, and dying from it will result in loot being lost, including the quiver.'''\n\n=== Modifiers ===\n\n*Because Sol is fought in the middle of the arena, players who have '''Reentry''' 2 or 3 active should avoid splattering the centre with molten sand, as this will severely limit space to avoid his attacks.\n*If '''Quartet''' is active, a random Fremennik warband member will spawn at the start of the fight and should be dealt with immediately.\n*'''Bees''' will continually spawn if active, making it necessary to kill them with the limited amount of space.\n*'''Solarflare''' orbs will continually rotate in a 5x5 pattern by the corners of the improvised arena.\n*If '''Totemic''' is active, the totems will begin spawning when Heredit reaches 50% of his health and will heal him for 75 hitpoints every 4.2 seconds if given the chance until destroyed. You can safely one-shot it with any manually casted spell; any weapon attack also works, but be careful of being dragged under him or onto molten sand. After the first totem is destroyed, it will respawn in 2 minutes, giving you plenty of time to finish the fight.\n\n<gallery mode=\"packed\" heights=\"400\" style=\"text-align:centre\">\nFile:Sol Reentry.mp4|Any puddles left behind by Reentry 2 will still damage you in the Sol fight.\nFile:Sol Quartet.mp4|Dealing with the extra frem with Quartet on is a common cause of death. You must shift-click while dodging and switching weapons, or you will red-click the frem and get hit by Sol.\nFile:Sol Solarflare.mp4|Solarflares orbit on fixed paths. The areas inside and in between the boxes are safe.\nFile:Sol Totemic.mp4|With Totemic on, a single totem spawns near Sol's SW tile at 50% HP. It respawns in 2 minutes, which is plenty of time to kill Sol.\n</gallery>\n\n==Tips==\n=== RuneLite plugins ===\nThe following RuneLite plugins are useful in the Colosseum:\n\n* '''Better NPC Highlight''': highlight the true tiles of the NPCs, and optionally colour-code them using the Presets. It is recommended to turn off Hull Highlight.\n** 12810 Jaguar warrior\n** 12811 Serpent shaman \n** 12812 Minotaur\n** 12813 Minotaur (with Red Flag active)\n** 12814 Fremennik warband archer \n** 12815 Fremennik warband seer \n** 12816 Fremennik warband berserker \n** 12817 Javelin Colossus \n** 12818 Manticore \n** 12819 Shockwave Colossus \n** 12821 Sol Heredit \n** 12823 Bees \n** 12825 Healing totem \n** 12826 Solarflare\n* '''Fortis Colosseum''': displays wave information, a handicap overlay, and a timer (used to anticipate reinforcements spawning, which occurs 40 seconds after the wave starts).\n* '''Visual Metronome''': useful for counting ticks at wave start and flicking Manticores.\n* '''Custom Menu Swaps''': allows you to prioritise certain actions, e.g., always left-click the melee frem when its clickbox overlaps with another frem.\n* '''Spell Reminders''': helps with Death Charge uptime.\n* '''Attack Ranges''': may be useful for keeping track of attack range with Myopia. \n** As of May 5th 2025, Attack ranges is bugged within the colosseum. Upon switching ranged weapons with a larger attack range than currently equipped, the plugin does not take into account any active Myopia modifier. For example, switching from a Venator bow with Myopia 1 to a Bowfa will show the attack range as 10 tiles, instead of 8 tiles. As an alternative, you can use the Line of Sight plugin and manually decrease the range setting when taking each tier of Myopia between rounds. Though this only allows you to show ranges for one weapon at a time, manually. \n\n===Safespot the melee NPCs===\nMelee NPCs can be safespotted on NW pillar by hugging the west side of the pillar; if '''Red Flag''' is active then this will not work for minotaurs.\n\n=== Don't let manticores charge ===\nWherever possible, avoid letting east manticores gain line-of-sight until the south spawns and reinforcements are dealt with. Leaving the manticores in the stack uncharged makes it much easier to safely run to another pillar or destack.\n\n=== Running pillars ===\nRunning to a different pillar (usually SW or SE) is a method used to simplify complicated stacks, potentially making them easier to deal with by breaking up a group of three or four stacked NPCs.\n\nOn your first quiver, it is likely that you will run pillars. It is strongly recommended to use the [https://los.colosim.com/ Line of Sight tool] to plan your pillar run so that the NPCs settle in a way that you can deal with. In addition:\n\n* You should do your best to pray against the incoming attacks as you run. Even in strong melee armour, enemies in the Colosseum can hit very hard.\n* As stated above, having Manticores uncharged greatly simplifies a pillar run.\n\nAlthough pillar runs are very useful for simplifying complicated stacks, it is '''not recommended to pillar run for easier stacks - i.e., two NPCs''', or three NPCs that are not in a Z-stack pattern (range, mage, range). Excessive pillar running is not only a waste of time, but it '''deprives you of valuable practice''' for flicking the enemies in the Colosseum, and may not be viable if you have Doom stacks. Running pillars as a panic response to stressful situations is risky and often will get you killed later in the wave.\n\n=== Jaguar Warrior safespot ===\n[[File:Jaguar safespot.mp4|thumb|Safespotting the Jaguar Warrior]]\nYou can use the jaguar safespot tiles from the tile pack above to safespot the reinforcement Jaguar Warrior as it spawns. Both tiles can be used; if you already have a ranger attacking you, you can use the tiles to dodge the sky javelins while praying against the ranger and shaman. This does not work for the Minotaur, as it is too large.\n\n===Stand on tile A as reinforcements spawn===\nIf dealing with a triangle or T stack, this prevents the reinforcements from getting caught on the stack.\n\n===Solarflare skip===\n[[File:Solar flare skip.mp4|thumb|Solarflare skip]]\nSolarflares can be skipped in different ways depending on the handicap level.\n\nAt Solarflare I & II, the flare moves every two ticks between corners. Only the second tick is damaging; you can wait until the flare's true tile overlaps with yours, and immediately click away to move without taking any damage.\n\nAt Solarflare III, the flare moves every tick between corners. You can skip it easily by clicking on its true tile as it is moving; by the time you move, the flare would have moved to the next tile.{{Fortis Colosseum}}", "revisionId": 14980462, "timestamp": "2025-09-05T12:50:26Z", "downloadedAt": "2025-09-05T20:37:34.418Z", "source": "osrs_wiki_watcher", "rawWikitext": true}