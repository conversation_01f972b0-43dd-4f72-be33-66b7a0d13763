{"title": "<PERSON><PERSON>", "namespace": "Main", "content": "{{External|rs|rsc=Macroing}}\n{{Otheruses|the illegal software|the [[Combat option]]|Auto Retaliate|the [[spell-casting]] option|Autocast}}\n{{Otheruses|def=no|the option under the [[chat interface]]|Autochat}}\n'''Macroing''', '''botting''', or '''autoing''' is the act of using third-party software to perform automated tasks. A macro operates by automating user input to the game. A simple macro may just move the cursor to a certain position and perform a click while more advanced ones may interact with the game client directly; as technology advances, botting methods as well as their countermeasures have evolved from simple macros to utilising {{wp|machine learning}}. The terms '''macroer''', '''autoer''', '''botter''' or '''bot''' usually refer to players who use such programs. Usage of macros is not allowed under the ''[[RuneScape]]'' [[Code of Conduct|rules]] (Rule 7) and may result in action taken against that player's [[account]], such as a temporary or permanent [[ban]]. Moreover, [[Jagex]] has the authority and power to reset or rollback a player's [[skill]] levels and/or [[bank]] value before initiating a ban.\n\nMacros can be programmed to perform almost any task. Typically, they are used to perform tasks deemed overly tedious by real players, such as training a particular skill, collecting a certain item, or quickly typing an offer or phrase. \n\nOn [[10 December]] [[2007]], Jagex implemented a number of updates aimed at ending the use of macros in the game. Although these updates have been successful at drastically cutting down on the number of both macros and [[Real world trading|real-world traders]], reactions among players were mixed concerning how well-implemented these updates were. As a result of a referendum, many of these updates which had lowered the number of bots in the game were removed on [[1 February]] [[2011]].\n\n==Internal workings==\n[[File:Macros.gif|frame|Several macroers chopped down a [[yew tree]]. Notice how they walk away as soon as the tree is cut down.]]\nLike normal players, a macro tool needs to control the game by giving the appropriate inputs. These controls result in some game reaction that the macro needs to observe and interpret to plan the next action. Typically, a macro is either a colour-coded or reflection-coded bot programmed in Java or in other programming languages.\n\n===Colour bots===\nColour bots are a primitive form of botting that uses colours in the game to perform. It is done by the bot being told to recognise a certain type of colour on the screen and clicking on that colour. After a certain period of time, the colour has either disappeared or is then clicked again. More complex colour bots can scan certain images or 3D objects in game. After using the same strategy, it then moves to another location. These bots are unreliable and are mostly rendered temporarily useless if the colours or the graphics are changed. Jagex broke lots of these bots by changing the colour in the [[random event]]s although some colour bots can get around this by scanning the object instead of looking for a certain colour onscreen. This poses a large challenge to these bots and their creators which means they constantly have to update their bots. As colour bots do not read or write to the game's code, if scripted well, they can be almost undetectable to Jagex. This is one of the main reasons why some players use colour bots even when injection is available.\n\n===Injection bots===\nAn injection bot is a type of bot that utilises the ''RuneScape'' code itself. It injects itself into the RuneScape client and is able to read the client's code. It makes sense of the code and is able to make choices based on what the code states it will do. This is similar to how we react to what we see on the screen. However, an injection bot does not see the pictures; it sees the computer code that generates the pictures and can modify that code to be alerted when things happen. By doing this, it can do very complex commands and typically can be coded to do anything that humans do and act as we do. When combating this type of bot, it must be given a piece of computer code that it has not seen before, or a variation of it that would cause it to hook into the wrong part of the code to receive notifications. When most injection bots mess up, it is often caused by Jagex updating or changing objects in the game. The most successful instance of messing up injection bots was the update introducing the {{RSL|ClusterFlutterer|Bot Nuke}}. This update disabled most injection and reflection bots by changing where they can access code and putting false code for the bots to read, thus messing them up and causing the weird behaviour.\n\n===Reflection bots===\nReflection bots create a mirror image of the ''RuneScape'' applet by accessing the loaded classes and then read the code of the \"reflected\" copy, without injecting any code. This is considered to be much harder to detect than injection but, if done right, both are completely undetectable. Most bots used both injection and reflection to be able to gather as much data as possible. A custom-engineered game client is used to run the bot rather than through a web browser with Jagex's official client (as is done with most colour-based bots). This allows the game to be slightly modified making it listen to fake mouse or key events (thus allowing the bot owner to do other things while using the bot) and to disable direct system access (say: faking runtime information) to mislead Jagex's servers.\n\n===OpenGL/DirectX bots===\nAnother type are graphic driver-based bots. These types of bots hijack the current API, such as DirectX or OpenGL, which render the game's graphics to the screen, and use the data gathered to locate players and other objects. These types of bots can also use ID's like injection/reflection but not all require a custom-engineered game client. These bots can basically read the 3D models from the graphics card to determine what's onscreen.\n\n===Packet bots===\nPacket bots were one of the first type of bots to be around in ''RuneScape''; they could send commands to Jagex's game servers in the form of packets filled with information such as the coordinates the bot wanted to walk towards. Packet bots no longer exist in ''RuneScape'' as Jagex took a swipe at the packet bots by encrypting and changing communications, resulting in one of the first massive bans. Cheating was pronounced dead, for the first time.\n\n===Gaming engine===\nThe gaming engine is the part of the macro that interacts with the game, and then analyses the responses. A software program running on the game-playing computer usually has the capability to generate mouse input (move, click, drag, etc.) and keyboard input (key down, key up). Analysing the response from the game can be done by capturing the entire game image (although some image processing might be required to obtain information about the game response). The gaming engine provides the basic mechanisms to play the game, but it requires task-specific knowledge to actually perform the macro operation. Some programs use colour recognition and click colours on the screen while others interface directly with the game.\n\n==Macro program risks==\nMany websites claim to have an undetectable macro program that will not only earn your character quick gold but will make it appear as if it is a real person playing instead of a program. In addition to the risk of getting banned for such programs, downloading these programs can lead to keyloggers or other malicious programs getting into your computer. This most often happens when the program is downloaded from a private source usually claiming to be a cracked version of an expensive macro program. Due to the risk of being banned and having your account hijacked, it is strongly suggested that you do '''NOT''' use any third-party programs related to ''RuneScape'' botting. The high risk of being banned is enough to deter most players from using third-party botting software.\n\n== Types of macro ==\nSeveral common types of macro are used by bots and cheaters, such as:\n\n*Autotyping programs that are used to repeat a specific message over and over. This is often used by players in populated areas like World 301 [[Varrock west bank]] where a trade offer quickly disappears in the volume of other public chat messages, or it's used to advertise a [[real world trading]] site. This can be a nuisance to other players, as with all the spam, and can be reported under the offensive language option. If advertising a website, they can be reported under the advertising websites option. If players do not want to report, they can just right-click the name on the chat box and hit ignore.\n*Autoclickers used to repeatedly click a desired spot or click in many locations faster than is possible with a mouse. Autoclickers are mostly used to circumvent the auto-logout system. They can also be used to perform simple actions, such as [[Low Level Alchemy|Low]]/[[High Level Alchemy]].\n*Autobuying programs that are used to buy large amounts of items from [[shops]]. They can also be used to buy and bank items that can be sold for a profit.\n*Many macros involve more complicated tasks such as [[Mining]], [[Woodcutting]], [[Fishing]], [[bestiary|monster-killing]], and more. These programs often have advanced programming that gives them the ability to solve random events, navigate between locations, respond to chat, avoid dangerous situations, and more.\n*Modern bots can leverage machine learning models to analyze text and generate contextually relevant responses. This enables them to hold simple conversations with players, such as answering questions or even engaging in a brief discussion. This makes them appear more believable and less like a typical bot, which makes them harder to detect.\n*Today's most sophisticated bots are far more advanced than simple autoclickers. They use advanced programming and algorithms to mimic human behavior with incredible precision, including realistic mouse movements. They can perform almost any task in the game, from complex quests with multiple steps to the most difficult endgame activities, such as bosses and [[raids]].\n*These programs can switch between different tasks, manage their gear and bank, sell items on the [[Grand Exchange]], and even communicate with other players. They are programmed to adapt to unexpected situations, and bypass many of the anti-botting measures implemented by the developers. This makes them a significant challenge for the game's integrity.\n\n==Identifying and reporting macros==\nMany macros or gold farmers can be found performing highly repetitive tasks such as [[Woodcutting|chopping]] [[trees]], or [[fishing]] [[lobsters]]. Although many activities can lead to suspicion of macroing, keep in mind that players who display the following characteristics are not always bots.\n*Names that are extremely difficult to report - A common example is a mixture of uppercase i and lowercase L to produce a name similar to \"<span style=\"font-family: Times New Roman, serif\">IlllIlIllIlI</span>\" or just random letters. A typical name would be \"ftdhfdjhb\". Reporting these player names can be difficult, but this can be remediated by turning on right-click reporting.\n*Low [[Combat level]] players [[crafting]], fishing, [[woodcutting]], or using [[Magic]] at high levels.\n*Only one [[skill]] trained (usually Woodcutting, Fishing, [[Mining]], or [[Melee]] [[combat]]). This can be checked by looking up the name on the [[Hiscores]] or casting [[Stat Spy]] (P2P only) on a suspected bot.\n*May demonstrate strange behaviour such as not being able to fish in certain [[Fishing spots|spots]] and thus running backwards and forwards from the fishing spot (or just not interacting at all with certain spots). Also, something that is common for [[Fletching]] macros is accidentally trading other people instead of clicking the bank unless it is a bank-specific macro, in which case it utilises the one-click bank feature.\n*Stopping in front of a door or gate that has closed.\n*Acting oddly if a player, follower, item, or [[NPC]] is on or near the spot they are working on or moving to.\n*Wearing very cheap [[equipment]] (for example, a [[Dragon med helm]], [[Granite platebody]], [[Dragon platelegs]], [[Dragon boots]], [[Abyssal whip]] and [[Anti-dragon shield]] are worn by the [[Dragon (monster)|dragon]]-killing bots).\n*Having no [[quest]]s done, or only a few. This can be checked with the equipment the person wears since some equipment needs the completion of a quest to wear. For example, a [[Rune platebody]] requires the completion of [[Dragon Slayer I]].\n*Using only a [[Bronze sword]]. Many of these bots are skiller bots, spam bots, and [[Sorceress's Garden]] bots.\n*Setting [[Hunter]] traps in a straight line, and simply standing on top of fires when a fire is lit in that location (can be used to steal traps from bots, as most bots will still drop the trap).\n\n==Stopping macros==\nCurrent bots usually have complex code that can respond to, and overcome, attempts to disrupt their task. Prior to the use of these more advanced bots, players would throw [[gnomeball]]s at bots, lure them to aggressive monsters, or close a door or gate to trap bots. Although simple techniques like these no longer work consistently, many players spend a great deal of time discovering new ways to impede the actions of bots. Jagex also continues to add features to the game that make macroing more difficult and to remove macroers from the game. Most bots avoid patterns in their movement during the present days like fletching at random intervals unlike repetitive behaviour of older bots. As time continues both sides get smarter in their way of stopping/botting which makes botting an endless problem.\n\n==Macros and the economy==\nSome players argue that macros benefit the game by performing tasks that no person would want to do, such as cutting [[yew tree]]s constantly. Many disagree, however, arguing that macros sell vast amounts of resources causing the price of those resources to plummet. This then hurts legitimate players who are trying to make money selling that same item.\n\nMany monsters that are monotonous and relatively safe to kill are targets for macroers, such as [[chaos druids]] (frequent good [[herb]] droppers), which then causes those herb prices to drop. However, areas that require difficult and/or complex [[quest]]s to complete most likely have no macros. In addition, unique drops from monsters that require human intelligence to kill will not have their prices fall because of bots. Other examples include the rare drops from the [[Dagannoth Kings]]. When the macroer finally gets banned for cheating, the rare items remain with the banned player, thus meaning there are fewer of those items in the game, which further drives up their prices.\n\nMacros can also cause the price of rare items to rise. Firstly, by spending their millions that were made easily through cheating they out-bid honest players for rare items, pushing their prices up. When the macroer finally gets banned for cheating, the rare items remain with the banned player meaning there are fewer of those items in the game, which further drives up their prices.\n\nMacros making money are known as gold farmers and most are for gold selling sites.\n\n==Response by Jagex==\n===Free trade removal===\n[[Jagex]] has responded to the use of macros by banning thousands of accounts, wiping stats, and deleting items. The most prominent response by Jagex was the removal of free trade and other changes that occurred on [[10 December]] [[2007]]. While the changes were highly controversial, they did succeed in greatly decreasing the prevalence of bots in ''RuneScape''. These changes did not, however, eradicate botting altogether, and Jagex continues to enhance its ability to ban bot accounts and disrupt bot activities to this day. Improvements in macro and real world trading detection led to the return of free trade on [[1 February]] [[2011]]. Jagex has further clarified its action against those who use bots in other [http://www.webcitation.org/5xUQJQdsY posts on the forums]. Bots continued to be a problem in game, however, prompting a post by Mod Paul on the RuneScape forums in an attempt to address the concerns that some players have had about the high number of bots and Jagex's attempts to combat them.{{CiteForum |author = Mod Paul|url = http://www.webcitation.org/62mt28ws6|title = Relief for Bots? WTF JAGEX!|forum = RuneScape|postdate = 30 September 2010}}\n\n===ClusterFlutterer===\nOn [[25 October]] [[2011]], an update code-named \"ClusterFlutterer\", also known as \"Bot Nuking Day\" or simply \"Nuke Day\", was implemented aimed at preventing reflection and injection bots from functioning. This aimed to prevent bots from functioning by directly reading the game's java code. Jagex believes that this consisted of 98% of all bots seen in the game. Also, upon revealing ClusterFlutterer, Jagex stated that over 1.5 million bots had been recently banned.\n\n===Botwatch===\nSeptember 2012 saw the addition of \"Botwatch\", a set of aggressive bot-detection software intended to more accurately find macros and automatically ban them.\n\n==Trivia==\n*Before being removed by [[Update:World Switcher & RotG (i)]], players who were suspected of botting were moved to the [[Bot world]]s, 385 and 386 which, at the time were not selectable from the world select menu, but could be accessed by manually typing in their world IDs into the browser's address bar. These worlds are now visible in the [[World switcher]] after Jagex decided not to continue with the practice because it was not effective.{{CiteTwitter|author=Mod Acorn|url=https://twitter.com/JagexAcorn/status/1042371526367096832|date=19 September 2018|archiveurl=https://archive.is/NgkFN|archivedate=8 April 2021|quote=It's not a thing anymore. The bots ended up just hopping back to Player worlds every time.}}\n\n==References==\n{{Reflist}}\n\n==See also==\n*[[Real world trading]]\n*{{RSL|Jagex_responses_to_real_world_trading|Jagex responses to real world trading}}\n*{{RSL|Update:Bots_and_real-world_trading|Update:Bots and real-world trading}}\n*{{RSL|Update:RuneScape_vs_Real-world_Trading|Update:RuneScape vs Real-world Trading}}\n*{{RSL|Update:Trade_and_Drop_Changes|Update:Trade and Drop Changes}}\n*{{RSL|Update:Wilderness_Changes,_Bounty_Hunter_and_Clan_Wars!|Update:Wilderness Changes, Bounty Hunter and Clan Wars!}}\n*[http://services.runescape.com/m=news/bot-nuking-day-making-runescape-fairer-and-more-fun Update:Bot-Nuking Day]\n[[Category:Rules]]", "revisionId": 14980658, "timestamp": "2025-09-05T20:01:02Z", "downloadedAt": "2025-09-05T20:37:16.859Z", "source": "osrs_wiki_watcher", "rawWikitext": true}