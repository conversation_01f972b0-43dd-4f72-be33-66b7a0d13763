{"title": "<PERSON>", "namespace": "Main", "content": "{{Infobox NPC\n|name = <PERSON>\n|image = [[File:Drew.png|120px]]\n|release = [[14 March]] [[2019]]\n|update = Updated Quest Panel, Sandstone Grinder and QoL\n|members = Yes\n|race = [[Human]]\n|location = [[Bandit Camp Quarry]]\n|gender = Male\n|options = Talk-to, Claim Sand, Check, Deposit buckets\n|examine = Takes care of the sandstone grinder, that thing must kick up a sandstorm when used.\n|map = {{Map|name=<PERSON>|x=3152|y=2909|r=3|mtype=square}}\n|id = 6771\n|leagueRegion = Desert\n}}\n[[File:<PERSON> chathead.png|left]]\n'''<PERSON>''' is a [[non-player character]] found just west of the [[Bandit Camp Quarry|quarry]]. He operates the [[Sandstorm]] grinder, which grinds [[sandstone]] into [[buckets of sand]], which can be collected from <PERSON> noted for 50 [[coins]] each, along with a [[bucket]]. He can store up to 25,000 buckets (banknotes accepted) for the player.\n\nWhen asked about himself, he tells the player he was enslaved in [[Desert Mining Camp]], where he was tasked to look after [[<PERSON>torm]]. While the guards weren't looking, he took apart the machine and stashed the pieces in boxes and barrels, and tricked the [[mine cart driver]] by telling him jokes. This references the method players use to smuggle [[<PERSON>]] out of the camp during [[The Tourist Trap]].\n\n==Dialogue==\n{{Hastranscript|npc}}\n\n==Trivia==\n*Drew and his grinder, Sandstorm, are a reference to {{WP|Sandstorm (instrumental)|Sandstorm}}, a trance instrumental composed by Finnish DJ {{WP|Darude}}.\n*If player stores close to the maximum amount of buckets,{{Clarify|How close to the maximum?}} he will have a chat message: \"I am holding onto (amount) of buckets for ya. Why would ya want so many buckets?\"\n\n{{Bandit Camp (Kharidian Desert)}}", "revisionId": ********, "timestamp": "2025-09-05T00:26:06Z", "downloadedAt": "2025-09-05T20:37:58.418Z", "source": "osrs_wiki_watcher", "rawWikitext": true}