{"title": "Achievement Diary", "namespace": "Main", "content": "{{External|rs=Area Tasks achievements}}\n{{redirect|Tasks|tasks given by Slayer Masters|Slayer task|League tasks|Raging Echoes League#Tasks}}\n{{redirect|Achievements|combat achievements|Combat Achievements|Steam profile achievements|Steam Achievements}}\n{| class=\"wikitable\" style=\"text-align:center;\" align=\"right\"\n|+Quick links\n|-\n|[[File:Attack icon.png|link=#Attack]]\n|[[File:Hitpoints icon.png|link=#Hitpoints]]\n|[[File:Mining icon.png|link=#Mining]]\n|-\n|[[File:Strength icon.png|link=#Strength]]\n|[[File:Agility icon.png|link=#Agility]]\n|[[File:Smithing icon.png|link=#Smithing]]\n|-\n|[[File:Defence icon.png|link=#Defence]]\n|[[File:Herblore icon.png|link=#Herblore]]\n|[[File:Fishing icon.png|link=#Fishing]]\n|-\n|[[File:Ranged icon.png|link=#Ranged]]\n|[[File:Thieving icon.png|link=#Thieving]]\n|[[File:Cooking icon.png|link=#Cooking]]\n|-\n|[[File:Prayer icon.png|link=#Prayer]]\n|[[File:Crafting icon.png|link=#Crafting]]\n|[[File:Firemaking icon.png|link=#Firemaking]]\n|-\n|[[File:Magic icon.png|link=#Magic]]\n|[[File:Fletching icon.png|link=#Fletching]]\n|[[File:Woodcutting icon.png|link=#Woodcutting]]\n|-\n|[[File:Runecraft icon.png|link=#Runecraft]]\n|[[File:Slayer icon.png|link=#Slayer]]\n|[[File:Farming icon.png|link=#Farming]]\n|-\n|[[File:Construction icon.png|link=#Construction]]\n|[[File:Hunter icon.png|link=#Hunter]]\n|[[File:Quests.png|25x25px|link=#Quest points]]\n|}\n[[File:Achievement Diaries.png|left|130px]]\n[[File:Achievement Diary tab.png|frame|The Achievement Diary interface.]]\nThe '''Achievement Diary''' (also known as '''Diaries''') is a one-off set of tasks and challenges exclusive to [[members]] that can be completed to obtain rewards and various benefits. Each Achievement Diary consists of tasks that are usually tied to a specific area, and are intended to test the player's skills and knowledge about the said area. There are currently twelve areas that have an Achievement Diary, and a total of 492 tasks.\n\nThe tasks of each area are split into four categories based on their difficulty: Easy, Medium, Hard, and Elite. The easiest tasks usually do not require any significant skill levels to complete, but most higher-level tasks require the player to have high skill levels and difficult quests completed, as well as in-depth knowledge of a particular area within [[Gielinor]], such as the [[TzHaar Fight Cave]].\n\nEasy requires skills up to level 40 (50 for ironmen), Medium up to level 65, Hard up to level 75, and Elite requires skills in the 90s. For this reason, if you are pursuing the completion of all the diaries of a specific difficulty, it is recommended that you have all your skills at the diary difficulty level.\n\nPlayers can complete any task difficulty of their choosing, but they will not be able to claim the rewards from that tier until all preceding tiers below it are completed. For example, a player who completes the Elite Wilderness task set needs to complete the easy, medium and hard sets before they can claim their elite rewards. Simply speaking to the task master after completing the task set will give the reward.\n\nRewards from completing a task set include experience lamps and a specific tier equipment, which provides various benefits for that specific area. Higher task sets improve upon existing benefits and add much better ones, making it lucrative to complete them.\n\nPlayers who have completed all the achievement diaries can purchase an [[Achievement diary cape]] from [[Twiggy O'Korn]] in [[Draynor Village]] for 99,000 [[coins]]. Completing all the achievement diaries will also add an optional cyan trim to the [[Quest point cape]]. Completing all the achievement diaries is a requirement for the black trim to the [[Music cape]] (with the additional requirements of all quests completed and all holiday music tracks unlocked).\n\n==List of diaries==\nThere are currently twelve areas that have diaries:\n\n{| class=\"wikitable lighttable align-center-2 align-center-3 align-center-4 align-center-5\"\n|-\n!Diaries\n!colspan=\"5\" |Main reward\n!Taskmaster\n!Highest requirement\n|-\n|[[Ardougne Diary]]\n|{{plinkp|Ardougne cloak 1}}\n|{{plinkp|Ardougne cloak 2}}\n|{{plinkp|Ardougne cloak 3}}\n|{{plinkp|Ardougne cloak 4}}\n|[[Ardougne cloak]]\n|[[Two-pints]]\n|{{SCP|Magic|94|link=yes}} {{Boostable|yes}}\n|-\n|[[Desert Diary]]\n|{{plinkp|Desert amulet 1}}\n|{{plinkp|Desert amulet 2}}\n|{{plinkp|Desert amulet 3}}\n|{{plinkp|Desert amulet 4}}\n|[[Desert amulet]]\n|[[Jarr]]\n|{{SCP|Fletching|95|link=yes}} {{Boostable|yes}}\n|-\n|[[Falador Diary]]\n|{{plinkp|Falador shield 1}}\n|{{plinkp|Falador shield 2}}\n|{{plinkp|Falador shield 3}}\n|{{plinkp|Falador shield 4}}\n|[[Falador shield]]\n|[[Sir Rebral]]\n|Unlocked all [[music]] tracks, '''or''' level [[99]] in any skill, '''or'''  {{SCP|Quest}} [[quest points|Completion of all quests]]\n|-\n|[[Fremennik Diary]]\n|{{plinkp|Fremennik sea boots 1}}\n|{{plinkp|Fremennik sea boots 2}}\n|{{plinkp|Fremennik sea boots 3}}\n|{{plinkp|Fremennik sea boots 4}}\n|[[Fremennik sea boots]]\n|[[Thorodin]]\n|{{SCP|Slayer|83|link=yes}} {{Boostable|yes}}\n|-\n|[[Kandarin Diary]]\n|{{plinkp|Kandarin headgear 1}}\n|{{plinkp|Kandarin headgear 2}}\n|{{plinkp|Kandarin headgear 3}}\n|{{plinkp|Kandarin headgear 4}}\n|[[Kandarin headgear]]\n|[[The 'Wedge']]\n|{{SCP|Smithing|90|link=yes}} {{Boostable|yes}}\n|-\n|[[Karamja Diary]]\n|{{plinkp|Karamja gloves 1}}\n|{{plinkp|Karamja gloves 2}}\n|{{plinkp|Karamja gloves 3}}\n|{{plinkp|Karamja gloves 4}}\n|[[Karamja gloves]]\n|[[Pirate Jackie the Fruit]]\n|{{SCP|Runecraft|91|link=yes}} {{Boostable|yes}}\n|-\n|[[Kourend & Kebos Diary]]\n|{{plinkp|Rada's blessing 1}}\n|{{plinkp|Rada's blessing 2}}\n|{{plinkp|Rada's blessing 3}}\n|{{plinkp|Rada's blessing 4}}\n|[[Rada's blessing]]\n|[[Elise]]\n|{{SCP|Slayer|95|link=yes}} {{Boostable|yes}}\n|-\n|[[Lumbridge & Draynor Diary]]\n|{{plinkp|Explorer's ring 1}}\n|{{plinkp|Explorer's ring 2}}\n|{{plinkp|Explorer's ring 3}}\n|{{plinkp|Explorer's ring 4}}\n|[[Explorer's ring]]\n|[[Hatius Cosaintus]]\n|{{SCP|Smithing|88|link=yes}} {{Boostable|yes}} and  {{SCP|Quest}} [[quest points|Completion of all quests]]\n|-\n|[[Morytania Diary]]\n|{{plinkp|Morytania legs 1}}\n|{{plinkp|Morytania legs 2}}\n|{{plinkp|Morytania legs 3}}\n|{{plinkp|Morytania legs 4}}\n|[[Morytania legs]]\n|[[Le-sabrè]]\n|{{SCP|Fishing|96|link=yes}} {{Boostable|yes}}\n|-\n|[[Varrock Diary]]\n|{{plinkp|Varrock armour 1}}\n|{{plinkp|Varrock armour 2}}\n|{{plinkp|Varrock armour 3}}\n|{{plinkp|Varrock armour 4}}\n|[[Varrock armour]]\n|[[Toby]]\n|{{SCP|Cooking|95|link=yes}} {{Boostable|yes}}\n|-\n|[[Western Provinces Diary]]\n|{{plinkp|Western banner 1}}\n|{{plinkp|Western banner 2}}\n|{{plinkp|Western banner 3}}\n|{{plinkp|Western banner 4}}\n|[[Western banner]]\n|[[Elder gnome child]]\n|{{SCP|Slayer|93|link=yes}} {{Boostable|no}}\n|-\n|[[Wilderness Diary]]\n|{{plinkp|Wilderness sword 1}}\n|{{plinkp|Wilderness sword 2}}\n|{{plinkp|Wilderness sword 3}}\n|{{plinkp|Wilderness sword 4}}\n|[[Wilderness sword]]\n|[[Lesser Fanatic]]\n|{{SCP|Magic|96|link=yes}} {{Boostable|yes}}\n|}\n\n{{clear}}\n\n==All requirements==\nThe table below shows the total skill requirements for all diaries in each difficulty.\n{{Sync}}\n<tabber>\n|-|\nEasy Diary=\n{| class=\"diary-table\"\n| width=\"280px\" style=\"vertical-align:top\" |\n{{DiarySkillStats\n|selectable=true\n|margin=no\n|extra-css=width:inherit;\n|Total = ~350\n|Mining = 40\n|Agility = 20\n|Smithing = 20\n|Herblore = 12\n|Fishing = 20\n|Ranged = 30\n|RangedNotes = {{efn|Not boostable.|name=\"noboost\"}}\n|Thieving = 25\n|Cooking = 30\n|Crafting = 23\n|CraftingNotes = {{efn|42 for ironmen|name=\"42 for ironmen\"}}\n|Firemaking = 15\n|Magic = 21\n|Fletching = 20\n|Woodcutting = 40\n|Runecraft = 9\n|Slayer = 15\n|Farming = 23\n|FarmingNotes = {{efn|47 for ironmen|name=\"47 for ironmen\"}}\n|Construction = 25\n|ConstructionNotes = {{efn|name=\"noboost\"}}\n|Hunter = 11\n|Combat = 40\n}}\n| width=\"225px\" style=\"vertical-align:top;\" |\n{| class=\"wikitable lighttable qc-active\" style=\"width:inherit;\"\n!Quest Requirements\n|- data-rowid=\"Big Chompy Bird Hunting\"\n|[[Big Chompy Bird Hunting]]\n|- data-rowid=\"Biohazard\"\n|[[Biohazard]]\n|- data-rowid=\"Cook's Assistant\"\n|[[Cook's Assistant]]\n|- data-rowid=\"Death Plateau\"\n|[[Death Plateau]]\n|- data-rowid=\"Doric's Quest\"\n|[[Doric's Quest]]\n|- data-rowid=\"Druidic Ritual\"\n|[[Druidic Ritual]]\n|- data-rowid=\"The Fremennik Trials\"\n|[[The Fremennik Trials]]\n|- data-rowid=\"Gertrude's Cat\"\n|[[Gertrude's Cat]]\n|- data-rowid=\"The Knight's Sword\"\n|[[The Knight's Sword]]\n|- data-rowid=\"Nature Spirit\"\n|[[Nature Spirit]]\n|- data-rowid=\"Plague City\"\n|[[Plague City]]\n|- data-rowid=\"Priest in Peril\"\n|[[Priest in Peril]]\n|- data-rowid=\"Rune Mysteries\"\n|[[Rune Mysteries]]\n|}\n| style=\"vertical-align: top\" |\n{| class=\"wikitable lighttable\"\n!Additional Requirements\n|- data-rowid=\"Elemental Workshop I\"\n|Started [[Elemental Workshop I]]\n|- data-rowid=\"The Giant Dwarf\"\n|Started [[The Giant Dwarf]]\n|- data-rowid=\"Icthlarin's Little Helper\"\n|Started [[Icthlarin's Little Helper]]\n|- data-rowid=\"Troll Stronghold\"\n|Started [[Troll Stronghold]]\n|- data-rowid=\"Enter the Abyss\"\n|Completed [[Enter the Abyss]]\n|-\n|30 [[Chompy bird]] Kills\n|-\n|Obtain 50 [[Kudos]]\n|}\n|}\n{| class=\"mw-collapsible mw-collapsed\"\n| style=\"text-align:left;\" |'''Items Required'''\n|-\n|\n{{Checklist|\n*1 set of [[bone]]s\n*1 set of [[logs]]\n*3 [[jute seed]]s\n*3 [[Rope]]s\n*about 10,063 [[coins]] (more are recommended)\n*Any alchable item\n*Any blamish snail shell (cannot use [[Snail shell]])\n*Any grimy [[herb]]\n*Any [[pickaxe]]\n*Any tannable [[hide]] + Coins: [[cowhide]] or green - black [[dragonhide]] ('''not''' a [[Suqah hide]])\n*Any [[axe]]\n*Any [[Pyramid Plunder]] artefact\n*Any [[team cape]] ([[Team cape x]], [[Team cape i]], and [[team cape zero]] work.\n*A way to enter [[Port Phasmatys]]\n*[[Battered key]]\n*[[Big fishing net]]\n*[[Bird snare]]\n*[[Blurite bar]]\n*[[Bonemeal]]\n*[[Bow string]]\n*[[Bread dough]] ''(consider taking multiple, in case you burn the bread)''\n*[[Bucket of slime]]\n*[[Bucket]]\n*[[Chisel]]\n*[[Climbing boots]]\n*[[Desert boots]]\n*[[Desert robe]]\n*[[Desert shirt]]\n*[[Earmuffs]]\n*[[Earth talisman]]/[[Earth tiara|tiara]]\n*Either a [[harpoon]], [[lobster pot]], [[small fishing net]], [[fishing rod]] and [[Fishing bait|bait]], or [[Barbarian fishing]]\n*Empty [[waterskin]]\n*[[Equipment]] to kill a level 56 [[jogre]]\n*[[Feather]]s\n*[[Fishbowl]] (filled)\n*[[Fly fishing rod]]\n*[[Ghostspeak amulet]]\n*[[Hammer]]\n*[[Knife]] or [[slash]]ing [[weapon]]\n*[[Light source]]\n*[[Limpwurt root]]\n*[[Mind talisman]]\n*[[Oak shortbow (u)]]\n*[[Rake]]\n*[[Rune essence|Rune]] or [[pure essence]]\n*[[Rusty sword]] (two are recommended)\n*[[Scarecrow]]\n*[[Seaweed]]\n*[[Seed dibber]]\n*[[Shantay pass (item)|Shantay pass]] (can be purchased at the [[Shantay Pass Shop]])\n*[[Shayzien medpack]]\n*[[Silk]]\n*[[Small fishing net]]\n*[[Soft clay]]\n*[[Spade]]\n*[[Tarromin potion (unf)]]\n*[[Thin snail]]\n*[[Tiara mould]]\n*[[Tiara]]\n*[[Tinderbox]]\n*[[Water talisman]] or [[Water tiara|tiara]], or access to the [[Abyss]]\n*[[Weaponry]], preferably [[Ranged]] or [[Magic]]\n*[[Wolfbane dagger]]\n''Recommended'':\n*[[Amulet of glory]] for faster transportation to [[Draynor]]\n*Any [[Ranged weapon]]\n*[[Food]] and [[antipoison]], unless you already have a [[potato cactus]]\n*[[Mind Altar teleport (tablet)]]\n*[[Nardah teleport]]\n*[[Necklace of passage]]\n*[[Spiny helmet]] or [[Slayer helmet]] for [[Wall beast]]s in [[Lumbridge Swamp Caves]]\n*[[Teleport]] [[runes]] or tabs for Lumbridge\n}}\n|-|\nMedium Diary=\n{| class=\"diary-table\"\n| width=\"280px\" style=\"vertical-align:top\" |\n{{DiarySkillStats\n|selectable=true\n|margin=no\n|extra-css=width:inherit;\n|Total = ~975\n|Mining = 55\n|Strength = 45\n|Agility = 52\n|Smithing = 50\n|Defence = 30\n|Herblore = 48\n|Fishing = 65\n|Ranged = 50\n|Thieving = 47\n|Cooking = 43\n|Prayer = 47\n|Crafting = 50\n|CraftingNotes = {{efn|name=\"noboost\"}}\n|Firemaking = 50\n|Magic = 60\n|Fletching = 50\n|Woodcutting = 61\n|Runecraft = 23\n|Slayer = 50\n|Farming = 45\n|FarmingNotes = {{efn|name=\"47 for ironmen\"}}\n|Construction = 37\n|Hunter = 53\n|Combat = 70\n|Quest = 32\n}}\n| width=\"225px\" style=\"vertical-align:top;\" |\n{| class=\"wikitable lighttable qc-active\" style=\"width:inherit;\"\n!Quest Requirements\n|- data-row-id=\"Animal Magnetism\"\n|[[Animal Magnetism]]\n|- data-rowid=\"The Ascent of Arceuus\"\n|[[The Ascent of Arceuus]]\n|- data-rowid=\"Between a Rock...\"\n|[[Between a Rock...]]\n|- data-rowid=\"Cabin Fever\"\n|[[Cabin Fever]]\n|- data-rowid=\"The Depths of Despair\"\n|[[The Depths of Despair]]\n|- data-rowid=\"The Dig Site\"\n|[[The Dig Site]]\n|- data-rowid=\"Dragon Slayer I\"\n|[[Dragon Slayer I]]\n|- data-rowid=\"Druidic Ritual\"\n|[[Druidic Ritual]]\n|- data-rowid=\"Dwarf Cannon\"\n|[[Dwarf Cannon]]\n|- data-rowid=\"Eagles' Peak\"\n|[[Eagles' Peak]]\n|- data-rowid=\"Elemental Workshop II\"\n|[[Elemental Workshop II]]\n|- data-rowid=\"Enakhra's Lament\"\n|[[Enakhra's Lament]]\n|- data-rowid=\"Enlightened Journey\"\n|[[Enlightened Journey]]\n|- data-rowid=\"The Eyes of Glouphrie\"\n|[[The Eyes of Glouphrie]]\n|- data-rowid=\"The Forsaken Tower\"\n|[[The Forsaken Tower]]\n|- data-rowid=\"The Fremennik Trials\"\n|[[The Fremennik Trials]]\n|- data-rowid=\"Garden of Tranquillity\"\n|[[Garden of Tranquillity]]\n|- data-rowid=\"Ghosts Ahoy\"\n|[[Ghosts Ahoy]]\n|- data-rowid=\"The Golem\"\n|[[The Golem]]\n|- data-rowid=\"The Grand Tree\"\n|[[The Grand Tree]]\n|- data-rowid=\"The Hand in the Sand\"\n|[[The Hand in the Sand]]\n|- data-rowid=\"Haunted Mine\"\n|[[Haunted Mine]]\n|- data-rowid=\"Horror from the Deep\"\n|[[Horror from the Deep]]\n|- data-rowid=\"In Search of the Myreque\"\n|[[In Search of the Myreque]]\n|- data-rowid=\"Jungle Potion\"\n|[[Jungle Potion]]\n|- data-rowid=\"Lost City\"\n|[[Lost City]]\n|- data-rowid=\"One Small Favour\"\n|[[One Small Favour]]\n|- data-rowid=\"The Queen of Thieves\"\n|[[The Queen of Thieves]]\n|- data-rowid=\"Recruitment Drive\"\n|[[Recruitment Drive]]\n|- data-rowid=\"Ratcatchers\"\n|[[Ratcatchers]]\n|- data-rowid=\"Rum Deal\"\n|[[Rum Deal]]\n|- data-rowid=\"Sea Slug\"\n|[[Sea Slug]]\n|- data-rowid=\"Shilo Village\"\n|[[Shilo Village]]\n|- data-rowid=\"A Soul's Bane\"\n|[[A Soul's Bane]]\n|- data-rowid=\"Spirits of the Elid\"\n|[[Spirits of the Elid]]\n|- data-rowid=\"Tai Bwo Wannai Trio\"\n|[[Tai Bwo Wannai Trio]]\n|- data-rowid=\"Tale of the Righteous\"\n|[[Tale of the Righteous]]\n|- data-rowid=\"Tower of Life\"\n|[[Tower of Life]]\n|- data-rowid=\"Tree Gnome Village\"\n|[[Tree Gnome Village]]\n|- data-rowid=\"Underground Pass\"\n|[[Underground Pass]]\n|- data-rowid=\"Waterfall Quest\"\n|[[Waterfall Quest]]\n|}\n| style=\"vertical-align: top\" |\n{| class=\"wikitable lighttable\"\n|-\n!Additional Requirements\n|- data-rowid=\"Fairytale II - Cure a Queen\"\n|Started [[Fairytale II - Cure a Queen]]\n|- data-rowid=\"In Aid of the Myreque\"\n|Started [[In Aid of the Myreque]]\n|- data-rowid=\"Olaf's Quest\"\n|Partial completion of [[Olaf's Quest]]\n|- data-rowid=\"Watchtower\"\n|Reached the part of the [[Watchtower]] quest that grants access to the [[Skavid caves]]\n|- data-rowid=\"Monkey Madness I\"\n|Reached the part of [[Monkey Madness I]] where you gain access to [[Ape Atoll]].\n|- data-rowid=\"Alfred Grimhand's Barcrawl\"\n|Completed [[Alfred Grimhand's Barcrawl]]\n|- data-rowid=\"Skippy and the Mogres\"\n|Completed [[Skippy and the Mogres]]\n|-\n|*Access to the [[Wilderness God Wars Dungeon]] (level 60 Strength or Agility required)\n|-\n|125 [[Chompy bird]] Kills\n|-\n|100% [[Tai Bwo Wannai Cleanup]] Favour\n|- data-rowid=\"Lair of Tarn Razorlor\"\n|Completed [[Lair of Tarn Razorlor]]\n|}\n|}\n|-|\nHard Diary=\n{| class=\"diary-table\"\n| width=\"280px\" style=\"vertical-align:top\" |\n{{DiarySkillStats\n|selectable=true\n|margin=no\n|extra-css=width:inherit;\n|Total = ~1550\n|Attack = 50\n|Mining = 70\n|Agility = 71\n|Smithing = 75\n|Defence = 70\n|Herblore = 66\n|Fishing = 70\n|Ranged = 70\n|Thieving = 75\n|Cooking = 70\n|Prayer = 70\n|Crafting = 70\n|Firemaking = 65\n|Magic = 72\n|Fletching = 70\n|Woodcutting = 71\n|Runecraft = 65\n|Slayer = 72\n|Farming = 74\n|Construction = 65\n|Hunter = 69\n|Combat = 100\n|Quest = 176\n}}\n| width=\"225px\" style=\"vertical-align:top;\" |\n{| class=\"wikitable lighttable qc-active\" style=\"width:inherit;\"\n!Quest Requirements\n|- data-rowid=\"Another Slice of H.A.M.\"\n|[[Another Slice of H.A.M.]]\n|- data-rowid=\"Contact!\"\n|[[Contact!]]\n|- data-rowid=\"Desert Treasure I\"\n|[[Desert Treasure I]]\n|- data-rowid=\"Dream Mentor\"\n|[[Dream Mentor]]\n|- data-rowid=\"Eadgar's Ruse\"\n|[[Eadgar's Ruse]]\n|- data-rowid=\"The Feud\"\n|[[The Feud]]\n|- data-rowid=\"Grim Tales\"\n|[[Grim Tales]]\n|- data-rowid=\"Haunted Mine\"\n|[[Haunted Mine]]\n|- data-rowid=\"Heroes' Quest\"\n|[[Heroes' Quest]]\n|- data-rowid=\"In Aid of the Myreque\"\n|[[In Aid of the Myreque]]\n|- data-rowid=\"King's Ransom\"\n|[[King's Ransom]]\n|- data-rowid=\"Legends' Quest\"\n|[[Legends' Quest]]\n|- data-rowid=\"Mourning's End Part II\"\n|[[Mourning's End Part II]]\n|- data-rowid=\"Recipe for Disaster\"\n|[[Recipe for Disaster]] (fully completed)\n|- data-rowid=\"Roving Elves\"\n|[[Roving Elves]]\n|- data-rowid=\"The Slug Menace\"\n|[[The Slug Menace]]\n|- data-rowid=\"Swan Song\"\n|[[Swan Song]]\n|- data-rowid=\"Tai Bwo Wannai Trio\"\n|[[Tai Bwo Wannai Trio]]\n|- data-rowid=\"Tears of Guthix\"\n|[[Tears of Guthix]]\n|- data-rowid=\"Throne of Miscellania\"\n|[[Throne of Miscellania]]\n|- data-rowid=\"Watchtower\"\n|[[Watchtower]]\n|}\n| style=\"vertical-align:top\" |\n{| class=\"wikitable lighttable\"\n!Additional Requirements\n|-\n|Unlocked the [[Bones to Peaches]] spell from the [[Mage Training Arena]]\n|-\n|Completed [[Barbarian Training]] in Fishing, Smithing, and Firemaking\n|- data-rowid=\"Mage Arena I\"\n|Completed [[Mage Arena I]]\n|- data-rowid=\"The Great Brain Robbery\"\n|Started [[The Great Brain Robbery]]\n|-\n|Defeat the [[Penance Queen]] in [[Barbarian Assault]]\n|-\n|Defeat the [[Kalphite Queen]]\n|-\n|Defeat the [[Giant Mole]]\n|-\n|Defeat [[Zulrah]]\n|-\n|Defeat the [[Chaos Elemental]]\n|-\n|Defeat the [[Chaos Fanatic]]\n|-\n|Defeat the [[Crazy archaeologist]]\n|-\n|Defeat [[Scorpia]]\n|-\n|Defeat a [[Ket-Zek]]\n|-\n|300 [[Chompy bird]] kills\n|-\n|Obtain 153 [[Kudos]]\n|-\n|Obtain [[Prospector helmet]] from the [[Motherlode Mine]]\n|-\n|[[Slayer helmet]]\n|-\n|Unlocked at least one of the three [[God spells]]\n|-\n|Completion of the [[Camelot training room]]\n|-\n|Full [[skull sceptre]]\n|}\n|}\n|-|\nElite Diary=\n{| class=\"diary-table\"\n| width=\"280px\" style=\"vertical-align:top\" |\n{{DiarySkillStats\n|selectable=true\n|margin=no\n|extra-css=width:inherit;\n|Total = ~1935\n|Attack = 50\n|AttackNotes = {{efn|name=\"noboost\"}}\n|Hitpoints = 70\n|HitpointsNotes = {{efn|name=\"noboost\"}}\n|Mining = 85\n|Strength = 76\n|Agility = 90\n|Smithing = 91\n|Defence = 70\n|DefenceNotes = {{efn|name=\"noboost\"}}\n|Herblore = 90\n|Fishing = 96\n|Ranged = 70\n|Thieving = 91\n|ThievingNotes = {{efn|name=\"noboost\"}}\n|Cooking = 95\n|Prayer = 85\n|PrayerNotes = {{efn|name=\"noboost\"}}\n|Crafting = 85\n|Firemaking = 85\n|Magic = 96\n|Fletching = 95\n|Woodcutting = 90\n|Runecraft = 91\n|Slayer = 95\n|SlayerNotes = {{efn|The level 95 requirement is boostable, but there is also a non-boostable level 93 requirement.}}\n|Farming = 91\n|Construction = 78\n|Hunter = 70\n|HunterNotes = {{efn|name=\"noboost\"}}{{efn|The level 70 requirement is not boostable, as it is needed to complete [[Song of the Elves]] for the Lumbridge Elite task to use the [[Quest cape]].}}\n|Combat = 100\n|Quest = {{Globals|quest points}}\n}}\n| style=\"vertical-align:top\" |\n{| class=\"wikitable lighttable\"\n|-\n!Additional Requirements\n|-\n|800,000 [[Nightmare Zone]] points (400,000 with [[Combat_Achievements/Hard|hard]] [[Combat Achievements]]) - Ardougne\n|-\n|[[Kq head|Kalphite queen head]] - Desert\n|-\n|Be a Master rank White Knight (1,200 [[Black Knight]] kills assuming no [[White Knight]] kills) - Falador\n|-\n|Defeat all the [[Dagannoth Kings]] and [[God Wars]] generals - Fremennik\n|-\n|Completed [[Barbarian Training]] - Kandarin\n|-\n|Level 5 in all [[Barbarian Assault]] roles - Kandarin\n|-\n|Own a [[fire cape]] or [[infernal cape]] - Karamja\n|-\n|Completed a raid in the [[Chambers of Xeric]] - Kourend & Kebos\n|-\n|Completed all [[quests]] - Lumbridge & Draynor\n|-\n|Loot barrows chest while wearing any complete [[Barrows]] set - Morytania\n|-\n|1,000 [[Chompy]] kills - Western Province\n|-\n|Any complete [[Void Knight equipment|void set]] - Western Province\n|-\n|Defeat the Wilderness bosses - [[Callisto]], [[Venenatis]], and [[Vet'ion]] - Wilderness\n|}\n|-|\n</tabber>\n{{Notelist}}\n\n==Individual requirements==\nThe tables below provide a list of diary tasks which have a skill and/or quest requirement on a per skill basis (collapsed by default, click [show] to view table).\n\n'''Note:''' This list does '''not''' contain all diary tasks are listed, as some tasks do not have a skill or quest requirement for completion.\n\n==={{plinkp|Agility|pic=Agility icon}} Agility===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Climb over the western [[Falador]] wall.\n|{{NA|None}}\n|{{SCP|Agility|5|sort=yes}}\n|{{NA|None}}\n|[[Falador Diary|Falador]]\n|data-sort-value=1|Easy\n|-\n|Cross the Coal truck log shortcut.\n|{{NA|None}}\n|{{SCP|Agility|20|sort=yes}}\n|\nNone<br/>\n'''Tip:''' Take a [[pickaxe]] to mine [[coal]] for a medium diary task.\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=1|Easy\n|-\n|Use the rope swing to travel to the [[Moss Giant Island]] north-west of [[Karamja]].\n|{{NA|None}}\n|{{SCP|Agility|10|sort=yes}}\n|{{NA|None}}\n|[[Karamja Diary|Karamja]]\n|data-sort-value=1|Easy\n|-\n|Explore [[Cairn Isle]] to the west of [[Karamja]].\n|{{NA|None}}\n|{{SCP|Agility|15|sort=yes}} (Higher levels give better chances of success)\n|Dramen Staff - if you use the local fairy ring ('''c-k-r)'''\n|[[Karamja Diary|Karamja]]\n|data-sort-value=1|Easy\n|-\n|Complete a lap of the [[Draynor Village Rooftop Course]].\n|{{NA|None}}\n|{{SCP|Agility|1|sort=yes}}\n|{{NA|None}}\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=1|Easy\n|-\n|Jump over the fence south of [[Varrock]].\n|{{NA|None}}\n|{{SCP|Agility|13|sort=yes}}\n|{{NA|None}}\n|[[Varrock Diary|Varrock]]\n|data-sort-value=1|Easy\n|-\n|Kill an [[earth warrior]] in the [[Edgeville Dungeon|Wilderness beneath Edgeville]].\n|{{NA|None}}\n|{{SCP|Agility|15|sort=yes}}\n|[[Weapon]] to kill an [[earth warrior]], [[Agility]] is required to reach them\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=1|Easy\n|-\n|Grapple over [[Yanille]]'s south wall.\n|{{NA|None}}\n|{{SCP|Agility|39|sort=yes}}{{SCP|Strength|38}}{{SCP|Ranged|21}}\n|\n[[Mith grapple]] and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=2|Medium\n|-\n|Climb to the summit of the [[Agility Pyramid]].\n|{{NA|None}}\n|{{SCP|Agility|30|sort=yes}}\n|[[Waterskin]]s and [[Desert robes]] recommended. '''You must''' '''speak to Simon Templeton''' at the base before you climb the pyramid for the task to count.\n'''{{Colour|red|Beware:}}''' When you're at the top (directly below the golden pyramid), you must climb the rocks, take the golden pyramid, and click continue in the dialogue (or climb the rocks again) for the task to count. '''Do not '''go through the passageway until you've done this.\n|[[Desert Diary|Desert]]\n|data-sort-value=2|Medium\n|-\n|Grapple up and then jump off the north [[Falador]] wall.\n|{{NA|None}}\n|{{SCP|Agility|11|sort=yes}}{{SCP|Strength|37}}{{SCP|Ranged|19}}\n|\n[[Mith grapple]] and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Squeeze through the crevice in the [[Dwarven Mines]].\n|{{NA|None}}\n|{{SCP|Agility|42|sort=yes}}\n|{{NA|None}}\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Complete a lap of the [[Barbarian Outpost|Barbarian agility course]].\n|[[Alfred Grimhand's Barcrawl]]\n|{{SCP|Agility|35|sort=yes}}\n|{{NA|None}}\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Cross the lava using the stepping stones within [[Brimhaven Dungeon]].\n|{{NA|None}}\n|{{SCP|Agility|12|sort=yes}}\n|Any [[woodcutting axe]], {{Coins|875}}\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Claim a ticket from the [[Brimhaven Agility Arena|Agility arena]] in [[Brimhaven]].\n|{{NA|None}}\n|None ({{SCP|Agility|40}} recommended)\n|{{Coins|200}}\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Use the boulder leap [[shortcut]] in the [[dense essence mine]].\n|{{NA|None}}\n|{{SCP|Agility|49|sort=yes}}\n|{{NA|None}}\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=2|Medium\n|-\n|Grapple across the [[River Lum]].\n|{{NA|None}}\n|{{SCP|Agility|8|sort=yes}}{{SCP|Strength|19}}{{SCP|Ranged|37}}\n|\n[[Mith grapple]] and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Complete a lap of the [[Al Kharid Rooftop Course]].\n|{{NA|None}}\n|{{SCP|Agility|20|sort=yes}}\n|{{NA|None}}\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Complete a lap of the [[Canifis Rooftop Course|Canifis Rooftop Agility Course]].\n|{{NA|None}}\n|{{SCP|Agility|40|sort=yes}}\n|{{NA|None}}\n|[[Morytania Diary|Morytania]]\n|data-sort-value=2|Medium\n|-\n|Complete a lap of the [[Varrock Rooftop Course]].\n|{{NA|None}}\n|{{SCP|Agility|30|sort=yes}}\n|{{NA|None}}\n|[[Varrock Diary|Varrock]]\n|data-sort-value=2|Medium\n|-\n|Take the [[agility shortcut]] from the [[Grand Tree]] to [[Otto's Grotto]].\n|\n[[Tree Gnome Village]]<br/>\n[[The Grand Tree]]\n|{{SCP|Agility|37|sort=yes}}\n|{{NA|None}}\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=2|Medium\n|-\n|Complete a lap of the [[Wilderness Agility Course]].\n|{{NA|None}}\n|{{SCP|Agility|52|sort=yes}}\n|{{NA|None}}\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=2|Medium\n|-\n|Enter the [[Wilderness God Wars Dungeon]].\n|{{NA|None}}\n| data-sort-value=\"60\" |\n{{SCP|Agility|60}} or<br/>\n{{SCP|Strength|60}}\n|{{NA|None}}\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=2|Medium\n|-\n|Complete a lap of the [[Pollnivneach Rooftop Course]].\n|{{NA|None}}\n|{{SCP|Agility|70|sort=yes}}\n|{{NA|None}}\n|[[Kandarin Diary|Desert]]\n|data-sort-value=3|Hard\n|-\n|Complete a lap of the [[Seers' Village Rooftop Course]].\n|{{NA|None}}\n|{{SCP|Agility|60|sort=yes}}\n|{{NA|None}}\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Kill a [[metal dragon]] in [[Brimhaven Dungeon]].\n|{{NA|None}}\n|{{SCP|Agility|12|sort=yes}}{{SCP|Woodcutting|34}}\n|Good combat stats and equipment,{{Coins|875}} or 5000 [[trading sticks]], any [[woodcutting axe]]. Recommended: [[anti-dragon shield]] and [[antifire potion]] to negate all [[dragonfire]] damage.\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Kill a [[Deathwing]] in the dungeon under the [[Kharazi Jungle]].\n|Partial completion of [[Legends' Quest]]<br/>(Until you access the cave with the Deathwings)\n|{{SCP|Agility|50|sort=yes}}{{SCP|Woodcutting|15}}{{SCP|Strength|50}}{{SCP|Thieving|50}}{{SCP|Mining|52}}\n|Any [[woodcutting axe]], [[machete]], any [[pickaxe]], [[lockpick]]\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Squeeze past the jutting wall on your way to the [[Cosmic Altar]].\n|[[Lost City]]\n|{{SCP|Agility|46|sort=yes}}\n|[[Dramen staff|Dramen]] or [[Lunar staff]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=3|Hard\n|-\n|Climb the advanced spike chain within [[Slayer Tower]].\n|{{NA|None}}\n|{{SCP|Agility|71|sort=yes}}\n|None<br/>\nClimb the spiky chain located on the middle floor in the north-eastern room. If you are boosting, bring several [[Agility potion]]s or [[Summer pie]]s.\n'''Additional note:''' Bring a [[Slayer helmet]] or [[nosepeg]] to defend against [[Aberrant spectre]]s. Also if damage is taken going up the spike chain it won't count towards the achievement.\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Squeeze through the obstacle pipe in [[Edgeville Dungeon]].\n|{{NA|None}}\n|{{SCP|Agility|51|sort=yes}}\n|{{NA|None}}\n|[[Varrock Diary|Varrock]]\n|data-sort-value=3|Hard\n|-\n|Use the shortcut to get to the bridge over [[River Salve|the Salve]].\n|[[Priest in Peril]]\n|{{SCP|Agility|65|sort=yes}}\n|None (located just outside of [[Paterdomus]], along the path south of the temple). ''The shortcut must be started from top side, the chat will indicate the stages.''\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Complete a lap of the [[Ape Atoll Agility Course]].\n|[[Monkey Madness I]]\n|{{SCP|Agility|48|sort=yes}}\n|[[Ninja monkey greegree]]\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Kill an [[elf]] with a [[crystal bow]].\n|[[Roving Elves]]\n|{{SCP|Agility|56|sort=yes}}{{SCP|Ranged|70}}\n|[[Crystal bow]] ([[Elves]] at the [[Dark beast]]s do not work.)\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Take the Agility Shortcut from [[Trollheim]] into the [[Wilderness]].\n|[[Death Plateau]]\n|{{SCP|Agility|64|sort=yes}}\n|{{NA|None}}\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=3|Hard\n|-\n|Complete a lap of the [[Falador Rooftop Course|Falador agility course]].\n|{{NA|None}}\n|{{SCP|Agility|50|sort=yes}}\n|{{NA|None}}\n|[[Falador Diary|Falador]]\n|data-sort-value=3|Hard\n|-\n|Complete a lap of [[Ardougne Rooftop Course|Ardougne's rooftop agility course]].\n|{{NA|None}}\n|{{SCP|Agility|90|sort=yes}}\n|{{NA|None}}\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=4|Elite\n|-\n|Jump over the strange floor in [[Taverley Dungeon]] (without taking damage).\n|{{NA|None}}\n|{{SCP|Agility|80|sort=yes}}\n|{{NA|None}}\n|[[Falador Diary|Falador]]\n|data-sort-value=4|Elite\n|-\n|Kill each of the [[God Wars Dungeon]] generals.\n|[[Troll Stronghold]]\n|{{SCP|Agility|70|sort=yes}}, {{SCP|Strength|70}}, {{SCP|Hitpoints|70}}, {{SCP|Ranged|70}}\n|Combat equipment to kill the [[God Wars Dungeon]] generals, high {{SCP|Combat}} strongly recommended. '''Boosts cannot be used'''. You must deal the most damage to the boss in order for the kill to count. See each individual pages for recommended gear/setups.\n'''Note:''' Unlike other similar tasks, kills do not reset upon logging out, hopping worlds, or completing other Achievement Diary tasks. They also do not provide a task update message (\"''Achievement Diary Stage Task - Current stage: x''\").\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=4|Elite\n|-\n|Complete a lap of the [[Rellekka Rooftop Course]].\n|{{NA|None}}\n|{{SCP|Agility|80|sort=yes}}\n|{{NA|None}}\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=4|Elite\n|-\n|Mix a [[Stamina mix]] on top of the [[Seers' Village]] bank.\n|[[Barbarian Herblore]]\n|{{SCP|Agility|60|sort=yes}}{{SCP|Herblore|86}}\n|[[Stamina potion]](2), [[caviar]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=4|Elite\n|-\n|Grapple across a pylon on the [[Dorgesh-Kaan Agility Course]].\n|[[Death to the Dorgeshuun]]\n|{{SCP|Agility|70|sort=yes}}{{SCP|Ranged|70}}{{SCP|Strength|70}}\n|\nA [[light source]], [[mith grapple]], and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=4|Elite\n|-\n|Use the Elven overpass advanced cliffside shortcut.\n|[[Underground Pass]]\n|{{SCP|Agility|85|sort=yes}}\n|{{NA|None}}\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Attack|pic=Attack icon}} Attack===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Equip an [[Iban's upgraded staff]] or upgrade an [[Iban's staff]].\n|[[Underground Pass]]\n|{{SCP|Attack|50|sort=yes}}{{SCP|Magic|50}}\n|\n{{Coins|200,000}} and [[Iban's staff]], or [[Iban's staff (u)|Iban's upgraded staff]].<br/>\n50 [[Attack]] is not needed if Iban's staff has not been upgraded yet, simply upgrade the staff to complete the task. Iban's upgraded staff is obtained by bringing Iban's staff to the [[Dark Mage]], located in [[West Ardougne]] at the last house before the entrance of the [[Underground Pass (dungeon)|Underground Pass]].\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=2|Medium\n|-\n|Defeat a [[locust rider]] with [[Keris]]. ([[Scarab mage]] works too)\n|[[Contact!]]\n|{{SCP|attack|50|sort=yes}} to wield [[Keris]]\n|[[Keris]], [[armour]] recommended, [[Light source]]\n|[[Desert Diary|Desert]]\n|data-sort-value=3|Hard\n|-\n|Enter the [[Warriors' Guild]].\n|{{NA|None}}\n|{{SCP|attack}}{{SCP|strength|130}} Combined levels or 99 in either\n|{{NA|None}}\n|[[Falador Diary|Falador]]\n|data-sort-value=3|Hard\n|-\n|Loot the [[Barrows chest]] while wearing any complete [[Barrows equipment|Barrows set]].\n|{{NA|None}}\n|data-sort-value=70|\nCombat requirement varies per set, see below:<br/>\n{{SCP|Defence|70}}{{SCP|Attack}} for [[Guthan the Infested's equipment|Guthan's]] or [[Verac the Defiled's equipment|Verac's]]<br/>\n{{SCP|Defence|70}}{{SCP|Attack}}{{SCP|Strength}} for [[Dharok the Wretched's equipment|Dharok's]] or [[Torag the Corrupted's equipment|Torag's]]<br/>\n{{SCP|Defence|70}}{{SCP|Ranged}} for [[Karil the Tainted's equipment|Karil's]]<br/>\n{{SCP|Defence|70}}{{SCP|Attack}}{{SCP|Magic}} for [[Ahrim the Blighted's equipment|Ahrim's]]\n|Any full [[Barrows equipment]] set.\n|[[Morytania Diary|Morytania]]\n|data-sort-value=4|Elite\n|-\n|Equip any complete [[Void Knight equipment|void set]].\n|{{NA|None}}\n|data-sort-value=42|\n{{SCP|Combat|40}}<br />\n{{Nowrap|{{SCP|Defence|42}}{{SCP|Attack}}{{SCP|Strength}}{{SCP|Ranged}}{{SCP|Magic}}{{SCP|Hitpoints}}}}<br />\n{{SCP|Prayer|22}}\n|[[Void knight top]]; [[Void knight robe]]; [[Void knight gloves]]; and [[Void mage helm|Void mage]], [[Void ranger helm|ranger]], or [[Void melee helm|melee helm]].\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Construction|pic=Construction icon}} Construction===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Find out what your family crest is from [[Sir Renitee]].\n|{{NA|None}}\n|{{SCP|Construction|16|sort=yes}}\n|None, but make sure you thank him to get the completion.\n|[[Falador Diary|Falador]]\n|data-sort-value=1|Easy\n|-\n|Enter your [[player-owned house]] from [[Hosidius]].\n|{{NA|None}}\n|{{SCP|Construction|25|sort=yes}}\n|{{Coins|8,750}}\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=1|Easy\n|-\n|Teleport to [[Pollnivneach]] with a redirected [[teleport to house]] tablet. ({{plink|Ironman Mode|pic=Ironman chat badge}} Alternative) Enter your [[Player-owned house]] from [[Pollnivneach]].\n|{{NA|None}}\n|{{SCP|Construction|20|sort=yes}}\n|[[Scroll of redirection]], [[Teleport to house|house tablet]] ({{plink|Ironman Mode|pic=Ironman chat badge}} Alternative) {{Coins|7,500}}\n|[[Desert Diary|Desert]]\n|data-sort-value=2|Medium\n|-\n|Repair a crane within [[Port Piscarilius]].\n|{{NA|None}}\n|{{SCP|Construction|30|sort=yes}}{{SCP|Crafting|30}}\n|[[Hammer]], 30-100 [[nail]]s, 3 [[planks]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=2|Medium\n|-\n|Pick up your [[pet rock]] from your [[POH]] [[menagerie]].\n|[[The Fremennik Trials]]\n|{{SCP|Construction|37|sort=yes}}\n|[[Pet rock]]. Enter your [[house]] in non-building mode; then, ''use'' your pet rock on a [[pet house]]. Then, you must retrieve the pet rock off the ground (by left-clicking it). Taking it from the \"stored pets\" interface on the pet house will '''not''' complete the diary task.\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=2|Medium\n|-\n|Enter your [[POH]] from [[Yanille]].\n|{{NA|None}}\n|{{SCP|Construction|50|sort=yes}}\n|{{Coins|25,000}}\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Have the [[Seers' Village]] [[estate agent]] decorate your house with Fancy Stone.\n|{{NA|None}}\n|{{SCP|Construction|50|sort=yes}}\n|{{Coins|25,000}} ({{NoCoins|30,000}} if your house is already decorated with Fancy Stone)\n'''You must finish the dialogue with the [[estate agent]], or the task will not count!'''\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Enter the [[Kharyrll Teleport|Kharyrll portal]] in your [[POH]] through a [[Portal Chamber]].\n|[[Desert Treasure I]]\n|{{SCP|Construction|50|sort=yes}}{{SCP|Magic|66}}\n|\nNone, if you have already built a portal.<br/>\n2 [[limestone brick]]s, a [[hammer]], and a [[saw]] to build a portal focus; 3 [[teak plank]]s, a [[hammer]], and a [[saw]] to build a portal; and {{RuneReq|Law=200|Blood=100}} to attune the portal. Must be in your own POH.<br/>\n'''Note:''' A [[portal nexus]] '''can''' be used instead, requiring {{RuneReq|Law=2000|Blood=1000}}, 4 [[marble block]]s, a [[hammer]], and a [[saw]].\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Have the [[Varrock]] [[estate agent]] decorate your house with Fancy Stone.\n|{{NA|None}}\n|{{SCP|Construction|50|sort=yes}} ('''cannot''' be boosted)\n|{{Coins|25,000}} ({{NoCoins|30,000}} if the player's house is already decorated with Fancy Stone, to switch off and back on)\nPlayers must finish the dialogue with the estate agent or the task will not count!\n|[[Varrock Diary|Varrock]]\n|data-sort-value=3|Hard\n|-\n|Build an [[Isafdar (painting)|Isafdar painting]] in your [[POH]] [[Quest Hall]].\n|[[Roving Elves]]\n|{{SCP|Construction|65|sort=yes}}\n|3 [[Mahogany plank]]s, 1 [[Isafdar painting]], [[saw]], [[hammer]]\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Speak to the [[Kalphite Queen head (mounted)|Kq head]] in your [[POH]].\n|[[Priest in Peril]]\n|{{SCP|Construction|78|sort=yes}}\n|[[Kq head]] (must be [[Stuffed kq head|stuffed]] by the [[Taxidermist]]), 50000 [[coin]]s, 2 [[mahogany plank]], 2 [[gold leaf]]\n|[[Desert Diary|Desert]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Cooking|pic=Cooking icon}} Cooking===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Cook a [[thin snail]] on the [[Port Phasmatys]] range.\n|{{NA|None}}\n|{{SCP|Cooking|12|sort=yes}}\n|[[Thin snail]] (plus 2 [[Ecto-token]]s and a [[ghostspeak amulet]] to enter [[Port Phasmatys]] if [[Ghosts Ahoy]] is not completed)\n|[[Morytania Diary|Morytania]]\n|data-sort-value=1|Easy\n|-\n|Catch and cook a [[bass]] in [[Catherby]].\n|{{NA|None}}\n|{{SCP|Cooking|43|sort=yes}}{{SCP|Fishing|46}}\n|[[Big fishing net]] (or {{Coins|20}} to buy one at the [[Harry's Fishing Shop|Catherby Fishing Shop]])\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Cook a [[Spider on stick]].\n|{{NA|None}}\n|{{SCP|Cooking|16|sort=yes}}\n|[[Spider carcass]], [[Skewer stick]]s (made from using a [[machete]] on [[Thatching spar]]s) or an [[arrow shaft]]\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Complete a game of [[Trouble Brewing]].\n|[[Cabin Fever]]\n|{{SCP|Cooking|40|sort=yes}}\n|Another player on the opposing team is required to start this game. Merely participating in a game is enough; winning is not required.\n|[[Morytania Diary|Morytania]]\n|data-sort-value=2|Medium\n|-\n|Complete a delivery for the [[Gnome Restaurant]].\n|None, but [[The Grand Tree]] strongly recommended\n|{{SCP|Cooking|29-42|sort=yes}}\n|29 Cooking is required to begin the [[minigame]], after which the level required depends on the requested food item, which can be up to 42. '''Note:''' You must first complete the Gnome Restaurant tutorial by speaking to [[Blurberry]] at [[Blurberry Bar]] and [[Aluft Gianne snr.]] at [[Gianne's Restaurant]].\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=2|Medium\n|-\n|Make a [[Chocolate bomb]] at the [[Grand Tree]].\n|{{NA|None}}\n|{{SCP|Cooking|42|sort=yes}}\n|[[Gnomebowl mould]], [[Gianne dough]], 4 [[Chocolate bar]]s, 1 [[Equa leaves|equa leaf]], 2 [[Pot of cream|pots of cream]], 1 [[chocolate dust]]. You must have [[Gianne's Cook Book]] in your inventory for this task to trigger properly.\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=2|Medium\n|-\n|Cook a [[raw karambwan]] thoroughly.\n|[[Tai Bwo Wannai Trio]]\n|{{SCP|Cooking|30|sort=yes}}\n|Talk to [[Tinsay]], after completing the Tai Bwo Wannai Trio quest, to learn how to cook a karambwan thoroughly. (After completion of [[Tai Bwo Wannai Trio]], he can be found in the southernmost hut in [[Tai Bwo Wannai]].)\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Catch and cook a [[monkfish]] in [[Piscatoris]].\n|[[Swan Song]]\n|{{SCP|Cooking|62|sort=yes}}{{SCP|Fishing|62}}\n|[[Small fishing net]] (A [[Range]] can be found in the south-west building in Piscatoris)\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Catch a [[manta ray]] in the [[Fishing Trawler]] and cook it in [[Port Khazard]].\n|{{NA|None}}\n|{{SCP|Cooking|91|sort=yes}}{{SCP|Fishing|81}}\n|Multiple [[Raw manta ray]]s may be needed in case the player burns them. [[Fishing]] boosts '''do not work''' for the [[Fishing Trawler]] minigame. However, [[Cooking]] may be boosted.\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=4|Elite\n|-\n|Bake a [[wild pie]] at the [[Nardah]] clay oven.\n|{{NA|None}}\n|{{SCP|Cooking|85|sort=yes}}\n|[[Raw wild pie]]\n|[[Desert Diary|Desert]]\n|data-sort-value=4|Elite\n|-\n|Fish and cook 5 [[Shark]]s in [[Catherby]] (at the range next to the bank) using the [[Cooking gauntlets]].\n|[[Family Crest]]\n|{{SCP|Cooking|80|sort=yes}}{{SCP|Fishing|76}}\n|[[Harpoon]], [[cooking gauntlets]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=4|Elite\n|-\n|Catch an [[anglerfish]] and cook it whilst in [[Great Kourend]].\n|{{NA|None}}\n|{{SCP|Cooking|84|sort=yes}}{{SCP|Fishing|82}}\n|[[Fishing rod]] and [[sandworms]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=4|Elite\n|-\n|Bake a [[summer pie]] in the [[Cooks' Guild|Cooking Guild]]. (The spell \"[[Bake Pie]]\" on the Lunar spell book CAN be used to complete this step.)\n|{{NA|None}}\n|{{SCP|Cooking|95|sort=yes}}\n|[[Raw summer pie]] and either a [[chef's hat]], [[Varrock armour 3]] or [[cooking cape]].\n|[[Varrock Diary|Varrock]]\n|data-sort-value=4|Elite\n|-\n|[[Raw dark crab|Fish]] and cook a [[dark crab]] in the [[Resource Area]].\n|{{NA|None}}\n|{{SCP|Cooking|90|sort=yes}}{{SCP|Fishing|85}}\n|[[Lobster pot]], [[dark fishing bait]], and {{Coins|7500}} ({{NoCoins|3750}} if hard tasks are done, {{NoCoins|6000}} with medium).\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Crafting|pic=Crafting icon}} Crafting===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Craft a [[tiara]] from scratch in [[Rellekka]].\n|[[The Fremennik Trials]]\n|{{SCP|Crafting|23|sort=yes}}{{SCP|Mining|20}}{{SCP|Smithing|20}}\n|[[Tiara mould]], any [[pickaxe]]—mine the [[silver ore]] from the [[Rellekka mine]] located on the east side of [[Rellekka]], a few steps north-west of the [[agility shortcut]] (an [[iron pickaxe]] can be obtained from north of the Rellekka mine). [[Superheating]] will not work.\n'''Note:''' You have to do this process without doing any other task or stage 2 will not complete and you will have to redo the process.\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=1|Easy\n|-\n|Get a [[Fishbowl (pet)|pet fish]] from [[Harry's Fishing Shop|Harry]] in [[Catherby]].\n(Talk to [[Harry]] with items needed in inventory)\n|{{NA|None}}\n|{{SCP|Crafting|42|sort=yes}} ({{plink|Ironman mode|pic=Ironman chat badge|txt=Ironman and ultimate ironman mode}} only to craft [[fishbowl]], or see the item page for NPCs that drop it)\n|[[Fishbowl]] filled with water, [[seaweed]] and {{Coins|10}}\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=1|Easy\n|-\n|Craft any [[snelm]] from scratch in [[Morytania]].\n|{{NA|None}}\n|{{SCP|Crafting|15|sort=yes}}\n|[[Chisel]], snail shell (Blamish shells works too. You do '''''not''''' need to hunt down a [[Giant snail]] in the [[Temple Trekking]] minigame)\n|[[Morytania Diary|Morytania]]\n|data-sort-value=1|Easy\n|-\n|Spin a [[bowl]] on the [[pottery wheel]] and fire it in the oven in [[Barbarian Village]].\n|{{NA|None}}\n|{{SCP|Crafting|8|sort=yes}}\n|[[Soft clay]]\n|[[Varrock Diary|Varrock]]\n|data-sort-value=1|Easy\n|-\n|Craft a [[basket]] on the [[Falador farm]] [[loom]].\n|{{NA|None}}\n|{{SCP|Crafting|30|sort=yes}} ({{SCP|Farming|30}} for {{plink|Ironman mode|pic=Ironman chat badge|txt=Ironman and ultimate ironman mode}} since [[Willow branch]]es can only be obtained through [[Farming]])\n|6 [[Willow branch]]es\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Mine some [[gold ore]] at the [[Crafting Guild]].\n|{{NA|None}}\n|{{SCP|Crafting|40|sort=yes}}{{SCP|Mining|40}}\n|Any [[pickaxe]], [[brown apron]] or [[Crafting cape]]\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Repair a crane within [[Port Piscarilius]].\n|{{NA|None}}\n|{{SCP|Crafting|30|sort=yes}}{{SCP|Construction|30}}\n|[[Hammer]], 30-100 [[Nail]]s, 3 [[planks]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=2|Medium\n|-\n|Craft a [[coif]] (not cowl) in the [[Lumbridge cow pen]] (eastern cows).\n|{{NA|None}}\n|{{SCP|Crafting|38|sort=yes}}\n|[[Leather]], [[needle]], [[thread]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Slay a [[Dust devil]] in the [[Smoke Dungeon|desert cave]] with a [[Slayer helmet]] equipped.\n|Started [[Desert Treasure I]]\n|{{SCP|Crafting|55|sort=yes}}{{SCP|Slayer|65}}{{SCP|Defence|10}}\n|[[Slayer helmet]], [[weapons]] and [[armour]]\n|[[Desert Diary|Desert]]\n|data-sort-value=3|Hard\n|-\n|Craft, string and enchant an [[amulet of power]] in [[Lumbridge]] (not upstairs in castle).\n|{{NA|None}}\n|{{SCP|Crafting|70|sort=yes}}{{SCP|Magic|57}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Cosmic=1|Earth=10}}, a [[gold bar]], a [[Diamond|cut diamond]], a [[ball of wool]], and an [[amulet mould]].\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=3|Hard\n|-\n|Make a [[rune crossbow]] yourself from scratch within [[Witchaven]] or [[Yanille]].\n|{{NA|None}}\n|{{SCP|Crafting|10|sort=yes}}{{SCP|Smithing|91}}{{SCP|Fletching|69}}\n|[[Yew logs]], [[sinew]], 1 [[runite bar]], [[hammer]], [[knife]].\n*Smith [[runite limbs]] in [[Yanille]].\n*Fletch a [[yew stock]] in Yanille.\n*Craft a [[crossbow string]] in [[Witchaven]] (using tree roots will not count).\n*Use the yew stock with the runite limbs.\n*Use the crossbow string with the [[Runite crossbow (u)]].\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=4|Elite\n|-\n|Create a [[dragonstone amulet]] in the [[Neitiznot]] furnace.\n|Started [[The Fremennik Isles]]\n|{{SCP|Crafting|80|sort=yes}}\n|[[Dragonstone]], [[gold bar]], [[amulet mould]] (ball of wool not needed)\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=4|Elite\n|-\n|Construct a pyre ship from [[magic logs]].\n|[[Barbarian Firemaking]]\n|{{SCP|Crafting|85|sort=yes}}{{SCP|Firemaking|85}}\n|[[Magic logs]], [[chewed bones]], [[tinderbox]], any [[woodcutting axe]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=4|Elite\n|-\n|Craft one or more [[Blood rune]]s.\n|{{NA|None}}\n|{{SCP|Crafting|38|sort=yes}}{{SCP|Runecraft|77}}{{SCP|Mining|38}}\n|Any [[pickaxe]] and [[chisel]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=4|Elite\n|-\n|Craft a [[Black d'hide body|black dragonhide body]] in [[Canifis]] bank.\n|{{NA|None}}\n|{{SCP|Crafting|84|sort=yes}}\n|3 [[black dragon leather]], [[needle]], [[thread]]\n|[[Morytania Diary|Morytania]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Defence|pic=Defence icon}} Defence===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Pray at the [[Stone circle|Altar of Guthix]] in [[Taverley]] whilst wearing full [[Initiate armour]].\n|[[Recruitment Drive]]\n|{{SCP|Defence|20|sort=yes}}\n{{SCP|Prayer|10}}\n|[[Initiate armour]]\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Slay a [[Dust devil]] in the [[Smoke Dungeon|desert cave]] with a [[Slayer helmet]] equipped.\n|Started [[Desert Treasure I]]\n|{{SCP|Defence|10|sort=yes}}{{SCP|Slayer|65}}{{SCP|Crafting|55}}\n|[[Slayer helmet]], [[weapons]] and [[armour]]\n|[[Desert Diary|Desert]]\n|data-sort-value=3|Hard\n|-\n|Recharge your [[Prayer]] in [[Port Sarim]] church while wearing full [[Proselyte armour|Proselyte]].\n|[[The Slug Menace]]\n|{{SCP|Defence|30|sort=yes}}\n|[[Proselyte armour]]\n|[[Falador Diary|Falador]]\n|data-sort-value=3|Hard\n|-\n|Equip a [[Dwarven helmet]] within the [[Dwarven Mine]]s.\n|[[Grim Tales]]\n|{{SCP|Defence|50|sort=yes}}\n|[[Dwarven helmet]] (if the completion of the task does not trigger when you enter the mines while wearing the helmet, take the helmet off and put it back on)\n|[[Falador Diary|Falador]]\n|data-sort-value=3|Hard\n|-\n|Purchase and equip a [[granite body]] from [[Barbarian Assault]].\n|{{NA|None}}\n|{{SCP|Defence|50|sort=yes}}{{SCP|Strength|50}}\n|[[Combat equipment]] to pass through Waves 1-10 and {{Coins|95,000}}\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Enter the [[Seers' Village]] courthouse with [[Piety]] turned on.\n|[[King's Ransom]], [[Knight Waves Training Grounds]]\n|{{SCP|Defence|70|sort=yes}}{{SCP|Prayer|70}}\n|{{NA|None}}\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Pray at the [[Nature Grotto|Altar of Nature]] in the Nature Grotto with [[Piety]] activated.\n|[[Nature Spirit]], [[King's Ransom]], [[Knight Waves Training Grounds]]\n|{{SCP|Defence|70|sort=yes}}{{SCP|Prayer|70}}<br/>'''(Cannot be boosted)'''\n|None (won't be counted unless you restore at least 1 [[prayer]] point).\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Perform the [[Quest point cape]] emote in the [[Wise Old Man]]'s house.\n|All\n|{{SCP|Defence|65|sort=yes}}\n|[[Quest point cape]]\n|[[Lumbridge & Draynor Diary]]\n|data-sort-value=4|Elite\n|-\n|Loot the [[Barrows chest]] while wearing any complete [[Barrows equipment|Barrows set]].\n|{{NA|None}}\n|data-sort-value=70|Combat requirement varies per set, see below:<br/>\n{{SCP|Defence|70|sort}}{{SCP|Attack}} for [[Guthan the Infested's equipment|Guthan's]] or [[Verac the Defiled's equipment|Verac's]]<br/>\n{{SCP|Defence|70}}{{SCP|Attack}}{{SCP|Strength}} for [[Dharok the Wretched's equipment|Dharok's]] or [[Torag the Corrupted's equipment|Torag's]]<br/>\n{{SCP|Defence|70}}{{SCP|Ranged}} for [[Karil the Tainted's equipment|Karil's]]<br/>\n{{SCP|Defence|70}}{{SCP|Attack}}{{SCP|Magic}} for [[Ahrim the Blighted's equipment|Ahrim's]]\n|Any full [[Barrows equipment]] set.\n|[[Morytania Diary|Morytania]]\n|data-sort-value=4|Elite\n|-\n|Equip any complete [[Void Knight equipment|void set]].\n|{{NA|None}}\n|data-sort-value=42|{{SCP|Combat|40}}<br />\n{{Nowrap|{{SCP|Defence|42}}{{SCP|Attack}}{{SCP|Strength}}{{SCP|Ranged}}{{SCP|Magic}}{{SCP|Hitpoints}}}}<br />\n{{SCP|Prayer|22}}\n|[[Void knight top]]; [[Void knight robe]]; [[Void knight gloves]]; and [[Void mage helm|Void mage]], [[Void ranger helm|ranger]], or [[Void melee helm|melee helm]].\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Farming|pic=Farming icon}} Farming===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Growth time\n!Diary\n!Difficulty\n|-\n|Plant [[Jute seed]]s in the [[farming patch]] north of [[McGrubor's Wood]].\n|{{NA|None}}\n|{{SCP|Farming|13|sort=yes}}\n|3 [[Jute seed]]s, [[rake]], and a [[seed dibber]].\n|{{NA|None}}\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=1|Easy\n|-\n|Place a [[Scarecrow]] in the [[Morytania]] [[Flower (patch)]].\n|{{NA|None}}\n|{{SCP|Farming|23|sort=yes}}(47 for [[Ironman mode|Ironman accounts]] since [[Watermelon]]s can only be obtained through [[Farming]])\n|[[Scarecrow]] (use an [[empty sack]] on a [[hay bale]] or a haystack to make a [[hay sack]], then combine it with a [[bronze spear]] and then a [[watermelon]]).\n|{{NA|None}}\n|[[Morytania Diary|Morytania]]\n|data-sort-value=1|Easy\n|-\n|Harvest some [[Strawberry|strawberries]] from the [[Ardougne]] [[farming patch]].\n|{{NA|None}}\n|{{SCP|Farming|31|sort=yes}}\n|3 [[Strawberry seed]]s, farming equipment (rake, spade, seed dibber, and watering can), 1 [[basket of apples]] (recommended)\n|data-sort-value=\"60\"|1 hour\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=2|Medium\n|-\n|Place a [[Scarecrow]] in the [[Falador farm]] flower patch.\n|{{NA|None}}\n|{{SCP|Farming|23|sort=yes}}(47 for [[Ironman mode|Ironman accounts]] since [[Watermelon]]s can only be obtained through [[Farming]])\n|[[Bronze spear]], [[watermelon]], [[hay sack]] (You can fill an [[empty sack]] with hay in the nearby chicken coop)\n|{{NA|None}}\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Pick some [[limpwurt root]] from the [[farming patch]] in [[Catherby]].\n|{{NA|None}}\n|{{SCP|Farming|26|sort=yes}}\n|[[Limpwurt seed]], [[rake]], [[seed dibber]], [[supercompost]] or [[ultracompost]] (recommended)\n|data-sort-value=\"20\"|20 minutes\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Grow a healthy [[fruit tree]] in the patch near [[Brimhaven]].\n|{{NA|None}}\n|{{SCP|Farming|27|sort=yes}}\n|Any [[fruit tree seed]] (or its [[sapling]] version), [[spade]], [[rake]]\n|data-sort-value=\"960\"|16 hours\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Enter the [[Farming Guild]].\n|{{NA|None}}\n|{{SCP|Farming|45|sort=yes}}\n|{{NA|None}}\n|{{NA|None}}\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=2|Medium\n|-\n|Pick a [[white tree fruit]].\n|[[Garden of Tranquillity]]\n|{{SCP|Farming|25|sort=yes}}\n|{{NA|None}}\n|data-sort-value=\"20\"|20 minutes\n|[[Varrock Diary|Varrock]]\n|data-sort-value=2|Medium\n|-\n|Check the health of a [[palm tree]] near [[Tree Gnome Village (location)|Tree Gnome Village]] (not in Gnome Stronghold).\n|{{NA|None}}\n|{{SCP|Farming|68|sort=yes}}\n|1 [[palm tree seed]] (or 1 [[palm sapling]]); recommended: 15 [[papaya fruit]]\n|data-sort-value=\"960\"|16 hours\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Pick some [[poison ivy berries]] from the patch south of [[East Ardougne]] (Near the [[Ardougne Monastery]]).\n|{{NA|None}}\n|{{SCP|Farming|70|sort=yes}}\n|1 [[poison ivy seed]]\n|data-sort-value=\"160\"|2 hours 40 minutes\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Plant some [[Logavano seed]]s at the [[Tithe Farm]].\n|{{NA|None}}\n|{{SCP|Farming|74|sort=yes}}\n|[[Seed dibber]]\n|{{NA|None}}\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=3|Hard\n|-\n|Pick some [[belladonna]] from the [[farming patch]] at [[Draynor Manor]].\n|{{NA|None}}\n|{{SCP|Farming|63|sort=yes}}\n|[[Belladonna seed]], [[seed dibber]], [[spade]], [[rake]], any [[gloves]].\n|data-sort-value=\"320\"|5 Hours 20 minutes\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=3|Hard\n|-\n|Harvest some [[watermelon]] from the [[allotment patch]] on [[Harmony Island]].\n|Started [[The Great Brain Robbery]]\n|{{SCP|Farming|47|sort=yes}}\n|3 [[Watermelon seed]]s, [[farming]] supplies\n|data-sort-value=\"80\"|1 hour 20 minutes\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Harvest some [[Mushroom|Bittercap mushrooms]] from the patch in [[Canifis]].\n|{{NA|None}}\n|{{SCP|Farming|53|sort=yes}}\n|[[Mushroom spore]], [[farming]] supplies\n|data-sort-value=\"240\"|4 hours\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Collect at least 2 [[yew roots]] from the [[tree patch]] in [[Varrock Palace]]. (If boosting, make sure to boost when digging up the [[yew tree (Farming)|yew tree]], not when planting!)\n|{{NA|None}}\n|{{SCP|Farming|68|sort=yes}}{{efn|Required to get two roots from one tree.|name=\"roots2\"}}{{SCP|Woodcutting|60}}\n|[[Yew sapling]], (10 [[Cactus spine]]s recommended), any woodcutting [[axe]], [[spade]]\n|data-sort-value=\"400\"|6 hours 40 minutes\n|[[Varrock Diary|Varrock]]\n|data-sort-value=3|Hard\n|-\n|Check the health of your [[palm tree]] in [[Lletya]]\n|Started [[Mourning's End Part I]]\n|{{SCP|Farming|68|sort=yes}}\n|1 [[palm sapling]], 15 [[Papaya fruit|papayas]] (recommended)\n|data-sort-value=\"960\"|16 hours\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Pick some [[torstol]] from the [[Herb patch|patch]] north of [[East Ardougne]].\n|{{NA|None}}\n|{{SCP|Farming|85|sort=yes}}\n|1 [[torstol seed]], [[ultracompost]] recommended\n|data-sort-value=\"80\"|1 hour 20 minutes\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=4|Elite\n|-\n|Find at least 3 [[magic roots]] at once when digging up your [[Magic tree (Farming)|magic tree]] in [[Falador]].\n|{{NA|None}}\n|{{SCP|Farming|91|sort=yes}}{{SCP|Woodcutting|75}}\n|[[Magic seed]], [[Spade]], any [[Woodcutting axe]]\n|data-sort-value=\"480\"|8 hours\n|[[Falador Diary|Falador]]\n|data-sort-value=4|Elite\n|-\n|Pick some [[dwarf weed]] from the [[herb patch]] at [[Catherby]].\n|{{NA|None}}\n|{{SCP|Farming|79|sort=yes}}\n|[[Dwarf weed seed]], [[seed dibber]], [[spade]], [[rake]], [[ultracompost]] (recommended)\n|data-sort-value=\"80\"|1 hour 20 minutes\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=4|Elite\n|-\n|Check the health of a [[palm tree]] in [[Brimhaven]].\n|{{NA|None}}\n|{{SCP|Farming|68|sort=yes}}\n|1 [[palm tree seed]] (or 1 [[palm sapling]]), 15 [[Papaya fruit]]s (recommended), [[spade]]\n|data-sort-value=\"960\"|16 hours\n|[[Karamja Diary|Karamja]]\n|data-sort-value=4|Elite\n|-\n|Check the health of your [[Calquat tree]] patch.\n|{{NA|None}}\n|{{SCP|Farming|72|sort=yes}}\n|1 [[Calquat tree seed]] (or 1 [[calquat sapling]]), 8 [[poison ivy berries]] (recommended)\n|data-sort-value=\"1280\"|21 hours 20 minutes\n|[[Karamja Diary|Karamja]]\n|data-sort-value=4|Elite\n|-\n|Create your own [[battlestaff]] from scratch within the [[Farming Guild]].\n|{{NA|None}}\n|{{SCP|Farming|85|sort=yes}}{{SCP|Fletching|40}}\n|[[Celastrus seed]], [[knife]], axe. Optional: 8 [[potato cactus]] to protect your crop\n|data-sort-value=\"800\"|13 hours 20 minutes\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=4|Elite\n|-\n|Have [[Prissy Scilla]] protect your [[Magic tree (Farming)|magic tree]].\n|{{NA|None}}\n|{{SCP|Farming|75|sort=yes}}\n|1 [[magic sapling]], 25 [[Coconut]]s\n|{{NA|None}}\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|}\n{{Notelist}}\n\n==={{plinkp|Firemaking|pic=Firemaking icon}} Firemaking===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Chop and burn some [[oak logs]] in the [[Fremennik Province]].\n|{{NA|None}}\n|{{SCP|Firemaking|15|sort=yes}}{{SCP|Woodcutting|15}}\n|Any [[axe]], [[tinderbox]]\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=1|Easy\n|-\n|Chop and burn some [[oak logs]] in [[Lumbridge]].\n|{{NA|None}}\n|{{SCP|Firemaking|15|sort=yes}}{{SCP|Woodcutting|15}}\n|Any [[axe]], [[tinderbox]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=1|Easy\n|-\n|Travel to [[Castle Wars]] by [[Balloon transport system|Hot Air Balloon]].\n|[[Enlightened Journey]]\n|{{SCP|Firemaking|50|sort=yes}}\n|[[Yew log]] (11 yew logs and 1 [[normal log]] to return back if [[balloon]] route hasn't been unlocked yet—you will have to unlock the route and then use it again afterwards. Unlocking the route costs 10 yew logs)\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=2|Medium\n|-\n|Chop and burn some [[willow logs]] in [[Taverley]] (can cut and burn willows just south along water).\n|{{NA|None}}\n|{{SCP|Firemaking|30|sort=yes}}{{SCP|Woodcutting|30}}\n|Any [[Woodcutting axe]], [[tinderbox]]\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Light a [[bullseye lantern]] at the [[Chemist]]'s in [[Rimmington]].\n|{{NA|None}}\n|{{SCP|Firemaking|49|sort=yes}}\n|[[Tinderbox]], [[Bullseye lantern]] (Must be a normal bullseye lantern)\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Subdue the [[Wintertodt]].\n|{{NA|None}}\n|{{SCP|Firemaking|50|sort=yes}}\n|Any [[axe]], [[tinderbox]] (food, [[knife]] and [[hammer]] recommended)\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=2|Medium\n|-\n|Use the [[Balloon transport system|balloon]] to travel from [[Varrock]].\n|[[Enlightened Journey]]\n|{{SCP|Firemaking|40|sort=yes}}\n|10 [[willow logs]] to unlock the [[Varrock]] [[balloon]], and 1 normal [[Logs|log]] to use the balloon to travel from Varrock.\n|[[Varrock Diary|Varrock]]\n|data-sort-value=2|Medium\n|-\n|Chop and burn some [[teak logs]] on [[Ape Atoll]].\n|[[Monkey Madness I]]\n|{{SCP|Firemaking|35|sort=yes}}{{SCP|Woodcutting|35}}\n|Any [[axe]], [[tinderbox]] ('''Note:''' [[Barbarian firemaking]] will not count toward the task! Do not use a [[bow]] to burn the logs!)(If you have {{SCP|Woodcutting|50}}{{SCP|Firemaking|50}} chop and burn some [[mahogany logs]] nearby while you're here to complete a hard diary task.)\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=2|Medium\n|-\n|Burn some [[yew logs]] on the [[Nardah]] Mayor's balcony.\n|{{NA|None}}\n|{{SCP|Firemaking|60|sort=yes}}\n|1 [[yew log]], [[tinderbox]]\n|[[Desert Diary|Desert]]\n|data-sort-value=3|Hard\n|-\n|Burn some [[maple logs]] with a bow in [[Seers' Village]].\n|[[Barbarian Firemaking]]\n|{{SCP|Firemaking|65|sort=yes}}\n|[[Maple logs]], any [[bow]] (except [[Ogre bow]], [[Crystal bow]], [[Twisted bow]], [[Dark bow]], [[Craw's bow]], or [[Cursed goblin bow]])\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Light your [[mining helmet]] in the [[Lumbridge Castle]] basement.\n|{{NA|None}}\n|{{SCP|Firemaking|65|sort=yes}}\n|[[Tinderbox]], [[mining helmet]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=3|Hard\n|-\n|Chop and burn some [[mahogany logs]] on [[Mos Le'Harmless]].\n|[[Cabin Fever]]\n|{{SCP|Firemaking|50|sort=yes}}{{SCP|Woodcutting|50}}\n|Any [[Woodcutting axe]] and [[tinderbox]] (a bow '''cannot''' be used). [[Witchwood icon]] recommended while traversing the [[Mos Le'Harmless Cave]] along with any [[light source]].\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Chop some [[yew logs]] in [[Varrock]] and burn them at the top of the Varrock church.\n|{{NA|None}}\n|{{SCP|Firemaking|60|sort=yes}}{{SCP|Woodcutting|60}}\n|Any woodcutting [[axe]], [[tinderbox]]. [[Barbarian firemaking]] does not work. Can be completed with a grown [[Yew tree (Farming)|yew tree]]. Use the church in the north-east corner of the city.\n|[[Varrock Diary|Varrock]]\n|data-sort-value=3|Hard\n|-\n|Chop and burn some [[mahogany logs]] on [[Ape Atoll]].\n|[[Monkey Madness I]]\n|{{SCP|Firemaking|50|sort=yes}}{{SCP|Woodcutting|50}}\n|Any [[Woodcutting axe]], [[tinderbox]]\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Construct a pyre ship from [[magic logs]].\n|[[Barbarian Firemaking]]\n|{{SCP|Firemaking|85|sort=yes}}{{SCP|Crafting|85}}\n|[[Magic logs]], [[chewed bones]], [[tinderbox]], any [[woodcutting axe]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=4|Elite\n|-\n|Cremate any [[Shade remains]] on a [[Magic pyre logs|Magic]] or [[Redwood pyre logs|Redwood pyre]].\n|[[Shades of Mort'ton]]\n|{{SCP|Firemaking|80|sort=yes}}\n|[[Magic pyre logs]], any [[shade remains]], [[tinderbox]]\n|[[Morytania Diary|Morytania]]\n|data-sort-value=4|Elite\n|-\n|Cut and burn some [[magic logs]] in the [[Resource Area]].\n|{{NA|None}}\n|{{SCP|Firemaking|75|sort=yes}}{{SCP|Woodcutting|75}}\n|Any [[Woodcutting axe]], [[tinderbox]], and {{Coins|7500}} ({{NoCoins|3750}} if hard tasks are done, {{NoCoins|6000}} with medium). You cannot use a shortbow to light the magic log (Barbarian firemaking) to finish this task.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Fishing|pic=Fishing icon}} Fishing===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Go out fishing on the [[Fishing Trawler]].\n|{{NA|None}}\n|{{SCP|Fishing|15}}\n|[[Fishing Trawler]]—only need to start the minigame\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=1|Easy\n|-\n|Catch a [[mackerel]] at [[Catherby]].\n|{{NA|None}}\n|{{SCP|Fishing|16|sort=yes}}\n|[[Big fishing net]] (or {{Coins|20}} to buy one at the [[Harry's Fishing Shop|Catherby Fishing Shop]])\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=1|Easy\n|-\n|Fish a [[Trout]] from the [[River Molch]]. Spot located south of [[Farming Guild]].\n|{{NA|None}}\n|{{SCP|Fishing|20|sort=yes}}\n|[[Fly fishing rod]], [[Feather]]s\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=1|Easy\n|-\n|Catch some [[anchovies]] in [[Al Kharid]].\n|{{NA|None}}\n|{{SCP|Fishing|15|sort=yes}}\n|[[Small fishing net]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=1|Easy\n|-\n|Catch some [[trout]] in the [[River Lum]] at [[Barbarian Village]].\n|{{NA|None}}\n|{{SCP|Fishing|20|sort=yes}}\n|[[Fly fishing rod]] and some [[Feather]]s\n|[[Varrock Diary|Varrock]]\n|data-sort-value=1|Easy\n|-\n|Catch and cook a [[bass]] in [[Catherby]].\n|{{NA|None}}\n|{{SCP|Fishing|46|sort=yes}}{{SCP|Cooking|43}}\n|[[Big fishing net]] (or {{Coins|20}} to buy one at the [[Harry's Fishing Shop|Catherby Fishing Shop]])\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Catch a [[Raw karambwan|karambwan]].\n|[[Tai Bwo Wannai Trio]] (partial completion)\n|{{SCP|Fishing|65|sort=yes}}\n|[[Karambwan vessel]], [[raw karambwanji]], [[small fishing net]]\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Catch a [[Bluegill]] on [[Lake Molch]].\n|{{NA|None}}\n|{{SCP|Fishing|43|sort=yes}}{{SCP|Hunter|35}}\n|[[King worm]] or [[fish chunks]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=2|Medium\n|-\n|Catch some [[salmon]] in [[Lumbridge]].\n|{{NA|None}}\n|{{SCP|Fishing|30|sort=yes}}\n|[[Fly fishing rod]], [[Feather]]s\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Fish some [[bass]] on [[Ape Atoll]].\n|[[Monkey Madness I]]\n|{{SCP|Fishing|46|sort=yes}}\n|[[Big fishing net]]\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=2|Medium\n|-\n|Catch a [[leaping sturgeon]].\n|[[Barbarian Fishing|Barbarian Training - Fishing]]\n|{{SCP|Fishing|70|sort=yes}}{{SCP|Agility|45}}<br/>{{SCP|Strength|45}}\n|[[Barbarian rod]], [[fishing bait]] or [[Feather]]s\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Catch and cook a [[monkfish]] in [[Piscatoris]].\n|[[Swan Song]]\n|{{SCP|Fishing|62|sort=yes}}{{SCP|Cooking|62}}\n|[[Small fishing net]] (A [[Range]] can be found in the south-west building in Piscatoris)\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Fish some [[raw lava eel]] in the [[Wilderness]].\n|{{NA|None}}\n|{{SCP|Fishing|53|sort=yes}}\n|[[Oily fishing rod]], [[fishing bait]], [[slash weapon]] to cut the webs.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=3|Hard\n|-\n|Catch a [[manta ray]] in the [[Fishing Trawler]] and cook it in [[Port Khazard]].\n|{{NA|None}}\n|{{SCP|Fishing|81|sort=yes}}{{SCP|Cooking|91}}\n|Multiple [[Raw manta ray]]s may be needed in case the player burns them. [[Fishing]] boosts '''do not work''' for the [[Fishing Trawler]] minigame. However, [[Cooking]] may be boosted.\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=4|Elite\n|-\n|Fish and cook 5 [[Shark]]s in [[Catherby]] (at the range next to the bank) using the [[Cooking gauntlets]].\n|[[Family Crest]]\n|{{SCP|Fishing|76|sort=yes}}{{SCP|Cooking|80}}\n|[[Harpoon]], [[cooking gauntlets]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=4|Elite\n|-\n|Catch an [[anglerfish]] and cook it whilst in [[Great Kourend]].\n|{{NA|None}}\n|{{SCP|Fishing|82|sort=yes}}{{SCP|Cooking|84}}\n|[[Fishing rod]] and [[sandworms]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=4|Elite\n|-\n|Catch a [[shark]] in [[Burgh de Rott]] with your bare hands.\n|[[In Aid of the Myreque]], parts of [[Barbarian Training]]\n|{{SCP|Fishing|96|sort=yes}}{{SCP|strength|76}}\n|{{NA|None}}\n|[[Morytania Diary|Morytania]]\n|data-sort-value=4|Elite\n|-\n|[[Raw dark crab|Fish]] and cook a [[dark crab]] in the [[Resource Area]].\n|{{NA|None}}\n|{{SCP|Fishing|85|sort=yes}}{{SCP|Cooking|90}}\n|[[Lobster pot]], [[dark fishing bait]], and {{Coins|7500}} ({{NoCoins|3750}} if hard tasks are done, {{NoCoins|6000}} with medium).\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Fletching|pic=Fletching icon}} Fletching===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Claim any [[Chompy bird hat]] from [[Rantz]].\n|[[Big Chompy Bird Hunting]]\n|{{SCP|Fletching|5|sort=yes}}{{SCP|Ranged|30}}\n|Must have killed at least 30 [[Chompy bird]]s or [[Jubbly bird]]s with [[ogre bellows]], [[ogre bow]] or [[comp ogre bow]], and [[Ogre arrow]]s. '''Must bring the bow to collect hat.'''\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=1|Easy\n|-\n|Fletch an [[oak shortbow]] in the [[Gnome Stronghold]] ([[oak tree]] just north of the bank by [[Nieve]]/[[Steve]]).\n|{{NA|None}}\n|{{SCP|Fletching|20|sort=yes}}\n|[[Oak shortbow (u)]], [[bow string]] ([[axe]] and [[knife]] optional, although it is not required)\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=1|Easy\n|-\n|String a [[maple shortbow]] in [[Seers' Village]] bank.\n|{{NA|None}}\n|{{SCP|Fletching|50|sort=yes}}\n|[[Maple shortbow (u)]], [[bow string]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Claim a [[Chompy bird hat]] from [[Rantz]] after registering at least 125 kills.\n|[[Big Chompy Bird Hunting]]\n|{{SCP|Fletching|5|sort=yes}}{{SCP|Ranged|30}}\n|[[Ogre bellows]], [[ogre bow]] or [[comp ogre bow]], [[Ogre arrow]]s\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=2|Medium\n|-\n|Create a [[yew longbow]] from scratch around [[Seers' Village]].\n|{{NA|None}}\n|{{SCP|Fletching|70|sort=yes}}{{SCP|Woodcutting|60}}<br/>{{SCP|Crafting|10}} (if crafting your own bowstring)\n|Any [[woodcutting axe]], [[bow string]], [[knife]] (one spawns south of Seers' Village bank)\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Claim a [[Chompy bird hat]] from [[Rantz]] after registering at least 300 kills.\n|[[Big Chompy Bird Hunting]]\n|{{SCP|Fletching|5|sort=yes}}{{SCP|Ranged|30}}\n|[[Ogre bellows]], [[ogre bow]] or [[comp ogre bow]], [[Ogre arrow]]s/brutal arrows\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Fletch some [[Dragon dart]]s at the [[Bedabin Camp]].\n|[[The Tourist Trap]]\n|{{SCP|Fletching|95|sort=yes}}\n|At least 1 [[dragon dart tip]] and 1 [[feather]]. You MUST be standing in one of the tents for this to work.\n|[[Desert Diary|Desert]]\n|data-sort-value=4|Elite\n|-\n|Create your own [[battlestaff]] from scratch within the [[Farming Guild]].\n|{{NA|None}}\n|{{SCP|Fletching|40|sort=yes}}{{SCP|Farming|85}}\n|[[Celastrus seed]], [[knife]], axe. Optional: 8 [[potato cactus]] to protect your crop\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=4|Elite\n|-\n|Smith and fletch ten [[Rune dart]]s within [[Varrock]].\n|[[The Tourist Trap]]\n|{{SCP|Fletching|81|sort=yes}}{{SCP|Smithing|89}}\n|1 [[runite bar]], 10 [[Feather]]s, [[hammer]]\n|[[Varrock Diary|Varrock]]\n|data-sort-value=4|Elite\n|-\n|Claim a [[Chompy bird hat]] from [[Rantz]] after registering at least 1,000 kills.\n|[[Big Chompy Bird Hunting]]\n|{{SCP|Fletching|5|sort=yes}}{{SCP|Ranged|30}}\n|[[Ogre bellows]], [[ogre bow]] or [[comp ogre bow]], [[Ogre arrow]]s\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|-\n|Fletch a [[magic longbow]] in the [[Tirannwn|Elven lands]].\n|[[Mourning's End Part I]] OR [[Fairytale I - Growing Pains]] (use fairy ring code {{fairycode|DLR}})\n|{{SCP|Fletching|85|sort=yes}}\n|[[Magic longbow (u)]], [[bow string]]\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Herblore|pic=Herblore icon}} Herblore===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Create a [[strength potion]] in the [[The Deeper Lode|Deeper Lode]] pub in [[Lovakengj]].\n|[[Druidic Ritual]]\n|{{SCP|Herblore|12|sort=yes}}\n|[[Tarromin potion (unf)]], [[limpwurt root]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=1|Easy\n|-\n|Create a [[combat potion]] in the [[desert]].\n|[[Druidic Ritual]]\n|{{SCP|Herblore|36|sort=yes}}\n|[[Harralander potion (unf)]], [[goat horn dust]] (must be made in the [[desert]] and not within city limits)\n|[[Desert Diary|Desert]]\n|data-sort-value=2|Medium\n|-\n|Create a [[Superantipoison]] potion from scratch in the [[Seers' Village]]/[[Catherby]] area.\n|[[Druidic Ritual]]\n|{{SCP|Herblore|48|sort=yes}}\n|[[Vial of water]], [[irit leaf]], [[unicorn horn dust]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Mix a [[Guthix balance]] potion while in [[Morytania]].\n|Partial completion of [[In Aid of the Myreque]]\n|{{SCP|Herblore|22|sort=yes}}\n|[[Restore potion]], [[garlic]] and [[silver dust]]\n|[[Morytania Diary|Morytania]]\n|data-sort-value=2|Medium\n|-\n|Mix a [[Super defence]] potion in the [[Fremennik Province]].\n|[[Druidic Ritual]]\n|{{SCP|Herblore|66|sort=yes}}\n|\n[[Vial of water]], [[cadantine]], [[white berries]]—<br/>\n[[Cadantine potion (unf)]] with white berries also works.<br/>\n'''''Note:''' Assembling the potion in [[Rellekka Hunter area]], Jatizso, or [[Neitiznot]] will '''not''' complete the diary task.''\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=3|Hard\n|-\n|Mix a [[Saradomin brew]] in [[Falador East Bank]].\n|[[Druidic Ritual]]\n|{{SCP|Herblore|81|sort=yes}}\n|[[Toadflax potion (unf)]], [[crushed nest]]\n|[[Falador Diary|Falador]]\n|data-sort-value=4|Elite\n|-\n|Mix a [[Stamina mix]] on top of the [[Seers' Village]] bank.\n|[[Barbarian Herblore]]\n|{{SCP|Herblore|86|sort=yes}}{{SCP|Agility|60}}\n|[[Stamina potion]](2), [[caviar]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=4|Elite\n|-\n|Create an [[Anti-venom]] potion whilst standing in the [[Horseshoe mine]].\n|[[Druidic Ritual]]\n|{{SCP|Herblore|87|sort=yes}}\n|1 [[Antidote++]], 5-20 [[Zulrah's scales]] (5 per dose)\n|[[Karamja Diary|Karamja]]\n|data-sort-value=4|Elite\n|-\n|Create a [[Super combat potion]] in [[Varrock West Bank]].\n|[[Druidic Ritual]]\n|{{SCP|Herblore|90|sort=yes}}\n|[[Super attack]] (4), [[super strength]] (4), [[super defence]] (4), [[torstol]]\n|[[Varrock Diary|Varrock]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Hitpoints|pic=Hitpoints icon}} Hitpoints===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Kill each of the [[God Wars Dungeon]] generals.\n|[[Troll Stronghold]]\n|{{SCP|Hitpoints|70|sort=yes}}{{SCP|Strength|70}}<br/>{{SCP|Agility|70}}{{SCP|Ranged|70}}\n|Combat equipment to kill the [[God Wars Dungeon]] generals, high {{SCP|Combat}} strongly recommended. '''Boosts cannot be used'''. You must deal the most damage to the boss in order for the kill to count. See each individual pages for recommended gear/setups.\n'''Note:''' Unlike other similar tasks, kills do not reset upon logging out, hopping worlds, or completing other Achievement Diary tasks. They also do not provide a task update message (\"''Achievement Diary Stage Task - Current stage: x''\").\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=4|Elite\n|-\n|Equip any complete [[Void Knight equipment|void set]].\n|{{NA|None}}\n|\n{{SCP|Combat|40}}<br />\n{{Nowrap|{{SCP|Defence|42}}{{SCP|Attack}}{{SCP|Strength}}{{SCP|Ranged}}{{SCP|Magic}}{{SCP|Hitpoints}}}}<br />\n{{SCP|Prayer|22}}\n|[[Void knight top]]; [[Void knight robe]]; [[Void knight gloves]]; and [[Void mage helm|Void mage]], [[Void ranger helm|ranger]], or [[Void melee helm|melee helm]].\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Hunter|pic=Hunter icon}} Hunter===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Catch a [[golden warbler]].\n|{{NA|None}}\n|{{SCP|Hunter|5|sort=yes}}\n|[[Bird snare]]. [[Necklace of passage|Necklace of Passage]] takes you right there.\n|[[Desert Diary|Desert]]\n|data-sort-value=1|Easy\n|-\n|Catch a [[cerulean twitch]].\n|{{NA|None}}\n|{{SCP|Hunter|11|sort=yes}}\n|[[Bird snare]]\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=1|Easy\n|-\n|Catch a [[copper longtail]].\n|{{NA|None}}\n|{{SCP|Hunter|9|sort=yes}}\n|[[Bird snare]]. Located north of Eagles' Peak. Fairy Ring {{fairycode|AKQ}} will put you directly at the location.\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=1|Easy\n|-\n|Catch an [[Orange salamander (Hunter)|orange salamander]].\n|{{NA|None}}\n|{{SCP|Hunter|47|sort=yes}}\n|At least 1 [[rope]] and [[small fishing net]]\n|[[Desert Diary|Desert]]\n|data-sort-value=2|Medium\n|-\n|Catch a [[Snowy knight]].\n|{{NA|None}}\n|{{SCP|Hunter|35|sort=yes}}\n|[[Butterfly net]] and [[butterfly jar]]\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=2|Medium\n|-\n|Trap a [[Horned Graahk]].\n|{{NA|None}}\n|{{SCP|Hunter|41|sort=yes}}\n|[[Teasing stick]], [[knife]], [[logs]]. Located just west of Fairy ring {{fairycode|CKR}}.\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Catch a [[Bluegill]] on [[Lake Molch]].\n|{{NA|None}}\n|{{SCP|Hunter|35|sort=yes}}{{SCP|Fishing|43}}\n|[[King worm]] or [[fish chunks]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=2|Medium\n|-\n|Catch a [[Chinchompa (Hunter)|chinchompa]] in the [[Kourend Woodland]].\n|Partial completion of [[Eagles' Peak]]\n(Until you learn how to use box traps) \n|{{SCP|Hunter|53|sort=yes}}\n|[[Box trap]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=2|Medium\n|-\n|Catch an [[Essence impling|essence]] or [[eclectic impling]] in [[Puro-Puro]].\n|[[Lost City]]\n|{{SCP|Hunter|42/50|sort=yes}}\n|[[Dramen staff|Dramen]] or [[Lunar staff]], [[Butterfly net]], [[impling jar]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Catch a [[Swamp lizard (Hunter)|swamp lizard]].\n|{{NA|None}}\n|{{SCP|Hunter|29|sort=yes}}\n|At least 1 [[rope]] and [[small fishing net]]\n|[[Morytania Diary|Morytania]]\n|data-sort-value=2|Medium\n|-\n|Trap a [[Spined larupia]].\n|{{NA|None}}\n|{{SCP|Hunter|31|sort=yes}}\n|[[Teasing stick]], [[logs]], [[knife]]. Located in [[Feldip Hunter area]], south-west of Fairy Ring {{fairycode|AKS}}.\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=2|Medium\n|-\n|Catch a [[Red salamander (Hunter)|Red Salamander]].\n|{{NA|None}}\n|{{SCP|Hunter|59|sort=yes}}\n|At least 1 [[rope]] and [[small fishing net]]\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Catch a [[Sabre-toothed kyatt]].\n|{{NA|None}}\n|{{SCP|Hunter|55|sort=yes}}\n|[[Teasing stick]], [[logs]], [[axe]], [[knife]]\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=3|Hard\n|-\n|Trade furs with the [[Fancy-dress shop owner|Fancy Dress Seller]] for a [[Spottier cape]] and equip it.\n|{{NA|None}}\n|{{SCP|Hunter|66|sort=yes}} ('''cannot''' be boosted).\n{{SCP|Hunter|69}} for [[Ironman Mode]] players to hunt the [[Dashing kebbit]]s. (this one can be boosted)\n|2 [[Dashing kebbit fur]], {{Coins|800}}.\n|[[Varrock Diary|Varrock]]\n|data-sort-value=3|Hard\n|-\n|Catch a [[dashing kebbit]].\n|{{NA|None}}\n|{{SCP|Hunter|69|sort=yes}}\n|(66 Hunter and Hunting potion) {{Coins|500}}\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Catch a [[Black salamander (Hunter)|black salamander]].\n|{{NA|None}}\n|{{SCP|Hunter|67|sort=yes}}\n|[[Small fishing net]], [[rope]]\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=3|Hard\n|}\n\n==={{plinkp|Magic|pic=Magic icon}} Magic===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!Items required\n!Diary\n!Difficulty\n|-\n|Cast [[Low Level Alchemy|Low Alchemy]] at the [[Fountain of Rune]].\n|{{NA|None}}\n|{{SCP|Magic|21|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\nAny item that can be alched.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=1|Easy\n|-\n|Equip an [[Iban's upgraded staff]] or upgrade an [[Iban's staff]].\n|[[Underground Pass]]\n|{{SCP|Magic|50|sort=yes}}{{SCP|Attack|50}}\n|\n{{Coins|200,000}} and [[Iban's staff]], or [[Iban's staff (u)|Iban's upgraded staff]].<br/>\n50 [[Attack]] is not needed if Iban's staff has not been upgraded yet, simply upgrade the staff to complete the task. Iban's upgraded staff is obtained by bringing Iban's staff to the [[Dark Mage]], located in [[West Ardougne]] at the last house before the entrance of the [[Underground Pass (dungeon)|Underground Pass]].\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=2|Medium\n|-\n|Cast the [[Ardougne Teleport]] spell.\n|[[Plague City]]\n|{{SCP|Magic|51|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Water=2|Law=2}}\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=2|Medium\n|-\n|[[Telegrab]] some [[Wine of zamorak]] at the [[Chaos Temple (Asgarnia)|Chaos Temple]] by the [[Wilderness]].\n|{{NA|None}}\n|{{SCP|Magic|33|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Air=1|Law=1}}<br/>\n'''Note:''' The [[Chaos Temple (Asgarnia)|temple]] is not actually in the [[Wilderness]]—it is simply by the edge of it. If the temple is ridden with bots, you are able to place your own wine and telegrab that, or go up to the upstairs wine located at the ladder at the entrance, if over {{SCP|Total|500|link=yes}}.\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Teleport to [[Falador]].\n|{{NA|None}}\n|{{SCP|Magic|37|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Air=3|Water=1|Law=1}}\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Teleport to [[Camelot]].\n|{{NA|None}}\n|{{SCP|Magic|45|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Air=5|Law=1}}\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Cast the [[Lumbridge Teleport]] spell.\n|{{NA|None}}\n|{{SCP|Magic|31|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Law=1|Air=3|Earth=1}}\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Cast the teleport to [[Varrock]] spell.\n|{{NA|None}}\n|{{SCP|Magic|25|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Law=1|Air=3|Fire=1}}\n|[[Varrock Diary|Varrock]]\n|data-sort-value=2|Medium\n|-\n|[[Charge Earth Orb|Charge]] an [[earth orb]].\n|{{NA|None}}\n|{{SCP|Magic|60|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Cosmic=3|Earth=30|Orb=1}}\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=2|Medium\n|-\n|Teleport to the [[Watchtower (building)|Watchtower]].\n|[[Watchtower]]\n|{{SCP|Magic|58|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Earth=2|Law=2}}\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Enter the [[Wizards' Guild|Magic Guild]].\n|{{NA|None}}\n|{{SCP|Magic|66|sort=yes}}\n|{{NA|None}}\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Refill your [[Waterskin]]s in the [[Desert]] using [[Lunar spells]].\n|[[Dream Mentor]]\n|{{SCP|Magic|68|sort=yes}}\n|\n[[Lunar spellbook]]:<br/>\n{{RuneReq|Astral=1|Fire=1|Water=3}} and an empty [[waterskin]].<br/>\n'''Note:''' Must be done within the desert itself, away from any city.\n|[[Desert Diary|Desert]]\n|data-sort-value=3|Hard\n|-\n|Teleport to [[Trollheim]].\n|[[Eadgar's Ruse]]\n|{{SCP|Magic|61|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Law=2|Fire=2}}\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=3|Hard\n|-\n|Teleport to [[Waterbirth Island]].\n|[[Lunar Diplomacy]]\n|{{SCP|Magic|72|sort=yes}}\n|\n[[Lunar spellbook]]:<br/>\n{{RuneReq|Law=1|Astral=2|Water=1}}\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=3|Hard\n|-\n|Charge a [[water orb]].\n|{{NA|None}}\n|{{SCP|Magic|56|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Cosmic=3|Water=30|Orb=1}} and either a [[dusty key]] or at least {{SCP|Agility|70}} for the shortcut.\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Teleport to [[Paddewwa]].\n|[[Desert Treasure I]]\n|{{SCP|Magic|54|sort=yes}}\n|\n[[Ancient Magicks]]:<br/>\n{{RuneReq|Law=2|Air=1|Fire=1}}\n|[[Varrock Diary|Varrock]]\n|data-sort-value=3|Hard\n|-\n|Cast [[Monster Examine]] on a [[mountain troll]] south of [[Mount Quidamortem]].\n|[[Dream Mentor]]\n|{{SCP|Magic|66|sort=yes}}\n|\n[[Lunar spellbook]]:<br/>\n{{RuneReq|Astral=1|Mind=1|Cosmic=1}}\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=3|Hard\n|-\n|Craft, string, and enchant an [[amulet of power]] in [[Lumbridge]] (not upstairs in castle).\n|{{NA|None}}\n|{{SCP|Magic|57|sort=yes}}{{SCP|Crafting|70}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Cosmic=1|Earth=10}}, a [[gold bar]], a [[Diamond|cut diamond]], a [[ball of wool]], and an [[amulet mould]].\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=3|Hard\n|-\n|Cast [[Bones to Peaches]] in [[Al Kharid Palace]].\n|{{NA|None}}\n|{{SCP|Magic|60|sort=yes}}<br />\nUnlocked [[Bones to Peaches]] from the [[Mage Training Arena]]\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Nature=2|Earth=4|Water=4}} and any [[Remains#Bones|bones]].<br/>\n'''Note:''' The [[magic tablet]] will '''not''' work.\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=3|Hard\n|-\n|Enter the [[Kharyrll Teleport|Kharyrll portal]] in your [[POH]] through a [[Portal Chamber]].\n|[[Desert Treasure I]]\n|{{SCP|Magic|66|sort=yes}}{{SCP|Construction|50}}\n|\nNone, if you have already built a portal.<br/>\n2 [[limestone brick]]s, a [[hammer]], and a [[saw]] to build a portal focus; 3 [[teak plank]]s, a [[hammer]], and a [[saw]] to build a portal; and {{RuneReq|Law=200|Blood=100}} to attune the portal. Must be in your own POH.<br/>\n'''Note:''' A [[portal nexus]] '''can''' be used instead, requiring {{RuneReq|Law=2000|Blood=1000}}, 4 [[marble block]]s, a [[hammer]], and a [[saw]].\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Kill [[Zulrah]].\n|Started [[Regicide]]\n|None, but high {{SCP|Magic}} and {{SCP|Ranged}} highly recommended\n|Decent [[Magic]] or [[Ranged]] equipment.\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Teleport to [[Ape Atoll]].\n|[[Recipe for Disaster]] ([[Recipe for Disaster/Freeing King Awowogei|Awowogei sub-quest]])\n|{{SCP|Magic|64|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Law=2|Fire=2|Water=2|Banana=1}}\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Cast one of the 3 [[God spells]] against another player in the [[Wilderness]].\n|[[Mage Arena I]]\n|{{SCP|Magic|60|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Blood=|Fire=|Air=}} and a valid staff to cast. Number of runes required and eligible staves depends on the spell:<br/>\n{{plink|Saradomin Strike}}: [[Saradomin staff]] or [[staff of light]].<br/>\n{{plink|Claws of Guthix}}: [[Guthix staff]], [[Void knight mace]], or [[staff of balance]].<br/>\n{{plink|Flames of Zamorak}}: [[Zamorak staff]], [[staff of the dead]], or [[toxic staff of the dead]].<br/>\nTo cast any of the [[god spells]] outside the [[Mage Arena]], you must first cast it 100 times in the arena (casting the spell on another player while inside the Mage Arena does not require 100 casts).<br/>\n'''Note:''' [[Splashing]] the spell will '''not''' count.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=3|Hard\n|-\n|[[Charge Air Orb|Charge]] an [[air orb]].\n|{{NA|None}}\n|{{SCP|Magic|66|sort=yes}}\n|\n[[Standard spellbook]]:<br/>\n{{RuneReq|Air=30|Cosmic=3|Orb=1}}\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=3|Hard\n|-\n|Cast [[Ice Barrage]] on another player within [[Castle Wars]].\n|[[Desert Treasure I]]\n|{{SCP|Magic|94|sort=yes}}\n|\n[[Ancient Magicks]]:<br/>\n{{RuneReq|Death=4|Blood=2|Water=6}}<br/>\n'''Note:''' [[Splashing]] the spell will '''not''' count.\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=4|Elite\n|-\n|Cast [[Ice Barrage]] against a foe in the [[Desert]].\n|[[Desert Treasure I]]\n|{{SCP|Magic|94|sort=yes}}\n|\n[[Ancient Magicks]]:<br/>\n{{RuneReq|Death=4|Blood=2|Water=6}}<br/>\n'''Note:''' [[Splashing]] the spell will '''not''' count. Must be done within the desert itself, away from any city.\n|[[Desert Diary|Desert]]\n|data-sort-value=4|Elite\n|-\n|Teleport to [[Catherby]].\n|[[Lunar Diplomacy]]\n|{{SCP|Magic|87|sort=yes}}\n|\n[[Lunar spellbook]]:<br/>\n{{RuneReq|Law=3|Astral=3|Water=10}}\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=4|Elite\n|-\n|Create an [[Ape atoll teleport (tablet)|Ape Atoll teleport tablet]].\n|[[Monkey Madness I]]\n|{{SCP|Magic|90|sort=yes}}\n|\n[[Arceuus spellbook]]:<br/>\n{{RuneReq|Soul=2|Blood=2|Law=2}} and a [[dark essence block]]. \n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=4|Elite\n|-\n|Loot the [[Barrows chest]] while wearing any complete [[Barrows equipment|Barrows set]].\n|{{NA|None}}\n| data-sort-value=\"70\" |\nCombat requirement varies per set, see below:<br/>\n{{SCP|Defence|70}}{{SCP|Attack}}{{SCP|Magic}} for [[Ahrim the Blighted's equipment|Ahrim's]]<br/>\n{{SCP|Defence|70}}{{SCP|Attack}} for [[Guthan the Infested's equipment|Guthan's]] or [[Verac the Defiled's equipment|Verac's]]<br/>\n{{SCP|Defence|70}}{{SCP|Attack}}{{SCP|Strength}} for [[Dharok the Wretched's equipment|Dharok's]] or [[Torag the Corrupted's equipment|Torag's]]<br/>\n{{SCP|Defence|70}}{{SCP|Ranged}} for [[Karil the Tainted's equipment|Karil's]]\n|Any full [[Barrows equipment]] set.\n|[[Morytania Diary|Morytania]]\n|data-sort-value=4|Elite\n|-\n|Fertilize the [[Morytania]] [[herb patch]] using [[Lunar spells]] ([[Fertile Soil]]).\n|[[Lunar Diplomacy]]\n|{{SCP|Magic|83|sort=yes}}\n|\n[[Lunar spellbook]]:<br/>\n{{RuneReq|Nature=2|Astral=3|Earth=15}}\n|[[Morytania Diary|Morytania]]\n|data-sort-value=4|Elite\n|-\n|Use [[Lunar magic]] to make 20 [[Mahogany plank]]s in the [[Varrock]] [[Lumber Yard]] ([[Plank Make]]).\n|[[Dream Mentor]]\n|{{SCP|Magic|86|sort=yes}}\n|\n[[Lunar spellbook]]:<br/>\n{{RuneReq|Nature=20|Astral=40|Earth=300}}, {{Coins|21,000}}, and 20 [[mahogany logs]].\n|[[Varrock Diary|Varrock]]\n|data-sort-value=4|Elite\n|-\n|Equip any complete [[Void Knight equipment|void set]].\n|{{NA|None}}\n| data-sort-value=\"42\" |\n{{SCP|Combat|40}}<br />\n{{Nowrap|{{SCP|Defence|42}}{{SCP|Attack}}{{SCP|Strength}}{{SCP|Ranged}}{{SCP|Magic}}{{SCP|Hitpoints}}}}<br />\n{{SCP|Prayer|22}}\n|[[Void knight top]]; [[Void knight robe]]; [[Void knight gloves]]; and [[Void mage helm|Void mage]], [[Void ranger helm|ranger]], or [[Void melee helm|melee helm]].\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|-\n|Teleport to [[Ghorrock Teleport|Ghorrock]].\n|[[Desert Treasure I]]\n|{{SCP|Magic|96|sort=yes}}\n|\n[[Ancient Magicks]]:<br/>\n{{RuneReq|Law=2|Water=8}}\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Mining|pic=Mining icon}} Mining===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Mine five [[clay]] in the north-eastern desert.\n|{{NA|None}}\n|{{SCP|Mining|5|sort=yes}}\n|Any [[pickaxe]]. [[Necklace of passage|Necklace of Passage]] takes you right there.\n|[[Desert Diary|Desert]]\n|data-sort-value=1|Easy\n|-\n|Smith some [[Blurite limbs]] on [[Doric]]'s anvil.\n|[[The Knight's Sword]], [[Doric's Quest]]\n|{{SCP|Mining|10|sort=yes}}{{SCP|Smithing|13}}\n|[[Hammer]], [[Blurite bar]]<br/>Remember, Doric's anvil is the one located north of [[Falador]]; don't mistake it with [[Thurgo]]'s anvil at [[Mudskipper Point]].\n|[[Falador Diary|Falador]]\n|data-sort-value=1|Easy\n|-\n|Craft a [[tiara]] from scratch in [[Rellekka]].\n|[[The Fremennik Trials]]\n|{{SCP|Mining|20|sort=yes}}{{SCP|Crafting|23}}<br />{{SCP|Smithing|20}}\n|[[Tiara mould]], any [[pickaxe]]—mine the [[silver ore]] from the [[Rellekka mine]] located on the east side of [[Rellekka]], a few steps north-west of the [[agility shortcut]] (an [[iron pickaxe]] can be obtained from north of the Rellekka mine). [[Superheating]] will not work.\n'''Note:''' You have to do this process without doing any other task or stage 2 will not complete and you will have to redo the process.\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=1|Easy\n|-\n|Mine some [[Gold ore|gold]] from the rocks on the north-west peninsula of [[Karamja]].\n|{{NA|None}}\n|{{SCP|Mining|40|sort=yes}}\n|Any [[pickaxe]]\n|[[Karamja Diary|Karamja]]\n|data-sort-value=1|Easy\n|-\n|Mine some [[iron ore]] at the [[Mount Karuulm mine]].\n|{{NA|None}}\n|{{SCP|Mining|15|sort=yes}}\n|Any [[pickaxe]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=1|Easy\n|-\n|Mine some [[iron ore]] at the [[Al Kharid mine]].\n|{{NA|None}}\n|{{SCP|Mining|15|sort=yes}}\n|Any [[pickaxe]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=1|Easy\n|-\n|Mine some [[Iron ore|iron]] in the [[south-east Varrock mine]].\n|{{NA|None}}\n|{{SCP|Mining|15|sort=yes}}\n|Any [[pickaxe]]\n|[[Varrock Diary|Varrock]]\n|data-sort-value=1|Easy\n|-\n|Mine some [[iron ore]] near [[Piscatoris]].\n|{{NA|None}}\n|{{SCP|Mining|15|sort=yes}}\n|Any [[pickaxe]]\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=1|Easy\n|-\n|Mine some [[iron ore]] in the [[Wilderness]].\n|{{NA|None}}\n|{{SCP|Mining|15|sort=yes}}\n|Any [[pickaxe]]. [[Iron rocks|Iron]] can be found north-west of the [[Mage of Zamorak]].\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=1|Easy\n|-\n|Mine some [[gold ore]] at the [[Crafting Guild]].\n|{{NA|None}}\n|{{SCP|Mining|40|sort=yes}}{{SCP|Crafting|40}}\n|Any [[pickaxe]], [[brown apron]] or [[Crafting cape]]\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Mine some [[coal]] in [[Rellekka]].\n|[[The Fremennik Trials]]\n|{{SCP|Mining|30|sort=yes}}\n|Any [[pickaxe]] (an [[iron pickaxe]] can be obtained from north of the [[Rellekka mine]])\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=2|Medium\n|-\n|Mine some [[gold]] at the [[Arzinian Mine]].\n|Near full completion of [[Between a Rock...]]\n|{{SCP|Mining|40|sort=yes}}\n|Any [[pickaxe]], [[gold helmet]], and 2 [[coins]] or a [[ring of charos (a)]]\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=2|Medium\n|-\n|Mine some [[coal]] near the [[coal trucks]].\n|{{NA|None}}\n|{{SCP|Mining|30|sort=yes}}\n|Any [[pickaxe]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Mine a [[red topaz]] from a [[gem rock]] (either from [[Shilo Village (location)|Shilo Village]] or [[Tai Bwo Wannai Cleanup]]).\n|[[Shilo Village]] or [[Jungle Potion]]\n|{{SCP|Mining|40|sort=yes}}\n|Any [[pickaxe]]\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Mine some [[volcanic sulphur]].\n|{{NA|None}}\n|{{SCP|Mining|42|sort=yes}}\n|Any [[pickaxe]]\n[[Face mask]], [[gas mask]] or [[slayer helmet]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=2|Medium\n|-\n|Mine some [[gold ore]] underneath the [[Grand Tree]].\n|[[The Grand Tree]]\n|{{SCP|Mining|40|sort=yes}}\n|Any [[pickaxe]]. Enter the mine by climbing down the trapdoor next to [[King Narnode Shareen]], then go north through the roots and head east.\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=2|Medium\n|-\n|Mine some [[mithril ore]] in the [[Wilderness]].\n|{{NA|None}}\n|{{SCP|Mining|55|sort=yes}}\n|Any [[pickaxe]]. Mithril can be found South of the Lava Maze and in the [[Resource Area]].\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=2|Medium\n|-\n|Mine some [[granite]].\n|{{NA|None}}\n|{{SCP|Mining|45|sort=yes}}\n|Any [[pickaxe]]\n|[[Desert Diary|Desert]]\n|data-sort-value=3|Hard\n|-\n|Enter the [[Mining Guild]] wearing a [[Prospector helmet]].\n|{{NA|None}}\n|{{SCP|Mining|60|sort=yes}}\n|[[Prospector helmet]]\n|[[Falador Diary|Falador]]\n|data-sort-value=3|Hard\n|-\n|Mine 5 [[Adamantite ore]]s on [[Jatizso]].\n|[[The Fremennik Isles]]\n|{{SCP|Mining|70|sort=yes}}\n|Any [[pickaxe]]. A [[dragon pickaxe]] is recommended to get a temporary boost from 67 to 70.\n'''''Tip:''' Four [[Adamantite ore]]s spawns are located right next to the entrance inside the mine right outside the walls of Jatizso. Hopping worlds does not reset the counter.''\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=3|Hard\n|-\n|Kill a [[Deathwing]] in the dungeon under the [[Kharazi Jungle]].\n|Partial completion of [[Legends' Quest]]<br/>(Until you access the cave with the Deathwings)\n|{{SCP|Mining|52|sort=yes}}{{SCP|Woodcutting|15}}{{SCP|Strength|50}}{{SCP|Agility|50}}{{SCP|Thieving|50}}\n|Any [[woodcutting axe]], [[machete]], any [[pickaxe]], [[lockpick]]\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Mine some [[Lovakite ore]].\n|{{NA|None}}\n|{{SCP|Mining|65|sort=yes}}\n|Any [[pickaxe]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=3|Hard\n|-\n|Mine some [[mithril ore]] in the [[Abandoned Mine]].\n|[[Haunted Mine]]\n|{{SCP|Mining|55|sort=yes}}\n|Any [[pickaxe]], [[crystal-mine key]] (obtained during [[Haunted Mine]]). [[Mithril rocks|Mithril]] can be found on levels 2 and 6. Alternatively, once you enter the cave look immediately south and use the \"Crawl-through Cart Tunnel Option.\" The key is not needed with this method.\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Mine some [[adamantite ore]] in [[Tirannwn]].\n|[[Regicide]]\n|{{SCP|Mining|70|sort=yes}}\n|Any [[pickaxe]] ([[Dragon pickaxe]] special attack can boost your mining level from 67 to 70.)\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Craft one or more [[Blood rune]]s.\n|{{NA|None}}\n|{{SCP|Mining|38|sort=yes}}{{SCP|Runecraft|77}}<br />{{SCP|Crafting|38}}\n|Any [[pickaxe]] and [[chisel]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=4|Elite\n|-\n|Smith a [[rune scimitar]] from scratch in the [[Resource Area]].\n|{{NA|None}}\n|{{SCP|Mining|85|sort=yes}}{{SCP|Smithing|90}}\n|Any [[pickaxe]], [[hammer]], 16 [[Coal]] and {{Coins|7500}} ({{NoCoins|3750}} if hard tasks are done, {{NoCoins|6000}} with medium).<br/>\n''Note: You must mine a [[Runite Golem|runite golem]] and then smelt '''both''' the [[runite ore]] and coal in the resource area for this to work. The 16 [[coal]] does not need to be mined and can be brought with you prior. Superheat '''does not count here! ''' You may leave the resource area to complete this task. Logging out does '''not''' interfere with the stages.''\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Prayer|pic=Prayer icon}} Prayer===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Pray at the [[Stone circle|Altar of Guthix]] in [[Taverley]] whilst wearing full [[Initiate armour]].\n|[[Recruitment Drive]]\n|{{SCP|Prayer|10|sort=yes}}{{SCP|Defence|20}}\n|[[Initiate armour]]\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Change your family crest to the [[Saradomin]] symbol.\n|{{NA|None}}\n|{{SCP|Prayer|70|sort=yes}}\n|{{Coins|5,000}} ({{NoCoins|10,000}} if your family crest is already the [[Saradomin]] symbol—talk to [[Sir Renitee]] in the [[White Knights' Castle]].)\n|[[Falador Diary|Falador]]\n|data-sort-value=3|Hard\n|-\n|Enter the [[Seers' Village]] courthouse with [[Piety]] turned on.\n|[[King's Ransom]], [[Knight Waves Training Grounds]]\n|{{SCP|Prayer|70|sort=yes}}{{SCP|Defence|70}}\n|{{NA|None}}\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Recharge your [[prayer]] at the [[Emir's Arena]] with [[Smite]] activated.\n|{{NA|None}}\n|{{SCP|Prayer|52|sort=yes}}\n|None. [[Ring of dueling]] is recommended for quick teleportation.\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=3|Hard\n|-\n|Pray at the [[Nature Grotto|Altar of Nature]] in the Nature Grotto with [[Piety]] activated.\n|[[Nature Spirit]], [[King's Ransom]]\n| data-sort-value=\"70\" |\n{{SCP|Prayer|70}}{{SCP|Defence|70}}<br />\n'''(Cannot be boosted)'''\n|None (won't be counted unless you restore at least 1 [[prayer]] point).\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Pray at the altar in [[Varrock Palace]] with [[Smite]] active. ({{FloorNumber|uk=1}}, use the staircase in the kitchen.)\n|{{NA|None}}\n|{{SCP|Prayer|52|sort=yes}}\n|{{NA|None}}\n|[[Varrock Diary|Varrock]]\n|data-sort-value=3|Hard\n|-\n|Restore at least 85 [[Prayer points]] when praying at the [[altar]] in [[Sophanem]].\n|Started [[Icthlarin's Little Helper]]\n|{{SCP|Prayer|85|sort=yes}}\n|None. '''Cannot''' be boosted\n|[[Desert Diary|Desert]]\n|data-sort-value=4|Elite\n|-\n|Equip any complete [[Void Knight equipment|void set]].\n|{{NA|None}}\n|data-sort-value=22|\n{{SCP|Combat|40}}<br />\n{{Nowrap|{{SCP|Defence|42}}{{SCP|Attack}}{{SCP|Strength}}{{SCP|Ranged}}{{SCP|Magic}}{{SCP|Hitpoints}}}}<br />\n{{SCP|Prayer|22}}\n|[[Void knight top]]; [[Void knight robe]]; [[Void knight gloves]]; and [[Void mage helm|Void mage]], [[Void ranger helm|ranger]], or [[Void melee helm|melee helm]].\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Ranged|pic=Ranged icon}} Ranged===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Claim any [[Chompy bird hat]] from [[Rantz]].\n|[[Big Chompy Bird Hunting]]\n|{{SCP|Ranged|30|sort=yes}}{{SCP|Fletching|5}}\n|Must have killed at least 30 [[Chompy bird]]s or [[Jubbly bird]]s with [[ogre bellows]], [[ogre bow]] or [[comp ogre bow]], and [[Ogre arrow]]s. '''Must bring the bow to collect hat.'''\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=1|Easy\n|-\n|Grapple over [[Yanille]]'s south wall.\n|{{NA|None}}\n|{{SCP|Ranged|21|sort=yes}}{{SCP|Strength|38}}{{SCP|Agility|39}}\n|\n[[Mith grapple]] and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=2|Medium\n|-\n|Grapple up and then jump off the north [[Falador]] wall.\n|{{NA|None}}\n|{{SCP|Ranged|19|sort=yes}}{{SCP|Strength|37}}{{SCP|Agility|11}}\n|\n[[Mith grapple]] and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Use the grapple shortcut to get from the [[water obelisk]] to [[Catherby]] shore.\n|{{NA|None}}\n|{{SCP|Ranged|39|sort=yes}}{{SCP|Agility|36}}{{SCP|Strength|22}}\n|\nRope, [[dusty key]] or 70 [[Agility]], [[mith grapple]], and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)<br/>\n'''Recommended:''' [[Anti-dragon shield]] and [[antipoison]].<br/>\n'''Tip:''' Take an [[unpowered orb]] and [[runes]] for [[Charge Water Orb]] for a hard diary task.\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Enter the [[Ranging Guild]].\n|{{NA|None}}\n|{{SCP|Ranged|40|sort=yes}}\n|{{NA|None}}\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Grapple across the [[River Lum]].\n|{{NA|None}}\n|{{SCP|Ranged|37|sort=yes}}{{SCP|Agility|8}}{{SCP|Strength|19}}\n|\n[[Mith grapple]] and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Purchase an [[Ava's accumulator|upgraded device]] from [[Ava]].\n|[[Animal Magnetism]]\n|{{SCP|Ranged|50|sort=yes}}{{Boostable|no}}\n|{{Coins|999}} '''OR''' [[Ava's attractor]], '''AND''' 75 [[steel arrows]]\n'''Note:''' The 75 [[steel arrows]] are required regardless of whether you bring coins or an attractor.\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Claim a [[Chompy bird hat]] from [[Rantz]] after registering at least 125 kills.\n|[[Big Chompy Bird Hunting]]\n|{{SCP|Ranged|30|sort=yes}}{{SCP|Fletching|5}}\n|[[Ogre bellows]], [[ogre bow]] or [[comp ogre bow]], [[Ogre arrow]]s\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=2|Medium\n|-\n|Use the crossbow shortcut south of the volcano.\n|{{NA|None}}\n|{{SCP|Ranged|42|sort=yes}}{{SCP|Agility|53}}{{SCP|Strength|21}}\n|\n[[Mith grapple]] and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Kill [[Zulrah]].\n|Started [[Regicide]]\n|None, but high {{SCP|Magic}} and {{SCP|Ranged}} highly recommended\n|Decent [[Magic]] or [[Ranged]] equipment.\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Claim a [[Chompy bird hat]] from [[Rantz]] after registering at least 300 kills.\n|[[Big Chompy Bird Hunting]]\n|{{SCP|Ranged|30|sort=yes}}{{SCP|Fletching|5}}\n|[[Ogre bellows]], [[ogre bow]] or [[comp ogre bow]], [[Ogre arrow]]s/brutal arrows\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Kill an [[elf]] with a [[crystal bow]].\n|[[Roving Elves]]\n|{{SCP|Ranged|70|sort=yes}}{{SCP|Agility|56}}\n|[[Crystal bow]] ([[Mourners]] do not work.)\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Kill each of the [[God Wars Dungeon]] generals.\n|[[Troll Stronghold]]\n|{{SCP|Ranged|70|sort=yes}}{{SCP|Agility|70}}<br/>{{SCP|Hitpoints|70}}{{SCP|Strength|70}}\n|Combat equipment to kill the [[God Wars Dungeon]] generals, high {{SCP|Combat}} strongly recommended. '''Boosts cannot be used'''. You must deal the most damage to the boss in order for the kill to count. See each individual pages for recommended gear/setups.\n'''Note:''' Unlike other similar tasks, kills do not reset upon logging out, hopping worlds, or completing other Achievement Diary tasks. They also do not provide a task update message (\"''Achievement Diary Stage Task - Current stage: x''\").\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=4|Elite\n|-\n|Grapple across a pylon on the [[Dorgesh-Kaan Agility Course]].\n|[[Death to the Dorgeshuun]]\n|{{SCP|Agility|70|sort=yes}}{{SCP|Ranged|70}}{{SCP|Strength|70}}\n|\nA [[light source]], [[mith grapple]], and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=4|Elite\n|-\n|Loot the [[Barrows chest]] while wearing any complete [[Barrows equipment|Barrows set]].\n|{{NA|None}}\n|data-sort-value=70|\nCombat requirement varies per set, see below:<br/>\n{{SCP|Defence|70}}{{SCP|Ranged}} for [[Karil the Tainted's equipment|Karil's]]<br/>\n{{SCP|Defence|70}}{{SCP|Attack}} for [[Guthan the Infested's equipment|Guthan's]] or [[Verac the Defiled's equipment|Verac's]]<br/>\n{{SCP|Defence|70}}{{SCP|Attack}}{{SCP|Strength}} for [[Dharok the Wretched's equipment|Dharok's]] or [[Torag the Corrupted's equipment|Torag's]]<br/>\n{{SCP|Defence|70}}{{SCP|Attack}}{{SCP|Magic}} for [[Ahrim the Blighted's equipment|Ahrim's]]\n|Any full [[Barrows equipment]] set.\n|[[Morytania Diary|Morytania]]\n|data-sort-value=4|Elite\n|-\n|Claim a [[Chompy bird hat]] from [[Rantz]] after registering at least 1,000 kills.\n|[[Big Chompy Bird Hunting]]\n|{{SCP|Ranged|30|sort=yes}}{{SCP|Fletching|5}}\n|[[Ogre bellows]], [[ogre bow]] or [[comp ogre bow]], [[ogre arrow]]s\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|-\n|Equip any complete [[Void Knight equipment|void set]].\n|{{NA|None}}\n|data-sort-value=42|\n{{SCP|Combat|40}}<br />\n{{Nowrap|{{SCP|Defence|42}}{{SCP|Attack}}{{SCP|Strength}}{{SCP|Ranged}}{{SCP|Magic}}{{SCP|Hitpoints}}}}<br />\n{{SCP|Prayer|22}}\n|[[Void knight top]]; [[Void knight robe]]; [[Void knight gloves]]; and [[Void mage helm|Void mage]], [[Void ranger helm|ranger]], or [[Void melee helm|melee helm]].\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Runecraft|pic=Runecraft icon}} Runecraft===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Craft some [[water rune]]s from Essence.\n|{{NA|None}}\n|{{SCP|Runecraft|5|sort=yes}}\n|[[Rune essence|Rune]] or [[pure essence]] and [[water talisman]], [[water tiara]], or access to the [[Abyss]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=1|Easy\n|-\n|Craft some [[Earth rune]]s from Essence.\n|{{NA|None}}\n|{{SCP|Runecraft|9|sort=yes}}\n|[[Earth talisman]] or [[Earth tiara|tiara]] and 1 [[Rune essence|rune]]/[[pure essence]], or use the [[Abyss]].\n|[[Varrock Diary|Varrock]]\n|data-sort-value=1|Easy\n|-\n|Craft some [[Lava runes]] at the [[fire altar]] in [[Al Kharid]].\n|{{NA|None}}\n|{{SCP|Runecraft|23|sort=yes}}\n|[[Fire talisman]] or [[Fire tiara|tiara]], [[earth talisman]], [[earth runes]], and [[pure essence]]. A [[binding necklace]] is recommended.\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Craft some [[Death rune]]s from Essence.\n|[[Mourning's End Part II]]\n|{{SCP|Runecraft|65|sort=yes}}\n|[[Pure essence]], [[death talisman]] (or [[Runecraft cape]] or access to the [[Abyss]])\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Craft 140 [[Mind rune]]s simultaneously from Essence.\n|{{NA|None}}\n|{{SCP|Runecraft|56|sort=yes}} or {{SCP|Runecraft|44|}} with [[Raiments of the Eye]] set\n|28 [[Pure essence|pure]] or [[rune essence]], [[mind tiara]] or access to the [[Abyss]]\n|[[Falador Diary|Falador]]\n|data-sort-value=3|Hard\n|-\n|Craft some [[nature rune]]s from Essence.\n|{{NA|None}}\n|{{SCP|Runecraft|44|sort=yes}}\n|[[Pure essence]], [[nature talisman]] (or access to the [[Abyss]])\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Craft 56 [[Cosmic runes]] simultaneously from Essence without the use of [[Runic extract|Extracts]].\n|[[Lost City]]\n|{{SCP|Runecraft|59|sort=yes}}\n|28 [[pure essence]] and [[cosmic tiara]] or access to the [[Abyss]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=3|Hard\n|-\n|Craft 252 [[Air Rune]]s simultaneously from Essence without the use of Extracts.\n|{{NA|None}}\n|{{SCP|Runecraft|88|sort=yes}} or {{SCP|Runecraft|55|}} with [[Raiments of the Eye]] set\n|[[Air tiara]] and 28 [[Rune essence|rune]] or [[pure essence]]\n|[[Falador Diary|Falador]]\n|data-sort-value=4|Elite\n|-\n|Craft 56 [[astral rune]]s simultaneously from Essence without the use of Extracts.\n|[[Lunar Diplomacy]]\n|{{SCP|Runecraft|82|sort=yes}}\n|28 [[pure essence]]\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=4|Elite\n|-\n|Craft 56 [[Nature rune]]s simultaneously from Essence without the use of Extracts.\n|{{NA|None}}\n|{{SCP|Runecraft|91|sort=yes}}\n|28 [[pure essence]] and [[nature tiara]], [[catalytic tiara]], or access to the [[Abyss]]\n|[[Karamja Diary|Karamja]]\n|data-sort-value=4|Elite\n|-\n|Craft one or more [[Blood rune]]s from [[Dark essence fragments]].\n|{{NA|None}}\n|{{SCP|Runecraft|77|sort=yes}}{{SCP|Mining|38}}\n{{SCP|Crafting|38}}\n|Any [[pickaxe]] and [[chisel]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=4|Elite\n|-\n|Craft 140 or more [[Water runes]] simultaneously from Essence without the use of Extracts.\n|{{NA|None}}\n|{{SCP|Runecraft|76|sort=yes}} or {{SCP|Runecraft|57|}} with [[Raiments of the Eye]] set\n|28 [[Rune essence|rune]] or [[pure essence]] and [[water tiara]] or access to the [[Abyss]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=4|Elite\n|-\n|Craft 100 or more [[earth runes]] simultaneously from Essence without the use of Extracts.\n|{{NA|None}}\n|{{SCP|Runecraft|78|sort=yes}} or {{SCP|Runecraft|52|}} with [[Raiments of the Eye]] set\n|25 [[Rune essence|rune]]/[[pure essence]]\n|[[Varrock Diary|Varrock]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Slayer|pic=Slayer icon}} Slayer===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Slay a [[cave bug]] in the [[Lumbridge Swamp Caves]].\n|{{NA|None}}\n|{{SCP|Slayer|7|sort=yes}}\n|[[Light source]], [[weapon]], and [[rope]] (if you have never entered the area through the surface of [[Lumbridge Swamp]] before)\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=1|Easy\n|-\n|Kill a [[banshee]] in the [[Slayer Tower]].\n|{{NA|None}}\n|{{SCP|Slayer|15|sort=yes}}\n|None ([[Earmuffs]] or a [[Slayer helmet]] are highly recommended)\n|[[Morytania Diary|Morytania]]\n|data-sort-value=1|Easy\n|-\n|Slay a [[desert lizard]].\n|{{NA|None}}\n|{{SCP|Slayer|22|sort=yes}}\n|[[Ice cooler]]\n|[[Desert Diary|Desert]]\n|data-sort-value=2|Medium\n|-\n|Kill a [[Mogre]] at [[Mudskipper Point]].\n|[[Skippy and the Mogres]]\n|{{SCP|Slayer|32|sort=yes}}\n|[[Fishing explosive]]\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Slay a [[brine rat]].\n|Started [[Olaf's Quest]]\n|{{SCP|Slayer|47|sort=yes}}\n|Any [[weapon]], [[spade]], [[food]] (it is not required to finish the quest to gain access to the [[Brine Rat Cavern]])\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=2|Medium\n|-\n|Kill a [[terror dog]].\n|[[Haunted Mine]], [[Lair of Tarn Razorlor]] ([[miniquest]])\n|{{SCP|Slayer|40|sort=yes}}\n|[[Weapon]] and [[armour]]. A [[Slayer ring]] can teleport you nearby.\n|[[Morytania Diary|Morytania]]\n|data-sort-value=2|Medium\n|-\n|Kill a [[fever spider]] on [[Braindeath Island]].\n|[[Rum Deal]]\n|{{SCP|Slayer|42|sort=yes}}\n|None ([[Slayer gloves]] recommended)\n|[[Morytania Diary|Morytania]]\n|data-sort-value=2|Medium\n|-\n|Kill a [[bloodveld]] in the [[Wilderness God Wars Dungeon]].\n|{{NA|None}}\n|{{SCP|Slayer|50|sort=yes}}\n|[[Weapon]] to kill [[Bloodveld]], [[God Wars Dungeon#God-related protection|God equipment]] to keep the gods' followers passive.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=2|Medium\n|-\n|Slay a [[Dust devil]] in the [[Smoke Dungeon|desert cave]] with a [[Slayer helmet]] equipped.\n|Started [[Desert Treasure I]]\n|{{SCP|Slayer|65|sort=yes}}{{SCP|Defence|10}}\n{{SCP|Crafting|55}}\n|[[Slayer helmet]], [[weapons]] and [[armour]]\n|[[Desert Diary|Desert]]\n|data-sort-value=3|Hard\n|-\n|Kill a [[Skeletal Wyvern]] in the [[Asgarnia Ice Dungeon]].\n|{{NA|None}}\n|{{SCP|Slayer|72|sort=yes}}\n|Decent [[weapons]] and [[armour]], [[Elemental shield|Elemental]] or [[Mind shield]], or a [[Dragonfire shield]] highly recommended\n|[[Falador Diary|Falador]]\n|data-sort-value=3|Hard\n|-\n|Be assigned a [[Slayer]] task by [[Duradel]] in [[Shilo Village (location)|Shilo Village]].\n|[[Shilo Village]]\n|{{SCP|Slayer|50|sort=yes}}{{SCP|Combat|100}}\n|{{NA|None}}\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Kill a [[wyrm]] in the [[Karuulm Slayer Dungeon]].\n|{{NA|None}}\n|{{SCP|Slayer|62|sort=yes}}\n|[[Combat equipment]], [[boots of stone]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=3|Hard\n|-\n|Kill a [[cave horror]].\n|[[Cabin Fever]]\n|{{SCP|Slayer|58|sort=yes}}\n|[[Witchwood icon]], [[light source]]\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Kill a [[spiritual warrior]] in the [[Wilderness God Wars Dungeon]].\n|{{NA|None}}\n|{{SCP|Slayer|68|sort=yes}}\n|[[Weapon]] to kill a [[Spiritual warrior]], [[God Wars Dungeon#God-related protection|God equipment]] to keep the gods' followers passive.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=3|Hard\n|-\n|Slay a [[Spiritual mage]] within the [[God Wars Dungeon]].\n|[[Troll Stronghold]]\n|{{SCP|Slayer|83|sort=yes}}\n|[[Combat equipment]]\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=4|Elite\n|-\n|Kill a [[hydra]] in the [[Karuulm Slayer Dungeon]].\n|{{NA|None}}\n|{{SCP|Slayer|95|sort=yes}}\n|[[Combat equipment]]. Boosts '''can''' be used to kill a standard hydra.\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=4|Elite\n|-\n|Kill an [[abyssal demon]] in the [[Slayer Tower]].\n|{{NA|None}}\n|{{SCP|Slayer|85|sort=yes}}\n|[[Combat equipment]]\n|[[Morytania Diary|Morytania]]\n|data-sort-value=4|Elite\n|-\n|Kill the [[Thermonuclear smoke devil]].\n|{{NA|None}}\n|{{SCP|Slayer|93|sort=yes}}\n|[[Weapons]] and [[armour]]. Boosts '''cannot''' be used.\nSlayer task is '''not''' required for your first kill.\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|-\n|Slay a [[spiritual mage]] inside the [[Wilderness God Wars Dungeon]].\n|{{NA|None}}\n|{{SCP|Slayer|83|sort=yes}}{{SCP|Agility|60}} or {{SCP|Strength|60}}\n|[[Weapon]] to kill [[Spiritual mage]], [[God Wars Dungeon#God-related protection|God equipment]] to keep god followers passive.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Smithing|pic=Smithing icon}} Smithing===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Smith some [[Blurite limbs]] on [[Doric]]'s anvil.\n|[[The Knight's Sword]], [[Doric's Quest]]\n|{{SCP|Smithing|13|sort=yes}}{{SCP|Mining|10}}\n|[[Hammer]], [[Blurite bar]]\nRemember, Doric's anvil is the one located north of [[Falador]]; don't mistake it with [[Thurgo]]'s anvil at [[Mudskipper Point]].\n|[[Falador Diary|Falador]]\n|data-sort-value=1|Easy\n|-\n|Craft a [[tiara]] from scratch in [[Rellekka]].\n|[[The Fremennik Trials]]\n|{{SCP|Smithing|20|sort=yes}}{{SCP|Crafting|23}}{{SCP|Mining|20}}\n|[[Tiara mould]], any [[pickaxe]]—mine the [[silver ore]] from the [[Rellekka mine]] located on the east side of [[Rellekka]], a few steps north-west of the [[agility shortcut]] (an [[iron pickaxe]] can be obtained from north of the Rellekka mine). [[Superheating]] will not work.<br/>'''Note:''' You have to do this process without doing any other task or stage 2 will not complete and you will have to redo the process.\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=1|Easy\n|-\n|Make a batch of [[Cannonball]]s at the [[Port Phasmatys]] furnace.\n|[[Dwarf Cannon]], partial completion of [[Ghosts Ahoy]]\n|{{SCP|Smithing|35|sort=yes}}\n|[[Steel bar]] and [[ammo mould]]\n|[[Morytania Diary|Morytania]]\n|data-sort-value=2|Medium\n|-\n|Smith a [[Gold helmet]] in the [[Resource Area]].\n|[[Between a Rock...]] (partially completed)\n|{{SCP|Smithing|50|sort=yes}}\n|{{Coins|7,500}}, 3 [[gold bar]]s, and a [[hammer]].<br/>\n'''Hint:''' bring a pickaxe to complete another medium task at the same time.<br/>\n'''Note:''' It does not work if you already have a [[gold helmet]]. You need to drop your helmet first, and then make a new one.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=2|Medium\n|-\n|Smith a [[Dragon sq shield]] in [[West Ardougne]].\n|[[Legends' Quest]]\n|{{SCP|Smithing|60|sort=yes}}\n|[[Shield left half]], [[shield right half]], [[hammer]]\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Smith a [[Mithril platebody]] near [[Ardougne]].\n|{{NA|None}}\n|{{SCP|Smithing|68|sort=yes}}\n|5 [[Mithril bar]]s, [[hammer]] (works at [[Port Khazard]], [[Yanille]], or [[West Ardougne]] [[anvil]])\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Create a [[Mithril platebody]] in [[Nardah]].\n|{{NA|None}}\n|{{SCP|Smithing|68|sort=yes}}\n|5 [[Mithril bar]]s, [[hammer]]\n|[[Desert Diary|Desert]]\n|data-sort-value=3|Hard\n|-\n|Obtain the [[Blast Furnace Foreman]]'s permission to use the [[Blast Furnace]] for free.\n|(Started) [[The Giant Dwarf]]\n|{{SCP|Smithing|60|sort=yes}}\n|None. Boosts '''do not''' work. Must be done through dialogue (chat options 1-3-1) in order to count.\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=3|Hard\n|-\n|Smith an [[adamant spear]] at [[Otto's Grotto]].\n|[[Barbarian Smithing]] (with [[Tai Bwo Wannai Trio]] as a prerequisite)\n|{{SCP|Smithing|75|sort=yes}}\n|[[Yew logs]], [[adamantite bar]], [[hammer]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Smelt an [[adamantite bar]] in [[The Forsaken Tower (location)|The Forsaken Tower]].\n|[[The Forsaken Tower]]\n|{{SCP|Smithing|70|sort=yes}}\n|[[Adamantite ore]], 6 [[coal]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=3|Hard\n|-\n|Smith an [[adamant scimitar]] in the [[Resource Area]].\n|{{NA|None}}\n|{{SCP|Smithing|75|sort=yes}}\n|[[Hammer]], {{Coins|7500}} ({{NoCoins|6000}} if medium tasks are done), 2 [[adamantite bar]]s\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=3|Hard\n|-\n|Make a [[rune crossbow]] yourself from scratch within [[Witchaven]] or [[Yanille]].\n|{{NA|None}}\n|{{SCP|Smithing|91|sort=yes}}{{SCP|Crafting|10}}\n\n{{SCP|Fletching|69}}\n|[[Yew logs]], [[sinew]], 1 [[runite bar]], [[hammer]], [[knife]].\n\n#Smith [[runite limbs]] in [[Yanille]].\n#Fletch a [[yew stock]] in Yanille.\n#Craft a [[crossbow string]] in [[Witchaven]] (using tree roots will not count).\n#Use the yew stock with the runite limbs.\n#Use the crossbow string with the [[Runite crossbow (u)]].\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=4|Elite\n|-\n|Smith a [[rune hasta]] at [[Otto's Grotto]].\n(The [[Barbarian anvil]] in [[Barbarian Village]] also works)\n|[[Barbarian Smithing]]\n|{{SCP|Smithing|90|sort=yes}}\n|[[Runite bar]], [[magic logs]], [[hammer]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=4|Elite\n|-\n|Smith an [[adamant platebody]] down [[Draynor Sewer]].\n|{{NA|None}}\n|{{SCP|Smithing|88|sort=yes}}\n|5 [[Adamantite bar]]s, [[hammer]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=4|Elite\n|-\n|Smith and fletch ten [[Rune dart]]s within [[Varrock]].\n|[[The Tourist Trap]]\n|{{SCP|Smithing|89|sort=yes}}{{SCP|Fletching|81}}\n|1 [[runite bar]], 10 [[Feather]]s, [[hammer]]\n|[[Varrock Diary|Varrock]]\n|data-sort-value=4|Elite\n|-\n|Smith a [[rune scimitar]] from scratch in the [[Resource Area]].\n|{{NA|None}}\n|{{SCP|Smithing|90|sort=yes}}{{SCP|Mining|85}}\n|Any [[pickaxe]], [[hammer]], 16 [[Coal]] and {{Coins|7500}} ({{NoCoins|3750}} if hard tasks are done, {{NoCoins|6000}} with medium).<br/>\n'''Note:''' You must mine a [[Runite Golem|runite golem]] and then smelt '''both''' the [[runite ore]] and coal in the resource area for this to work. The 16 [[coal]] does not need to be mined and can be brought with you prior. Superheat '''does not count here! ''' You may leave the resource area to complete this task. Logging out does '''not''' interfere with the stages.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Strength|pic=Strength icon}} Strength===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Grapple over [[Yanille]]'s south wall.\n|{{NA|None}}\n|{{SCP|Strength|38|sort=yes}}{{SCP|Agility|39}}{{SCP|Ranged|21}}\n|\n[[Mith grapple]] and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=2|Medium\n|-\n|Grapple up and then jump off the north [[Falador]] wall.\n|{{NA|None}}\n|{{SCP|Strength|37|sort=yes}}{{SCP|Agility|11}}{{SCP|Ranged|19}}\n|\n[[Mith grapple]] and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Use the grapple shortcut to get from the [[water obelisk]] to [[Catherby]] shore.\n|{{NA|None}}\n|{{SCP|Strength|22|sort=yes}}{{SCP|Agility|36}}{{SCP|Ranged|39}}\n|\nRope, [[dusty key]] or 70 [[Agility]], [[mith grapple]], and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)<br/>\n'''Recommended:''' [[Anti-dragon shield]] and [[antipoison]].<br/>\n'''Tip:''' Take an [[unpowered orb]] and [[runes]] for [[Charge Water Orb]] for a hard diary task.\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Grapple across the [[River Lum]].\n|{{NA|None}}\n|{{SCP|Strength|19|sort=yes}}{{SCP|Agility|8}}{{SCP|Ranged|37}}\n|\n[[Mith grapple]] and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Enter the [[Wilderness God Wars Dungeon]].\n|{{NA|None}}\n|{{SCP|Strength|60|sort=yes}} '''or''' {{SCP|Agility|60}}\n|{{NA|None}}\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=2|Medium\n|-\n|Enter the [[Warriors' Guild]].\n|{{NA|None}}\n|{{SCP|attack}}{{SCP|strength|130}} combined levels '''or''' 99 in either\n|{{NA|None}}\n|[[Falador Diary|Falador]]\n|data-sort-value=3|Hard\n|-\n|Catch a [[leaping sturgeon]].\n|[[Barbarian Fishing|Barbarian Training - Fishing]]\n|{{SCP|Strength|45|sort=yes}}{{SCP|Fishing|70}}{{SCP|Agility|45}}\n|[[Barbarian rod]], [[fishing bait]] or [[Feather]]s\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Purchase and equip a [[granite body]] from [[Barbarian Assault]].\n|{{NA|None}}\n|{{SCP|Strength|50|sort=yes}}{{SCP|Defence|50}}\n|[[Combat equipment]] to pass through Waves 1-10 and {{Coins|95,000}}\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Use the crossbow shortcut south of the volcano.\n|{{NA|None}}\n|{{SCP|Strength|21|sort=yes}}{{SCP|Agility|53}}{{SCP|Ranged|42}}\n|\n[[Mith grapple]] and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Kill a [[Deathwing]] in the dungeon under the [[Kharazi Jungle]].\n|Partial completion of [[Legends' Quest]]\n(Until you access the cave with the Deathwings)\n|{{SCP|Strength|50|sort=yes}}{{SCP|Woodcutting|15}}{{SCP|Agility|50}}<br/>{{SCP|Thieving|50}}{{SCP|Mining|52}}\n|Any [[woodcutting axe]], [[machete]], any [[pickaxe]], [[lockpick]]\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Kill each of the [[God Wars Dungeon]] generals.\n|[[Troll Stronghold]]\n|{{SCP|Strength|70|sort=yes}}{{SCP|Agility|70}}<br/> {{SCP|Hitpoints|70}}{{SCP|Ranged|70}}\n|Combat equipment to kill the [[God Wars Dungeon]] generals, high {{SCP|Combat}} strongly recommended. '''Boosts cannot be used'''. You must deal the most damage to the boss in order for the kill to count. See each individual pages for recommended gear/setups.\n'''Note:''' Unlike other similar tasks, kills do not reset upon logging out, hopping worlds, or completing other Achievement Diary tasks. They also do not provide a task update message (\"''Achievement Diary Stage Task - Current stage: x''\").\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=4|Elite\n|-\n|Grapple across a pylon on the [[Dorgesh-Kaan Agility Course]].\n|[[Death to the Dorgeshuun]]\n|{{SCP|Agility|70|sort=yes}}{{SCP|Ranged|70}}{{SCP|Strength|70}}\n|\nA [[light source]], [[mith grapple]], and any [[Crossbow (weapon)|crossbow]] that you can wield.<br/>\n(Ironmen need {{SCP|Fletching|59|link=yes}} and {{SCP|Smithing|59|link=yes}} to make the grapple from scratch; or to kill one of the NPCs that drop them.)\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=4|Elite\n|-\n|Loot the [[Barrows chest]] while wearing any complete [[Barrows equipment|Barrows set]].\n|{{NA|None}}\n|\nCombat requirement varies per set, see below:<br/>\n{{SCP|Defence|70}}{{SCP|Attack}}{{SCP|Strength}} for [[Dharok the Wretched's equipment|Dharok's]] or [[Torag the Corrupted's equipment|Torag's]]<br/>\n{{SCP|Defence|70}}{{SCP|Attack}} for [[Guthan the Infested's equipment|Guthan's]] or [[Verac the Defiled's equipment|Verac's]]<br/>\n{{SCP|Defence|70}}{{SCP|Ranged}} for [[Karil the Tainted's equipment|Karil's]]<br/>\n{{SCP|Defence|70}}{{SCP|Attack}}{{SCP|Magic}} for [[Ahrim the Blighted's equipment|Ahrim's]]\n|Any full [[Barrows equipment]] set.\n|[[Morytania Diary|Morytania]]\n|data-sort-value=4|Elite\n|-\n|Catch a [[shark]] in [[Burgh de Rott]] with your bare hands.\n|[[In Aid of the Myreque]], parts of [[Barbarian Training]]\n|{{SCP|Strength|76|sort=yes}}{{SCP|Fishing|96}}\n|{{NA|None}}\n|[[Morytania Diary|Morytania]]\n|data-sort-value=4|Elite\n|-\n|Equip any complete [[Void Knight equipment|void set]].\n|{{NA|None}}\n|\n{{SCP|Combat|40}}<br />\n{{Nowrap|{{SCP|Defence|42}}{{SCP|Attack}}{{SCP|Strength}}{{SCP|Ranged}}{{SCP|Magic}}{{SCP|Hitpoints}}}}<br />\n{{SCP|Prayer|22}}\n|[[Void knight top]]; [[Void knight robe]]; [[Void knight gloves]]; and [[Void mage helm|Void mage]], [[Void ranger helm|ranger]], or [[Void melee helm|melee helm]].\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|-\n|Slay a [[spiritual mage]] inside the [[Wilderness God Wars Dungeon]].\n|{{NA|None}}\n|{{SCP|Strength|60|sort=yes}} '''or''' {{SCP|Agility|60}}<br/>'''and'''<br/>{{SCP|Slayer|83}}\n|[[Weapon]] to kill [[Spiritual mage]], [[God Wars Dungeon#God-related protection|God equipment]] to keep god followers passive.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Thieving|pic=Thieving icon}} Thieving===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Steal a [[cake]] from the [[East Ardougne]] [[Ardougne Baker's Stall.|market stalls]].\n|{{NA|None}}\n|{{SCP|Thieving|5|sort=yes}}\n|{{NA|None}}\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=1|Easy\n|-\n|Open the sarcophagus in the first room of [[Pyramid Plunder]].\n|Started [[Icthlarin's Little Helper]]\n|{{SCP|Thieving|21|sort=yes}}\n|{{NA|None}}\n|[[Desert Diary|Desert]]\n|data-sort-value=1|Easy\n|-\n|Steal from the [[Keldagrim]] crafting or baker's stall.\n|Started [[The Giant Dwarf]]\n|{{SCP|Thieving|5|sort=yes}}\n|{{NA|None}}\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=1|Easy\n|-\n|Steal from a [[Fruit Stall|Hosidius Fruit Stall]].\n|{{NA|None}}\n|{{SCP|Thieving|25|sort=yes}}\n|{{NA|None}}\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=1|Easy\n|-\n|Steal from the [[tea stall]] in [[Varrock]].\n|{{NA|None}}\n|{{SCP|Thieving|5|sort=yes}}\n|{{NA|None}}\n|[[Varrock Diary|Varrock]]\n|data-sort-value=1|Easy\n|-\n|Pickpocket the [[master farmer]] north of [[East Ardougne]].\n|{{NA|None}}\n|{{SCP|Thieving|38|sort=yes}}\n|{{NA|None}}\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=2|Medium\n|-\n|Steal a [[Phoenix feather]] from the [[Desert phoenix]].\n|{{NA|None}}\n|{{SCP|Thieving|25|sort=yes}}\n|None. '''{{Colour|red|Beware:}}''' You may already have a [[Phoenix quill pen]] in your bank from [[The Golem]] quest, which won't allow you to get another feather.\n|[[Desert Diary|Desert]]\n|data-sort-value=2|Medium\n|-\n|Pickpocket a [[Falador]] [[guard]].\n|{{NA|None}}\n|{{SCP|Thieving|40|sort=yes}}\n|{{NA|None}}\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Steal from the [[Rellekka]] [[Fish stall]]s\n|[[The Fremennik Trials]]\n|{{SCP|Thieving|42|sort=yes}}\n|{{NA|None}}\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=2|Medium\n|-\n|Steal from the chest in [[Hemenster]].\n|{{NA|None}}\n|{{SCP|Thieving|47|sort=yes}}\n|[[Lockpick]]. The chest is located in the building between the [[range]] and the [[anvil]].\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=2|Medium\n|-\n|Pickpocket [[Martin the Master Gardener]].\n|{{NA|None}}\n|{{SCP|Thieving|38|sort=yes}}\n|{{NA|None}}\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Attempt to steal from [[King Lathas]]' chest.\n|{{NA|None}}\n|{{SCP|Thieving|72|sort=yes}}\n|[[Lockpick]]\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Knock out and [[pickpocket]] a [[Menaphite Thug]].\n|[[The Feud]]\n|{{SCP|Thieving|65|sort=yes}}\n|Any [[blackjack]]\n|[[Desert Diary|Desert]]\n|data-sort-value=3|Hard\n|-\n|Crack a [[wall safe]] within [[Rogues' Den]].\n|{{NA|None}}\n|{{SCP|Thieving|50|sort=yes}}\n|None, you only need to crack any of the [[Wall safe]]s in the main area (where the bank is); cracking the safes that you can only access by completing the [[mini-game]] will not complete the task.\n|[[Falador Diary|Falador]]\n|data-sort-value=3|Hard\n|-\n|Steal from the [[Keldagrim]] Gem Stall.\n|(Started) [[The Giant Dwarf]]\n|{{SCP|Thieving|75|sort=yes}}\n|{{NA|None}}\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=3|Hard\n|-\n|Kill a [[shadow hound]] in the [[Shadow Dungeon]].\n|Started [[Desert Treasure I]]\n|{{SCP|Thieving|53|sort=yes}}\n|[[Ring of visibility]], [[combat equipment]]\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Kill a [[Deathwing]] in the dungeon under the [[Kharazi Jungle]].\n|Partial completion of [[Legends' Quest]]\n(Until you access the cave with the Deathwings)\n|{{SCP|Thieving|50|sort=yes}}{{SCP|Woodcutting|15}}{{SCP|Strength|50}}\n\n{{SCP|Agility|50}}\n\n{{SCP|Mining|52}}\n|Any [[woodcutting axe]], [[machete]], any [[pickaxe]], [[lockpick]]\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Deliver an [[Port Piscarilius#Stealing artefacts|artefact]] to [[Captain Khaled]].\n|{{NA|None}}\n|{{SCP|Thieving|49|sort=yes}}\n|[[Lockpick]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=3|Hard\n|-\n|Pickpocket a [[gnome]].\n|[[Tree Gnome Village]]\n|{{SCP|Thieving|75|sort=yes}}\n|{{NA|None}}\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Pickpocket a [[hero]].\n|{{NA|None}}\n|{{SCP|Thieving|80|sort=yes}}\n|{{NA|None}}\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=4|Elite\n|-\n|Attempt to picklock the door to the basement of [[Yanille Agility dungeon]]. (Must have hands free, must succeed)\n|{{NA|None}}\n|{{SCP|Thieving|82|sort=yes}}\n|1 [[lockpick]]\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=4|Elite\n|-\n|Steal from the Grand Gold Chest in the final room of [[Pyramid Plunder]].\n|Started [[Icthlarin's Little Helper]]\n|{{SCP|Thieving|91|sort=yes}}\n|[[Thieving]] '''cannot''' be boosted to get into the level 91 room with the gold chest.\n|[[Desert Diary|Desert]]\n|data-sort-value=4|Elite\n|-\n|Steal from a [[Dorgesh-Kaan Rich Chest|Dorgesh-Kaan rich chest]].\n|[[Death to the Dorgeshuun]]\n|{{SCP|Thieving|78|sort=yes}}\n|[[Lockpick]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=4|Elite\n|-\n|Steal from the [[Chest (Rogues' Castle)|Rogues' chest]].\n|{{NA|None}}\n|{{SCP|Thieving|84|sort=yes}}\n|{{NA|None}}\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=4|Elite\n|-\n|Pickpocket an [[elf]].\n|[[Mourning's End Part I]]\n|{{SCP|Thieving|85|sort=yes}}\n|{{NA|None}}\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=4|Elite\n|}\n\n==={{plinkp|Woodcutting|pic=Woodcutting icon}} Woodcutting===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Chop and burn some [[oak logs]] in the [[Fremennik Province]].\n|{{NA|None}}\n|{{SCP|Woodcutting|15|sort=yes}}{{SCP|Firemaking|15}}\n|Any [[axe]], [[tinderbox]]\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=1|Easy\n|-\n|Chop and burn some [[oak logs]] in [[Lumbridge]].\n|{{NA|None}}\n|{{SCP|Woodcutting|15|sort=yes}}{{SCP|Firemaking|15}}\n|Any [[axe]], [[tinderbox]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=1|Easy\n|-\n|Chop some [[teak logs]] near [[Uzer]].\n|{{NA|None}}\n|{{SCP|Woodcutting|35|sort=yes}}\n|Any [[axe]]\n|[[Desert Diary|Desert]]\n|data-sort-value=2|Medium\n|-\n|Chop and burn some [[willow logs]] in [[Taverley]] (can cut and burn willows just south along water).\n|{{NA|None}}\n|{{SCP|Woodcutting|30|sort=yes}}{{SCP|Firemaking|30}}\n|Any [[Woodcutting axe]], [[tinderbox]]\n|[[Falador Diary|Falador]]\n|data-sort-value=2|Medium\n|-\n|Chop the vines to gain deeper access to [[Brimhaven Dungeon]].\n|{{NA|None}}\n|{{SCP|Woodcutting|10|sort=yes}}\n|Any [[woodcutting axe]], {{Coins|875}}\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Climb the stairs within [[Brimhaven Dungeon]].\n|{{NA|None}}\n|{{SCP|Woodcutting|10|sort=yes}}\n|Any [[woodcutting axe]], {{Coins|875}} ('''{{Colour|red|Warning:}}''' Beware of level 92 [[Greater demon]]s at the top of the stairs.)\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Earn 100% favour in the [[Tai Bwo Wannai Cleanup]] minigame.\n|[[Jungle Potion]]\n|{{SCP|Woodcutting|10|sort=yes}}\n|[[Tai Bwo Wannai Cleanup#Necessary items|Tai Bwo Wannai Cleanup items]]\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Cut a log from a [[teak tree]].\n|[[Jungle Potion]]\n|{{SCP|Woodcutting|35|sort=yes}}\n|100 [[trading sticks]] (to enter [[Hardwood Grove]]), any [[Woodcutting axe]] '''OR''' any [[machete]], any woodcutting axe (to enter [[Kharazi Jungle]])\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Cut a log from a [[mahogany tree]].\n|[[Jungle Potion]]\n|{{SCP|Woodcutting|50|sort=yes}}\n|100 [[trading sticks]] (to enter [[Hardwood Grove]]), any [[Woodcutting axe]] '''OR''' any [[machete]], any woodcutting axe (to enter [[Kharazi Jungle]])\n|[[Karamja Diary|Karamja]]\n|data-sort-value=2|Medium\n|-\n|Chop some [[mahogany logs]] north of the [[Farming Guild]].\n|{{NA|None}}\n|{{SCP|Woodcutting|50|sort=yes}}\n|Any [[axe]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=2|Medium\n|-\n|Chop some [[willow logs]] in [[Draynor Village]].\n|{{NA|None}}\n|{{SCP|Woodcutting|30|sort=yes}}\n|Any [[axe]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=2|Medium\n|-\n|Obtain some [[bark]] from a [[hollow tree]].\n|{{NA|None}}\n|{{SCP|Woodcutting|45|sort=yes}}\n|Any [[Woodcutting axe]] ([[fairy ring]] accessible by code: {{Fairycode|alq}})\n|[[Morytania Diary|Morytania]]\n|data-sort-value=2|Medium\n|-\n|Chop and burn some [[teak logs]] on [[Ape Atoll]].\n|[[Monkey Madness I]]\n|{{SCP|Woodcutting|35|sort=yes}}{{SCP|Firemaking|35}}\n|Any [[axe]], [[tinderbox]] ('''Note:''' [[Barbarian firemaking]] will not count toward the task! Do not use a [[bow]] to burn the logs!)(If you have {{SCP|Woodcutting|50}}{{SCP|Firemaking|50}} chop and burn some [[mahogany logs]] nearby while you're here to complete a hard diary task.)\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=2|Medium\n|-\n|Chop some [[yew logs]] from an [[ent]].\n|{{NA|None}}\n|{{SCP|Woodcutting|61|sort=yes}}\n|Rune [[axe]] or better—stat boosts do not work. An [[ent]] can be killed just east from the [[Corporeal Beast]]'s lair, so it is recommended to bring a charged [[games necklace]]. Killing an ent in the [[Woodcutting Guild]] dungeon will not count towards this task.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=2|Medium\n|-\n|Craft a [[Neitiznot shield]] on [[Neitiznot]].\n|[[The Fremennik Isles]]\n|{{SCP|Woodcutting|56|sort=yes}}\n|Any [[axe]], 2 [[arctic pine logs]], [[hammer]], 1 [[rope]], 1 [[bronze nail]].\n(An axe and hammer can be bought from [[Neitiznot]].)\n|[[Fremennik Diary|Fremennik]]\n|data-sort-value=3|Hard\n|-\n|Create a [[yew longbow]] from scratch around [[Seers' Village]].\n|{{NA|None}}\n|{{SCP|Woodcutting|60|sort=yes}}{{SCP|Fletching|70}}\n\n{{SCP|Crafting|10}} (if crafting your own bowstring)\n|Any [[woodcutting axe]], [[bow string]], [[knife]] (one spawns south of [[Seers' Village]] bank)\n|[[Kandarin Diary|Kandarin]]\n|data-sort-value=3|Hard\n|-\n|Collect 5 [[Palm leaf|palm leaves]].\n|Partial completion of [[Legends' Quest]] (Until you access [[Kharazi Jungle]])\n|{{SCP|Woodcutting|15|sort=yes}}\n|Any [[woodcutting axe]], [[machete]] ('''Tip:''' You can pick up and drop the same [[palm leaf]] five times.)\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Kill a [[metal dragon]] in [[Brimhaven Dungeon]].\n|{{NA|None}}\n|{{SCP|Woodcutting|34|sort=yes}}{{SCP|Agility|12}}\n|Good combat stats and equipment, {{Coins|875}} or 5000 [[trading sticks]], any [[woodcutting axe]]. Recommended: [[anti-dragon shield]] and [[antifire potion]] to negate all [[dragonfire]] damage.\n|[[Karamja Diary|Karamja]]\n|data-sort-value=3|Hard\n|-\n|Enter the [[Woodcutting Guild]].\n|{{NA|None}}\n|{{SCP|Woodcutting|60|sort=yes}}\n|{{NA|None}}\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=3|Hard\n|-\n|Travel from [[Lumbridge]] to [[Edgeville]] on a [[waka canoe]].\n|{{NA|None}}\n|{{SCP|Woodcutting|57|sort=yes}}\n|Any [[axe]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=3|Hard\n|-\n|Chop and burn some [[mahogany logs]] on [[Mos Le'Harmless]].\n|[[Cabin Fever]]\n|{{SCP|Woodcutting|50|sort=yes}}{{SCP|Firemaking|50}}\n|Any [[Woodcutting axe]] and [[tinderbox]] (a bow '''cannot''' be used). [[Witchwood icon]] recommended while traversing the [[Mos Le'Harmless Cave]] along with any [[light source]].\n|[[Morytania Diary|Morytania]]\n|data-sort-value=3|Hard\n|-\n|Make a [[Waka canoe]] near [[Edgeville]].\n|{{NA|None}}\n|{{SCP|Woodcutting|57|sort=yes}}\n|Any woodcutting [[axe]]\n|[[Varrock Diary|Varrock]]\n|data-sort-value=3|Hard\n|-\n|Chop some [[yew logs]] in [[Varrock]] and burn them at the top of the Varrock church.\n|{{NA|None}}\n|{{SCP|Woodcutting|60|sort=yes}}{{SCP|Firemaking|60}}\n|Any woodcutting [[axe]], [[tinderbox]]. [[Barbarian firemaking]] does not work. Players must chop the [[yew tree]] next to the [[Varrock]] church, behind [[Varrock Palace]]. Use the church in the north-east corner of the city.\n|[[Varrock Diary|Varrock]]\n|data-sort-value=3|Hard\n|-\n|Collect at least 2 [[yew roots]] from the [[tree patch]] in [[Varrock Palace]]. (If boosting, make sure to boost when digging up the [[yew tree (Farming)|yew tree]], not when planting!)\n|{{NA|None}}\n|{{SCP|Woodcutting|60|sort=yes}}{{SCP|Farming|68}}{{efn|Required to get two roots from one tree.|name=\"roots2\"}}\n|[[Yew sapling]], (10 [[Cactus spine]]s recommended), any woodcutting [[axe]], [[spade]]\n|[[Varrock Diary|Varrock]]\n|data-sort-value=3|Hard\n|-\n|Chop and burn some [[mahogany logs]] on [[Ape Atoll]].\n|[[Monkey Madness I]]\n|{{SCP|Woodcutting|50|sort=yes}}{{SCP|Firemaking|50}}\n|Any [[Woodcutting axe]], [[tinderbox]]\n|[[Western Provinces Diary|Western Provinces]]\n|data-sort-value=3|Hard\n|-\n|Find at least 3 [[magic roots]] at once when digging up your [[Magic tree (Farming)|magic tree]] in [[Falador]].\n|{{NA|None}}\n|{{SCP|Woodcutting|75|sort=yes}}{{SCP|Farming|91}}\n|[[Magic seed]], [[Spade]], any [[Woodcutting axe]]\n|[[Falador Diary|Falador]]\n|data-sort-value=4|Elite\n|-\n|Chop some [[redwood logs]].\n|{{NA|None}}\n|{{SCP|Woodcutting|90|sort=yes}}\n|Any [[axe]]\n|[[Kourend & Kebos Diary|Kourend & Kebos]]\n|data-sort-value=4|Elite\n|-\n|Chop some [[magic logs]] at the [[Mage Training Arena]].\n|{{NA|None}}\n|{{SCP|Woodcutting|75|sort=yes}}\n|Any [[axe]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=4|Elite\n|-\n|Cut and burn some [[magic logs]] in the [[Resource Area]].\n|{{NA|None}}\n|{{SCP|Woodcutting|75|sort=yes}}{{SCP|Firemaking|75}}\n|Any [[Woodcutting axe]], [[tinderbox]], 6000 coins if the Wilderness medium diary is done, 3750 coins if the Wilderness hard diary is done (or else bring 7500). You cannot use a shortbow to light the magic log (Barbarian firemaking) to finish this task.\n|[[Wilderness Diary|Wilderness]]\n|data-sort-value=4|Elite\n|}\n{{Notelist}}\n\n==={{plinkp|Quest points|pic=Quest point icon}} Quest points===\n{| class=\"wikitable sortable lighttable qc-active\"\n!class=\"unsortable\"|Task\n!Quest(s)&nbsp;required\n!data-sort-type=\"number\"|Skill(s)&nbsp;required\n!class=\"unsortable\"|Items required\n!Diary\n!Difficulty\n|-\n|Recharge some [[jewellery]] at the Totem pole in the [[Legends' Guild]].\n|[[Legends' Quest]]\n|{{SCP|Quest|107|sort=yes}} [[Quest points]] (for [[Legends' Quest]])\n|Either an uncharged [[skills necklace]] or [[combat bracelet]]\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Smith a [[Dragon sq shield]] in [[West Ardougne]].\n|[[Legends' Quest]]\n|{{SCP|Quest|107|sort=yes}} [[Quest points]] (for [[Legends' Quest]])<br />{{SCP|Smithing|60}}\n|[[Shield left half]], [[shield right half]], [[hammer]]\n|[[Ardougne Diary|Ardougne]]\n|data-sort-value=3|Hard\n|-\n|Purchase some [[Barrows gloves]] from the [[Culinaromancer's Chest]].\n|[[Recipe for Disaster]] (full completion)\n| data-sort-value='175' |\n{{SCP|Quest|175}} [[Quest points]] (8 of which you will receive by releasing all the trapped characters and 79 of which you will have simply by having completed all required quests.)<br/>\nSee [[Recipe for Disaster|quest]] requirements.\n|{{Coins|130,000}}\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=3|Hard\n|-\n|Perform a [[Skillcape|Cape of Accomplishment]] or [[Quest cape|Quest point cape]] emote at the top of [[Falador Castle|White Knights' Castle]].\n|None, or completed all [[quests]]\n|{{SCP|Quest|{{Globals|quest points}}|sort=yes}} [[Quest points]] or any {{SCP|overall|99}} [[Skill]]\n|[[Cape of Accomplishment]] or [[Quest point cape]] ([[Cabbage cape]] or [[max cape]] will not work)\n|[[Falador Diary|Falador]]\n|data-sort-value=4|Elite\n|-\n|Perform the [[Quest point cape]] emote in the [[Wise Old Man]]'s house.\n|All [[Quest]]s\n| data-sort-value='{{Globals|quest points}}' |\n{{SCP|Quest|{{Globals|quest points}}}} [[Quest points]]<br/>\n[[Quest point cape#Minimum skill levels|See here]].\n|[[Quest point cape]]\n|[[Lumbridge & Draynor Diary|Lumbridge & Draynor]]\n|data-sort-value=4|Elite\n|}\n\n==Gallery==\n<gallery mode=\"packed\" heights=\"250\" style=\"text-align:left\">\nFile:Easy diary set equipped male.png|alt=Easy diary set|The Easy diary set on body type A.\nFile:Easy diary set equipped female.png|alt=Easy diary set|The Easy diary set on body type B.\nFile:Medium diary set equipped male.png|alt=Medium diary set|The Medium diary set on body type A.\nFile:Medium diary set equipped female.png|alt=Medium diary set|The Medium diary set on body type B.\nFile:Hard diary set equipped male.png|alt=Hard diary set|The Hard diary set on body type A\nFile:Hard diary set equipped female.png|alt=Hard diary set|The Hard diary set on body type B\nFile:Elite diary set equipped male.png|alt=Elite diary set|The Elite diary set on body type A\nFile:Elite diary set equipped female.png|alt=Elite diary set|The Elite diary set on body type B\nFile:Achievement Diary easy outfit concept.png|alt=Diary set concept|An extremely early concept of the easy set shown at Runefest 2014.\n</gallery>\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 28 July 2016\n|update = Dragon Spear Changes & Beta 3\n|change =\n* You can now complete tasks in achievement diaries without having first started the diary.\n* The Achievement Diaries list is now ordered alphabetically.\n}}\n{{Subject changes\n|date = 22 October 2015\n|update = Abyssal Bludgeon Buff\n|poll = \n|change = \n* Some multi-stage achievement diary tasks have been restructured to resolve issues where their progress got confused.\n* Corrected punctuation and grammar in some achievement diary tasks.\n}}\n{{Subject changes\n|date = 16 July 2015\n|update = Duel Arena Rework\n|change = Fixed misspelling of \"receive\" in Ardougne, Karamja, Morytania and Wilderness Diaries.\n}}\n{{Subject changes\n|date = 18 June 2015\n|update = Multiple Clues & Midsummer\n|change = Degraded barrows equipment, mage arena capes and achievement diary reward items are now also protected on death, outside of PvP, in line with other untradeable items.\n}}\n{{Subject changes\n|date = 14 May 2015\n|update = System Update & Poll 33\n|change = Prevented some diary task messages from appearing if you haven't started the related diary.\n}}\n{{Subject changes\n|date = 7 May 2015\n|update = Slayer Partners & Teleports\n|change = The achievement diary teleports now have some fancy new [[graphical updates]]. The teleports to the Ardougne monastery, Ardougne farming patch, Falador cabbage patch, slime pit and Nardah all received a makeover.\n}}\n{{Subject changes\n|date = 30 April 2015\n|update = Mutagens, Mo & Much More\n|change = Added some area checks for a couple of diary tasks.\n}}\n{{Subject changes\n|date = 19 March 2015\n|update = Boss Pets, Sets, Chat & More\n|change = \n*Fixed a number of zone checks and typos relating to achievement diaries.\n*Task increment messages should no longer appear on F2P Worlds.\n}}\n{{Subject changes footer}}\n\n==See also==\n* [[Achievement Diary/All achievements]], a list of every task sorted by certain requirements\n* [[Achievement Diary/Rewards]], a list of all rewards for comparison\n\n{{Achievement diary}}\n{{Interface}}", "revisionId": 14980177, "timestamp": "2025-09-04T23:04:02Z", "downloadedAt": "2025-09-05T20:38:02.305Z", "source": "osrs_wiki_watcher", "rawWikitext": true}