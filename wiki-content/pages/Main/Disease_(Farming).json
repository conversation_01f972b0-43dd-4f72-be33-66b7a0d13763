{"title": "Disease (Farming)", "namespace": "Main", "content": "{{otheruses|the condition that affects planted crops|the condition that affects players|Disease}}\n[[File:Diseased limpwurt plant (stage 4).png|thumb|200px|A diseased limpwurt plant.]]\n'''Disease''' is a condition that can afflict most crops grown through the [[Farming]] [[skill]].\nWhen a healthy crop ends a growth cycle, there is a chance it may become diseased. While diseased, the crop stops growing and does not advance to the next stage. Crops do not recover from disease on their own, and may die at the end of the diseased cycle if left uncured. The cycle length of a diseased crop will tend to become one tier slower than if it were healthy, using the next slowest [[Farming#Growth timing|growth tick]] than typical for the crop.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1621627215187255299|date=3 February 2023|archiveurl=https://archive.ph/HETtz|archivedate=6 February 2023|quote=Yes, the original dev set the diseased versions to use the next slowest tick rate. I can't check them all very readily to see if they're still consistently like that, but that was the trend anyway.}} Dead crops yield no experience and require use of a [[spade]] to dig up their remains before planting another crop in the patch.\n\nPlayers may treat plants in diseased herb, flower, allotment, hops, fruit tree, hardwood tree, spirit, celastrus, belladonna, cactus, and mushroom patches with [[plant cure]] to restore the crop to health. Doing so may let the crop advance to the next growth stage normally upon the end of the cycle, although it is possible that it may become diseased again. For diseased trees and bushes, the player may use [[secateurs]] on the plant to trim the diseased branch and restore the crop to health. Alternatively, if a player has access to the [[Lunar Magic]] spellbook they may use the [[Cure Plant]] spell instead, which saves the player valuable inventory space during [[Farming runs]].\n\nPlayers may purchase plant cure from a [[Farming shop]], but it is usually both more convenient and less expensive to purchase it from the nearest [[Group of Advanced Gardeners|farmer]].\n\nWhen using secateurs, there is a 75% chance to successfully prune the tree or bush, resulting in the message {{Mes|You have successfully removed all the diseased leaves.}} The other 25% of the time players will receive the message {{Mes|There are still some diseased leaves left.}} and simply need to try again until they succeed. The amount of time it takes to ''attempt'' to prune the plant is randomized, and on average decreases with Farming level.{{CiteDiscord|author=Mod Lenny|url=https://discord.com/channels/177206626514632704/269673599554551808/850452648888893460|date=4 June 2021|quote=So it's got a normal skill roll to just check whether or not it should do anything, and if that goes through, I wanna say that it's just a straight 1/4 chance of getting \"There are still some diseased leaves left.\" but it's written in a way that's making me question myself.... instead of <code>random(4) > 0</code> it checks if <code>modulo(random(100),4) > 0)</code> .... pass the check = succeed/fail, fail the check = just prune again|ctxquote=it's possible to fail pruning a diseased tree (something like \"there are still more diseased leaves to trim\"). anyone want to ask Ash if the chance of this is based on farming level/crop/magic secateurs vs. regular?}}\n\nCrops grow in real-time, so they may become diseased when a player is offline. In addition, crops that have been cured of disease may become afflicted once again. Crops cannot become diseased in the first growth stage (i.e. immediately after the seed has been planted), and fully grown crops will not become diseased.\n\nAn [[amulet of nature]] alerts a player to disease if worn, and may also be rubbed to check whether or not a plant is still healthy.\n\n==Reducing disease risk==\nEach crop type has a predetermined probability of becoming diseased each crop window. Treating a farming patch with [[compost]], [[supercompost]] or [[ultracompost]] before (or immediately after) planting a crop reduces the chance of disease during all growth cycles by 50%, 80% or 90%, respectively, rounded down to the nearest 1/128th. If an [[Iasor seed|Iasor]] is planted and alive, disease risk of all crops is reduced by a further 80%,{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1116719428006236162|date=12 April 2019|archiveurl=https://archive.is/S8rwJ|archivedate=28 May 2020|quote=Attas affects everything except itself and compost bins. Iasor reduces the chance to 20% of what it would otherwise have been, from what I can see in the code. For Kronos info, I'd suggest trying Kieren.}} to a minimum chance of 1/128 per growth cycle. The risk of a plant becoming diseased without any treatment varies depending on the crop (and potentially the specific patch, or even the individual growth stage).{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1619361305860898817|date=28 January 2023|archiveurl=https://archive.ph/7ss9E|archivedate=6 February 2023|quote=No, it can be different for different crops in each patch too, or even different for each growth state of each crop.}} These rates are unknown, except for the following crops:{{CiteTwitter|author=Mod Kieren|url=https://twitter.com/JagexKieren/status/90585556**********|date=7 September 2017|archiveurl=https://archive.is/HmrkK|archivedate=28 May 2020|quote=So with the release of Ultracompost I figured I'd explain how compost affects crops dying. Sorry it is very word heavy! Fire any questions:).|notes=''Note:'' The image in the tweet says the ultracompost multiplier is 1/7, but later in the thread it is corrected to 1/10. Additionally, collected data shows that it is overwhelmingly likely that when creating the graphic, Kieren adjusted the denominator by one to account for a random roll of 0-127, but did not also adjust the numerator, hence the difference in table by +1.}}\n\n{|class=\"wikitable\" style=\"text-align:center;\"\n!Plant\n!Cycles with Disease Chance\n! title=\"Evidence suggests that Mod Kieren's rates may be referring to a maximum disease roll and may be off by 1/128. The numbers above are based on the assumption that a valid disease roll ranges from 0 up to and including this maximum disease roll. The numerators in the table are incremented by 1 to account for a roll of 0 being included. Check the relevant discussion on the talk page for more information.\" | Disease Chance per Cycle<sup class=\"noprint small\">&#91;?&#93;</sup> \n!with Compost\n!with Supercompost\n!with Ultracompost\n!with Ultracompost and Iasor\n|-\n|All [[Fruit tree]]s\n|4||18/128 (~{{#expr: (18/128) * 100 round 1}}%)||9/128 (~{{#expr: (9/128) * 100 round 1}}%)||4/128 (~{{#expr: (4/128) * 100 round 1}}%)||2/128 (~{{#expr: (2/128) * 100 round 1}}%)||1/128 (~{{#expr: (1/128) * 100 round 1}}%)\n|-\n|[[Maple tree (Farming)|Maple Trees]]\n|7||13/128 (~{{#expr: (13/128) * 100 round 1}}%)||7/128 (~{{#expr: (7/128) * 100 round 1}}%)||3/128 (~{{#expr: (3/128) * 100 round 1}}%)||2/128 (~{{#expr: (2/128) * 100 round 1}}%)||1/128 (~{{#expr: (1/128) * 100 round 1}}%)\n|-\n|[[Magic tree (Farming)|Magic Tree]]\n|11||9/128 (~{{#expr: (9/128) * 100 round 1}}%)||5/128 (~{{#expr: (5/128) * 100 round 1}}%)||2/128 (~{{#expr: (2/128) * 100 round 1}}%)||1/128 ({{#expr: (1/128) * 100 round 1}}%)||1/128 (~{{#expr: (1/128) * 100 round 1}}%)\n|-\n|All [[Herb patch|Herbs]]\n|3||27/128 (~{{#expr: (27/128) * 100 round 1}}%)||14/128 (~{{#expr: (14/128) * 100 round 1}}%)||6/128 (~{{#expr: (6/128) * 100 round 1}}%)||3/128 (~{{#expr: (3/128) * 100 round 1}}%)||1/128 (~{{#expr: (1/128) * 100 round 1}}%)\n|}\n\nThe probability a crop will fully grow without getting diseased and dying is <math>(1-P)^{n}</math>, where <math>P</math> is the probability found in the above table and <math>n</math> is the number of growth cycles where the crop may become diseased. For example, fruit trees can become diseased in 4 of their 6 growth cycles, so if ultracompost is used the tree has a <math>\\left(1-\\frac{2}{128}\\right)^{4} = 93.89\\%</math> chance of fully growing without becoming diseased and dying.\n\nIf a player forgets to treat a farming patch, the expensive but useful [[Fertile Soil]] spell may be used to treat the patch with supercompost (or ultracompost if you have previously read the [[Ash covered tome]]) both before and after planting a [[seed]] or [[sapling]]. Supercompost and ultracompost can also be used before or after.\n\nWatering allotment, flower, and hops patches with a [[watering can]] reduces the risk of disease occurring at the end of that growth cycle.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1206382608466268160|date=16 December 2019|archiveurl=https://archive.is/OzPlm|archivedate=16 December 2019|quote=[Does watering crops eliminate chance of disease for one grow cycle stage or just reduce it?] It just reduces it. - [Got it. In that case, how much does watering reduce chance of disease?] There isn't a formula as far as I know - the dev who made the skill just set different numbers.}} During the next growth cycle, the plant changes from being watered back to normal and has a higher risk for disease once more, unless it is watered again. Only allotment, flower, and hops patches may be watered.\n\n==Eliminating disease risk==\nPlanting different [[Flower seed|flowers]] or a [[scarecrow]] provides protection from disease to a variety of crops in allotment patches once the flower is fully grown.\n\nThe special herb patches unlocked by completing [[My Arm's Big Adventure]] and [[Making Friends with My Arm]] are immune to disease, although herb yields may still be increased by treating the patches with super/ultracompost.\n\nWith the easy tier of the [[Kourend & Kebos Diary]] complete, the allotment, herb, and flower patches in [[Hosidius]] will no longer become diseased.\n\nWith Champion status in the [[Fortis Colosseum]], requiring 16,000 [[Glory]], the herb patch west of [[Civitas illa Fortis]] will no longer become diseased.\n\nWith the elite tier of the [[Falador Diary]] complete, the tree patch in [[Falador Park]] will no longer become diseased.\n\nOtherwise, the only way to eliminate disease risk entirely from all other patches is to pay one of the [[Farmer (NPC)|farmers]] near the [[Farming/Patch locations|farming patch]] to watch over it and ensure that it grows to completion. Payment must be in the exact form requested. For instance, a farmer does not accept 5 [[apples]] in lieu of a [[basket of apples]]. However, farmers do accept [[note]]d payment, therefore a noted basket of apples would be accepted. Note that farmers require separate payment for each allotment patch.\n\nThe [[Belladonna patch|belladonna]] and [[Mushroom patch|mushroom]] patches do not have farmers available to oversee them, however. While farmers can oversee allotment patches, they cannot prevent the nearby herb or flower patches from becoming diseased.\n\n[[Poison ivy bush]]es are immune to disease. Farmers will not accept payment to watch over a poison ivy bush.\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 23 October 2024\n|update = Halloween Fixes & Miscellaneous Changes\n|change = A bug has been fixed in which curing diseased herbs may cause a different herb to resume growing.\n}}\n{{Subject changes footer}}\n\n==References==\n{{Reflist}}\n\n[[Category:Farming]]", "revisionId": 14980570, "timestamp": "2025-09-05T17:22:14Z", "downloadedAt": "2025-09-05T20:37:24.808Z", "source": "osrs_wiki_watcher", "rawWikitext": true}