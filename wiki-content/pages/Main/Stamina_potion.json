{"title": "Stamina potion", "namespace": "Main", "content": "{{External|rs}}\n{{Redirect|Stamina|the run energy system|Energy}}\n{{Infobox Item\n|defver = 4\n|version1 = 1 dose\n|version2 = 2 dose\n|version3 = 3 dose\n|version4 = 4 dose\n|smwname1 = (1)\n|smwname2 = (2)\n|smwname3 = (3)\n|smwname4 = (4)\n|name1 = Stamina potion(1)\n|name2 = Stamina potion(2)\n|name3 = Stamina potion(3)\n|name4 = Stamina potion(4)\n|image1 = [[File:Stamina potion(1).png]]\n|image2 = [[File:Stamina potion(2).png]]\n|image3 = [[File:Stamina potion(3).png]]\n|image4 = [[File:Stamina potion(4).png]]\n|release = [[3 July]] [[2014]]\n|update = Halos, God Books & Stamina\n|members = Yes\n|quest = No\n|tradeable = Yes\n|placeholder = Yes\n|equipable = No\n|stackable = No\n|noteable = Yes\n|options = Drink, Empty, Drop\n|examine1 = 1 dose of stamina potion.\n|examine2 = 2 doses of stamina potion.\n|examine3 = 3 doses of stamina potion.\n|examine4 = 4 doses of stamina potion.\n|value1 = 100\n|value2 = 200\n|value3 = 300\n|value4 = 400\n|weight1 = 0.020\n|weight2 = 0.025\n|weight3 = 0.030\n|weight4 = 0.035\n|exchange = Yes\n|id1 = 12631\n|id2 = 12629\n|id3 = 12627\n|id4 = 12625\n}}\n{{Synced switch\n|version1 = [[File:Stamina potion(1) detail.png|left|130px]]\n|version2 = [[File:Stamina potion(2) detail.png|left|130px]]\n|version3 = [[File:Stamina potion(3) detail.png|left|130px]]\n|version4 = [[File:Stamina potion(4) detail.png|left|130px|class=pageimage]]\n}}\nA '''stamina potion''' restores 20% of the player's [[Energy|run energy]] per dose, and offers a 70% reduction in run energy depletion for 2 minutes. This effect does not stack, and drinking additional doses before the timer has run out will simply reset it back to 2 minutes. When the stamina effect is active the boot in the run energy orb changes from its usual yellow to orange.\n\nA stamina potion can be made at level 77 [[Herblore]] by mixing a [[super energy]] potion with [[amylase crystal]]s. Each dose grants 25.5 Herblore [[experience]], and requires one amylase crystal. \n\nAfter [[Barbarian Training#Barbarian Herblore|Barbarian Herblore Training]] is completed, [[caviar]] can be added to a stamina potion(2) to produce a [[stamina mix]]. This requires 86 Herblore and provides 60 Herblore experience. A stamina mix has 2 doses, each of which heals 6 [[Hitpoints]] in addition to the usual stamina potion behaviour.\n\n== Utility ==\n[[File:Stamina Run Icon.png|left|thumb|width=100x100|The run energy icon during the stamina effect.]]\nStamina potions are extremely useful in activities that require a lot of running. With a low enough [[weight]], the reduced energy depletion will result in minimal loss of energy even with non-stop running, especially combined with the 20% restoration when taking another dose. \n\nThe relevance of stamina potions diminished with the 2025 [[Update:Run Energy Changes|Run Energy Changes]] update, as run energy drain was decreased across the board. However, the impact of the stamina potion effect on energy drain is still significant, and stamina potions remain relevant at some activities.\n\nCommon uses for stamina potions include: [[questing]], [[player killing]], [[Blast Furnace]], [[Herbiboar|hunting herbiboar]], [[Runecraft|runecrafting]], some [[Agility course|agility courses]] (e.g., [[Ape Atoll Agility Course|ape atoll]]), and [[Raids|raiding]] at [[Theatre of Blood|ToB]] or [[Chambers of Xeric|CoX]] (especially solo raids). They are also used to build a [[Revitalisation pool|revitalization pool]] as well as charge the [[ring of endurance]], with each dose granting one charge for the ring.\n\n== Ironmen access ==\nDue to their high Herblore requirement, ironmen cannot make them until much later in their account progression. For this reason, there are several methods of obtaining stamina potions that are used.\n\n==== Dr Jekyll ====\nStamina potions can be obtained by giving [[torstol]] to [[Jekyll and Hyde|Dr Jekyll]] when he appears. Due to the rarity of the event, players often save up ten staminas to build the [[revitalisation pool]] in their [[player-owned house]], which requires 70 [[Construction]]. Outside of obtaining it through monster drops, players without the [[Farming]] level to grow torstol can obtain it from the [[Reward Cart|Wintertodt Reward Cart]], the [[Dark Chest|dark chest]] on the [[Isle of Souls]], or the [[Sinister chest]] in the [[Yanille Agility Dungeon]]. Players lacking the required level to clean Torstol can have [[Zahur]] clean their grimy herbs, alternatively clean Torstol can also be obtained from [[Zulrah]] or [[Nature impling]]s.\n\nWhile Dr Jekyll normally despawns if a player runs into a bank, several locations (typically those without a booth) are exempt from this. Examples include the banks on [[Lunar Isle]], in the [[Myths' Guild]], and at the [[Theatre of Blood]]. Players lucky enough to be near those locations when he spawns can use this to grab a torstol from the bank, if they have one. An unprepared player may minigame teleport to the [[Burthorpe Games Room]] and run to [[Emerald Benedict]] in the [[Rogues' Den|Rogue's Den]] to withdraw a banked herb with a few seconds to spare.\n\nDr Jekyll has a higher chance to spawn when [[Underwater]] because the pool of random events is smaller. Players can deliberately farm stamina potions by spending time underwater when a random event would typically occur.\n\n==== Theatre of Blood ====\nPlayers with friends capable of carrying them through the [[Pestilent Bloat]] and [[The Maiden of Sugadinti|Maiden]] fights of the [[Theatre of Blood]] will gain access to a chest which contains one 4-dose stamina potion. This requires completion of [[Priest in Peril]], meaning that [[Skill pure|skillers]] cannot use this method.\n\n==Creation==\n{{Switch infobox\n|text1 = 1 dose\n|item1 = {{Recipe\n|skill1 = Herblore\n|skill1lvl = 77\n|skill1boostable = Yes\n|skill1exp = 25.5\n|members = Yes\n|ticks = 2\n|mat1 = Super energy(1)\n|mat2 = Amylase crystal\n|mat2quantity = 1\n|output1 = Stamina potion(1)\n}}\n|text2 = 2 dose\n|item2 = {{Recipe\n|skill1 = Herblore\n|skill1lvl = 77\n|skill1boostable = Yes\n|skill1exp = 51\n|members = Yes\n|ticks = 2\n|mat1 = Super energy(2)\n|mat2 = Amylase crystal\n|mat2quantity = 2\n|output1 = Stamina potion(2)\n}}\n|text3 = 3 dose\n|item3 = {{Recipe\n|skill1 = Herblore\n|skill1lvl = 77\n|skill1boostable = Yes\n|skill1exp = 76.5\n|members = Yes\n|ticks = 2\n|mat1 = Super energy(3)\n|mat2 = Amylase crystal\n|mat2quantity = 3\n|output1 = Stamina potion(3)\n}}\n|text4 = 4 dose\n|item4 = {{Recipe\n|skill1 = Herblore\n|skill1lvl = 77\n|skill1boostable = Yes\n|skill1exp = 102\n|members = Yes\n|ticks = 2\n|mat1 = Super energy(4)\n|mat2 = Amylase crystal\n|mat2quantity = 4\n|output1 = Stamina potion(4)\n}}\n}}\n\n==Products==\n{{Uses material list|Stamina potion(1)|Stamina potion(2)|Stamina potion(3)|Stamina potion(4)}}\n\n==Price per dose==\n{{Price per dose|Stamina potion}}\n\n==Item sources==\n{{Drop sources|Stamina potion(4)}}\n\n===Shop locations===\n{{Store locations list|Stamina potion(4)}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 8 January 2025\n|update = Run Energy Changes\n|change = You can now start the Make-X processes for creating stamina potions without cancelling your character's existing movement. \n}}\n{{Subject changes\n|date = 13 October 2016\n|update = The Ourania Altar\n|poll = \n|change = The Jekyll random event can now ask for a clean torstol if you have one.\n}}\n{{Subject changes\n|date = 15 April 2016\n|update = Bank Placeholder Feedback Changes\n|change = Half full potions could now be turned into bank placeholders.\n}}\n{{Subject changes\n|date = 26 November 2015\n|update = Zulrah Scale Fishing\n|change = The game message received when drinking Stamina potions has been added to the filter.\n}}\n{{Subject changes\n|date = 10 July 2014\n|update = The Game Filter is Here!\n|change = The colour of the Stamina potions has been [[Graphical updates|recoloured]] to be more distinct from the [[Zamorak brew]].\n}}\n{{Subject changes footer}}\n\n===Gallery (historical)===\n{{Switch infobox\n|text1 = 1 dose\n|item1 =\n|text2 = 2 dose\n|item2 =\n|text3 = 3 dose\n|item3 =\n|text4 = 4 dose\n|item4 =\n}}\n{|style=\"text-align: center\" cellpadding=\"15\"\n|\n{{Synced switch\n|version1 = [[File:Stamina potion(1) (v1) detail.png|center|300x150px]]\n|version2 = [[File:Stamina potion(2) (v1) detail.png|center|300x150px]]\n|version3 = [[File:Stamina potion(3) (v1) detail.png|center|300x150px]]\n|version4 = [[File:Stamina potion(4) (v1) detail.png|center|300x150px]]\n}}\n|\n{{Synced switch\n|version1 = [[File:Stamina potion(1) detail.png|center|300x150px]]\n|version2 = [[File:Stamina potion(2) detail.png|center|300x150px]]\n|version3 = [[File:Stamina potion(3) detail.png|center|300x150px]]\n|version4 = [[File:Stamina potion(4) detail.png|center|300x150px]]\n}}\n|-\n|\n{{Synced switch\n|version1 = [[File:Stamina potion(1) v1.png|center]]\n|version2 = [[File:Stamina potion(2) v1.png|center]]\n|version3 = [[File:Stamina potion(3) v1.png|center]]\n|version4 = [[File:Stamina potion(4) v1.png|center]]\n}}\n|\n{{Synced switch\n|version1 = [[File:Stamina potion(1).png|center]]\n|version2 = [[File:Stamina potion(2).png|center]]\n|version3 = [[File:Stamina potion(3).png|center]]\n|version4 = [[File:Stamina potion(4).png|center]]\n}}\n|-\n|3 July 2014 –<br/>10 July 2014\n|10 July 2014 –<br/>present\n|}\n\n==Trivia==\n* The sound that plays when a stamina potion wears off is that of the [[Homunculus]].\n* A predecessor of the stamina potion, energy renewal potions, were originally offered as a reward from the [[Nightmare Zone]] [[minigame]]. This potion would have fully restored run energy, and kept it at 100% for 30 seconds; a tradeable ingredient from the reward shop would be used to create them, requiring level 92 Herblore to make (and level 96 to create the barbarian mix version).\n** When it was polled in [[Poll:Old School feature poll 10: New Rewards|Old School feature poll 10: New Rewards]], it failed the poll with only 71.5% support.\n\n{{potions}}\n[[Category:Items storable in the potion storage]]\n[[Category:Temporary skill boost]]", "revisionId": 14980451, "timestamp": "2025-09-05T12:00:50Z", "downloadedAt": "2025-09-05T20:37:35.126Z", "source": "osrs_wiki_watcher", "rawWikitext": true}