{"title": "June 2025 Sailing Beta", "namespace": "Main", "content": "<!-- {{Sailing Beta Content|This}} -->\n[[File:Sailing logo.png|left|200px]]\nThe '''June 2025 Sailing Beta''' was an exclusive preview of the [[Sailing]] skill, available to all [[members]] on special worlds separate from the live game. Running from '''June 26th to July 3rd, 2025''', this playtest offered a glimpse into the early stages of the skill, focusing on initial levels and progression. Sailing levels were capped at 30 during this testing phase, as the skill was not fully implemented.\n\nSome new additions since the [[March 2025 Sailing Alpha]] included [[ship combat]], [[bounty tasks]] &ndash; assignments to kill monsters at sea, [[crew management]] &ndash; recruiting NPC sailors to work on your ship, and random events at sea.\n\nUpon logging into a Sailing beta world, players were required to complete an in-game code to verify they are using supported clients. For more information, [[June 2025 Sailing Beta#Access|click here]].\n\nWhen entering the beta worlds, players started in [[Port Sarim]] with level 30 in [[Construction]], [[Smithing]], and all combat skills, along with completion of [[The Knight's Sword]] and [[Current Affairs]]. After completing or skipping the introductory [[Pandemonium]] quest, access to [[The Pandemonium]] island was unlocked and players were able to board their own raft through the gangplank found on the east dock.\n\nDuring the beta, players were able to open an [[June_2025_Sailing_Beta#Sailing_Beta_Guide|integrated FAQ]] interface to learn more about the beta, and were encouraged to share their feedback in the Sailing Beta Survey.\n\n{{Listen\n|align = right\n|title = Sailing level up - normal\n|filename = Sailing Level Up!.ogg\n|desc = The jingle that plays when levelling up.\n}}\n\n{{Listen\n|align = right\n|title = Sailing level up (Level 30)\n|filename = Sailing Level Up! (Special).ogg\n|desc = Grander variation of the level up jingle, used for reaching 30, the max level for the Beta.\n}}\n\n==Changes From Alpha==\nSeveral changes were made to the playtest version of Sailing shown to players between the previous [[March 2025 Sailing Alpha]] and the June 2025 beta. Specifically [[Ship combat]], [[Crewmates]], [[Bounty tasks]], and [[Sailing random events]] were implemented in addition to a slew of new items to help enable these features. There were also changes to the guide interface shown to players and the ability to skip the introductory quests and explore the new features quicker.\n\n==Access==\n[[File:Sailing beta captcha.png|thumb|right|The captcha screen that appears when logging in on a supported client.]]\nThe beta can be played via one of the following supported clients:\n* [[Java Client]]\n* [[New Official Client]]\n* [[Mobile client]]\n* [[RuneLite]] - The GPU and 117 HD plugins are '''NOT''' supported. You must turn off GPU and 117 HD to use the vanilla renderer or use the GPU (experimental) plugin from the Plugin Hub. Certain plugins from the Plugin Hub such as \"Dimmer\" and \"Large Logout\" are also '''NOT''' supported.\n\n[[HDOS]] is '''NOT''' supported for the Sailing beta.\n\nJoin one of the Sailing Beta worlds. You will be asked \"Can you see a code?\". If you do not see a code ensure you are using one of the supported clients with proper configuration. If you see the code enter the four digit code in the chatbox. \n\nIf it is your first time joining the Sailing beta, use the character customisation screen to customise your character.\n\nIf you wish to use your standard character login to a standard world, open the in-game [[Settings]], then click \"Convert save to beta worlds\".\n\nThe following worlds are classified as [[server]]s for the Sailing beta:\n{| class=\"wikitable sortable align-center-1 align-center-3 sticky-header\" style=\"text-align: left;\"\n!World\n!Location\n!<span title=\"Members, red indicates PvP worlds, black indicates Deadman Mode, light blue/teal indicates Speedrunning, and blue indicates Tournament worlds\" style=\"cursor:help; margin-left:3px;\">Type</span><!-- mems= pvp, deadman, yes, no, tournament-->\n!Activity\n{{WorldLine|400|United States (east)|mems=sailing|Sailing Beta World}}\n{{WorldLine|405|Germany|mems=sailing|Sailing Beta World}}\n{{WorldLine|406|Germany|mems=sailing|Sailing Beta World}}\n{{WorldLine|407|United Kingdom|mems=sailing|Sailing Beta World}}\n{{WorldLine|408|United Kingdom|mems=sailing|Sailing Beta World}}\n{{WorldLine|409|United States (west)|mems=sailing|Sailing Beta World}}\n{{WorldLine|410|United States (west)|mems=sailing|Sailing Beta World}}\n{{WorldLine|411|United States (west)|mems=sailing|Sailing Beta World}}\n{{WorldLine|412|Australia|mems=sailing|Sailing Beta World}}\n{{WorldLine|442|United States (west)|mems=sailing|Sailing Beta World}}\n{{WorldLine|460|Germany|mems=sailing|Sailing Beta World}}\n{{WorldLine|470|United States (east)|mems=sailing|Sailing Beta World}}\n{{WorldLine|471|United States (east)|mems=sailing|Sailing Beta World}}\n{{WorldLine|472|United States (east)|mems=sailing|Sailing Beta World}}\n{{WorldLine|538|United States (west)|mems=sailing|Sailing Beta World}}\n{{WorldLine|564|United Kingdom|mems=sailing|Sailing Beta World}}\n{{WorldLine|566|United Kingdom|mems=sailing|Sailing Beta World}}\n{{WorldLine|574|United States (east)|mems=sailing|Sailing Beta World}}\n{{WorldLine|582|Germany|mems=sailing|Sailing Beta World}}\n{{WorldLine|585|Australia|mems=sailing|Sailing Beta World}}\n{{WorldLine|586|Germany|mems=sailing|Sailing Beta World}}\n{{WorldLine|587|United Kingdom|mems=sailing|Sailing Beta World}}\n{{WorldLine|588|United States|mems=sailing|Sailing Beta World}}\n{{WorldLine|589|United States|mems=sailing|Sailing Beta World}}\n{{WorldLine|590|Australia|mems=sailing|Sailing Beta World}}\n|}\n\n==Basic gameplay==\n[[File:Sailing beta boat navigation.png|right|thumb|The Navigation interface whilst aboard a ship.]] \nSailing changes player navigation significantly from the typical point-and-click gameplay throughout the rest of Gielinor. After obtaining and boarding your first boat at a [[gangplank]] at any port, a navigation interface will replace the player's combat options tab detailing useful information about the currently boarded vessel such as its name, [[facilities]], stats, and [[crewmates]]. Below are the possible names for both the raft and the boat:\n\n{| class=\"wikitable mw-collapsible mw-collapsed\"\n|+Raft and boat names\n!Descriptor\n!Noun\n|-\n|Adamant\n|Aegis\n|-\n|Almighty\n|Aeon\n|-\n|Ancient\n|Age\n|-\n|Angry\n|Anchor\n|-\n|Armadyl's\n|Axe\n|-\n|Ascended\n|Bane\n|-\n|Bandos'\n|Beard\n|-\n|Beautiful\n|Beast\n|-\n|Black\n|Blood\n|-\n|Blasted\n|Blow\n|-\n|Bloody\n|Bounty\n|-\n|Blue\n|Builder\n|-\n|Brave\n|Calm\n|-\n|Bronze\n|Cannon\n|-\n|Brown\n|Chaos\n|-\n|Cheeky\n|Chaser\n|-\n|Cinnamon\n|Clam\n|-\n|Classy\n|Claw\n|-\n|Clever\n|Clipper\n|-\n|Cursed\n|Cloud\n|-\n|Dainty\n|Coral\n|-\n|Dark\n|Craft\n|-\n|Dawn\n|Crow\n|-\n|Daylight\n|Dagger\n|-\n|Deadly\n|Dasher\n|-\n|Death\n|Death\n|-\n|Dragon\n|Demon\n|-\n|Dream\n|Diver\n|-\n|Earnest\n|Diviner\n|-\n|Enigmatic\n|Doom\n|-\n|Excessive\n|Dragon\n|-\n|Extreme\n|Epoch\n|-\n|Fabulous\n|Excess\n|-\n|Fatal\n|Flyer\n|-\n|Fearful\n|Fury\n|-\n|Fervent\n|Glow\n|-\n|Fierce\n|Grace\n|-\n|Final\n|Heart\n|-\n|Fine\n|Hind\n|-\n|First\n|Hunter\n|-\n|Flying\n|Joker\n|-\n|Foul\n|Killer\n|-\n|Frivolous\n|Knife\n|-\n|Gallant\n|Knight\n|-\n|Giant\n|Lament\n|-\n|Golden\n|Law\n|-\n|Gorgeous\n|Lion\n|-\n|Green\n|Loner\n|-\n|Grey\n|Manta\n|-\n|Grim\n|Mind\n|-\n|Grumpy\n|Miner\n|-\n|Happy\n|Mirage\n|-\n|Holy\n|Moon\n|-\n|Honest\n|Ocean\n|-\n|Illustrious\n|Oyster\n|-\n|Implacable\n|Pearl\n|-\n|Indomitable\n|Pride\n|-\n|Insightful\n|Rage\n|-\n|Iron\n|Raider\n|-\n|Jaunty\n|Rain\n|-\n|Jolly\n|Ranger\n|-\n|King\n|Raven\n|-\n|Lady\n|Reaver\n|-\n|Last\n|Sea\n|-\n|Laughing\n|Shine\n|-\n|Light\n|Siren\n|-\n|Little\n|Slayer\n|-\n|Lonely\n|Storm\n|-\n|Lord\n|Sun\n|-\n|Lunar\n|Sword\n|-\n|Lusty\n|Terror\n|-\n|Merry\n|Tide\n|-\n|Mighty\n|Time\n|-\n|Misty\n|Treade\n|-\n|Mithril\n|Warrior\n|-\n|Morbid\n|Wave\n|-\n|Nervous\n|Whip\n|-\n|Night\n|Wind\n|-\n|Nightmare\n|\n|-\n|Noisome\n|\n|-\n|Omniscient\n|\n|-\n|Piebald\n|\n|-\n|Pink\n|\n|-\n|Prancing\n|\n|-\n|Prime\n|\n|-\n|Prince\n|\n|-\n|Princess\n|\n|-\n|Proud\n|\n|-\n|Pure\n|\n|-\n|Purple\n|\n|-\n|Queen\n|\n|-\n|Raging\n|\n|-\n|Rainbow\n|\n|-\n|Ralos'\n|\n|-\n|Rampaging\n|\n|-\n|Ranul's\n|\n|-\n|Red\n|\n|-\n|Reeking\n|\n|-\n|Ribald\n|\n|-\n|Rising\n|\n|-\n|Rune\n|\n|-\n|Sad\n|\n|-\n|Saradomin's\n|\n|-\n|Savage\n|\n|-\n|Scornful\n|\n|-\n|Screaming\n|\n|-\n|Sea\n|\n|-\n|Second\n|\n|-\n|Serene\n|\n|-\n|Smashing\n|\n|-\n|Smiling\n|\n|-\n|Steel\n|\n|-\n|Storm\n|\n|-\n|Sturdy\n|\n|-\n|Sun\n|\n|-\n|Supreme\n|\n|-\n|Swift\n|\n|-\n|Terrifying\n|\n|-\n|Third\n|\n|-\n|Thunder\n|\n|-\n|Tide\n|\n|-\n|Trusty\n|\n|-\n|Twilight\n|\n|-\n|Ultimate\n|\n|-\n|Unholy\n|\n|-\n|Unsinkable\n|\n|-\n|Vibrant\n|\n|-\n|Wandering\n|\n|-\n|Wave\n|\n|-\n|Weeping\n|\n|-\n|Whimsical\n|\n|-\n|White\n|\n|-\n|Wily\n|\n|-\n|Wind\n|\n|-\n|Winged\n|\n|-\n|Yellow\n|\n|-\n|Zamorak's\n|\n|}\n\nNote: Up to three distinct ships can be owned by the player at one time with larger vessels allowing multiple other players to be aboard.\n\nPlayers can move around the deck of their ship normally as they would, but to begin sea navigation, players must click the helm at the back of their ship. Whilst navigating a ship, a white arrow will appear around the player's boat in the direction of their cursor which can be clicked to set the ship's intended direction. Once a direction has been decided and the ship has rotated to point in that direction, movement can be commenced by adjusting the ship's speed using the up and down arrows in the ship's navigation interface--even allowing players to move in reverse--with a set and unset toggle button to immediately stop and start ship movement.\n\nWhilst sailing, gusts of wind will appear around the [[Sails (Sailing)|sails]] of a ship allowing players to trim them and receive a boost of speed for 20 ticks. These winds will appear every 49 ticks naturally or 20 ticks after a previous trimming speed boost has worn off. Trimming sails on a [[Kandarin raft]] without an active [[wind catcher]] grants 35 Sailing experience. Trimming sails on a [[Kandarin small boat]] without an active wind catcher grants 50 Sailing experience. If a [[wind catcher]] has been built on a player's vessel, the Sailing experience is halved upon trimming the sails, and a [[wind mote]] is generated; releasing a wind mote from a wind catcher grants 150 Sailing experience, increasing the overall experience provided by trimming sails. \n\n<gallery>\nFile:Sailing without sails set.png|Sailing without the sails set\nFile:Regular sailing.png|Sailing with sails set\nFile:Sailing when wind gust appears.png|You feel a gust of wind while sailing\nFile:Sailing with trim boost.png|You trim the sails, catching the wind for a burst of speed!\nFile:Sail trim timer official.png|Interface showing remaining time until sails can be trimmed on the [[New Official Client]].\nFile:Sail trim boost succeed official.png|Interface when boost for trimming sail is active on the [[New Official Client]].\nFile:Sail trim boost timer official.png|Interface when time to trim the sails on the [[New Official Client]].\n</gallery>\n\nShips themselves have associated Hitpoints which can be reduced from combat, obstacles, rough waters, and other seafaring adventurers; If your ship's HP is reduced to zero, you will capsize, lose all items in your cargo hold, and be transported to the nearest Port where you will need to retrieve your capsized vessel at the cost of gold before you can venture back out to sea. There are three depths of water, shallow, open, and deep sea which limit boat access depending on the size and strength of the vessel's hull.\n\n==Quests==\n[[Pandemonium]] is the introductory [[quest]] for players to the Sailing skill. It can be skipped by pressing \"Complete\" in the Introduction tab of the Sailing Beta Guide menu, which sets the Sailing level to 10. Alternatively, it can be started by speaking to [[Will]] or [[Anne]] in Port Sarim. The quest teaches the basics of Sailing such as how to sail a ship, dock at a port, and perform other general gameplay tasks. Upon completing or skipping this quest, players are rewarded with their first boat, a [[Kandarin raft]].\n\n[[Prying Times]] is a miniquest introducing prying open [[sealed crate]]s. It can be started by speaking to [[Steve Beanie]] after the completion of Pandemonium. Upon completing this miniquest, players are rewarded with a [[crowbar]] which allows completion of certain Sea charting tasks.\n\nThe [[Current Affairs]] quest is automatically marked as completed in the quest list interface, with the journal reading \"I spoke to people and did some things and stuff.\" but it is not playable in the Beta.\n\n===Sailing quests===\n{| class=\"wikitable sortable lighttable mw-collapsible\" style=\"text-align:center;\"\n!Quest\n!{{SCP|Sailing}} Experience<br/>reward\n!data-sort-type=\"number\"|{{SCP|Sailing}} Sailing<br/>requirement\n!Other requirements\n|-\n|[[Pandemonium]]||512||{{NA}}||{{NA}}\n|-\n|[[Prying Times]]||1000||14||{{SCP|Smithing|30}}, [[Pandemonium]], [[The Knight's Sword]]\n|-\n|[[Current Affairs]]||{{NA}}||22||{{SCP|Fishing|10}}, [[Pandemonium]]\n|}\n\n==Sailing areas==\n{{Map|x=2950|y=2950|mapID=-3|zoom=-2|width=300|height=400|align=right|caption=The Ardent Ocean (North) and the Unquiet Ocean (South) are accessible during the Sailing Beta. For a large image with labels [{{filepath:Sailing beta world map.png}} click here].}}\n[[File:Port map icon.png]] Two oceans are accessible during the Sailing Beta, the [[Ardent Ocean]] and the [[Unquiet Ocean (Sailing alpha)|Unquiet Ocean]], each being comprised of several seas such as the [[Bay of Sarim]].\n\nThe Ardent Ocean is mostly comprised of [[Ocean depth|shallow sea]] with some [[Ocean depth|open sea]] in the east. It is bounded by [[Feldip Hills]] to the south-west, [[Kandarin]] to the west, [[Asgarnia]] to the north, [[Misthalin]] to the north-east, and the [[Kharidian Desert]] to the west. Its southern limit is a line between [[Corsair Cove]] and the [[Ruins of Unkah]]. It encompasses the islands of [[Crandor]], [[Entrana]], [[Karamja]], [[The Pandemonium]], [[Tutorial Island]], and [[Tempoross Cove]]. The [[River Lum]] flows into the Kharidian Gulf.\n\nThe Unquiet Ocean comprises the southernmost waters of [[Gielinor]]'s ocean and is mostly open sea with some [[Ocean depth|deep sea]] in the south. It encompasses the islands of [[Ape Atoll]], [[Charred Island]], [[Dognose Island]], the [[Isle of Bones]], [[The Great Conch]], [[Last Light]], [[The Little Pearl]], and the [[Void Knights' Outpost]]. The Unquiet Ocean flows into the [[Kharidian Ocean]] to the east, and into the [[Western Ocean (Sailing alpha)|Western Ocean]] to the west.\n\n===Docking points===\n[[Docking points]] are placed on various islands and landmasses to embark and dock your ship. There are 15 docking points accessible during the Sailing Beta with associated level requirements to dock at them and various facilities to assist in your Sailing experience.\n\n{{Table/Docking locations}}\n\n==Ship customisation==\n[[File:Shipyard map icon.png]] Two different ship types are available, and up to 5 boats can be purchased in the beta.\n{| class=\"wikitable sortable align-center-4\"\n! {{SCP|Sailing}} Level\n!colspan=\"2\" class=\"unsortable\"| Ship\n!class=\"unsortable\"| Image\n! [[Game square|Size]]\n! Components\n! Facility hotspots\n! Player capacity\n! Crew slots\n!class=\"unsortable\"| Uses\n! Cost\n|-\n|1\n|{{plinkt|Kandarin raft|pic=War ship}}\n|[[File:Behind the Scenes of Sailing- Volume 2 - Part 2 (2).png|125x120px]]\n|1x3\n|\n*Hull\n*Sail\n*Steering\n|1\n|2\n|0\n| \n*The player's first ship\n*Earned as a reward for completing the [[Pandemonium]] quest\n|{{Coins|1000}}\n|-\n|10\n|{{plinkt|Kandarin small boat|pic=Toy ship}}\n|[[File:Kandarin small boat detail.png|125x120px]]\n|2x5\n|\n*Keel\n*Hull\n*Sail\n*Steering\n|4\n|5\n|2\n| \n*Claimed for free from a [[Shipwright]] after reaching level 10 Sailing\n*Recommended for shipwreck salvaging and Barracuda Trials\n|{{Coins|10000}}\n|}\n\nShips can be upgraded and customised to players specific needs depending on the content they wish to complete, with different [[facilities]] benefitting and enabling different sailing activities. This customisation can be done at the [[shipyard]] which can be accessed by selecting the Customise-boat option on a [[shipwright]] at any [[port]]. When at the shipyard, the nearby [[boat schematics]] board allows players to select which of the ships components they wish to upgrade: upgrading a ship's hull allows it to overcome gentle rapids, and upgrading a ship's sails allows it to overcome stormy seas.\n\nKandarin rafts contain only one facility hotspot while Kandarin small boats contain four separate facility hotspots to allow for multiple facilities such as cargo holds, salvaging hooks, cannons and wind catcher. Upgrading your ship's facilities will award players with Sailing and Construction experience and requires upgrade materials such as [[planks]], [[nails]], and [[cloth]] of various tiers.\n\n==Port tasks==\n[[File:Sailing courier port task.png|thumb|right|150px|An example of a courier port task.]][[File:Sailing bounty port task.png|thumb|right|150px|An example of a bounty port task.]]\n[[File:Notice board map icon.png]] Port tasks are a Sailing activity that involves the player picking up various tasks from [[Notice board (Sailing)|Notice boards]] located at specific docks and completing objectives for Sailing experience and resources.\n\n===Courier tasks===\n{{Main|Courier tasks}}\nCourier tasks specify two ports to bring a number of cargo between with both origin and destination being randomly generated regardless of which port you collect the task. Initially a maximum of 2 Port tasks can be accepted at one time by the player, with this increasing by level up to 5 in total allowing players to complete multiple tasks at once supposing efficient pathing between ports. The amount of cargo required per task can differ and be as low as a single piece. Once a courier task has been accepted, players will need both hands free in order to collect cargo from a nearby [[ledger table]] before placing it into the player's [[Basic cargo hold|cargo hold]]s aboard their boats. Collecting and unloading the cargo at the destination's ledger table rewards the player with Sailing experience and items useful for shipbuilding, such as logs, nails, and gems.\n\n===Bounty tasks===\n{{Main|Bounty tasks}}\nBounty tasks assign players with a specific seafaring monster to kill in hopes of collecting a specific item similar to [[Hunter rumours]]. Once you have the required number of items, return to the Port master for your reward.\n{{clear}}\n\n==Sea charting==\n{{Main|Sea charting}}\n[[File:Captain's log sea charting interface.png|thumb|right|The Captain's log displaying some sea charting tasks for the Port of Sarim.]]\n[[File:Port master map icon.png]] [[Sea charting]] involves performing bespoke tasks in various parts of the sea and are intended to have players explore Gielinor's oceans and reward them with once-off experience for doing so. These tasks vary from simple exploration to puzzles and even data collection, many of which requiring specific tools to complete. Most charting tools can be collected from your ship's cargo hold or from the associated task NPC other than the [[crowbar]] which is rewarded upon completion of the miniquest [[Prying Times]].\n\n===Charting tools===\nCharting tools can be withdrawn from the [[basic cargo hold]] by clicking the duck icon in the bottom-left of the cargo hold interface. There is also a right-click \"Take all\" option.\n[[File:Spyglass location bubbles.png|thumb|right|A location where the [[spyglass]] can be used.]]\n[[File:Current duck location ripples.png|thumb|right|A location where the [[current duck]] can be used.]]\n{| class=\"wikitable align-left-2 align-left-3\" style=\"max-width:75%\"\n! colspan=\"2\" |Tool\n!Description\n|-\n|{{plinkt|Captain's log}}\n|To record and document sea charting. It will also tell you information about where you can chart\n|-\n|{{plinkt|Spyglass}}\n|Observe notable areas when your ship is positioned upon a group of bubbles on the ocean surface.\n|-\n|{{plinkt|Current duck}}\n|Track the currents of the sea when your ship is positioned upon a ripple on the ocean surface by placing and following the duck.\n|-\n|{{plinkt|Crowbar}}\n|Pry open [[sealed crate]]s found at sea and choose whether you want to drink what you find inside. The quest [[Prying Times]] provide you with more information about how to use the Crowbar.\n|-\n|{{plinkt|Deep sea helmet}}\n|rowspan=\"2\"|Gather sea depth data after assisting mermaids.\n|-\n|{{plinkt|Deep sea apparatus}}\n|}\n\n===Available tasks===\n{{Main|Sea charting#All tasks}}\nDuring the Sailing Beta, a total of 29 tasks are available in the [[Bay of Sarim]], [[Kharidian Sea]], [[Lumbridge Basin]], [[Mudskipper Sound]]  and [[Rimmington Strait]] — all within the [[Ardent Ocean]].\n\n*Completing all 6 tasks in the [[Bay of Sarim]] awards 259 bonus Sailing XP.\n*Completing all 6 tasks in the [[Kharidian Sea]] awards 259 bonus Sailing XP.\n*Completing all 5 tasks in the [[Lumbridge Basin]] awards 267 bonus Sailing XP.\n*Completing all 7 tasks in [[Mudskipper Sound]] awards 274 bonus Sailing XP.\n*Completing all 5 tasks in [[Rimmington Strait]] awards 288 bonus Sailing XP.\n*Completing all 29 tasks in the [[Ardent Ocean]] awards 288 bonus Sailing XP.\n*Completing all the same 29 total tasks available in the Sailing Alpha awards another 288 bonus Sailing XP.\n\n==Shipwreck salvaging==\n[[File:Salvaging map icon.png]] Salvaging is a low-intensity gathering activity in Sailing where players will move adjacent to [[sunken shipwrecks]] across the ocean, deploy their [[Salvaging hook (disambiguation)|salvaging hooks]], and collect resources for small amounts of Sailing experience. [[Salvage]] collected comes in a number of varieties and can be added to your ship's [[Basic cargo hold|cargo hold]] if the facility has been built. Salvaging hooks themselves come in various metal tiers, with associated Sailing level requirements which allow for gathering at higher tier locations and with greater success.\n\n[[File:Salvage station map icon.png]] Once collected, salvage can be automatically sorted and processed into items at a [[salvaging station]] found at most ports. This process awards some token experience and returns potentially useful or valuable items.\n\nThere are two types of shipwrecks:\n{| class=\"wikitable sortable align-center-3\"\n! {{SCP|Sailing}} Level\n! Shipwreck\n!class=\"unsortable\"| Image\n! {{SCP|Sailing}} XP for salvaging\n!colspan=\"2\" | Salvage\n! {{SCP|Sailing}} XP for sorting\n!class=\"unsortable\"| Notable drops\n<!--!class=\"unsortable\"| Locations\n!class=\"unsortable\"| Map\n!class=\"unsortable\"| Closest [[salvaging station]]s-->\n|-\n|12\n|[[Small shipwreck]]\n|[[File:Small shipwreck.png|100x100px]]\n|13\n|{{plinkt|Small salvage}}\n|2\n| \n*[[Clue scroll (easy)]]\n*[[Sunken locket]]\n<!--|\n*[[Bedabin Basin]] (5 spawns)\n|{{Map|3091:2959|3097:2936|3126:2957|3125:2926|3120:2990|mtype=pin|mapID=-3|height=150|width=150}}\n|\n*[[The Pandemonium]]-->\n|-\n|25\n|[[Fisherman shipwreck]]\n|[[File:Fisherman shipwreck.png|100x100px]]\n|30\n|{{plinkt|Fishy salvage}}\n|4\n|\n*[[Clue scroll (hard)]]\n*[[Sunken locket#Green|Sunken locket]]\n*[[Rune sword]]\n*[[Uncut dragonstone]]\n<!--|\n*[[The Feldip Strait|Feldip Strait]] (5 spawns)\n|{{Map|2705:3051|2722:3066|2730:3039|2672:3027|2665:3063|mtype=pin|mapID=-3|height=150|width=150}}\n|\n*[[Port Khazard]] (requires 30 Sailing)\n*[[Brimhaven]]-->\n|}\n\nThere are two locations where shipwrecks can be found:\n{| class=\"wikitable sortable align-center-3\"\n!Ocean\n!Sea\n!class=\"unsortable\"|Map\n!class=\"unsortable\"|Shipwrecks\n!class=\"unsortable\"|Closest [[salvaging station]]s\n|-\n|[[Ardent Ocean]]\n|[[Kharidian Sea]]\n|{{Map|3091:2959|3097:2936|3126:2957|3125:2926|3120:2990|mtype=pin|mapID=-3|height=200|width=200}}\n|\n*[[Small shipwreck]]s (5 spawns)\n|\n*[[The Pandemonium]]\n|-\n|[[Ardent Ocean]]\n|[[Feldip Gulf]]\n|{{Map|2705:3051|2722:3066|2730:3039|2672:3027|2665:3063|mtype=pin|mapID=-3|height=200|width=200}}\n|\n*[[Fisherman shipwreck]]s (5 spawns)\n|\n*[[Port Khazard]] (requires 30 Sailing)\n*[[Brimhaven]] (requires 25 Sailing)\n|}\n\n==Barracuda Trials==\n[[File:Barracuda trial interface (The Tempor Tantrum).png|thumb|right|The rank selection interface for The Tempor Tantrum Barracuda trial.]] [[File:Barracuda trial icon.png]] [[Barracuda Trials]] are a skilling activity unlocked at level 30 Sailing. Gameplay involves navigating obstacle courses at sea in which players must deliver [[rum]] and [[lost supplies]] to the [[Barracudas]] as quickly as possible, with completion granting a chunk of Sailing experience. Each trial has three [[Barracuda rank|ranks]] of difficulty (Swordfish, Shark, and Marlin), with increasingly strict time limits and collection requirements, the first completion of each tier awards additional bonus Sailing experience and unlocks the ability to attempt the subsequent tier. Your fastest completion time and achieved ranks are tracked by the [[Scoreboard (Barracuda Trials)|scoreboard]].\n\n===Mechanics===\nAdditional mechanics involved in Barracuda trials outside of typical Sailing navigation include [[Gentle rapids|rapids]] which when sailed across provide a stacking bonus to your ship's speed that decays over time; precise use of these rapids is necessary for achieving faster completion times. Items such as [[lost supplies]] are scattered throughout the trial and a certain number must be collected by simply sailing within 2 tiles of the object. Trials also include a main objective such as a [[rum shipment]] which must be collected for completion and is generally only found in one location unlike lost supplies. Obstacles are also littered throughout the trial such as the [[Lightning cloud (sailing)|lightning cloud]] which move around in a small radius, after a period of time they increase in size before calling down lightning around itself and dealing 10 damage to your ship if nearby.\n\n===HUD===\n[[File:Barracuda trial HUD.png|thumb|right|The HUD for a Barracuda Trial.]]\nUpon entering a Barracuda trial, a HUD will appear in the top left of the screen. There are several icons on the HUD:\n* A reset button for Resetting and Quick Resetting the current trial.\n* A main objective icon which indicates whether or not the required trial item has been collected.\n* A live timer recording your current time in the trial.\n* A goal time for the difficulty rank of the trial.\n* A icon displaying what rank is being attempted.\n* Two icons recording the number of [[rum shipments]] and [[lost supplies]] currently collected out of the required amount for completion.\n\n===The Tempor Tantrum===\n{{Map|mtype:text,3035,2928,label:Rum-dashed Ralph|mapID=-3|mtype:text,3035,2728,label:Rum shipment collection|align=right}}\n{{Main|The Tempor Tantrum}}\nThe first Barracuda Trial unlocked at level 30 Sailing is [[The Tempor Tantrum]] which can be started by speaking to [[Rum-dashed Ralph]] north-west of [[The Storm Tempor]]. This trial tasks you with collecting a [[rum shipment]] from the [[merchant ship]] south of [[Tempoross]] before returning to Ralph. Additional lost supplies can be found along the route, a number of which must be collected for completion with the amount increasing depending on the rank being attempted. Requirements include a ship with at least an [[Oak hull]] to withstand the traversal of gentle rapids.\n\n{| class=\"wikitable align-center-1\"\n! colspan=\"2\" |Rank\n!Time\n!{{plink|Rum shipments}}\n!{{plink|Lost supplies}}\n!{{SCP|Sailing}} Completion XP\n!{{SCP|Sailing}} Bonus XP\n! colspan=\"2\" |Reward\n|-\n|[[File:Unranked rank.png|32x32px]]\n|Unranked\n|{{NA}}\n|1\n|10\n|200\n|{{NA}}\n|{{NA}}\n|{{NA}}\n|-\n|[[File:Swordfish rank.png|32x32px]]\n|Swordfish\n|2:00\n|1\n|14\n|400\n|2,500\n|{{plinkt|Placeholder swordfish reward}}\n|-\n|[[File:Shark rank.png|32x32px]]\n|Shark\n|3:06\n|2\n|25\n|1,200\n|5,000\n|{{plinkt|Placeholder shark reward}}\n|-\n|[[File:Marlin rank.png|32x32px]]\n|Marlin\n|5:00\n|3\n|36\n|2,500\n|8,000\n|{{plinkt|Placeholder marlin reward}}\n|}\n\n==Sailing Beta Guide==\nDuring the beta a guide could be accessed using the information button which was displayed at all times. A link to the feedback section was displayed at the top left of every page within the guide. The guide sections covered an introduction, port tasks, charting the sea, the shipyard, shipwreck salvaging, exploration, and barracuda trials. Each of these sections contained a summary, tasks which the player could complete, and FAQs. There was also an about section which contained a summary and FAQs on the beta and Sailing skill. A full transcript of the guide can be found [[Transcript:June 2025 Sailing Beta guide|here]]. \n\n<gallery>\nSailing beta info.png|Information button\nSailing beta Guide interface.png|Main guide interface\nSailing beta Guide interface - about.png|The about section\nSailing beta Guide interface - charting tasks.png|Tasks in the charting section\n</gallery>\n\n==Music==\n{| class=\"wikitable sortable\"\n!Name\n! class=\"unsortable\" |Unlock details\n! class=\"unsortable\" |Music track\n|-\n|[[Honkytonky Sea Shanty 2]]\n|Unlocked in [[Pandemonium (location)|the Pandemonium]]\n|[[File:Honkytonky Sea Shanty 2.ogg]]\n|}\nWhile on a boat, a playlist of sailing themed songs will play.\n{| class=\"wikitable sortable\"\n!Name\n! class=\"unsortable\" |Music track\n|-\n|[[High Seas]]\n|[[File:High Seas.ogg]]\n|-\n|[[Sea Shanty 2]]\n|[[File:Sea Shanty 2.ogg]]\n|-\n|[[Sea Shanty]]\n|[[File:Sea Shanty.ogg]]\n|-\n|[[Crest of a Wave]]\n|[[File:Crest of a Wave.ogg]]\n|-\n|[[Jolly R]]\n|[[File:Jolly R.ogg]]\n|}\n\n== Gallery ==\n<gallery>\nFile:Sailing beta world map.png|The sailing beta world map including area labels\n</gallery>\n\n==References==\n;General resources\n# {{PlainCiteNews|url=https://secure.runescape.com/m=news/behind-the-scenes-of-sailing-volume-3-sailing-alpha-launch-date---runefest?oldschool=1|title=Behind the Scenes of Sailing: Volume 3 (Sailing Alpha Launch Date) - RuneFest|newsdate=1 March 2025}}\n# {{PlainCiteVideo|author=|title=Sailing Alpha Preview - OSRS Q&A Livestream March 18th|url=https://www.youtube.com/watch?v=2aR-rEbpqAg|date=18 March 2025|site=YouTube|notes=|name=}}\n# {{PlainCiteNews|url=https://secure.runescape.com/m=news/sailing-alpha-live-now?oldschool=true|title=Sailing Alpha Live Now!\n|newsdate=20 March 2025}}\n# {{PlainCiteVideo|author=|title=Unlocking the Seas: The Complete OSRS Sailing Alpha Breakdown|url=https://www.youtube.com/watch?v=nOa20hB0OO0|site=YouTube|notes=|name=}}\n# {{PlainCiteVideo|author=|title=Sailing Alpha Modcast - OSRS Discord Stage March 24th|url=https://www.youtube.com/watch?v=QLW2me5QFWA|site=|notes=|name=}}\n\n{{Sailing}}", "revisionId": 14980276, "timestamp": "2025-09-05T03:05:13Z", "downloadedAt": "2025-09-05T20:37:53.608Z", "source": "osrs_wiki_watcher", "rawWikitext": true}