{"title": "Money making guide/Delivering food in Gnome Restaurant", "namespace": "Main", "content": "{{Mmgtable\n|Activity = [[Gnome Restaurant]]\n|Category = Collecting\n|Image = [[File:<PERSON><PERSON><PERSON>'s Restaurant.png|300px]]\n|Skill =\n* {{SCP|Cooking|42}}\n* {{SCP|Magic|61}}\n|Quest = \n* [[Troll Romance]] {{^|<PERSON><PERSON><PERSON>}}\n* [[Fishing Contest]] {{^|Ninto}}\n* [[The Grand Tree]] {{^|Penwie}}\n* [[The Giant Dwarf]] {{^|Manglethorp}}\n* [[Monkey Madness II]] for the [[royal seed pod]] (optional)\n|Other = \n* Completion of the [[Desert Diary#Elite|elite Desert Diary]] for the [[desert amulet 4]] (optional)\n* Completion of the [[Ardougne Diary#Medium|medium Ardougne Diary]] for the [[Ardougne cloak 2]] (optional)\n|Item =\n* A fully-filled [[Reward token (Gnome Restaurant)|reward token]]\n* [[Weight-reducing clothing]]\n* [[Teleportation]] items of choice (see table below)\n|Experience1 = Cooking\n|Experience1num = 150\n|isperkill = y\n|kph = 30\n|kph name = Deliveries per hour\n|Input1 = Toad crunchies\n|Input1num = 1 * (1/20)\n|Input2 = Spicy crunchies\n|Input2num = 1 * (1/20)\n|Input3 = Worm crunchies\n|Input3num = 1 * (1/20)\n|Input4 = Chocchip crunchies\n|Input4num = 1 * (1/20)\n|Input5 = Fruit batta\n|Input5num = 1 * (1/20)\n|Input6 = Toad batta\n|Input6num = 1 * (1/20)\n|Input7 = Worm batta\n|Input7num = 1 * (1/20)\n|Input8 = Vegetable batta\n|Input8num = 1 * (1/20)\n|Input9 = Cheese+tom batta\n|Input9num = 1 * (1/20)\n|Input10 = Worm hole\n|Input10num = 1 * (1/20)\n|Input11 = Veg ball\n|Input11num = 1 * (1/20)\n|Input12 = Tangled toad's legs\n|Input12num = 1 * (1/20)\n|Input13 = Chocolate bomb\n|Input13num = 1 * (1/20)\n|Input14 = Fruit blast\n|Input14num = 1 * (1/20)\n|Input15 = Pineapple punch\n|Input15num = 1 * (1/20)\n|Input16 = Wizard blizzard\n|Input16num = 1 * (1/20)\n|Input17 = Short green guy\n|Input17num = 1 * (1/20)\n|Input18 = Drunk dragon\n|Input18num = 1 * (1/20)\n|Input19 = Choc saturday\n|Input19num = 1 * (1/20)\n|Input20 = Blurberry special\n|Input20num = 1 * (1/20)\n|Input21 = Ring of dueling(8)\n|Input21num = (1/8) * (1/6)\n|Input22 = Games necklace(8)\n|Input22num = (1/8) * (1/6)\n|Input23 = Stamina potion(4)\n|Input23num = 4\n|Input23isph = y\n|Output1 = Grand seed pod\n|Output1num = 5 * (23/512) * (4/6)\n|Output2 = Gnome scarf\n|Output2num = 1 * (23/384) * (2/6)\n|Output3 = Gnome goggles\n|Output3num = 1 * (23/384) * (2/6)\n|Output4 = Gnomeball\n|Output4num = 1 * (10/128) * (6/6)\n|Output5 = Mint cake\n|Output5num = 1 * (23/512) * (3/6)\n|Output6 = Tooth half of key\n|Output6num = 1 * ( ((23/768) * (2/6)) + ((23/1024) * (4/6)) )\n|Output7 = Loop half of key\n|Output7num = 1 * ( ((23/768) * (2/6)) + ((23/1024) * (4/6)) )\n|Output8 = Toadflax\n|Output8num = 3 * (10/128) * (6/6)\n|Output9 = Snapdragon\n|Output9num = 1 * (10/128) * (6/6)\n|Output10 = Uncut opal\n|Output10num = ((2+3) / 2) * (10/128) * (6/6)\n|Output11 = Uncut jade\n|Output11num = ((2+7) / 2) * (10/128) * (6/6)\n|Output12 = Uncut red topaz\n|Output12num = 1 * (10/128) * (6/6)\n|Output13 = Uncut sapphire\n|Output13num = ((7+10) / 2) * (10/128) * (6/6)\n|Output14 = Uncut emerald\n|Output14num = ((3+7) / 2) * (10/128) * (6/6)\n|Output15 = Uncut ruby\n|Output15num = ((2+5) / 2) * (10/128) * (6/6)\n|Output16 = Uncut diamond\n|Output16num = 1 * (10/128) * (6/6)\n|Output17 = Bird nest (seeds)\n|Output17num = 1 * (5/128) * (6/6)\n|Output17value = {{Average drop value|mob=Bird nest (seeds)|raw=y}}\n|Output18 = Raw oomlie\n|Output18num = 1 * (23/512) * (1/6)\n|Output19 = Gold ore\n|Output19num = ((6+11) / 2) * (23/512) * (1/6)\n|Output20 = Coal\n|Output20num = ((12+22) / 2) * (23/512) * (1/6)\n|Location = [[Grand Tree]]\n|Members = Yes\n|Intensity = High\n|Details = \n__NOTOC__\n'''The profit rate assumes 30 orders are delivered every hour by default. Other assumptions include that the player has already earned enough credits to fully fill up their [[Reward token (Gnome Restaurant)|reward token]], and that all orders are rejected except if the customer is [[Captain Ninto]], [[Captain Daerkin]], [[Wingstone]], [[Penwie]], [[Brambickle]], or [[Professor Manglethorp]].'''\n\nTo start [[Gnome Restaurant]], talk to [[Gianne jnr.]] on the {{FloorNumber|uk=1}} of the [[Grand Tree]]. Only accept delivery requests sent in by Captain Ninto, Captain Daerkin, Wingstone, Penwie, Brambickle, or Professor Manglethorp. If another customer is mentioned, do not progress the dialogue any further, but simply talk to Gianne jnr. again and select the hard delivery option (option 2). This is only possible if enough deliveries have been made beforehand to fully fill up a [[Reward token (Gnome Restaurant)|reward token]], meaning it would award 10 items of gnome food on activation.\n\n====Making the food====\n{{Main|Gnome cooking}}\nTo achieve maximum number of deliveries per hour, it is advisable to stock up on gnome food before accepting deliveries, either by lovingly preparing them oneself or by purchasing them at the Grand Exchange. Note that supply might be low if using the latter option, so orders may have to be placed well in advance. If the drinks and dishes are homecooked, the number of deliveries per hour can be adjusted downwards accordingly. In this case, the cost of the various gnome food items used in this calculation can still be considered adequate, as they represent the opportunity cost of not listing the items for sale, and ingredient costs are negligible.\n\nPreparing the food culinaries on the fly is viable option, as players have to factor in the banking time compared to the small run into [[Hudo]]s kitchen and preparing dishes there, although one must have [[Toad's legs|toad legs]] and [[King worm|worms]] in their inventory for the occasional order that can't be cooked just by the stock provided by Hudo. Extra toad legs and king worms should also be stocked up to avoid the long run to collect them during deliveries.\n\nAlthough preparing food items on the fly is viable, players should consider preparing bulk of drinks beforehand as the run up to [[Heckel Funch]] takes a long time even with the royal seed pod. Iron players who want to prepare drinks in bulk can obtain the alcoholic drinks easily at [[Sunlight's Sanctum]] in [[Aldarin]] as it's very close to a bank.\n\n====Delivering the food====\n{{(}}{{!}} class=\"wikitable\" style=\"vertical-align:top;\"\n![[Captain Ninto]]\n![[Captain Daerkin]]\n![[Wingstone]]\n{{!}}-\n{{!}}{{Map|name=Captain Ninto|2869,9877|mapID=20|r=3|mtype=square}}\n{{!}}{{Map|name=Captain Daerkin|3354,3222|r=5|mtype=square}}\n{{!}}{{Map|name=Wingstone|3381,2891|rectX=86|rectY=67|zoom=1|mtype=rectangle}}\n{{!}}-\n!Tunnel beneath [[White Wolf Mountain]]\n!Stands of the [[Emir's Arena]]\n!Between [[Nardah]] and the [[Agility Pyramid]]\n{{!}}-\n{{!}} style=\"max-width:300px; vertical-align:top;\" {{!}}\n*[[Taverley teleport]]\n*[[Games necklace]] to [[Burthorpe Games Room]], then run south-west\n*[[Varrock teleport]], then enter the [[Keldagrim minecart system|minecart system]] through the trapdoor and take minecart to White Wolf Mountain\n{{!}} style=\"max-width:300px; vertical-align:top;\" {{!}}\n*[[Ring of dueling]]\n*[[Gnome glider]] to Al Kharid\n{{!}} style=\"max-width:300px; vertical-align:top;\" {{!}}\n*[[Desert amulet 4]]\n*[[Nardah teleport]]\n*[[Pharaoh's sceptre]]\n*[[Fairy ring]] {{Fairycode|DLQ}}\n*[[Pollnivneach teleport]] (with 71 Agility)\n{{!}}-\n{{!}}{{)}}\n\n{{(}}{{!}} class=\"wikitable\"\n![[Penwie]]\n![[Brambickle]]\n![[Professor Manglethorp]]\n{{!}}-\n{{!}}{{Map|name=Penwie|2937,2970|r=17|mtype=square}}\n{{!}} {{Map|name=Brambickle|2782,3865|r=5|mtype=square}}\n{{!}} {{Map|name=Professor Manglethorp|2868,10198|r=6|mapID=10|mtype=square}}\n{{!}}-\n!West of the [[gnome glider]] on [[Karamja]]\n!Atop [[Trollweiss Mountain]]\n!Western wing of the [[Keldagrim Palace]]\n{{!}}-\n{{!}} style=\"max-width:300px; vertical-align:top;\" {{!}}\n*[[Gnome glider]] to Karamja\n{{!}} style=\"max-width:300px; vertical-align:top;\" {{!}}\n*[[Trollheim teleport]], then head north and pass through the [[Trollweiss Dungeon]]\n*[[Ghommal's hilt]] teleport to the [[God Wars Dungeon]]\n{{!}} style=\"max-width:300px; vertical-align:top;\" {{!}}\n*[[Varrock teleport]], then enter the [[Keldagrim minecart system|minecart system]] through the trapdoor\n*[[Blast Furnace]] teleport on the [[grouping]] tab\n{{!}}-\n{{!}}{{)}}\n\n====Returning to Gianne jnr.====\n{{Map|2580,3240|width=280|2555:3260,desc:[[Spirit tree]]|2606:3219,icon:bluePin,desc:[[Ardougne Monastery]]|mtype=pin|align=right|caption=From the monastery, head north-west to the spirit tree.}}\nAfter the order has been delivered, head back to the Grand Tree through one of the following methods:\n#The fastest way is to use a [[royal seed pod]], which is a reward from [[Monkey Madness II]] and has unlimited charges.\n#Another way is to access the [[spirit tree]] network, which can be done at the [[Grand Exchange]] in [[Varrock]].\n#Alternatively, teleport to the [[Ardougne Monastery]] with an [[Ardougne cloak]], then run north-west past the [[Clock Tower (building)|Clock Tower]] to reach the spirit tree.\n#Teleport to [[Castle Wars]] with a ring of dueling, then take a [[balloon transport system|balloon]] to the [[Tree Gnome Stronghold]] at the cost of one [[Magic logs|magic log]] per flight.\n}}\n{{Money making}}", "revisionId": 14980553, "timestamp": "2025-09-05T16:34:42Z", "downloadedAt": "2025-09-05T20:37:26.727Z", "source": "osrs_wiki_watcher", "rawWikitext": true}