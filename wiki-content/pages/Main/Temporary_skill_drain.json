{"title": "Temporary skill drain", "namespace": "Main", "content": "A '''temporary skill drain''' is an effect caused by [[item]]s, [[monster]]s, [[weapon]] and [[ammunition]] [[special attacks]] or passive abilities, or interactions with [[scenery]] or the environment, which temporarily lowers a player's level in one or more [[skills]]. This can be counter-acted with [[temporary skill boost]]s or [[stat restoration]].\n\nSkill drains on players can prevent them from performing specific actions if their level is underneath the requirement, particularly when attempting skilling activities. For example, a player grinding [[Musca mushroom]]s would get 6 [[musca powder]] at 80 and higher [[Herblore]]; however, if this was drained to 62, the player would get 4 powder per action. Likewise, if a player had 85 [[Mining]] but was drained 5 levels to 80, they would not be able to mine [[runite rocks]] until the stat is restored.\n\nSome skill drains stack; drinking multiple [[wine]]s will lower [[Attack]] by 2 for every one consumed, however some combat related special attacks and spells will only apply when the stat is at or above the unchanged level.\n\nStats are restored at a rate of 1 per 60 seconds, reduced to 30 if the prayer [[Rapid Restore]] is active.\n\nWhen used on monsters, this is primarily to lower their combat stats, primarily [[Defence]], so they are easier to hit. This is generally done with an [[elder maul]], [[dragon warhammer]] or [[bandos godsword]]. A few weapons are able to drain multiple stats at once, such as [[Emberlight]] (all combat stats) or the [[Tonalztics of ralos]]/[[accursed sceptre]] (magic and defence). Outside of draining defence however, draining other combat stats has very little impact in most cases.\n\nMost monsters follow the same rules as players, with a few exceptions unique to them:\n\n*Some monsters are immune to stat drains.\n**These include [[Verzik Vitur]], the Awakened variants of [[the Forgotten Four]], and contracted [[Yama]] fights.\n*Some monsters restore stats at a faster rate than normal, and can restore/raise them during phase transitions.\n*Some monsters are able to adjust their stats as their health lowers.\n*Some monsters have a hard cap on stat drains; for example, a monster with a hard cap of 50 and a Defence level of 250 means that the lowest its Defence level could be is 200.\n\nOn the tables below, CurrentLevel refers to the left-side number on the skills interface and StaticLevel refers to the right-side.\n\n==List of temporary skill draining effects==\n===Items===\n{|class=\"wikitable align-center-1\"\n!colspan=\"2\"|Item\n!Drain\n!Other effects\n|-\n|{{plinkt|Vodka}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Strength|link=yes}} 1 + floor(StaticLevel * 0.05)<br/>{{SCP|Hitpoints|link=yes}} 5\n|-\n|{{plinkt|Whisky}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Strength|link=yes}} 1 + floor(StaticLevel * 0.05)<br/>{{SCP|Hitpoints|link=yes}} 5\n|-\n|{{plinkt|Brandy}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Strength|link=yes}} 1 + floor(StaticLevel * 0.05)<br/>{{SCP|Hitpoints|link=yes}} 5\n|-\n|{{plinkt|Gin}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Strength|link=yes}} 1 + floor(StaticLevel * 0.05)<br/>{{SCP|Hitpoints|link=yes}} 5\n|-\n|{{plinkt|Short green guy}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Strength|link=yes}} 1 + floor(StaticLevel * 0.05)<br/>{{SCP|Hitpoints|link=yes}} 5\n|-\n|{{plinkt|Premade sgg}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Strength|link=yes}} 1 + floor(StaticLevel * 0.05)<br/>{{SCP|Hitpoints|link=yes}} 5\n|- \n|{{plinkt|Blurberry special}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Hitpoints|link=yes}} 7<br/>{{SCP|Strength|link=yes}} 2 + floor(StaticLevel * 0.05)\n|- \n|{{plinkt|Premade blurb' sp.}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Hitpoints|link=yes}} 7<br/>{{SCP|Strength|link=yes}} 2 + floor(StaticLevel * 0.05)\n|-\n|{{plinkt|Drunk dragon}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Hitpoints|link=yes}} 5<br/>{{SCP|Strength|link=yes}} 2 + floor(StaticLevel * 0.05)\n|-\n|{{plinkt|Premade dr' dragon}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Hitpoints|link=yes}} 5<br/>{{SCP|Strength|link=yes}} 2 + floor(StaticLevel * 0.05)\n|-\n|{{plinkt|Choc saturday}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Hitpoints|link=yes}} 5<br/>{{SCP|Strength|link=yes}} 2 + floor(StaticLevel * 0.05)\n|-\n|{{plinkt|Premade choc s'dy}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Hitpoints|link=yes}} 5<br/>{{SCP|Strength|link=yes}} 2 + floor(StaticLevel * 0.05)\n|-\n|{{plinkt|Wizard blizzard}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Hitpoints|link=yes}} 5<br/>{{SCP|Strength|link=yes}} 1 + floor(StaticLevel * 0.06)\n|-\n|{{plinkt|Premade wiz blz'd}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Hitpoints|link=yes}} 5<br/>{{SCP|Strength|link=yes}} 1 + floor(StaticLevel * 0.06)\n|-\n|{{plinkt|Jug of wine}}\n|{{SCP|Attack|link=yes}} -2\n|{{SCP|Hitpoints|link=yes}} 11\n|-\n|{{plinkt|Saradomin brew}}\n|{{SCP|Attack|link=yes}} - (CurrentLevel * 0.1) - 2<br/>{{SCP|Magic|link=yes}} - (CurrentLevel * 0.1) - 2<br/>{{SCP|Ranged|link=yes}} - (CurrentLevel * 0.1) - 2<br/>{{SCP|Strength|link=yes}} - (CurrentLevel * 0.1) - 2\n|{{SCP|Hitpoints|link=yes}} 2 + (StaticLevel * 0.15)<br/>{{SCP|Defence|link=yes}} 2 + (StaticLevel * 0.2)\n|-\n|{{plinkt|Ancient brew}}\n|{{SCP|Attack|link=yes}} - 2 - (CurrentLevel * 0.1<br/>{{SCP|Defence|link=yes}} - 2 - (CurrentLevel * 0.1<br/>{{SCP|Strength|link=yes}} - 2 - (CurrentLevel * 0.1\n|{{SCP|Magic|link=yes}} 2 + (StaticLevel * 0.05)<br/>{{SCP|Prayer|link=yes}} 2 + (StaticLevel * 0.1)\n|-\n|{{plinkt|Ancient mix}}\n|{{SCP|Attack|link=yes}} - 2 - (CurrentLevel * 0.1<br/>{{SCP|Defence|link=yes}} - 2 - (CurrentLevel * 0.1<br/>{{SCP|Strength|link=yes}} - 2 - (CurrentLevel * 0.1\n|{{SCP|Magic|link=yes}} 2 + (StaticLevel * 0.05)<br/>{{SCP|Prayer|link=yes}} 2 + (StaticLevel * 0.1)<br/>{{SCP|Hitpoints|link=yes}} 6\n|-\n|{{plinkt|Asgarnian ale}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 2\n|{{SCP|Hitpoints|link=yes}} 1<br/>{{SCP|Strength|link=yes}} 2\n|-\n|{{plinkt|Asgarnian ale(m)}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 3\n|{{SCP|Hitpoints|link=yes}} 1<br/>{{SCP|Strength|link=yes}} 3\n|-\n|{{plinkt|Axeman's folly}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 2<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.02) - 2\n|{{SCP|Woodcutting|link=yes}} 1<br/>{{SCP|Hitpoints|link=yes}} 1\n|-\n|{{plinkt|Axeman's folly(m)}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Woodcutting|link=yes}} 2<br/>{{SCP|Hitpoints|link=yes}} 2\n|-\n|{{plinkt|Bandit's brew}}\n|{{SCP|Defence|link=yes}} - floor(Currentlevel * 0.06) - 3<br/>{{SCP|Strength|link=yes}} - floor(Currentlevel * 0.06) - 3\n|{{SCP|Attack|link=yes}} 1<br/>{{SCP|Hitpoints|link=yes}} 1<br/>{{SCP|Thieving|link=yes}} 1\n|-\n|{{plinkt|Beer}}\n|{{SCP|Attack|link=yes}} - floor(Currentlevel * 0.06) - 1\n|{{SCP|Hitpoints|link=yes}} 1<br/>{{SCP|Strength|link=yes}} 1 + floor(StaticLevel * 0.02)\n|-\n|{{plinkt|Chef's delight}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 2<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.05) - 2\n|{{SCP|Cooking|link=yes}} 1 + floor(StaticLevel * 0.05)<br/>{{SCP|Hitpoints|link=yes}} 1\n|-\n|{{plinkt|Chef's delight(m)}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 3<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.05) - 3\n|{{SCP|Cooking|link=yes}} 2 + floor(StaticLevel * 0.05)<br/>{{SCP|Hitpoints|link=yes}} 2\n|-\n|{{plinkt|Cider}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 2<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.02) - 2\n|{{SCP|Farming|link=yes}} 1<br/>{{SCP|Hitpoints|link=yes}} 1\n|-\n|{{plinkt|Mature cider}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Farming|link=yes}} 2<br/>{{SCP|Hitpoints|link=yes}} 2\n|-\n|{{plinkt|Cider (Player-owned house)}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 2<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.02) - 2\n|{{SCP|Farming|link=yes}} 2<br/>{{SCP|Hitpoints|link=yes}} 1\n|-\n|{{plinkt|Dragon bitter}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 2\n|{{SCP|Hitpoints|link=yes}} 1<br/>{{SCP|Strength|link=yes}} 2 \n|-\n|{{plinkt|Dragon bitter(m)}}<!--test on <50 strength account-->\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 2\n|{{SCP|Hitpoints|link=yes}} 2<br/>{{SCP|Strength|link=yes}} 3\n|-\n|{{plinkt|Dragon bitter (keg)}}\n|{{SCP|Attack|link=yes}} - 1\n|{{SCP|Hitpoints|link=yes}} 1<br/>{{SCP|Strength|link=yes}} 2 \n|-\n|{{plinkt|Dragon bitter(m) (keg)}}\n|{{SCP|Attack|link=yes}} - 2\n|{{SCP|Hitpoints|link=yes}} 1<br/>{{SCP|Strength|link=yes}} 2\n|-\n|{{plinkt|Dwarven stout}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.04) - 2<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.04) - 2<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.04) - 2\n|{{SCP|Hitpoints|link=yes}} 1<br/>{{SCP|Mining|link=yes}} 1<br/>{{SCP|Smithing|link=yes}} 1\n|-\n|{{plinkt|Dwarven stout(m)}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.04) - 3<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.04) - 3<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.04) - 3\n|{{SCP|Hitpoints|link=yes}} 2<br/>{{SCP|Mining|link=yes}} 2<br/>{{SCP|Smithing|link=yes}} 2\n|-\n|{{plinkt|Greenman's ale}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.04) - 2<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.04) - 2<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.04) - 2\n|{{SCP|Herblore|link=yes}} 1<br/>{{SCP|Hitpoints|link=yes}} 1\n|-\n|{{plinkt|Greenman's ale(m)}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.04) - 3<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.04) - 3<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.04) - 3\n|{{SCP|Hitpoints|link=yes}} 2<br/>{{SCP|Herblore|link=yes}} 2\n|-\n|{{plinkt|Slayer's respite}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 2<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.02) - 2\n|{{SCP|Hitpoints|link=yes}} 1<br/>{{SCP|Slayer|link=yes}} 2\n|-\n|{{plinkt|Slayer's respite(m)}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Hitpoints|link=yes}} 2<br/>{{SCP|Slayer|link=yes}} 4\n|-\n|{{plinkt|Wizard's mind bomb}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.05) - 1\n|{{SCP|Hitpoints|link=yes}} 1<br/>{{SCP|Magic|link=yes}} 2 + floor(StaticLevel * 0.02)\n|-\n|{{plinkt|Mature wmb}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 2<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.05) - 2<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.05) - 2\n|{{SCP|Hitpoints|link=yes}} 2<br/>{{SCP|Magic|link=yes}} 3 + floor(StaticLevel * 0.02)\n|-\n|{{plinkt|Grog}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 3\n|{{SCP|Hitpoints|link=yes}} 3<br/>{{SCP|Strength|link=yes}} 1 + floor(StaticLevel * 0.04)\n|-\n|{{plinkt|Beer tankard}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.1) - 2\n|{{SCP|Hitpoints|link=yes}} 4<br/>{{SCP|Strength|link=yes}} 2 + floor(StaticLevel * 0.04)\n|-\n|{{plinkt|Keg of beer}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.5) - 4\n|{{SCP|Hitpoints|link=yes}} 15<br/>{{SCP|Strength|link=yes}} 2 + floor(StaticLevel * 0.1)\n|-\n|{{plinkt|Braindeath 'rum'}}<!--test strength on <50 account-->\n|{{SCP|Agility|link=yes}} - (CurrentLevel * 0.09) - 3<br/>{{SCP|Attack|link=yes}} - (CurrentLevel * 0.06) - 1<br/>{{SCP|Defence|link=yes}} - (CurrentLevel * 0.08) - 2<br/>{{SCP|Herblore|link=yes}} - (CurrentLevel * 0.06) - 1<br/>{{SCP|Magic|link=yes}} - (CurrentLevel * 0.05) - 1<br/>{{SCP|Ranged|link=yes}} - (CurrentLevel * 0.06) - 1<br/>{{SCP|Prayer|link=yes}} - (CurrentLevel * 0.06) - 1\n|{{SCP|Hitpoints|link=yes}} 14<br/>{{SCP|Strength|link=yes}} +3<br/>{{SCP|Mining|link=yes}} +1 \n|-\n|{{plinkt|Karamjan rum}}\n|{{SCP|Attack|link=yes}} - (CurrentLevel * 0.02) - 3\n|{{SCP|Hitpoints|link=yes}} +5<br/>{{SCP|Strength|link=yes}} 1 + floor(StaticLevel * 0.05)\n|-\n|{{plinkt|Half full wine jug}}\n|{{SCP|Attack|link=yes}} -2\n|{{SCP|Hitpoints|link=yes}} 7\n|-\n|{{plinkt|Bottle of wine}}\n|{{SCP|Attack|link=yes}} -3\n|{{SCP|Hitpoints|link=yes}} +14\n|-\n|{{plinkt|Zamorak brew}}\n|{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.1) - 2<br/>{{SCP|Hitpoints|link=yes}} - floor(CurrentLevel * 0.12)\n|{{SCP|Attack|link=yes}} 2 + floor(StaticLevel * 0.2)<br/>{{SCP|Strength|link=yes}} 2 + floor(StaticLevel * 0.12)<br/>{{SCP|Prayer|link=yes}} floor(StaticLevel * 0.1)\n|-\n|{{plinkt|Zamorak mix}}\n|{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.1) - 2<br/>{{SCP|Hitpoints|link=yes}} - floor(CurrentLevel * 0.12)\n|{{SCP|Attack|link=yes}} 2 + floor(StaticLevel * 0.2)<br/>{{SCP|Hitpoints|link=yes}} 6<br/>{{SCP|Strength|link=yes}} 2 + floor(StaticLevel * 0.12)<br/>{{SCP|Prayer|link=yes}} floor(StaticLevel * 0.1)\n|-\n|{{plinkt|Bloody bracer}}\n|{{SCP|Prayer|link=yes}} - (CurrentLevel * 0.04) - 2\n|{{SCP|Hitpoints|link=yes}} +2\n|-\n|{{plinkt|Blood pint}}\n|{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.04) - 2<br/>{{SCP|Prayer|link=yes}} - floor(CurrentLevel * 0.04) - 2\n|{{SCP|Attack|link=yes}} 2 + floor(StaticLevel * 0.04)<br/>{{SCP|Strength|link=yes}} 2 + floor(StaticLevel * 0.04)\n|-\n|{{plinkt|Elven dawn}}\n|{{SCP|Strength|link=yes}} -1\n|{{SCP|Hitpoints|link=yes}} +1<br/>{{SCP|Agility|link=yes}} +1\n|-\n|{{plinkt|Lizardkicker}}\n|{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.04) - 2<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.04) - 2\n|{{SCP|Ranged|link=yes}} +4\n|-\n|{{plinkt|Trapper's tipple}}\n|{{SCP|Attack|link=yes}} -2<br/>{{SCP|Strength|link=yes}} - 1\n|{{SCP|Hunter|link=yes}} +2<br/>{{SCP|Hitpoints|link=yes}} 1\n|-\n|{{plinkt|Sunbeam ale}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 1\n|{{SCP|Agility|link=yes}} +1<br/>{{SCP|Strength|link=yes}} +1<br/>{{SCP|Hitpoints|link=yes}} 1\n|-\n|{{plinkt|Steamforge brew}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 2<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.05) - 2\n|{{SCP|Magic|link=yes}} +1<br/>{{SCP|Mining|link=yes}} +1<br/>{{SCP|Hitpoints|link=yes}} 1\n|-\n|{{plinkt|Sun-shine}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Strength|link=yes}} floor(Level * 0.05) + 1<br/>{{SCP|Hitpoints|link=yes}} 5\n|-\n|{{plinkt|Blackbird red}}\n|{{SCP|Attack|link=yes}} -5<br/>{{SCP|Slayer|link=yes}} -1\n|{{SCP|Hunter|link=yes}} +1<br/>{{SCP|Hitpoints|link=yes}} +16\n|-\n|{{plinkt|Chilhuac red}}\n|{{SCP|Attack|link=yes}} -5<br/>{{SCP|Cooking|link=yes}} -1\n|{{SCP|Firemaking|link=yes}} +1<br/>{{SCP|Hitpoints|link=yes}} +16\n|-\n|{{plinkt|Eclipse red}}\n|{{SCP|Attack|link=yes}} -5<br/>{{SCP|Fletching|link=yes}} -1\n|{{SCP|Woodcutting|link=yes}} +1<br/>{{SCP|Hitpoints|link=yes}} +16\n|-\n|{{plinkt|Ixcoztic white}}\n|{{SCP|Attack|link=yes}} -5<br/>{{SCP|Herblore|link=yes}} -1\n|{{SCP|Farming|link=yes}} +1<br/>{{SCP|Hitpoints|link=yes}} +16\n|-\n|{{plinkt|Metztonalli white}}\n|{{SCP|Attack|link=yes}} -5<br/>{{SCP|Firemaking|link=yes}} -1\n|{{SCP|Runecraft|link=yes}} +1<br/>{{SCP|Hitpoints|link=yes}} +16\n|-\n|{{plinkt|Tonameyo white}}\n|{{SCP|Attack|link=yes}} -5<br/>{{SCP|Thieving|link=yes}} -1\n|{{SCP|Prayer|link=yes}} points +1<br/>{{SCP|Hitpoints|link=yes}} +16\n|-\n|{{plinkt|Chichilihui rosé}}\n|{{SCP|Attack|link=yes}} -5<br/>{{SCP|Farming|link=yes}} -1\n|{{SCP|Herblore|link=yes}} +1<br/>{{SCP|Hitpoints|link=yes}} +16\n|-\n|{{plinkt|Imperial rosé}}\n|{{SCP|Attack|link=yes}} -5<br/>{{SCP|Agility|link=yes}} -1\n|{{SCP|Slayer|link=yes}} +1<br/>{{SCP|Hitpoints|link=yes}} +16\n|-\n|{{plinkt|Rum}}\n|{{SCP|Attack|link=yes}}  - floor(CurrentLevel * 0.02) - 3\n|{{SCP|Strength|link=yes}} +5<br/>{{SCP|Hitpoints|link=yes}} 5\n|-\n|{{plinkt|Moon-lite}}\n|{{SCP|Attack|link=yes}} -4\n|{{SCP|Strength|link=yes}} +5<br/>{{SCP|Hitpoints|link=yes}} 5\n|-\n|{{plinkt|Jangerberries}}\n|{{SCP|Defence|link=yes}} -1\n|{{SCP|Attack|link=yes}} +2<br/>{{SCP|Strength|link=yes}} +1<br/>{{SCP|Prayer|link=yes}} +1\n|-\n|{{plinkt|Xeric's aid (-)}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.07) - 1<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.07) - 1<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.07) - 1<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.07) - 1\n|{{SCP|Hitpoints|link=yes}} floor(CurrentLevel * 0.7) + 1<br/>{{SCP|Defence|link=yes}} floor(CurrentLevel * 0.14) + 1\n|-\n|{{plinkt|Xeric's aid}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.09) - 2<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.09) - 2<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.09) - 2<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.09) - 2\n|{{SCP|Hitpoints|link=yes}} floor(CurrentLevel * 0.12) + 2<br/>{{SCP|Defence|link=yes}} floor(CurrentLevel * 0.18) + 2\n|-\n|{{plinkt|Xeric's aid (+)}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.1) - 4<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.1) - 4<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.1) - 4<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.1) - 4\n|{{SCP|Hitpoints|link=yes}} floor(CurrentLevel * 0.15) + 5<br/>{{SCP|Defence|link=yes}} floor(CurrentLevel * 0.2) + 5\n|-\n|{{plinkt|Spicy stew}} (red)\n|{{SCP|Attack|link=yes}} -1&ndash;5<br/>{{SCP|Defence|link=yes}} -1&ndash;5<br/>{{SCP|Magic|link=yes}} -1&ndash;5<br/>{{SCP|Attack|link=yes}} -1&ndash;5<br/>{{SCP|Ranged|link=yes}} -1&ndash;5<br/>{{SCP|Strength|link=yes}} -1&ndash;5\n|{{SCP|Attack|link=yes}} +1&ndash;5<br/>{{SCP|Defence|link=yes}} +1&ndash;5<br/>{{SCP|Magic|link=yes}} -1&ndash;5<br/>{{SCP|Attack|link=yes}} +1&ndash;5<br/>{{SCP|Ranged|link=yes}} +1&ndash;5<br/>{{SCP|Strength|link=yes}} +1&ndash;5\n|-\n|{{plinkt|Spicy stew}} (orange)\n|{{SCP|Construction|link=yes}} -1&ndash;5<br/>{{SCP|Cooking|link=yes}} -1&ndash;5<br/>{{SCP|Crafting|link=yes}} -1&ndash;5<br/>{{SCP|Firemaking|link=yes}} -1&ndash;5<br/>{{SCP|Fletching|link=yes}} -1&ndash;5<br/>{{SCP|Runecraft|link=yes}} -1&ndash;5<br/>{{SCP|Smithing|link=yes}} -1&ndash;5\n|{{SCP|Construction|link=yes}} +1&ndash;5<br/>{{SCP|Cooking|link=yes}} +1&ndash;5<br/>{{SCP|Crafting|link=yes}} +1&ndash;5<br/>{{SCP|Firemaking|link=yes}} +1&ndash;5<br/>{{SCP|Fletching|link=yes}} +1&ndash;5<br/>{{SCP|Runecraft|link=yes}} +1&ndash;5<br/>{{SCP|Smithing|link=yes}} +1&ndash;5\n|-\n|{{plinkt|Spicy stew}} (yellow)\n|{{SCP|Agility|link=yes}} -1&ndash;5<br/>{{SCP|Hunter|link=yes}} -1&ndash;5<br/>{{SCP|Prayer|link=yes}} -1&ndash;5<br/>{{SCP|Slayer|link=yes}} -1&ndash;5<br/>{{SCP|Thieving|link=yes}} -1&ndash;5\n|{{SCP|Agility|link=yes}} +1&ndash;5<br/>{{SCP|Hunter|link=yes}} +1&ndash;5<br/>{{SCP|Prayer|link=yes}} +1&ndash;5<br/>{{SCP|Slayer|link=yes}} +1&ndash;5<br/>{{SCP|Thieving|link=yes}} +1&ndash;5\n|-\n|{{plinkt|Spicy stew}} (brown)\n|{{SCP|Farming|link=yes}} -1&ndash;5<br/>{{SCP|Fishing|link=yes}} -1&ndash;5<br/>{{SCP|Herblore|link=yes}} -1&ndash;5<br/>{{SCP|Mining|link=yes}} -1&ndash;5<br/>{{SCP|Woodcutting|link=yes}} -1&ndash;5\n|{{SCP|Farming|link=yes}} +1&ndash;5<br/>{{SCP|Fishing|link=yes}} +1&ndash;5<br/>{{SCP|Herblore|link=yes}} +1&ndash;5<br/>{{SCP|Mining|link=yes}} +1&ndash;5<br/>{{SCP|Woodcutting|link=yes}} +1&ndash;5\n|-\n|{{plinkt|Kebab}}\n|Chance to drain {{SCP||link=yes}} -3 or -4 in any skill\nChance to drain all {{SCP|Attack}}{{SCP|Defence}}{{SCP|Strength}} [[Melee]] stats -3\n|Chance to restore {{SCP|Hitpoints|link=yes}}\nChance to boost all {{SCP|Attack}}{{SCP|Defence}}{{SCP|Strength}} [[Melee]] stats +2\n|-\n|{{plinkt|Super kebab}}\n|1/32 chance for: {{SCP||link=yes}} -3 in any skill\n|5/8 chance for restoring {{SCP|Hitpoints|link=yes}} 3 + StaticLevel * 0.07\n|-\n|{{plinkt|Poison chalice}}\n|8/32 chance for: {{SCP|Attack}}{{SCP|Defence}}{{SCP|Strength|link=yes}} -1<br/>\n2/32 chance for {{SCP|Hitpoints|link=yes}} -5%<br/>\n1/32 chance for: {{SCP|Hitpoints|link=yes}} -50% {{SCP|Attack}}{{SCP|Defence}}{{SCP|Strength|link=yes}} -(1&ndash;3)\n|-\n|{{plinkt|Nectar}}\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>\n|}\n\n===Monsters===\n{| class=\"wikitable sortable\"\n|-\n![[Monster]]!!Effect!!Notes\n|-\n|[[Aberrant spectre]]/[[Deviant spectre]]/[[Repugnant spectre]]/[[Abhorrent spectre]]\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.8)<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.4)<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.8)<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.8)<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.8)<br/>{{SCP|Prayer|link=yes}} - floor(CurrentLevel * 0.4)<br/>{{SCP|Agility|link=yes}} - floor(CurrentLevel * 0.4)\n|Without a [[nose peg]] or [[slayer helmet]] equipped.\n|-\n|[[Essyllt]]||{{SCP|Attack|link=yes}} -5<br/>{{SCP|Defence|link=yes}} -5<br/>{{SCP|Magic|link=yes}} -5<br/>{{SCP|Ranged|link=yes}} -5<br/>{{SCP|Strength|link=yes}} -5||Ranged attack: Does not stack.\n|-\n|[[Sea Troll Queen]]||{{SCP|Prayer|link=yes}} -(Current Prayer * 0.2) - 10||[[Protect from Magic]] negates it\n|-\n|[[Tz-Kih]]||{{SCP|Prayer|link=yes}} -1||Every attack\n|-\n|[[Damis#Second Form]]||{{SCP|Prayer|link=yes}} - floor(Current Prayer * 0.05) - 1||Every tick\n|-\n|[[Scorpia]]||{{SCP|Prayer|link=yes}} -2||Every attack against active protection [[Prayer]]s\n|-\n|[[Ahrim the Blighted]]||{{SCP|Attack|link=yes}} -5%<br/>{{SCP|Defence|link=yes}} -5%<br/>{{SCP|Strength|link=yes}} -5%<br/>{{SCP|Strength|link=yes}} -5||Casts do not stack: [[Confuse]], [[Weaken]], [[Curse]]<br/>Blighted Aura: Stacks, 20% chance on each successful hit\n|-\n|[[Karil the Tainted]]||{{SCP|Agility|link=yes}} -20%||Tainted Aura: Stacks, 25% chance\n|-\n|[[Kamen]]||{{SCP|Agility|link=yes}} -4||One time, must accept his brew during [[Underground Pass]]. Also gives +1 Strength.\n|-\n|[[Loar Shade]]||{{SCP|Strength|link=yes}} -1<!--attack too-->||Small chance\n|-\n|[[Sir Bedivere]]||{{SCP|Strength|link=yes}} -5||Chance on hit, only if at or above current level\n|-\n|[[Sir Pelleas]]||{{SCP|Defence|link=yes}} -5||Chance on hit, only if at or above current level\n|-\n|[[Sir Tristram]]||{{SCP|Strength|link=yes}} -5||Chance on hit, only if at or above current level\n|-\n|[[Sir Palomedes]]||{{SCP|Attack|link=yes}} -5||Chance on hit, only if at or above current level\n|-\n|[[Sir Lucan]]||{{SCP|Defence|link=yes}} -5||Chance on hit, only if at or above current level\n|-\n|[[Sir Gawain]]||{{SCP|Attack|link=yes}} -5||Chance on hit, only if at or above current level\n|-\n|[[Sir Kay]]||{{SCP|Defence|link=yes}} -5||Chance on hit, only if at or above current level\n|-\n|[[Sir Lancelot]]||{{SCP|Defence|link=yes}} -5<br/>{{SCP|Strength|link=yes}} -5||Chance on hit, only if at or above current level\n|-\n|[[Mother]]||{{SCP|Prayer|link=yes}} - floor(Current Prayer * 0.1) - 5||In melee range slam attack\n|-\n|[[Cockatrice]] / [[Cockathrice]] / [[Moonlight Cockatrice]]||{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.4)<br/>{{SCP|Agility|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.4)<br/>{{SCP|Prayer|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.4)<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.4)<br/>||No [[mirror shield]] or [[V's shield]] when attacked\n|-\n|[[Basilisk]] / [[Monstrous basilisk]]||{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.6)<br/>{{SCP|Agility|link=yes}} - floor(CurrentLevel * 0.3)<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.3)<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.6)<br/>{{SCP|Prayer|link=yes}} - floor(CurrentLevel * 0.3)<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.6)<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.6)||No [[mirror shield]] or [[V's shield]] when attacked\n|-\n|[[Basilisk Youngling]]||{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.5)<br/>{{SCP|Agility|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.5)<br/>{{SCP|Prayer|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.5)<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.5)||No [[mirror shield]] or [[V's shield]] when attacked\n|-\n|[[Basilisk Knight]] / [[Basilisk Sentinel]]\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.7)<br/>{{SCP|Agility|link=yes}} - floor(CurrentLevel * 0.35)<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.35)<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.7)<br/>{{SCP|Prayer|link=yes}} - floor(CurrentLevel * 0.35)<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.7)<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.7)\n|With no [[mirror shield]] or [[V's shield]] when attacked\n|-\n|[[Leech]]||{{SCP||link=yes}} -1||Any skill, every attack up to 4 skills per attack\n|-\n|[[Corsair Traitor]]<br/>[[Ithoi the Navigator]]||{{SCP|Defence|link=yes}} -5%<br/>{{SCP|Defence|link=yes}} -15%||Quest/normal NMZ: Cast [[curse]]<br/>Hard mode:On cast with Earth Blast\n|-\n|[[Elvarg]]||{{SCP|Attack|link=yes}} -floor(CurrentLevel * 0.7)<br/>{{SCP|Strength|link=yes}} -floor(CurrentLevel * 0.7)<br/>{{SCP|Defence|link=yes}} -floor(CurrentLevel * 0.7)<br/>{{SCP|Prayer|link=yes}} -floor(CurrentLevel * 0.95)<br/>{{SCP|Prayer|link=yes}} -floor(CurrentLevel * 0.1)||Assumes that an {{plink|anti-dragon shield}} is not equipped.\n|-\n|[[Mourner]]||Lowers all combat stats to 20||On hit of thrown potion (Instant kill avoids this)\n|-\n|[[Shadow spider]]||{{SCP|Prayer|link=yes}}  - ceiling(Current Prayer * 0.5)||On hit\n|-\n|[[Spinolyp]]||{{SCP|Prayer|link=yes}} -1||On successful hit\n|-\n|[[Goblin Champion]]||{{SCP|Prayer|link=yes}} - floor(Current Prayer * 0.1) - 5||\n|-\n|[[Slug Prince]]||{{SCP|Prayer|link=yes}} -1||1 Prayer point per damage dealt\n|-\n|[[Dust devil]] / [[Choke devil]]||{{SCP|Agility|link=yes}} -floor(CurrentLevel * 0.5)<br/>{{SCP|Attack|link=yes}} -100%<br/>{{SCP|Defence|link=yes}} -floor(CurrentLevel * 0.5)<br/>{{SCP|Magic|link=yes}} -100%<br/>{{SCP|Ranged|link=yes}} -100%<br/>{{SCP|Strength|link=yes}} -100%||Getting attacked without a [[Slayer mask]] or [[facemask]]\n|-\n|[[Smoke devil]] / [[Nuclear smoke devil]] / [[Thermonuclear smoke devil]]||{{SCP|Agility|link=yes}} -floor(CurrentLevel * 0.5)<br/>{{SCP|Attack|link=yes}} -floor(CurrentLevel * 0.85)<br/>{{SCP|Defence|link=yes}} -floor(CurrentLevel * 0.5)<br/>{{SCP|Magic|link=yes}} -floor(CurrentLevel * 0.85)<br/>{{SCP|Ranged|link=yes}} -floor(CurrentLevel * 0.85)<br/>{{SCP|Strength|link=yes}} -floor(CurrentLevel * 0.85)||Getting attacked without a [[Slayer mask]] or [[facemask]]\n|-\n|[[Karamel]]||{{SCP||link=yes}} -(1&ndash;3)||Every attack until stats are 3 or lower. [[Protect from Magic]] negates it\n|-\n|[[Witch's Experiment]]||{{SCP|Attack|link=yes}} - floor(BaseLevel * .06) - 1<br/>{{SCP|Defence|link=yes}} - floor(BaseLevel * .06) - 1<br/>{{SCP|Strength|link=yes}} - floor(BaseLevel * .06) - 1||Only casts it once\n|-\n|[[Glod]]||{{SCP|Prayer|link=yes}} -floor(Current Prayer * 0.02) - 20||\n|-\n|[[Galvek]]||{{SCP||link=yes}} -(1&ndash;3)||Phase 2 only wind attack, also drains 40%+ run energy\n|-\n|[[King Black Dragon]]||{{SCP||link=yes}} -1&ndash;2||Shocking breath [[dragonfire]]:\n|-\n|[[Melzar the mad]]||||\n|-\n|[[Slash Bash]]||{{SCP|Prayer|link=yes}} -(4&ndash;11)||On hit\n|-\n|[[Nezikchened]]||Phase 1 (on spawn): Prayer -> 6<br/>Phase 2 Prayer -> 4||[[Protection prayer]]s negate on hit. Drains pray when spawning and landing a hit *Might be wrong.\n|-\n|[[Jal-MejRah]]||{{SCP|Attack|link=yes}} -1<br/>{{SCP|Defence|link=yes}} -1<br/>{{SCP|Magic|link=yes}} -1<br/>{{SCP|Ranged|link=yes}} -1<br/>{{SCP|Strength|link=yes}} -1<br/>Run energy: -3%<br/>||Run energy every attack, stats only sometimes\n|-\n|[[Typhor]]||{{SCP|Attack|link=yes}} - floor(Level * .7)<br/>{{SCP|Strength|link=yes}} - floor(Level * .7)<br/>{{SCP|Magic|link=yes}} - floor(Level * .7)<br/>{{SCP|Ranged|link=yes}} - floor(Level * .7)<br/>{{SCP|Defence|link=yes}} - floor(Level * .35)<br/>{{SCP|Agility|link=yes}} - floor(Level * .35)<br/>{{SCP|Prayer|link=yes}} - floor(Level * .35)||Without a mirror shield\n|-\n|[[The Jormungand]]||{{SCP|Attack|link=yes}} - floor(Level * .8)<br/>{{SCP|Strength|link=yes}} - floor(Level * .8)<br/>{{SCP|Magic|link=yes}} - floor(Level * .8)<br/>{{SCP|Ranged|link=yes}} - floor(Level * .8)<br/>{{SCP|Defence|link=yes}} - floor(Level * .4)<br/>{{SCP|Agility|link=yes}} - floor(Level * .4)<br/>{{SCP|Prayer|link=yes}} - floor(Level * .4)||Without a mirror shield, standard attack.\n|-\n|[[The Jormungand]] special||{{SCP|Attack|link=yes}} - floor(Level * .24)<br/>{{SCP|Strength|link=yes}} - floor(Level * .24)<br/>{{SCP|Magic|link=yes}} - floor(Level * .24)<br/>{{SCP|Ranged|link=yes}} - floor(Level * .24)<br/>{{SCP|Defence|link=yes}} - floor(Level * .2)<br/>{{SCP|Agility|link=yes}} - floor(Level * .2)<br/>{{SCP|Prayer|link=yes}} - floor(Level * .2)||Only if you don't turn away, special attack.\n|-\n|[[Banshee]]||{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Agility|link=yes}} - floor(CurrentLevel * 0.1)<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.1)<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Prayer|link=yes}} - floor(CurrentLevel * 0.1)<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.2)||Getting attacked without a [[Slayer mask]] or [[earmuffs]]\n|-\n|[[Screaming banshee]]||||\n|-\n|[[Twisted Banshee]]||{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Agility|link=yes}} - floor(CurrentLevel * 0.1)<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.1)<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Prayer|link=yes}} - floor(CurrentLevel * 0.1)<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.2)<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.2)||Getting attacked without a [[Slayer mask]] or [[earmuffs]]\n|-\n|[[Screaming twisted banshee]]||||\n|-\n|[[Great Olm]]||{{SCP|Overall|link=yes}} -2 {{X}} 5||Affected player takes 5 damage and 2 stat drain every few ticks for 5 hits.\n|-\n|[[Great Olm]]||{{SCP|Prayer|link=yes}} -floor(CurrentPrayer * .5)||Sphere attack\n|-\n|[[Skeletal Mystic]]||{{SCP|Prayer|link=yes}} -floor(CurrentLevel * 0.05) - 1||Doesn't drain if Defence is at or below 65? <!-- might be below 2/3, test was done at 99, they stopped casting at 65-->\n|-\n|[[Invrigar the Necromancer]]||{{SCP|Attack|link=yes}} -5<br/>{{SCP|Strength|link=yes}} -5<br/>{{SCP|Defence|link=yes}} -5\n|-\n|[[Dark Wizard]]||{{SCP|Attack|link=yes}} -5<br/>{{SCP|Strength|link=yes}} -5||One of these at a time, random cast spells\n|-\n|[[The Maiden of Sugadinti]]||{{SCP|Prayer|link=yes}} -(3&ndash;5)||When standing on blood spots <!-- unsure of this value -->\n|-\n|[[Corporeal Beast]]||{{SCP|Magic|link=yes}} -(1&ndash;2)<br/>{{SCP|Prayer|link=yes}} -1&ndash;2||On successful hit: Magic attack drains either\n|-\n|[[Abomination]]||{{SCP||link=yes}} -(1&ndash;3)||All stats\n|-\n|[[Gorak]]||{{SCP||link=yes}} -(1&ndash;4)||All stats\n|-\n|[[Venenatis]]||{{SCP|Prayer|link=yes}} -(15&ndash;20)||Curse-like projectile\n|-\n|[[Summoned Soul]]||{{SCP|Prayer|link=yes}} -30||\n|-\n|[[Feral Vampyre]]||{{SCP|Strength|link=yes}} -1||Unknown percentage of attacks.\n|-\n|[[Kalphite Queen]]||{{SCP|Prayer|link=yes}} -1||Only Ranged attack\n|-\n|[[Abyssal portal]]||{{SCP|Prayer|link=yes}} -3||Every 2 [[ticks]] while within range\n|-\n|[[Catablepon]]||{{SCP|Strength|link=yes}} -(CurrentLevel * 0.05) - 1||It will re-drain once Strength if it is at least {{SCP|Strength|link=yes}} -(StaicLevel * 0.08) - 3 <!-- This may still be incorrect-->\n|-\n|[[Broodoo victim]]||{{SCP|Attack|link=yes}} -(1&ndash;3)<br/>{{SCP|Defence|link=yes}} -(1&ndash;3)<br/>{{SCP|Magic|link=yes}} -(1&ndash;3)<br/>{{SCP|Ranged|link=yes}} -(1&ndash;3)<br/>{{SCP|Strength|link=yes}} -(1&ndash;3)||[[Protect from Magic]] negates this, every 45 ticks. Also can cast [[bind]].\n|-\n|[[Strongbones]]||{{SCP|Attack|link=yes}} -floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Defence|link=yes}} -floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Strength|link=yes}} -floor(CurrentLevel * 0.05) - 1||May be an effect of killing [[Skoblin]]s\n|-\n|[[Redeyes]]||{{SCP|Attack|link=yes}} -floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Defence|link=yes}} -floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Strength|link=yes}} -floor(CurrentLevel * 0.05) - 1||[[Protect from Magic]] negates this, cannot drain again until restored\n|-\n|[[Sourhog]]||{{SCP|Attack|link=yes}} -floor(CurrentLevel * 0.9)<br/>{{SCP|Defence|link=yes}} -floor(CurrentLevel * 0.9)||Only occurs when fighting without [[reinforced goggles]] or a [[Slayer helmet]]\n|-\n|[[Zamorak wizard]]||{{SCP|Magic|link=yes}} -5%||Casts [[Flames of Zamorak]], [[Protect from Magic]] negates this\n|-\n|[[Saradomin wizard]]||{{SCP|Prayer|link=yes}} -1||Casts [[Saradomin Strike]], [[Protect from Magic]] negates this\n|-\n|[[Necromancer]]||{{SCP|Strength|link=yes}} -floor(CurrentLevel * 0.05) - 1||Casts [[Weaken]], if strength is already drained, it will not be cast again until it is fully restored.\n|-\n|[[Duke Sucellus]]||{{SCP||link=yes}} ?||From a shadow orb generated during the awakened boss fight.\n|}\n\n===Environment===\n{| class=\"wikitable align-center-1\"\n!Location\n!Drain\n!Occurence\n|-\n|[[Ice Path]]\n|{{SCP|-1|link=yes}}\n|Every 10 [[tick]]s. The [[chill]] instantly drains all run energy and special attack. Igniting a [[Fire of Unseasonal Warmth]] will prevent the drain from occurring on part of the path.\n|-\n|Above [[God Wars Dungeon]]\n|{{SCP|-1|link=yes}}\n|Every 10 [[tick]]s. The [[chill]] instantly drains all run energy and special attack. Igniting a [[Fire of Unseasonal Warmth]] will prevent the drain from occurring around the area.\n|-\n|[[Trollweiss Dungeon]] cave entrance\n|{{SCP|-1|link=yes}}\n|Every 10 [[tick]]s. Instantly drains all run energy and special attack\n|-\n|[[Abyss]]\n|{{SCP|Prayer|link=yes}} -100%\n|Upon entering\n|-\n|[[Entrana Dungeon]]\n|{{SCP|Prayer|link=yes}} -floor(CurrentLevel * 0.95)\n|Upon entering\n|-\n|[[Zamorak's Fortress]]\n|{{SCP|Prayer|link=yes}} -100%\n|Crossing the river in [[God Wars Dungeon]]\n|-\n|Tarn's room in [[Tarn's Lair]]\n|{{SCP|Prayer|link=yes}} -1\n|Every 2 [[ticks]]\n|-\n|[[Barrows]] Crypts/Tunnels\n|{{SCP|Prayer|link=yes}} -(8&ndash;14)\n|Every 30 ticks (18 seconds). Begins at eight increasing by one for each brother killed. Can be negated by wearing a [[ghommal's hilt 2]] or better.\n|}\n\n===Interactions===\n{| class=\"wikitable align-center-1\"\n!Object/Item\n!Drain\n!Occurence\n|-\n|[[Silver sickle (b)]]<br/>\n[[Silver sickle (b)]]<br/>\n[[Emerald sickle (b)]]<br/>\n[[Ruby sickle (b)]]<br/>\n[[Enchanted emerald sickle (b)]]<br/>\n[[Enchanted ruby sickle (b)]]<br/>\n[[Ivandis flail]]<br/>\n[[Blisterwood sickle]]<br/>\n[[Blisterwood flail]]\n|{{SCP|Prayer|link=yes}} -(between 1-6 prayer points at random)\n|Casting [[Bloom]]\n|-\n|[[Disease]]\n|{{SCP|Skills|link=yes}} -# except [[Prayer]]\n|Drains [[Hitpoints]] if the intended skill to drain is already at 1\n|-\n|[[Wall safe (lobby)|Wall safe]]\n|{{SCP|Thieving|link=yes}} -1\n|\n|-\n|[[Rogue Trader]] runedoku chest - force\n|{{SCP|Agility|-0–3|link=yes}}\n{{SCP|Strength|-0–3|link=yes}}\n| Both skills will be drained by the same amount\n|-\n|[[Rogue Trader]] runedoku chest - pick\n|{{SCP|Thieving|-0–3|link=yes}}\n|\n|-\n|Orange [[Demonic Symbol]] in [[Zalcano|Zalcano]]\n| -25% of current [[run energy]] <!-- this amount may go up the longer it is stood upon-->\n|Also deals damage.\n|-\n|[[Magical Obelisk]]\n| -100% [[run energy]] \n|When wearing [[Zamorak]]ian items. Also, 3 damage is dealt.\n|-\n|[[Dark essence block]]s on the [[Dark Altar]]\n| {{SCP|Prayer|link=yes}} -1\n|Per [[dense essence block]] converted\n|-\n|[[Magical Obelisk]]\n| -50% [[run energy]] \n|When wearing [[Bandos]]ian, [[Armadyl]]ean, [[Guthix]]ian items. Also, 1 damage will be dealt.\n|-\n|[[Klenter's Pyramid]]\n| -15% [[Run energy]]\n|Requires 20% run energy to attempt.\n|-\n|[[Prayer book]]\n|Poison: {{SCP|Prayer|link=yes}} Amount of poison / 2<br/>Venom: {{SCP|Prayer|link=yes}} -50\n|Each Prayer point removes 2 levels of poison. If there are not enough prayer points to remove it completely, it will reduce only it.<br/>Turning venom into poison requires at least 1 Prayer point, consuming 50 at most if possible.\n|-\n|Making [[Enchanted vial]] with [[Binding book]]\n|{{SCP|Magic|link=yes}} -5<br/>{{SCP|Prayer|link=yes}} -5\n|Per enchanted vial\n|-\n|[[Extractor hat]] in [[Elemental Workshop II]]\n|{{SCP|Magic|link=yes}} -20\n|Once the [[Primed mind bar]] is made\n|-\n|[[Rantz]]'s Locked Ogre Chest\n|{{SCP|Strength|link=yes}} -1\n|Unsuccessful attempts\n|-\n|[[Ogre Coffin]]\n|{{SCP|Strength|link=yes}} -(1&ndash;3)\n|Only during [[Zogre Flesh Eaters]]\n|-\n|[[Ogre Coffin]]\n|{{SCP|Thieving|link=yes}} -(1&ndash;4)\n|Requires 20 [[Thieving]], upon failing to pick the lockowers your Thieving stat temporarily.\n|-\n|[[Wine of Zamorak]] at [[Chaos Temple (hut)]] or [[Chaos Temple (Asgarnia)]]\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Hitpoints|link=yes}} - floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Prayer|link=yes}} - floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Ranged|link=yes}} - floor(CurrentLevel * 0.05) - 1<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.05) - 1\n|Attempting to pick up or [[Telekinetic Grab|Telekinetic grabbing]] it\n|-\n|Switching to [[Ancient Magicks]] inside the [[Ancient Pyramid]]\n|{{SCP|Prayer|link=yes}} -100%\n|On activation\n|-\n|[[Darts (Brimhaven Agility Arena)]]\n|{{SCP|Agility|link=yes}} -2\n|\n|-\n|[[Rocky Ledge]]s in [[Viyeldi caves]]\n|{{SCP|Agility|link=yes}} -(4&ndash;8)\n|On fail\n|-\n|[[Dwarf cannon]] [[Railing]] repair\n|{{SCP|Strength|link=yes}} -1 OR<br/>{{SCP|Crafting|link=yes}} -1\n|Failing to repair the railing. Also deals 1&ndash;2 damage\n|-\n|[[Altar (Forthos Dungeon)|Altar of the sun]]\n|{{SCP|Prayer|link=yes}} -100%\n|When it is turned into the [[Cursed Altar]] by the [[Shaeded Beast]], deals up to 30 damage\n|-\n|[[H.A.M. Hideout]] trapdoor\n|{{SCP|Thieving|link=yes}} -1\n|\n|-\n|Repairing damaged [[Torva armour]]\n|{{SCP|Prayer|link=yes}} ( - floor(CurrentLevel * 0.333) - 10 ) {{X}} 3\n|\n|-\n|[[Rogues' Den]] Contortion bars\n|{{SCP|Agility|link=yes}} -5 \n|\n|-\n|[[Rogues' Den]] Pendulum\n|{{SCP|Agility|link=yes}} -3 \n|\n|-\n|[[Rogues' Den]] Wall\n|{{SCP|Thieving|link=yes}} -1\n|\n|-\n|[[Rogues' Den]] Wall (crusher)\n|{{SCP|Agility|link=yes}} -5\n|On failure.\n|-\n|[[Rogues' Den]] Wall (crusher)\n|{{SCP|Thieving|link=yes}} -1\n|On success\n|-\n|[[Rogues' Den]] Ledge\n|{{SCP|Agility|link=yes}} -1\n|First wall hang is 4, first walk-over ledge is 4, long ledge is 7\n|-\n|[[Rogues' Den]] Blade\n|{{SCP|Agility|link=yes}} -3\n|\n|-\n|[[Rogues' Den]] door\n|{{SCP|Thieving|link=yes}} -10\n|\n|-\n|[[Rogues' Den]] wall (blade)\n|{{SCP|Agility|link=yes}} -10\n|\n|-\n|[[Rogues' Den]] wall (spike)\n|{{SCP|Thieving|link=yes}} -1\n|\n|-\n|[[Rogues' Den]] wallsafe\n|{{SCP|Thieving|link=yes}} -1\n|\n|-\n|[[Oldak]]\n|{{SCP|Magic|link=yes}} -2 OR<br/>{{SCP|Runecraft|link=yes}} -2\n|{{SCP|Magic|link=yes}} +2 OR<br/>{{SCP|Runecraft|link=yes}} +2\n|-\n|[[Ancient wyvern shield]]\n|{{SCP|Magic|link=yes}} -100%\n|When creating the shield.\n|-\n|[[Grand seed pod]]\n|{{SCP|Farming|link=yes}} -5\n|Squash option.\n|-\n|[[Marble trap]]\n|{{SCP|Agility|link=yes}} - floor(CurrentLevel * 0.05) - 1\n|On failure.\n|-\n|[[Ball]]\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.06) - 1<br/>\n{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.06) - 1<br/>\n{{SCP|Hitpoints|link=yes}} - floor(CurrentLevel * 0.06) - 1<br/>\n{{SCP|Strength|link=yes}} - floor(CurrentLevel * 0.06) - 1\n|Picking it up before defeating the experiment.\n|-\n|[[Table (King's Ransom)]]\n|{{SCP|Skills|link=yes}} -#\n|Choosing any of 3 items will teleport the player and drain a random skill by a random amount. Also, 5 damage will be dealt.\n|}\n\n===Weapons===\n{| class=\"wikitable align-center-1\"\n!colspan=\"2\"|Weapon\n!Drain\n!Occurence\n|-\n|{{plinkt|Tonalztics of ralos}} [[special attack]]\n|{{SCP|Defence|link=yes}} - 10% of {{SCP|Magic|link=yes}} level\n|On successful hit, the target's Defence level is reduced by 10% of its Magic level (20% if charged and both hits deal damage).\n|-\n|{{plinkt|Dinh's bulwark}} [[special attack]]\n|Combat [[skills]] -5%\n|On successful hit, drains the target's highest offensive stat. If all stats are equal, it will drain both Attack and Strength, then Ranged, or Magic. \n|-\n|{{plinkt|Abyssal whip}} [[special attack]]\n| -10% [[run energy]]\n| +10% run energy to the attacker\n|-\n|{{plinkt|Mud pie}}\n| -25% [[run energy]]\n|-\n|{{plinkt|Statius's warhammer}} [[special attack]]\n|{{SCP|Defence|link=yes}} -30%\n|On hit greater than 0\n|-\n|{{plinkt|Darklight}} [[special attack]]\n|{{SCP|Attack|link=yes}} -5%<br/>{{SCP|Defence|link=yes}} -5%<br/>{{SCP|Strength|link=yes}} -5%\n|On hit\n|-\n|{{plinkt|Arclight}} [[special attack]]\n|{{SCP|Attack|link=yes}} -5%<br/>{{SCP|Defence|link=yes}} -5%<br/>{{SCP|Strength|link=yes}} -5%\n|On hit\n|-\n|{{plinkt|Holy water}}\n|{{SCP|Defence|link=yes}} -5%\n|On hit (against a [[Demon (attribute)|demon]])\n|-\n|{{plinkt|Dragon battleaxe}} [[special attack]]\n|{{SCP|Attack|link=yes}} -10%<br/>{{SCP|Defence|link=yes}} -10%<br/>{{SCP|Magic|link=yes}} -10%<br/>{{SCP|Ranged|link=yes}} -10%\n|Activating special\n|-\n|{{plinkt|Elder maul}} [[special attack]]\n|{{SCP|Defence|link=yes}} -35%\n|On hit greater than 0\n|-\n|{{plinkt|Dragon warhammer}} [[special attack]]\n|{{SCP|Defence|link=yes}} -30%\n|On hit greater than 0\n|-\n|{{plinkt|Bandos godsword}} [[special attack]]\n|{{SCP|Defence|link=yes}}<br/>{{SCP|Strength|link=yes}}<br/>{{SCP|Prayer|link=yes}}<br/>{{SCP|Attack|link=yes}}<br/>{{SCP|Magic|link=yes}}<br/>{{SCP|Ranged|link=yes}}\n|On hit: Based on max hit in order until it reaches 0 then moves onto the next skill\n|-\n|{{plinkt|Barrelchest anchor}} [[special attack]]\n|{{SCP|Defence|link=yes}}<br/>{{SCP|Attack|link=yes}}<br/>{{SCP|Ranged|link=yes}}<br/>{{SCP|Magic|link=yes}}\n|On hit: 10% of damage dealt from skills above 1 in order\n|-\n|{{plinkt|Bone dagger}} [[special attack]] <br>\n(and poisoned variants)\n|{{SCP|Defence|link=yes}}\n|On hit: equal to damage dealt. 100% chance to hit when not the last attacker\n|-\n|{{plinkt|Ancient mace}} [[special attack]]\n|{{SCP|Prayer|link=yes}}\n|On hit: equal to damage dealt\n|-\n|{{plinkt|Dorgeshuun crossbow}} [[special attack]]\n|{{SCP|Defence|link=yes}}\n|On hit: equal to damage dealt. 100% chance to hit when not the last attacker\n|-\n|{{plinkt|Seercull}} [[special attack]]\n|{{SCP|Magic|link=yes}}\n|On hit: equal to damage dealt. 100% chance to hit\n|-\n|{{plinkt|Topaz bolts (e)}}\n|{{SCP|Magic|link=yes}} -1\n|4%/4.4% Chance\n|-\n|{{plinkt|Accursed sceptre}} [[special attack]]\n|{{SCP|Defence|link=yes}} -15%<br>{{SCP|Magic|link=yes}} -15%\n|On successful hit; cannot drain below -15%\n|-\n|{{plinkt|Weaken}}\n|{{SCP|Strength|link=yes}} -5%\n|\n|-\n|{{plinkt|Confuse}}\n|{{SCP|Attack|link=yes}} -5%\n|\n|-\n|{{plinkt|Curse}}\n|{{SCP|Defence|link=yes}} -5%\n|\n|-\n|{{plinkt|Vulnerability}}\n|{{SCP|Defence|link=yes}} -10%\n|\n|-\n|{{plinkt|Enfeeble}}\n|{{SCP|Strength|link=yes}} -10%\n|\n|-\n|{{plinkt|Stun}}\n|{{SCP|Attack|link=yes}} -10%\n|\n|-\n|{{plinkt|Flames of Zamorak}}\n|{{SCP|Magic|link=yes}} -5%\n|\n|-\n|{{plinkt|Claws of Guthix}}\n|{{SCP|Defence|link=yes}} -5%\n|\n|-\n|{{plinkt|Saradomin Strike}}\n|{{SCP|Prayer|link=yes}} -1\n|On successful hit\n|-\n|{{plinkt|Shadow Rush}}\n|{{SCP|Attack|link=yes}} -10%\n|Increased by 100% with [[Zuriel's staff]]\n|-\n|{{plinkt|Shadow Burst}}\n|{{SCP|Attack|link=yes}} -10%\n|Increased by 100% with [[Zuriel's staff]]\n|-\n|{{plinkt|Shadow Blitz}}\n|{{SCP|Attack|link=yes}} -15%\n|Increased by 100% with [[Zuriel's staff]]\n|-\n|{{plinkt|Shadow Barrage}}\n|{{SCP|Attack|link=yes}} -15%\n|Increased by 100% with [[Zuriel's staff]]\n|-\n|{{plinkt|Smite}}\n|{{SCP|Prayer|link=yes}}\n|Active on hit, 25% of the damage dealt\n|-\n|[[File:Ahrim's armour set.png]]\n|[[Ahrim the Blighted's equipment]]\n|{{SCP|Strength|link=yes}} -5\n|Blighted Aura: On [[Magic]] hit, 25% chance\n|-\n|[[File:Karil's armour set.png]]\n|[[Karil the Tainted's equipment]]\n|{{SCP|Agility|link=yes}} -20%\n|Tainted Shot: On [[Ranged]] hit, 25% chance\n|-\n|[[File:Torag's armour set.png]]\n|[[Torag the Corrupted's equipment]]\n| -20% [[run energy]]\n|Corruption: On successful [[Melee]] hit, 25% chance\n|-\n|{{plinkt|Broodoo shield (combat)}}\n|{{SCP|Attack|link=yes}} -(CurrentLevel * 0.05) - 1\n|[[Shield slot]] only; 10% chance\n|-\n|{{plinkt|Broodoo shield (disease)}}\n|{{SCP|Strength|link=yes}} -(CurrentLevel * 0.05) - 1\n|[[Shield slot]] only; 10% chance\n|-\n|{{plinkt|Broodoo shield (poison)}}\n|{{SCP|Defence|link=yes}} -(CurrentLevel * 0.05) - 1\n|[[Shield slot]] only; 10% chance\n|}\n\n==Alfred Grimhand's Barcrawl==\n{|class=\"wikitable align-center-1\"\n!Drink\n!Drain\n|-\n|Liverbane ale\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>{{SCP|Firemaking|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>{{SCP|Fletching|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>{{SCP|Woodcutting|link=yes}} - floor(CurrentLevel * 0.05) - 5\n|-\n|Fire Brandy\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.1) - 5<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.1) - 5\n|-\n|Black Skull Ale\n|{{NA}}\n|-\n|Ape Bite Liqueur\n|{{NA}}\n|-\n|Heart Stopper\n|{{SCP|Hitpoints|link=yes}} - floor(CurrentLevel * .25)\n|-\n|Fire Toad Blast\n|{{SCP|Hitpoints|link=yes}} - floor(CurrentLevel * .05)\n|-\n|Supergrog\n|{{SCP|Attack|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>{{SCP|Cooking|link=yes}} - floor(CurrentLevel * 0.05) - 5<br/>{{SCP|Herblore|link=yes}} - floor(CurrentLevel * 0.05) - 5\n|-\n|Uncle Humphrey's Gutrot\n|{{SCP|Smithing|link=yes}} - floor(CurrentLevel * (0.05) - 5<br/>{{SCP|Strength|link=yes}} - floor(CurrentLevel * (0.05) - 5<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * (0.05) - 5<br/>{{SCP|Attack|link=yes}} - floor(CurrentLevel * (0.05) - 5\n|-\n|Hand of Death Cocktail\n|{{SCP|Ranged|link=yes}} - floor(CurrentLevel * (0.05) - 5<br/>{{SCP|Defence|link=yes}} - floor(CurrentLevel * (0.05) - 5<br/>{{SCP|Fishing|link=yes}} - floor(CurrentLevel * (0.05) - 5<br/>{{SCP|Attack|link=yes}} - floor(CurrentLevel * (0.05) - 5\n|-\n|Olde Suspiciouse\n|{{SCP|Defence|link=yes}} - floor(CurrentLevel * (0.05) - 5<br/>{{SCP|Crafting|link=yes}} - floor(CurrentLevel * (0.05) - 5<br/>{{SCP|Attack|link=yes}} - floor(CurrentLevel * (0.05) - 5<br/>{{SCP|Mining|link=yes}} - floor(CurrentLevel * (0.05) - 5<br/>{{SCP|Magic|link=yes}} - floor(CurrentLevel * (0.05) - 5\n|}\n\n[[Category:Mechanics]]", "revisionId": 14980594, "timestamp": "2025-09-05T18:16:45Z", "downloadedAt": "2025-09-05T20:37:22.668Z", "source": "osrs_wiki_watcher", "rawWikitext": true}