{"title": "Duke Su<PERSON>us/Strategies", "namespace": "Main", "content": "[[File:Fighting Duke <PERSON>.png|thumb|300px|A player fighting <PERSON>, a chthonian demon sealed in the Ghorrock Prison.]]\nAfter the completion of [[Desert Treasure II - The Fallen Empire]], players can fight a stronger variant of [[<PERSON>]]. An even stronger awakened variant can be fought by entering the asylum gates with an [[awakener's orb]] in the player's inventory and selecting \"Yes\" when prompted.\n\nIf the player dies while fighting <PERSON>, their [[grave]] will appear by the asylum gates. Players will have their reclamation fee reduced by 75% until they have five kills, after which the standard death cost will apply.\n\n==Requirements==\n* Completion of {{SCP|Quest}} [[Desert Treasure II - The Fallen Empire]]\n\n===Suggested skills===\n* {{SCP|Attack|75+}}\n* {{SCP|Strength|75+}}\n* {{SCP|Defence|70+}}\n* {{SCP|Hitpoints|80+}}\n* {{SCP|Agility|70+}}\n* {{SCP|Mining|70+}}\n* {{SCP|Magic|76+}} for [[Resurrection spells]]\n\n==Transportation==\nThe quickest methods of reaching [[Ghorrock Prison]], where <PERSON> resides, are:\n* Using the [[ring of shadows]] to teleport to the [[Ghorrock Dungeon]]. This requires having used the [[frozen tablet]] on it, which is an untradeable 1/25 drop from <PERSON> Sucellus.\n* Using an [[icy basalt]] (or [[Weiss Portal]]), squeezing through the broken fence to the east, then climbing downstairs to the dungeon past the [[Salt Mine]].\n\nOnce in the [[Ghorrock Dungeon]], head north-east to the heavy door to enter [[Ghorrock Prison]], where players will reach the asylum entrance directly instead of traversing through the entire prison as they did during the quest.\n\n==Fight overview==\n===Brewing poison===\n[[File:Duke Sucellus - prep phase vent patterns.png|thumb|300px|The order in which the vents ignite, going from highlighted tiles marked 1-3, assuming the player is not in the vent area. If they are, the number that goes first will be random.]]\nUpon entering the prison, Duke Sucellus is initially asleep and must be weakened with [[arder-musca poison]]s, made from resources found within the asylum - [[arder powder]], [[musca powder]], and [[salax salt]]. Making this poison requires six portions of each, and two poisons are required to wake the Duke up, so twelve of each will be needed in total. To the left and right of the asylum upon entering it, there is a [[pestle and mortar]] and [[iron pickaxe]] on the walls that players can take.\n\nThe salt piles are found in the centre of the asylum, surrounded by gas vents; three of these will release gas in a 3x3 AoE from the vent, draining 4 prayer points and dealing up to 12 damage that can be reduced with a [[slayer helmet]] or [[gas mask]]. Once a vent stops emitting gas, the next one will be one vent to the east, looping back to the opposite side if already on the eastern-most vent. Players are also recommended to bring the best pickaxe available, as this increases the rate of mining them. Having 80+ Mining also ensures that only two harvests need to be made.\n\n[[File:Duke Sucellus - running past extremities.mp4|thumb|400px|The player demonstrates safely running past an extremity that is in the middle of its gazing attack. By starting from the tile as shown, running causes the player's true tile (or current tile) to always skip over a tile; in this case, skipping over the tiles directly in front of an extremity.]]\nMeanwhile, the mushrooms are found at the end of the opposing sides of the asylum, past the [[Extremity|extremities]]. Up there, ice falls will randomly strike on a tile, freezing the player for 2 ticks (1.2 seconds) and dealing up to 18 damage. These occur specifically in the area right before the second staircase on both sides, and are indicated by a shadow looming over the tile they will strike on.\n\nIn addition, the extremities will periodically wake in succession and unleash their gaze in a cone radius; they will start from the middle, south, and then north in that order on both sides. Getting caught by their gaze will freeze the player for 5 ticks (3 seconds), drain 1/4th of their [[prayer points]], and deal up to 70 damage. Players can ignore the extremities entirely by starting from one tile south of the southern extremity and running straight to the mushrooms, only having to watch out for ice falls. To do the same in reverse, start running from one tile north of the northern extremity.\n\nIn both of these corridors, there are only two safe spots in the actual area; the tile used to grab the mushrooms from, and the tile east from it for the arder mushrooms and the tile west for the musca mushrooms. These two tiles will never be targeted by ice falls and are safe from the nearby extremity.\n\nGrinding [[arder mushroom|arder]] and [[musca mushroom]] yields varying amounts of powder; at level 70 Herblore, each mushroom provides five powder, so three mushrooms of each type are needed. At 80+ Herblore, players will only need to grind two of each mushroom.\n\nAfter acquiring twelve [[salax salt]], [[arder powder]], and [[musca powder]], place them in both [[Fermentation Vat]]s and let it ferment for several seconds before collecting two finished poisons. Players may prepare poison for 2, 3, or 4 kills ahead with Herblore level 62, 71, or 82 respectively.\n\n====Advanced tips====\nPlayers looking to speed up the preparation process may opt to use [[tick manipulation]] by performing an action to delay the skilling timer (such as cutting a [[celastrus bark]]). By running towards the resource and performing the action before a specific tile, and then instantly clicking on the resource, players can gather double the resources.\n\n<gallery mode=\"packed\" heights=\"300\" style=\"text-align:left\">\nDuke Sucellus - tick manip (mushroom).mp4|Once the player's true tile (green) is on the marked yellow tile (or right before it), click the [[celastrus bark]] and the mushroom in quick succession to pick two mushrooms simultaneously.\nDuke Sucellus - tick manip (salt).mp4|Once the player's true tile (green) is on the marked yellow tile (or adjacent), click the [[celastrus bark]] and the salt pile in quick succession to obtain two sets of salax salt. Using this method will negate the need for a powerful pickaxe, and players can simply take the iron one found in the asylum.\n</gallery>\n\nRather than making the [[Arder-musca poison]]s, the player can alternatively grind mushrooms to obtain 40 powder of its respective type, then feed it directly to Sucellus. While each powder only deals 5 damage, the player can rapidly feed him every half-tick or so, filling up the bar much faster. The advantage of this method is that it significantly reduces the amount of run energy expended, making [[stamina potion]]s completely unnecessary with 70+ [[Agility]]. However, it is very click-intensive, and a misclick can drag the player into an active vent.\n\n===Battle phase===\nDuke Sucellus has 485 health and attacks on a five-tick cycle (3.0s). It is highly recommended that players melee Sucellus, as he is weak to slash attacks.\n\nIn addition, unlike other [[Demon (attribute)|demonic monsters]], Duke Sucellus only has a 70% vulnerability to [[demonbane weapons]]; [[Arclight]] gives a 49% boost to accuracy and damage as opposed to the usual 70%. The [[Ward of Arceuus]] spell can be used to reduce the damage of Sucellus' regular attacks (though the 10% damage reduction from the spell does not reduce damage of normal icicle attacks when using protect from melee as it is capped at 5), and the benefit does not apply to peripheral forms of damage like those inflicted by gas flares or extremities.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1700752935095775549|date=10 September 2023|archiveurl=https://archive.ph/YZHro{{^|https://archive.ph/oC1QK}}|archivedate=10 September 2023|quote=[Hi sorry to bother you, just wanted to know if the Ward of Arceuus spell works to reduce damage taken from Duke Sucellus auto attacks, and likewise vents, ice barrage shadows, and eye statues?] I think it'd work against the Duke's direct attacks, but probably not the peripheral stuff like vents & statues, if I'm reading my colleagues' code correctly.}}\n\nWhen in melee range (including diagonally across), Sucellus will raise his arms, causing icicles to spike up from the ground. These icicles have a 3x3 AoE and will deal up to 11 damage if [[Protect from Melee]] is not active, or 5 with. This is a standard attack and can occasionally miss, though Sucellus' high stats means this is very unlikely. Shortly afterwards, he will slam his arms back down, shattering the icicles in a 1x1 AoE on them. Getting caught in this slam will deal up to 56 damage without prayer, or 41 with, and will always hit regardless of the player's bonuses.\n\nIf outside of melee range, Sucellus will instead launch a blue projectile, which deals up to 48 damage if not protected against, and deals slightly less damage with [[Protect from Magic]], though will generally deal significant damage. The damage only applies upon impact. However, getting hit by this attack, even if protected against, will disqualify the player from a \"perfect\" kill.\n\n'''Tip:''' If using a 4-tick weapon like the [[Emberlight]] or [[Arclight]], it's possible to eat half of a [[summer pie]] after every hit during this phrase as it only adds one tick of eat delay. This is because it's a 5-tick cycle. A player may wish to do this as Summer pies give other benefits, such as their ability restore 10% run energy per half, give an agility buff, and come a lower cost. The short eat delay also makes it possible to restore large amounts of health quicker than [[manta ray]]s would due to their short eat delay – however this requires more clicking. \n\nDuke Sucellus has two special attacks:\n[[File:Duke Sucellus - melee flinching.mp4|thumb|400px|The player demonstrates a full cycle of Duke Sucellus' attacks. Here, the Duke attacks three times; while it would normally launch one Gas Flare afterward, the player had damaged him under the 50% health threshold where he would launch two Gas Flares instead.(Note: Duke Sucellus' health is currently 485)]]\n* '''Freezing Gaze''' - Every fifth standard attack, he will open the eye within his body, indicated by the game message {{mes|Duke Sucellus turns his gaze upon you...|colour=#a53fff}} If the player is not standing directly behind a pillar 5 ticks (3 seconds) after the attack is initiated, the player will take up to 101 damage. This is almost assured to kill the player, so avoiding it is essential. This attack takes 6 ticks in total.\n** Players can quickly get one more hit off shortly after the first game message appears, or resume attacking the instant the game message {{mes|You manage to avoid Duke Sucellus' gaze.|colour=#229628}} appears.\n* '''Gas Flare''' - He will launch gas towards the vent closest to the player, leaving dangerous gas there for a few seconds. The gas deals up to 12 damage, which can be reduced with a [[slayer helmet]] or [[gas mask]]. Avoid them by moving to the opposite pillar.\n** The Gas Flare attack will always be launched three standard attacks after the first Freezing Gaze. If above 75% health, Sucellus will use this attack after every five standard attacks, four above 25%, and three below 25%. However, Sucellus will never use this attack immediately after a Freezing Gaze, in which case it will do an extra standard attack. Once the 75% threshold is crossed (and 25% for enraged phase), the change in frequency of the Gas Flare attack will apply after the next gas flares are launched. If above 50% of his health (242 hitpoints), he will spit out one Gas Flare, and two otherwise.\n\n====Enrage phase====\nUpon reaching 25% of his health (121 hitpoints), Sucellus will enter an enrage phase, attacking one tick faster to start on a 2.4 second (4 tick) cycle.\n\nIf using a five-tick weapon, you will only be able to safely attack Sucellus every two attacks, as the third attack will result in the player getting hit by his slamming attack when they do so. Should the player wish to, they may switch to a four-tick weapon such as Arclight or Emberlight to match his attack speed, however it is not necessary in normal mode.\n\nIf you wish to continue killing Sucellus after using up all of your [[Arder-musca poison]]s, run towards one of the mushroom spawns to immediately prepare picking them, as he will \"respawn\" shortly before one of them is reached.\n\nWhen picking up drops, be mindful of [[mist rune]] and [[chaos rune]] drops if using the [[Arceuus spellbook]], as these will cause other spells to become available, such as [[Vile Vigour]] and medium-tier thralls. These should be left on the ground until the trip concludes.\n\n==Awakened mode==\nIn Awakened mode, Duke Sucellus has the following changes:\n* The boss' health increases from 485 to '''1,697'''.\n* The boss is immune to [[stat drain]]s.\n* The boss' poison tolerance meter increases from 200 to '''370''', requiring three poisons to wake him up.\n* Upon reaching less than ~75% of his health, he will launch four Gas Flares in a row; above that, only once. The gas' damage is also increased to ~26 or 27 if hit without proper face covering.\n* Around 32% health, a '''black orb''' will move from side to side in front of him that deals up to 27 damage and drains all stats by 8 if hit by it.\n* Periodically, Duke Sucellus will cover the player in bile, resulting in the player leaking poison for 15 ticks. If standing in a poison pool, the player will take 9-12 poison damage every tick and be poisoned starting at 2 damage if no poison immunity is present.\n* The [[black mask]] and [[Slayer helmet (i)|Slayer helmet]] do not apply their usual on-task damage and accuracy bonuses while on a Duke Sucellus [[boss slayer task]].{{CiteNews|url=https://secure.runescape.com/m=news/desert-treasure-ii---tweaks-and-drop-rates?oldschool=1|quote=Slayer tasks no longer apply to the Awakened versions of the Desert Treasure II bosses.|title=Desert Treasure II - Tweaks and Drop Rates|newsdate=03 August 2023}}\n[[File:Duke Sucellus - awakened poison walking.png|thumb|right|300px|The tiles marked for walking Sucellus while splattered with poison. Start from the tile behind the pillar, then walk to tiles 1, 2, 4 and then into melee distance. After this first batch, then walk to tiles 1-4 and repeat two more times. At the start of enrage poison, utilise the pillar tile, tile 1 and 4 for the first instance, then tiles 1, 2 and 4 for the remaining two instances.]]\nBefore starting the fight, drop supplies on the pillars closest to Duke Sucellus, enough so that the mushrooms, salt, and poison can be picked without juggling. The damage dealt from ice falls and extremities are the same as in normal mode, though much more care is recommended to avoid unnecessarily draining supplies before even fighting the boss. Drink potions and heal up as needed before using the final poison on him to begin the fight. As with normal mode, players must always force the Duke to use melee, as his magic attacks can hit very hard; up to 36 through prayer, and 71 without. The melee chips are still the same as in normal mode, though the melee attack does hit significantly harder, up to 81 without prayer and around 60 with.\n\nWhile not necessary, a [[Menaphite remedy]] can be helpful in restoring drained stats every 15 seconds. The player's primary form of healing pre-enrage should be solely Saradomin brews, as damage during this time should only be from the chip damage caused from icicles. Solids are much more helpful in the enrage phase when some recoverable mistakes are made.\n\nFlick Duke Sucellus as required; it is highly important that the player does not get caught by his Freezing Gaze, as it has a significantly higher max hit and can potentially kill in one hit. Once Duke Sucellus starts spitting out multiple Gas Flares (around 75% of his health), run south to the middle row of vents and wait for the last spit to appear before moving back in, as it is important not to let the row in front of him be immersed in dangerous gas, especially with poison and enrage. Around 65% of his health, Sucellus will perform Gas Spits one auto-attack after Freezing Gaze.\n\nWhen Duke Sucellus splatters the player in bile, the player will be notified with the message \"Duke Sucellus covers you in bile. It slowly drips off your body and onto the floor.\", resulting the player leaking poison for 15 tiles if walking (or one tick running). This attack will begin to be used upon reaching 65% of his health, and will always occur after Gas Flares, though it is not guaranteed each time. If the bile does appear, use the walking method with the tiles indicated on the picture to the right; pre-enrage, the player must walk four tiles and then be next to Sucellus on the fifth tile to avoid getting hit by magic, while post-enrage, the player must walk three tiles and be next to Sucellus on the fourth tile.\n\nUpon reaching ~32% of his health, an [[Orb of Darkness]] will begin to appear in the row in front of Sucellus from either side of the first set of pillars, which deals up to 27 damage and drains all stats by 8 if hit. These orbs will continually spawn until the encounter ends, and will appear every ~10 or so seconds regardless if the player is hit by it or not. These should be avoided whenever possible; though if there is no other choice, it is recommended to tank the orb, as it is much less dangerous than trying to tank one of Sucellus' auto-attacks.\n\nOnce the enrage phase begins, it is important to sync with Sucellus' attacks so that the player will not make mistakes as the chances of dying significantly increase, as it is possible to get biled twice in a single attack rotation. If the second bile occurs, players '''will always''' take damage at some point from the pools, due to the Freezing Gaze attack being lined up one auto-attack after the second Gas Flare. Therefore, minimise time spent in the phase by dumping special attacks to reduce the boss' health.\n\n==Equipment==\nDuke Sucellus is weak to slash, so slashing weapons are preferred for the fight.\n\nThe [[scythe of vitur]] will hit three times due to Sucellus' large size, but is costly to use. An [[emberlight]], if available, should be used for cost-conscious players as it performs slightly worse but with no upkeep, while being a 4 tick weapon which can allow for additional hits at some points in the fight. The [[soulreaper axe]] is another alternative as a five-tick weapon, however it will lose soul stacks every few kills, when the player must restock on poisons.\n\nPlayers should use a [[bandos godsword]] to help lower Sucellus' Defence, as he has a very high Defence level. Due to this, a [[bellator ring]] performs better than an [[ultor ring]] in most cases. It is not recommended to use an [[Elder maul|Elder Maul]] or [[Dragon warhammer|Dragon Warhammer]] because it is much more likely to miss due to Duke Sucellus' high crush Defence. \n\n{{Recommended equipment\n| style = Melee\n| head1 = {{plink|Slayer helmet (i)}}{{efn|Only while on a Slayer task.}} / {{plink|Oathplate helm}} > {{plink|Serpentine helm}}{{efn|If it does not lose max hits to Torva. Deals extra poison damage: [[https://dps.osrs.wiki?id=TransdimensionalOrbsBlade DPS Calculator]]}}\n| head2 = {{plink|Torva full helm}} / {{plink|Neitiznot faceguard}}\n| head3 = {{plink|Void melee helm}}{{efn|Only when using full [[Void Knight equipment]]. Do not use with [[Emberlight]], [[Arclight]], or a [[Scythe of vitur]]|name=\"voidgood\"}}\n| head4 = {{plink|Blood moon helm}}\n| head5 = {{plink|Helm of neitiznot}}\n| neck1 = {{plink|Amulet of rancour}}\n| neck2 = {{plink|Amulet of torture}}\n| neck3 = {{plink|Amulet of blood fury}}\n| neck4 = {{plink|Amulet of fury}}\n| neck5 = {{plink|Amulet of glory}}\n| cape1 = {{plink|Infernal cape}}\n| cape2 = {{plink|Fire cape}}\n| body1 = {{plink|Oathplate chest}}\n| body2 = {{plink|Torva platebody}}\n| body3 = {{plink|Bandos chestplate}} (or {{plinkp|Fighter torso}}{{plinkp|Blood moon chestplate}})\n| body4 = {{plink|Elite void top}}{{efn|name=\"voidgood\"}}/{{plink|Void knight top}}{{efn|name=\"voidgood\"}}\n| legs1 = {{plink|Oathplate legs}}\n| legs2 = {{plink|Torva platelegs}}\n| legs3 = {{plink|Bandos tassets}} (or {{plinkp|Blood moon tassets}})\n| legs4 = {{plink|Elite void robe}}{{efn|name=\"voidgood\"}}/{{plink|Void knight robe}}{{efn|name=\"voidgood\"}}\n| legs5 = {{plink|Obsidian platelegs}}\n| weapon1 = {{plink|Scythe of vitur}}\n| weapon2 = {{plink|Emberlight}}{{efn|4t weaponry match Duke's cycle during enrage, but can lose damage at the start compared to a 5t weapon.|name=4t}} / {{plink|Soulreaper axe}}\n| weapon3 = {{plink|Noxious halberd}} /  {{plink|Blade of saeldor}}\n| weapon4 = {{plink|Arclight}}{{efn|name=4t}} / {{plink|Zombie axe}} / {{plink|Abyssal whip}}{{efn|name=4t}}\n| shield1 = {{plink|Avernic defender}}\n| shield2 = {{plink|Dragon defender}}\n| ammo1 = {{plink|Rada's blessing 4}} /<br/> {{plink|Ghommal's lucky penny}}{{efn|If using Scythe of vitur, Serpentine helm, or Arclight}}\n| ammo2 = {{plink|God blessing|pic=Peaceful blessing}} /<br/>{{plink|Rada's blessing 3|txt=Rada's blessing 3/2}}\n| hands1 = {{plink|Ferocious gloves}}\n| hands2 = {{plink|Barrows gloves}} / {{plink|Void knight gloves}}{{efn|name=\"voidgood\"}}\n| feet1 = {{plink|Avernic treads (max)}}\n| feet2 = {{plink|Primordial boots}}\n| feet3 = {{plink|Echo boots}}\n| feet5 = {{plink|Dragon boots}}\n| ring1 = {{plink|Bellator ring}} / {{plink|Lightbearer}}{{efn|Can be brought as a ring swap between kills}}\n| ring2 = {{plink|Ultor ring}}\n| ring3 = {{plink|Berserker ring (i)}}\n| ring4 = {{plink|Ring of suffering (ri)}}\n| ring5 = {{plink|Ring of shadows}}\n| special1 = {{plink|Bandos godsword}}\n| special2 = {{plink|Voidwaker}}\n| special3 = {{plink|Dragon claws}} / {{plink|Burning claws}}\n| special4 = {{plink|Crystal halberd}}\n}}\n{{notelist}}\n\n==Inventory recommendations==\n{{Inventory\n|align = right\n|1 = Bandos godsword\n|2 = Lightbearer\n|3 = Cooked moonlight antelope\n|4 = Cooked moonlight antelope\n|5 = Cooked moonlight antelope\n|6 = Cooked moonlight antelope\n|7 = Cooked moonlight antelope\n|8 = Cooked moonlight antelope\n|9 = Cooked moonlight antelope\n|10 = Cooked moonlight antelope\n|11 = Cooked moonlight antelope\n|12 = Cooked moonlight antelope\n|13 = Cooked moonlight antelope\n|14 = Cooked moonlight antelope\n|15 = Cooked moonlight antelope\n|16 = Saradomin brew(4)\n|17 = Prayer regeneration potion(4)\n|18 = Super restore(4)\n|19 = Super restore(4)\n|20 = Super restore(4)\n|21 = Divine super combat potion(4)\n|22 = Stamina potion(4)\n|23 = Book of the dead\n|24 = Ring of shadows\n|25 = Divine super combat potion(4)\n|26 = Dragon pickaxe\n|27 = Divine rune pouch\n|28 = Teleport to house (tablet)\n}}{{Rune pouch|Blood rune|Death rune|Aether rune|Smoke rune\n| align = right\n}}\n*A stamina potion if you have a lower Herblore or Agility level.\n*4-7 [[prayer potion]]s or [[super restore]]s if using [[amulet of blood fury]] depending on trip length. A [[Prayer regeneration potion]] is recommended if you are capable of surviving for long trips.\n**Consider bringing [[Saradomin brew]]s alongside super restores for longer trips, as well as an extra [[super combat potion]].\n*[[Ring of shadows]] for teleporting back.\n**Use [[icy basalt]] if you have not received the [[frozen tablet]]. Alternatively, teleport to Weiss using a player-owned house portal nexus.\n*[[Cooked moonlight antelope]] restores 26 hitpoints per bite, while [[Cooked dashing kebbit]] restores 23 Hitpoints and 10% run energy per bite. However, hunter meats cannot be eaten quickly back to back for their full effect.\n**Swap for [[shark]]s or [[anglerfish]] if you are confident in getting perfect kills and want to reduce costs, bring [[Cooked Karambwan]] if you are still learning and need the ability to heal quickly.\n**[[Cooked Karambwan|Karambwans]] also have shorter eat delay, so using them as a primary food is cheap and effective for faster kills.\n**You can also bring [[summer pie]]s, which restore 22 Hitpoints and 20% run energy per pie. Each half only adds an eat delay of 1 tick (0.6 seconds), one bite can be eaten between attacks if using a 4 tick weapon, such as the [[Emberlight]], without losing damage.\n**[[Guthix rest]] heal 5hp per dose, restore energy, and reduce poison severity. They have no eat delay similar to [[Saradomin brews]] and also allow overhealing. They also do not reduce stats, unlike the Saradomin brew.\n*The [[book of the dead]] and a [[divine rune pouch]] to cast [[Death charge]] and [[Resurrection spells]] for [[Thrall]]s. The rune pouch should contain [[Blood rune|Blood]], [[Fire rune|fire]], [[Aether rune|aether]] (Or [[Cosmic rune|Cosmic]] and [[Soul rune|Soul]] runes separately), and [[Death rune]]s. Thralls add extra damage and Death charge allows more usage of special attacks.\n**[[Vile Vigour]] can optionally be brought to use prayer points to restore run energy if needed, for players with lower [[Herblore]] who cannot prepare as many poisons at a time. This can be done by swapping out the fire runes for [[smoke rune]]s.\n**[[Nature rune]]s can also be brought to cast [[Ward of Arceuus]] when still learning the boss, as it slightly reduces the damage taken and Prayer drained from mistakes made, although for more experienced players it is more valuable to bring other supplies to extend trips.\n*The best [[pickaxe]] you can use, as this increases the rate you get salt.\n**You can also 3-tick mine the salt if you collect the mushrooms first, which makes all pickaxes equal; in this case you can simply take the iron pickaxe from the wall.\n*A [[house tab]] or other teleport configured to be one click, for emergency escapes or leaving.\n*Upon entering the arena, drop 3-4 supplies to make room for mushrooms, salt, and a [[pestle and mortar]].\n\n==Plugins==\n\n===Better NPC Highlight===\n* To highlight the tiles of the gas flare vents, paste <code>12198</code> to True Tile -> True Tile IDs\n* To highlight the true tile of Awakened Duke's [[Orb of Darkness]], paste <code>11708</code>  to True Tile -> True Tile IDs\n==References==\n{{Reflist}}\n\n[[Category:Strategies]]", "revisionId": 14980052, "timestamp": "2025-09-04T20:52:07Z", "downloadedAt": "2025-09-05T20:38:25.207Z", "source": "osrs_wiki_watcher", "rawWikitext": true}