{"title": "Overload (Chambers of Xeric)", "namespace": "Main", "content": "{{otheruses|the item brewed within the [[Chambers of Xeric]]|the item purchased from the [[Nightmare Zone]]|Overload (Nightmare Zone)}}\n{{Infobox Item\n|defver = 4\n|version1 = 1 dose\n|version2 = 2 dose\n|version3 = 3 dose\n|version4 = 4 dose\n|smwname1 = (1)\n|smwname2 = (2)\n|smwname3 = (3)\n|smwname4 = (4)\n|name1 = Overload (1)\n|name2 = Overload (2)\n|name3 = Overload (3)\n|name4 = Overload (4)\n|image1 = [[File:Overload (1) (Chambers of Xeric).png]]\n|image2 = [[File:Overload (2) (Chambers of Xeric).png]]\n|image3 = [[File:Overload (3) (Chambers of Xeric).png]]\n|image4 = [[File:Overload (4) (Chambers of Xeric).png]]\n|release = [[5 January]] [[2017]]\n|update = Chambers of Xeric\n|members = Yes\n|quest = No\n|tradeable = Yes\n|placeholder = No\n|equipable = No\n|stackable = No\n|noteable = No\n|options = Drink, Drop\n|examine = The power of elder warriors, twisted archers and kodai wizards combined; extremely dangerous. It was mixed reasonably well.\n|value = 50\n|weight = 0.020\n|id1 = 20989\n|id2 = 20990\n|id3 = 20991\n|id4 = 20992\n}}\n{{Synced switch\n|version1 = [[File:Overload (1) (Chambers of Xeric) detail.png|left|150x150px]]\n|version2 = [[File:Overload (2) (Chambers of Xeric) detail.png|left|150x150px]]\n|version3 = [[File:Overload (3) (Chambers of Xeric) detail.png|left|150x150px]]\n|version4 = [[File:Overload (4) (Chambers of Xeric) detail.png|left|150x150px]]\n}}\nAn '''overload''' is a potion that boosts all the player's combat stats by 5 + 13%, while damaging them for 50 [[hitpoints]]. This boost is repeated every 15 seconds for five minutes until the effects wear off, and the player is then healed 50 hitpoints.\n\nIt can only be made in the [[Chambers of Xeric]] with 75 to 90 [[Herblore]] by using a [[noxifer]] with any variant of an [[elder potion|elder]], [[twisted potion|twisted]], and [[kodai potion]].\n\nThis potion is the standard variant of the overload potions, and cannot be taken out of the [[Chambers of Xeric]]. The [[Overload (-)|weakest variant]] requires 60 Herblore, whilst the [[Overload (+)|strongest variant]] requires 90 Herblore.\n\n==Effects==\nBoosts to most combat skills is calculated with: <math>\\lfloor SkillLevel \\times \\frac{13}{100} \\rfloor + 5</math>\n{| class=\"wikitable\" style=\"text-align:center;\"\n!{{SCP}} Level\n!Boost\n!{{SCP}} Level\n!Boost\n|-\n|'''1-7'''||+5||'''54-61'''||+12\n|-\n|'''8-15'''||+6||'''62-69'''||+13\n|-\n|'''16-23'''||+7||'''70-76'''||+14\n|-\n|'''24-30'''||+8||'''77-84'''||+15\n|-\n|'''31-38'''||+9||'''85-92'''||+16\n|-\n|'''39-46'''||+10||'''93-99'''||+17\n|-\n|'''47-53'''||+11\n|}\n\n==Creation==\n{{Recipe\n|skill1 = Herblore\n|skill1lvl = 75\n|skill1boostable = Yes\n|skill1exp = 50\n|members = Yes\n|ticks = 2\n|mat1 = Noxifer\n|mat1cost = No\n|mat2 = Elder potion (4)\n|mat2cost = No\n|mat3 = Twisted potion (4)\n|mat3cost = No\n|mat4 = Kodai potion (4)\n|mat4cost = No\n|output1 = Overload (Chambers of Xeric)\n|output1txt = Overload (4)\n|output1pic = Overload (Chambers of Xeric)\n|output1cost = No\n}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 28 May 2020\n|update = Chambers of Xeric Improvements\n|poll = Old School Content Poll 71: Gauntlet, Grotesques, CoX and more\n|change = The [[value]] was changed from 2 to 50 so that they are more likely to appear on top of the loot pile alongside raid supplies and bones.\n}}\n{{Subject changes\n|date = 22 March 2018\n|update = Upcoming Changes and Fake XP Drop Improvements\n|change = The item's value was increased from 1 to 2.\n}}\n{{Subject changes footer}}\n\n{{Items that can damage oneself}}\n{{Chambers of Xeric}}\n{{Potions}}\n[[Category:Temporary skill boost]]\n[[Category:Attack boosting items]]\n[[Category:Defence boosting items]]\n[[Category:Magic boosting items]]\n[[Category:Ranged boosting items]]\n[[Category:Strength boosting items]]", "revisionId": 14980588, "timestamp": "2025-09-05T18:06:37Z", "downloadedAt": "2025-09-05T20:37:23.117Z", "source": "osrs_wiki_watcher", "rawWikitext": true}