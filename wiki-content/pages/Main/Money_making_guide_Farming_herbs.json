{"title": "Money making guide/Farming herbs", "namespace": "Main", "content": "{{Mmgtable recurring\n|Activity = Farming [[Herb|Herbs]]\n|Image = [[File:Farming herbs.gif]]\n|Activity Time = 6\n|Recurrence Time = 90 minutes\n|Skill =\n* {{#switch:{{max|{{#var:guamprofit}}|{{#var:marrentillprofit}}|{{#var:tarrominprofit}}|{{#var:harralanderprofit}}||{{#var:ranarrprofit}}||{{#var:toadflaxprofit}}||{{#var:iritprofit}}||{{#var:avantoeprofit}}||{{#var:kwuarmprofit}}||{{#var:snapdragonprofit}}||{{#var:huascaprofit}}||{{#var:cadantineprofit}}||{{#var:lantadymeprofit}}||{{#var:dwarfweedprofit}}||{{#var:torstolprofit}}}}\n|{{#var:guamprofit}} = {{SCP|Farming|9}}\n|{{#var:marrentillprofit}} = {{SCP|Farming|14}}\n|{{#var:tarrominprofit}} = {{SCP|Farming|19}}\n|{{#var:harralanderprofit}} = {{SCP|Farming|26}}\n|{{#var:ranarrprofit}} = {{SCP|Farming|32}}\n|{{#var:toadflaxprofit}} = {{SCP|Farming|38}}\n|{{#var:iritprofit}} = {{SCP|Farming|44}}\n|{{#var:avantoeprofit}} = {{SCP|Farming|50}}\n|{{#var:kwuarmprofit}} = {{SCP|Farming|56}}\n|{{#var:snapdragonprofit}} = {{SCP|Farming|62}}\n|{{#var:huascaprofit}} = {{SCP|Farming|65}}\n|{{#var:cadantineprofit}} = {{SCP|Farming|67}}\n|{{#var:lantadymeprofit}} = {{SCP|Farming|73}}       \n|{{#var:dwarfweedprofit}} = {{SCP|Farming|79}}\n|{{#var:torstolprofit}} = {{SCP|Farming|85}} }} (9 minimum, 32+ highly recommended, 65+ if using the [[Farming Guild]] [[herb patch]])\n* {{SCP|Agility|73}} (optional - for improved access to the [[Trollheim]] [[herb patch]]) \n|Item =\n* '''Farming equipment:'''\n* [[{{#var:bestseed}}]]s\n* [[Magic secateurs]] (highly recommended)\n* [[Farming cape]] (optional)\n* [[Bottomless compost bucket]] (highly recommended)\n* [[Amulet of nature]] (optional)\n* [[Runes]] for [[teleportation]] and [[spells]] (optional)\n* An [[Elemental staves|Elemental staff]] to reduce [[teleportation]] and [[spell]] costs (optional)\n* '''Transportation recommendations:'''\n* [[Player-owned house]] for access to all teleports\n* [[Ardougne cloak 2]]/[[Ardougne cloak 3|3]]/[[Ardougne cloak 4|4]] or [[Skills necklace]] for the [[Ardougne]] [[herb patch]]  \n* [[Catherby]] [[herb patch]] - [[Catherby teleport]] or [[Camelot Teleport]]\n* [[Explorer's ring 2]]/[[Explorer's ring 3|3]]/[[Explorer's ring 4|4]] or [[Amulet of Glory]] for the [[Falador]] [[herb patch]]\n* [[Farming cape]] or [[Skills necklace]] for the [[Farming guild]] [[herb patch]] \n* [[Harmony island teleport]] for the [[Harmony island]] [[herb patch]]\n* [[Xeric's talisman]] or [[Hosidius teleport]] for the [[Hosidius]] [[herb patch]]\n* [[Ectophial]] or [[Fairy ring]] {{Fairycode|alq}} for the [[Port Phasmatys]] [[herb patch]]\n* [[Stony basalt]] or [[Trollheim teleport]] for the [[Trollheim]] [[herb patch]]\n* [[Quetzal whistle]] or [[Civitas illa fortis teleport]] for the [[Varlamore]] [[herb patch]]\n* [[Icy basalt]] for [[Weiss]] [[herb patch]] \n |Quest =\n* [[Ghosts Ahoy]]\n* [[My Arm's Big Adventure]]\n* [[Fairytale I - Growing Pains]]\n* [[Making Friends with My Arm]] for the [[Weiss]] [[herb patch]] (optional)\n* [[Children of the Sun]] for the [[Civitas illa Fortis]] herb patch (optional)\n* [[Dream Mentor]] (optional)\n|Other = [[Tool leprechaun]] with a [[seed dibber]], a [[spade]], a [[rake]], and some [[Ultracompost]] <br>\n{{SCP|Diary}} [[Medium Lumbridge & Draynor Diary]] (or higher) for [[Explorer's ring 2]] teleport to cabbage patch adjacent to the [[Falador farm]] [[herb patch]] (optional) <br>\n{{SCP|Diary}} [[Medium Ardougne Diary]] (or higher) for [[Ardougne cloak 2]] teleport to [[Ardougne]] [[herb patch]] (optional) <br>\n{{SCP|Diary}} [[Hard Fremennik Diary]] for the enhanced [[Stony basalt]] teleport (optional)\n{{SCP|Diary}} [[Elite Morytania Diary]] if using the [[Harmony Island]] [[herb patch]] (optional)\n|Other Benefits = \n{{#switch:{{max|{{#var:guamprofit}}|{{#var:marrentillprofit}}|{{#var:tarrominprofit}}|{{#var:harralanderprofit}}||{{#var:ranarrprofit}}||{{#var:toadflaxprofit}}||{{#var:iritprofit}}||{{#var:avantoeprofit}}||{{#var:kwuarmprofit}}||{{#var:snapdragonprofit}}||{{#var:huascaprofit}}||{{#var:cadantineprofit}}||{{#var:lantadymeprofit}}||{{#var:dwarfweedprofit}}||{{#var:torstolprofit}}}}\n|{{#var:guamprofit}} = {{SCP|Farming|{{Formatexpr|10*(11+12.5*8.8+36) round 0}}}}\n|{{#var:marrentillprofit}} = {{SCP|Farming|{{Formatexpr|10*(13.5+15*8.8+36) round 0}}}}\n|{{#var:tarrominprofit}} = {{SCP|Farming|{{Formatexpr|10*(16+18*8.8+36) round 0}}}}\n|{{#var:harralanderprofit}} = {{SCP|Farming|{{Formatexpr|10*(21.5+24*8.8+36) round 0}}}}\n|{{#var:ranarrprofit}} = {{SCP|Farming|{{Formatexpr|10*(26.5+30.5*8.8+36) round 0}}}}\n|{{#var:toadflaxprofit}} = {{SCP|Farming|{{Formatexpr|10*(34+38.5*8.8+36) round 0}}}}\n|{{#var:iritprofit}} = {{SCP|Farming|{{Formatexpr|10*(43+48.5*8.8+36) round 0}}}}\n|{{#var:avantoeprofit}} = {{SCP|Farming|{{Formatexpr|10*(54.5+61.5*8.8+36) round 0}}}}\n|{{#var:kwuarmprofit}} = {{SCP|Farming|{{Formatexpr|10*(69+78*8.8+36) round 0}}}}\n|{{#var:snapdragonprofit}} = {{SCP|Farming|{{Formatexpr|10*(87.5+98.5*8.8+36) round 0}}}}\n|{{#var:huascaprofit}} = {{SCP|Farming|{{Formatexpr|10*(86.5+110*8.8+36) round 0}}}}\n|{{#var:cadantineprofit}} = {{SCP|Farming|{{Formatexpr|10*(106.5+120*8.8+36) round 0}}}}\n|{{#var:lantadymeprofit}} = {{SCP|Farming|{{Formatexpr|10*(134.5+151.5*8.8+36) round 0}}}}       \n|{{#var:dwarfweedprofit}} = {{SCP|Farming|{{Formatexpr|10*(170.5+192*8.8+36) round 0}}}}\n|{{#var:torstolprofit}} = {{SCP|Farming|{{Formatexpr|10*(199.5+224.5*8.8+36) round 0}}}} }}\n|Input1 = \n{{#vardefineecho:bestseed|\n{{#vardefine:guamprofit|{{#expr:({{GEP|Grimy guam leaf}}-floor({{GEP|Grimy guam leaf|}}/50))*88-({{GEP|Guam seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:marrentillprofit|{{#expr:({{GEP|Grimy marrentill}}-floor({{GEP|Grimy marrentill|}}/50))*88-({{GEP|Marrentill seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:tarrominprofit|{{#expr:({{GEP|Grimy tarromin}}-floor({{GEP|Grimy tarromin|}}/50))*88-({{GEP|Tarromin seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:harralanderprofit|{{#expr:({{GEP|Grimy harralander}}-floor({{GEP|Grimy harralander|}}/50))*88-({{GEP|Harralander seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:ranarrprofit|{{#expr:({{GEP|Grimy ranarr weed}}-floor({{GEP|Grimy ranarr weed|}}/50))*88-({{GEP|Ranarr seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:toadflaxprofit|{{#expr:({{GEP|Grimy toadflax}}-floor({{GEP|Grimy toadflax|}}/50))*88-({{GEP|Toadflax seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:iritprofit|{{#expr:({{GEP|Grimy irit leaf}}-floor({{GEP|Grimy irit leaf|}}/50))*88-({{GEP|Irit seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:avantoeprofit|{{#expr:({{GEP|Grimy avantoe}}-floor({{GEP|Grimy avantoe|}}/50))*88-({{GEP|Avantoe seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:kwuarmprofit|{{#expr:({{GEP|Grimy kwuarm}}-floor({{GEP|Grimy kwuarm|}}/50))*88-({{GEP|Kwuarm seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:huascaprofit|{{#expr:({{GEP|Grimy huasca}}-floor({{GEP|Grimy huasca|}}/50))*88-({{GEP|Huasca seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:snapdragonprofit|{{#expr:({{GEP|Grimy snapdragon}}-floor({{GEP|Grimy snapdragon|}}/50))*88-({{GEP|Snapdragon seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:cadantineprofit|{{#expr:({{GEP|Grimy cadantine}}-floor({{GEP|Grimy cadantine|}}/50))*88-({{GEP|Cadantine seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:lantadymeprofit|{{#expr:({{GEP|Grimy lantadyme}}-floor({{GEP|Grimy lantadyme|}}/50))*88-({{GEP|Lantadyme seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:dwarfweedprofit|{{#expr:({{GEP|Grimy dwarf weed}}-floor({{GEP|Grimy dwarf weed|}}/50))*88-({{GEP|Dwarf weed seed|10}}+{{GEP|Ultracompost|5}})}}}}\n{{#vardefine:torstolprofit|{{#expr:({{GEP|Grimy torstol}}-floor({{GEP|Grimy torstol|}}/50))*88-{{GEP|Torstol seed|10}}+{{GEP|Ultracompost|5}}}}}}\n{{#switch:{{max|{{#var:guamprofit}}|{{#var:marrentillprofit}}|{{#var:tarrominprofit}}|{{#var:harralanderprofit}}||{{#var:ranarrprofit}}||{{#var:toadflaxprofit}}||{{#var:iritprofit}}||{{#var:avantoeprofit}}||{{#var:kwuarmprofit}}||{{#var:snapdragonprofit}}||{{#var:huascaprofit}}||{{#var:cadantineprofit}}||{{#var:lantadymeprofit}}||{{#var:dwarfweedprofit}}||{{#var:torstolprofit}}}}\n|{{#var:guamprofit}} = Guam seed\n|{{#var:marrentillprofit}} = Marrentill seed\n|{{#var:tarrominprofit}} = Tarromin seed\n|{{#var:harralanderprofit}} = Harralander seed\n|{{#var:ranarrprofit}} = Ranarr seed\n|{{#var:toadflaxprofit}} = Toadflax seed\n|{{#var:iritprofit}} = Irit seed\n|{{#var:avantoeprofit}} = Avantoe seed\n|{{#var:kwuarmprofit}} = Kwuarm seed\n|{{#var:snapdragonprofit}} = Snapdragon seed\n|{{#var:huascaprofit}} = Huasca seed\n|{{#var:cadantineprofit}} = Cadantine seed\n|{{#var:lantadymeprofit}} = Lantadyme seed\n|{{#var:dwarfweedprofit}} = Dwarf weed seed\n|{{#var:torstolprofit}} = Torstol seed}}}}\n|Input1num = 10\n|Input2 = Ultracompost\n|Input2num = 5\n|Output1 = \n{{#vardefineecho:bestherb|\n{{#switch:{{max|{{#var:guamprofit}}|{{#var:marrentillprofit}}|{{#var:tarrominprofit}}|{{#var:harralanderprofit}}||{{#var:ranarrprofit}}||{{#var:toadflaxprofit}}||{{#var:iritprofit}}||{{#var:avantoeprofit}}||{{#var:kwuarmprofit}}||{{#var:snapdragonprofit}}||{{#var:huascaprofit}}||{{#var:cadantineprofit}}||{{#var:lantadymeprofit}}||{{#var:dwarfweedprofit}}||{{#var:torstolprofit}}}}\n|{{#var:guamprofit}} = Grimy guam leaf\n|{{#var:marrentillprofit}} = Grimy marrentill\n|{{#var:tarrominprofit}} = Grimy tarromin\n|{{#var:harralanderprofit}} = Grimy harralander\n|{{#var:ranarrprofit}} = Grimy ranarr weed\n|{{#var:toadflaxprofit}} = Grimy toadflax\n|{{#var:iritprofit}} = Grimy irit leaf\n|{{#var:avantoeprofit}} = Grimy avantoe\n|{{#var:kwuarmprofit}} = Grimy kwuarm\n|{{#var:snapdragonprofit}} = Grimy snapdragon\n|{{#var:huascaprofit}} = Grimy huasca\n|{{#var:cadantineprofit}} = Grimy cadantine\n|{{#var:lantadymeprofit}} = Grimy lantadyme\n|{{#var:dwarfweedprofit}} = Grimy dwarf weed\n|{{#var:torstolprofit}} = Grimy torstol}}}}\n|Output1num = 88\n|Location = [[Ardougne]], [[Catherby]], [[Falador]], [[Farming Guild]],  [[Harmony Island]], [[Hosidius]], [[Port Phasmatys]], [[Trollheim]], [[Varlamore]] and [[Weiss]] [[herb patch]]es\n|Category = Farming\n|Details = '''This guide assumes 8.8 [[herbs]] from each [[Herb patch|patch]], thus 88 herbs per run. Your profit per run may vary depending on a multitude of factors, exact rates for your specific conditions can be calculated using the following calculator: [[Calculator:Farming/Herbs]].'''\n\n[[Farming]] [[herb]]s  can be a very profitable way to spend a few minutes at a time, with [[{{#var:bestherb}}]] currently being the most profitable. [[Magic secateurs]] help increase profits slightly due to the 10% boost to crop yield. Generally, good harvests yield 6-15 herbs per patch, but the norm is six to ten, so players can expect an average of 8.8 herbs per patch.\n\nPlayers can further increase their profits:\n*If they use a [[Bottomless compost bucket]] to halve the amount of [[Ultracompost]] used and make inventory management easier. This is generally preferable to the use of [[Fertile Soil]] as it is lower cost and minimises use of inventory space.\n*If they use [[Resurrect crops]] to revive dead crops. Note that this is only recommended for high value seeds as the high cost of casting the spell can result in reduced profit for low value seeds.\n*If they plant [[Anima]] plants ({{SCP|Farming|76}}, boostable). [[Iasor]], [[Kronos]], and [[Attas]] decrease the rate of disease, decrease the amount of time it takes for crops to grow, and increase the yield of crops, respectively.\n*If they own a [[Farming cape]], due to the 5% boost to crop yield. \n*If they attune an [[Amulet of Nature]] to one of their herb patches, and keep it with them while doing other tasks around the game, so that they can check frequently whether their crops are fully grown.\n*If they use the [[Geomancy]] spell that can monitor the health of their patches and tell them when their patches are fully grown.\n\nPlayers should have 10 [[{{#var:bestseed}}]]s and their selected means of [[teleportation]]. The player can start their herb run at any herb patch and withdraw any additional supplies needed from the [[tool leprechaun]]. Pick any [[{{#var:bestherb}}]]s that have survived (or clear dead herbs), plant a new [[{{#var:bestseed}}]] and use [[Ultracompost]] on the patch. The player can then move onto the next patch in any order. An example of a herb run is provided below. \n\n*Use the [[Catherby teleport]] and run north to the patch.\n*Use the [[Ardougne cloak 2]]/[[Ardougne cloak 3|3]]/[[Ardougne cloak 4|4]] teleport to get to the patch instantly.\n*Empty the [[Ectophial]] and run west of the undead farm to the [[Port Phasmatys]] patch.\n*Use a [[Stony basalt]] to be teleported next to the shortcut leading to the [[Trollheim]] patch (requires completion of Making Friends with My Arm.)\n*Use the [[Explorer's ring 2]]/[[Explorer's ring 3|3]]/[[Explorer's ring 4|4]] teleport and run north to the [[Falador]] patch.\n*Teleport to Xeric's Glade using the [[Xeric's Talisman]] and run south to the [[Hosidius]] patch.\n*Use an [[Icy basalt]] teleport to get to the [[Weiss]] patch instantly.\n*Use the [[Farming Guild]] teleport on the [[Skills necklace]] to get to the patch instantly.\n*Teleport to the [[Hunter Guild]] via a [[Quetzal whistle]] and run north to the [[Varlamore]] patch.\n*If you have the [[Morytania_Diary#Elite|Morytania Elite Diary]], cast [[Harmony island teleport]] and run south to the patch.\n}}\n\n=Farming Herb Profitability=\n\nThe below table details all farmable herbs, their profitability, and the expected experience gained per run based on an average of 88 herbs per run. Players wishing to level farming may want to consider farming herbs with a slightly lower Profit Per Instance but significantly higher Average Farming Experience Per Instance. \n\nAll prices are calculated using current [[Grand Exchange]] market prices, meaning the actual profit per hour may vary from what is stated here, especially for lower volume items. If you notice a discrepancy between profits listed here and profits listed on guide pages, it is possibly a caching issue. You can force this page to update by clicking {{Purge|here|tag=span|noinplace=true}}.\n\n{| class=\"wikitable sortable\"\n|+ Farming Herbs Profitability\n|-\n! Farming <br> Level\n! Input Seed\n! Output Herb\n! Input Cost\n! Output Value <br> (includes G.E. tax)\n! Profit <br> (per instance) \n! Effective Profit <br> (per hour)  \n! Average Farming Experience <br> (per instance)\n|-\n| {{SCP|Farming|9}} || {{plink|Guam seed}} || {{plink|Grimy guam leaf}} || {{Coins|{{GEP|Guam seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy guam leaf}}-floor({{GEP|Grimy guam leaf}}/50))*88|0}}}} || {{Coins|{{#var:guamprofit}}}} || {{Coins|{{decimals|60/6*{{#var:guamprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(11+12.5*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|14}} || {{plink|Marrentill seed}} || {{plink|Grimy marrentill}} || {{Coins|{{GEP|Marrentill seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy marrentill}}-floor({{GEP|Grimy marrentill}}/50))*88|0}}}} || {{Coins|{{#var:marrentillprofit}}}} || {{Coins|{{decimals|60/6*{{#var:marrentillprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(13.5+15*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|19}} || {{plink|Tarromin seed}} || {{plink|Grimy tarromin}} || {{Coins|{{GEP|Tarromin seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy tarromin}}-floor({{GEP|Grimy tarromin}}/50))*88|0}}}} || {{Coins|{{#var:tarrominprofit}}}} || {{Coins|{{decimals|60/6*{{#var:tarrominprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(16+18*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|26}} || {{plink|Harralander seed}} || {{plink|Grimy harralander}} || {{Coins|{{GEP|Harralander seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy harralander}}-floor({{GEP|Grimy harralander}}/50))*88|0}}}} || {{Coins|{{#var:harralanderprofit}}}} || {{Coins|{{decimals|60/6*{{#var:harralanderprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(21.5+24*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|32}} || {{plink|Ranarr seed}} || {{plink|Grimy ranarr weed}} || {{Coins|{{GEP|Ranarr seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy ranarr weed}}-floor({{GEP|Grimy ranarr weed}}/50))*88|0}}}} || {{Coins|{{#var:ranarrprofit}}}} || {{Coins|{{decimals|60/6*{{#var:ranarrprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(26.5+30.5*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|38}} || {{plink|Toadflax seed}} || {{plink|Grimy toadflax}} || {{Coins|{{GEP|Toadflax seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy toadflax}}-floor({{GEP|Grimy toadflax}}/50))*88|0}}}} || {{Coins|{{#var:toadflaxprofit}}}} || {{Coins|{{decimals|60/6*{{#var:toadflaxprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(34+38.5*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|44}} || {{plink|Irit seed}} || {{plink|Grimy irit leaf}} || {{Coins|{{GEP|Irit seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy irit leaf}}-floor({{GEP|Grimy irit leaf}}/50))*88|0}}}} || {{Coins|{{#var:iritprofit}}}} || {{Coins|{{decimals|60/6*{{#var:iritprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(43+48.5*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|50}} || {{plink|Avantoe seed}} || {{plink|Grimy avantoe}} || {{Coins|{{GEP|Avantoe seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy avantoe}}-floor({{GEP|Grimy avantoe}}/50))*88|0}}}} || {{Coins|{{#var:avantoeprofit}}}} || {{Coins|{{decimals|60/6*{{#var:avantoeprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(54.5+61.5*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|56}} || {{plink|Kwuarm seed}} || {{plink|Grimy kwuarm}} || {{Coins|{{GEP|Kwuarm seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy kwuarm}}-floor({{GEP|Grimy kwuarm}}/50))*88|0}}}} || {{Coins|{{#var:kwuarmprofit}}}} || {{Coins|{{decimals|60/6*{{#var:kwuarmprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(69+78*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|62}} || {{plink|Snapdragon seed}} || {{plink|Grimy snapdragon}} || {{Coins|{{GEP|Snapdragon seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy snapdragon}}-floor({{GEP|Grimy snapdragon}}/50))*88|0}}}} || {{Coins|{{#var:snapdragonprofit}}}} || {{Coins|{{decimals|60/6*{{#var:snapdragonprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(87.5+98.5*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|65}} || {{plink|Huasca seed}} || {{plink|Grimy huasca}} || {{Coins|{{GEP|Huasca seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy huasca}}-floor({{GEP|Grimy huasca}}/50))*88|0}}}} || {{Coins|{{#var:huascaprofit}}}} || {{Coins|{{decimals|60/6*{{#var:huascaprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(86.5+110*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|67}} || {{plink|Cadantine seed}} || {{plink|Grimy cadantine}} || {{Coins|{{GEP|Cadantine seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy cadantine}}-floor({{GEP|Grimy cadantine}}/50))*88|0}}}} || {{Coins|{{#var:cadantineprofit}}}} || {{Coins|{{decimals|60/6*{{#var:cadantineprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(106.5+120*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|73}} || {{plink|Lantadyme seed}} || {{plink|Grimy lantadyme}} || {{Coins|{{GEP|Lantadyme seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy lantadyme}}-floor({{GEP|Grimy lantadyme}}/50))*88|0}}}} || {{Coins|{{#var:lantadymeprofit}}}} || {{Coins|{{decimals|60/6*{{#var:lantadymeprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(134.5+151.5*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|79}} || {{plink|Dwarf weed seed}} || {{plink|Grimy dwarf weed}} || {{Coins|{{GEP|Dwarf weed seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy dwarf weed}}-floor({{GEP|Grimy dwarf weed}}/50))*88|0}}}} || {{Coins|{{#var:dwarfweedprofit}}}} || {{Coins|{{decimals|60/6*{{#var:dwarfweedprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(170.5+192*8.8+36) round 0}}}}\n|-\n| {{SCP|Farming|85}} || {{plink|Torstol seed}} || {{plink|Grimy torstol}} || {{Coins|{{GEP|Torstol seed|10}}+{{GEP|Ultracompost|5}}}} || {{Coins|{{decimals|({{GEP|Grimy torstol}}-floor({{GEP|Grimy torstol}}/50))*88|0}}}} || {{Coins|{{#var:torstolprofit}}}} || {{Coins|{{decimals|60/6*{{#var:torstolprofit}}|0}}}} || {{SCP|Farming|{{Formatexpr|10*(199.5+224.5*8.8+36) round 0}}}} \n|-\n|}\n\n{{Money making}}\n[[Category:MMG/Skilling]]", "revisionId": 14980705, "timestamp": "2025-09-05T20:40:43Z", "downloadedAt": "2025-09-05T20:44:11.249Z", "source": "osrs_wiki_watcher", "rawWikitext": true}