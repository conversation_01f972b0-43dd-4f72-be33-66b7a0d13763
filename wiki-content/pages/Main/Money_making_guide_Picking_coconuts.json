{"title": "Money making guide/Picking coconuts", "namespace": "Main", "content": "{{Mmgtable recurring\n|Exclude = Yes\n|Activity = Picking [[coconut]]s\n|Image = [[File:Coconut detail.png|100px]]\n|Activity Time = 3\n|Recurrence Time = 270 minutes\n|Skill = {{SCP|Farming|68 or 85 (recommended)}}\n|Quest = [[The Grand Tree]]<br/>Started [[Mourning's End Part I]]\n|Item = [[Teleport crystal]]\n|Other = Fully grown [[Palm tree]] in fruit tree patches\n|Other Benefits = {{SCP|Farming|1,494}}\n|Output1 = Coconut\n|Output1num = 36\n|Location = [[Catherby]], [[Brimhaven]], [[Tree Gnome Stronghold]], [[Tree Gnome Village (location)|Tree Gnome Village]], [[Lletya]], and [[Farming Guild]] (85 Farming required) [[fruit tree patch]]es\n|Category = Farming\n|Details = [[Coconut]]s grow on [[palm tree]]s, and are used to make [[coconut milk]], used in high-level [[Herblore]], being the secondary ingredients used to make [[Weapon Poison++]] and [[Antidote++]]. They're also used to pay farmers to look after [[magic tree]]s and [[dragonfruit tree]]s. Once a palm tree has grown fully, it can be harvested repeatedly, with a coconut growing back every 45 minutes on average, for a maximum of six per palm tree.\n\nHarvesting trips are most efficient when the player has at the most, 4 items in their inventory, to minimise time spent running to the [[tool leprechaun]]s to note coconuts. An efficient inventory setup includes a [[construction cape]], [[farming cape]], and a [[teleportation crystal]]. This way, the player only needs to use the noting function of the tool leprechaun once. Alternative methods to get to each fruit tree patch are provided below the most efficient route.\n\n'''Most efficient route:'''\nBegin the trip by using the construction cape to teleport to the [[Brimhaven]] house portal and run north, harvesting all 6 coconuts from the [[palm tree]]. Next, use a [[teleport crystal]] to teleport to [[Lletya]]. Harvest the palm tree and note 12 coconuts with the [[tool leprechaun]] located 2 tiles north of the tree. Then, use the [[farming cape]] to teleport to the [[Farming Guild]]. Run north into the advanced tier of the guild to harvest the palm tree. Next, cast [[Catherby Teleport]] and harvest the coconuts growing on the palm tree found on the east side of the shore. Then, teleport to your [[player-owned house]] and use the [[Spirit tree]] to teleport to the [[Gnome Stronghold]]. Harvest the palm tree to the east and return back to the Spirit tree, travelling finally to the [[Tree Gnome Village (location)|Tree Gnome Village]]. Squeeze through the fence to the south-west and exit the maze quickly by following [[Elkoy]]. Travel south-west and harvest the coconuts of the final palm tree. \n\n'''Alternative methods:'''\n\nBrimhaven: [[Brimhaven teleport]] tablet, teleporting outside a player-owned house located in Brimhaven, taking the ship from Ardougne, [[Charter ship|charting a ship]] from Catherby, or using the Spirit Tree network to teleport to Brimhaven. \n\nFarming Guild: [[Skills necklace]] teleport, use of the Spirit Tree network to teleport to the Farming Guild, or [[fairy ring]] (code {{Fairycode|CIR}}). Note that the fruit tree patch is located in the advanced tier of the Farming Guild which requires 85 farming ([[boostable]]) to enter. This money-making method otherwise only requires 68 Farming, the skill level needed to plant and harvest palm trees.\n\nCatherby: Catherby teleport portal in a [[portal chamber]], Catherby teleport using a [[Portal nexus]], [[Camelot teleport]], or [[Charter ship]].\n\nGnome Stronghold: [[Royal seed pod]] teleport, use of a [[Spirit tree]], [[Slayer ring]] teleport to [[Stronghold slayer cave]], [[Achievement Diary Cape]] teleport to the [[Elder gnome child]] location, or use of a [[gnome glider]] to the [[Grand tree]].\n\n\nAs this uses the same patches as [[papaya tree]]s, it cannot be used at the same time as the [[Money making guide/Picking papayas|picking papayas]] method. Whichever one is more profitable should be used.\n}}\n{{Money making}}\n[[Category:MMG/Recurring]]", "revisionId": 14980537, "timestamp": "2025-09-05T15:23:41Z", "downloadedAt": "2025-09-05T20:37:30.090Z", "source": "osrs_wiki_watcher", "rawWikitext": true}