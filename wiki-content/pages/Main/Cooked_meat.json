{"title": "Cooked meat", "namespace": "Main", "content": "{{External|rs|rsc=Cookedmeat|dw=Steak}}\n{{Infobox Item\n|name = Cooked meat\n|image = [[File:Cooked meat.png]]\n|release = [[4 January]] [[2001]]\n|update = Runescape beta is now online!\n|members = No\n|quest = No\n|tradeable = Yes\n|placeholder = Yes\n|equipable = No\n|stackable = No\n|noteable = Yes\n|edible = Yes\n|options = Eat, Drop\n|examine = Mmm this looks tasty.\n|value = 4\n|weight = 0.283\n|respawn = 200\n|exchange = Yes\n|id = 2142\n}}\n[[File:Cooked meat detail.png|left|130px]]\n'''Cooked meat''' is a type of [[food]] that restores 3 [[Hitpoints]] when eaten. It can be obtained by cooking various [[Raw meat|raw meats]] on a [[fire]] or [[cooking range]], granting 30 [[experience]] when successful. When cooking there is a chance to fail, resulting in [[burnt meat]]. The [[burn rate]] decreases as the player's [[Cooking]] level increases. Cooked meat is one of a number of foods with a reduced burn rate while using the [[Cooking range (Lumbridge Castle)|Lumbridge Castle range]] with [[Cook's Assistant]] completed. It will stop burning entirely at Cooking level 34, or 31 when using the Lumbridge range. \n\nCooked meat is used in the making of [[stew]]s, [[Meat pie|pie]]s, and [[steak sandwich|steak sandwiches]], or as a [[Meat pizza|topping]] on [[pizza]]s. \n\nUnlike most food, players can deliberately burn cooked meat by using it on a fire or cooking range. One burnt meat is required for [[Witch's Potion]]. \n\n==Creation==\n<tabber>\nRaw beef=\n{{Recipe\n|skill1 = Cooking\n|skill1lvl = 1\n|skill1exp = 30\n|members = No\n|ticks = 4\n|ticksnote = 1 tick for the first meat, 3 ticks for the second, then 4 ticks for the rest.\n|facilities = Cooking range\n|mat1 = Raw beef\n|output1 = Cooked meat\n|output1itemnote = This recipe has a chance to give a [[burnt meat]] instead.\n}}\n|-|\nRaw bear meat=\n{{Recipe\n|skill1 = Cooking\n|skill1lvl = 1\n|skill1exp = 30\n|members = No\n|ticks = 4\n|ticksnote = 1 tick for the first meat, 3 ticks for the second, then 4 ticks for the rest.\n|facilities = Cooking range\n|mat1 = Raw bear meat\n|output1 = Cooked meat\n|output1itemnote = This recipe has a chance to give a [[burnt meat]] instead.\n}}\n|-|\nRaw boar meat=\n{{Recipe\n|skill1 = Cooking\n|skill1lvl = 1\n|skill1exp = 30\n|members = No\n|ticks = 4\n|ticksnote = 1 tick for the first meat, 3 ticks for the second, then 4 ticks for the rest.\n|facilities = Cooking range\n|mat1 = Raw boar meat\n|output1 = Cooked meat\n|output1itemnote = This recipe has a chance to give a [[burnt meat]] instead.\n}}\n|-|\nRaw rat meat=\n{{Recipe\n|skill1 = Cooking\n|skill1lvl = 1\n|skill1exp = 30\n|members = No\n|ticks = 4\n|ticksnote = 1 tick for the first meat, 3 ticks for the second, then 4 ticks for the rest \n|facilities = Cooking range\n|mat1 = Raw rat meat\n|output1 = Cooked meat\n|output1itemnote = This recipe has a chance to give a [[burnt meat]] instead.\n}}\n|-|\nRaw yak meat=\n{{Recipe\n|skill1 = Cooking\n|skill1lvl = 1\n|skill1exp = 30\n|members = Yes\n|ticks = 4\n|ticksnote = 1 tick for the first meat, 3 ticks for the second, then 4 ticks for the rest \n|facilities = Cooking range\n|mat1 = Raw yak meat\n|output1 = Cooked meat\n|output1itemnote = This recipe has a chance to give a [[burnt meat]] instead.\n}}\n</tabber>\n\n===Cooking chance===\n{{Skilling success chart|label=Cooked meat cooking chance\n|label1=Fire or Range|low1=128|high1=512|req1=1|color1=gray|image1=Cooked meat.png\n|label2=Lumbridge range|low2=138|high2=532|req2=1|color2=green|image2=Lumbridge Teleport.png\n|label3=Hosidius +5%|low3=140|high3=524|req3=1|color3=red|image3=Cooked meat.png\n|label4=Hosidius +10%|low4=153|high4=537|req4=84|color4=blue|image4=Cooked meat.png\n}}\n\n==Uses==\n===Quests===\n*[[Witch's Potion]]: 1 × cooked meat to make [[burnt meat]]\n*[[Below Ice Mountain]]: 1 × cooked meat to make [[steak sandwich]]\n\n==Products==\n{{Uses material list|Cooked meat}}\n\n==Item sources==\n{{Drop sources|Cooked meat}}\n\n===Spawns===\n{{ItemSpawnTableHead|league=yes}}\n{{ItemSpawnLine|name=Cooked meat|location=[[Barbarian Village]] - Long Hall|members=No|3077,3441,qty:1|3080,3443,qty:1|leagueRegion=Misthalin}}\n{{ItemSpawnLine|name=Cooked meat|location=[[Shayziens' Wall]] - northern outpost|members=Yes|1367,3575,qty:1|leagueRegion=Kourend}}\n{{ItemSpawnLine|name=Cooked meat|location=[[Kebos Lowlands]] - [[General Vir]]'s tent in the [[Battlefront]]|members=Yes|1384,3764,qty:1|leagueRegion=Kourend}}\n{{ItemSpawnLine|name=Cooked meat|location=[[Corsair Cove]] - [[Arsen the Thief]]'s house ({{FloorNumber|uk=1}})|members=No|plane=1\n|2558,2856,qty:1|leagueRegion=Kandarin}}\n{{ItemSpawnTableBottom}}\n\n===Shop locations===\n{{Store locations list|Cooked meat}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 29 May 2025\n|update = Yama CAs & More!\n|change = Cooked meat has been exempted from the [[Grand Exchange#Convenience fee and item sink|Grand Exchange convenience fee]].\n}}\n{{Subject changes footer}}\n\n{{Meat}}\n{{Pies}}\n[[Category:Meat]]\n[[Category:Items exempt from Grand Exchange tax]]", "revisionId": 14980684, "timestamp": "2025-09-05T20:37:33Z", "downloadedAt": "2025-09-05T20:44:15.154Z", "source": "osrs_wiki_watcher", "rawWikitext": true}