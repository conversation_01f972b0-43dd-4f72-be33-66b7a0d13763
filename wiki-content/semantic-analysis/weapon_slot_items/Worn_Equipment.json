{"title": "Worn Equipment", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [], "relationships": [{"from": "Worn Equipment", "to": "crystal bow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "toxic blowpipe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "blessing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Decorative armour (quiver)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "<PERSON><PERSON><PERSON>'s quiver", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Bolt pouch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "minigame", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Items Kept on Death", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Barbarian Assault", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Clan Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "team cape", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Soul Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Blue icon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Red icon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "hairstyle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Equipment Stats", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Armour/Highest bonuses", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Calculator:Armoury", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Death", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Items Kept on Death", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Protect Item", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Guide Prices", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Weight", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": "Inventory", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Worn Equipment", "to": ":Category:Slot tables", "type": "references", "confidence": 0.7, "source": "wiki_link"}], "embedding": [0.011510416865348816, -0.08510380238294601, 0.01949181593954563, 0.029837841168045998, 0.03525235876441002, 0.020546643063426018, 0.07233447581529617, 0.031785860657691956, 0.0318799652159214, -0.017644664272665977, 0.021439507603645325, 0.05840592086315155, 0.02452407404780388, -0.04796988144516945, 0.05918341502547264, 0.020418578758835793, -0.05972844362258911, -0.02106867916882038, -0.07948481291532516, 0.056712813675403595, -0.0051894839853048325, -0.041541676968336105, -0.028423067182302475, 0.0004586198483593762, 0.10587318986654282, -0.03309706225991249, 0.020973384380340576, 0.006324172019958496, 0.017140261828899384, -0.041266489773988724, 0.07514654844999313, -0.008815804496407509, -0.002391957677900791, -0.053400345146656036, 2.086997255901224e-06, 0.005362437106668949, 0.03967767581343651, -0.018288902938365936, -0.08903078734874725, 0.008516785688698292, -0.018969960510730743, 0.019741736352443695, 0.07412443310022354, 0.03406118229031563, 0.04703955352306366, -0.1085764616727829, -0.023464059457182884, 0.07563269883394241, -0.04140462726354599, -0.013963065110147, -0.01010567881166935, 0.10047786682844162, -0.008058452047407627, -0.027309125289320946, 0.0606139674782753, -0.03346946835517883, 0.013336796313524246, 0.022071629762649536, 0.0736570730805397, 0.039715684950351715, 0.026095468550920486, 0.009386090561747551, -0.0018529415829107165, 0.0528898760676384, 0.0915326327085495, -0.0332413986325264, -0.017218610271811485, 0.02495795674622059, 0.01771342195570469, 0.02785992994904518, 0.015359176322817802, -0.016099879518151283, 0.0125812329351902, -0.05356719344854355, 0.008990945294499397, -0.03516625985503197, 0.01016081590205431, -0.033998411148786545, -0.03693719580769539, -0.0027647949755191803, 0.007898968644440174, 0.036711230874061584, 0.011042646132409573, 0.006604395806789398, -0.013422148302197456, 0.003745667403563857, -0.02359829470515251, 0.022922851145267487, -0.03132691979408264, 0.0008738074684515595, -0.02787747047841549, -0.002443093340843916, 0.01684822328388691, 0.0015237773768603802, -0.04383327439427376, 0.023740341886878014, 0.035858552902936935, -0.060373201966285706, 0.04141184687614441, 0.007349259220063686, -0.00491008535027504, 0.04592391103506088, -0.0572655163705349, 0.03292953595519066, -0.03846997395157814, -0.024421894922852516, -0.02627895399928093, -0.06707651913166046, -0.037603266537189484, 0.005053930915892124, 0.0038211543578654528, -0.039333295077085495, 0.008883673697710037, -0.0038723291363567114, -0.005825744941830635, -0.025897571817040443, 0.048411983996629715, -0.02219919115304947, 0.020922942087054253, -0.02483169361948967, 0.053753722459077835, -0.029401583597064018, 0.022447379305958748, -0.004633626434952021, 0.014842638745903969, -0.015466530807316303, 0.00996185839176178, 0.05201180651783943, -0.018672937527298927, -0.002297382801771164, -0.015666669234633446, -0.06387239694595337, 0.021899016574025154, -0.05488984286785126, -0.024602947756648064, -0.05789881572127342, -0.01184006780385971, -0.0074701569974422455, -0.04342726990580559, 0.008843174204230309, 0.04802221059799194, 0.05917118489742279, 0.024563897401094437, 0.004641218110918999, 0.01556735672056675, 0.006271337158977985, 0.028410684317350388, 0.026659807190299034, -0.012926151975989342, 0.013110024854540825, -0.058762840926647186, 0.005988053511828184, 0.017576754093170166, 0.020614223554730415, 0.020610129460692406, 0.01575341634452343, 0.020715802907943726, -0.012616834603250027, 0.0038586219307035208, -0.042494237422943115, -0.028530308976769447, -0.07620947808027267, -0.06457976251840591, -0.00949015375226736, -0.020388105884194374, -0.03031160868704319, -0.020099688321352005, -0.02048058621585369, 0.003404867136850953, -0.0006048859795555472, -0.02029772661626339, -0.0061718313954770565, -0.047717079520225525, 2.668191518750973e-05, -0.012586249969899654, -0.01044235285371542, 0.11659795045852661, -0.020608343183994293, -0.08159632980823517, -0.01738147996366024, 0.06868169456720352, -0.019603118300437927, 0.016927337273955345, 0.016440507024526596, 0.009731050580739975, -0.06319186091423035, -0.026420745998620987, -0.04434763267636299, -0.02198694460093975, -0.00034611410228535533, -0.015994610264897346, 0.010578722693026066, 0.046667225658893585, -0.07869777828454971, 0.005387287586927414, -0.05149013176560402, -0.02792615257203579, -0.053238723427057266, 0.007533715572208166, -0.014506269246339798, 0.005939219146966934, -0.017743760719895363, -0.03533722460269928, 0.10397765785455704, -0.0079897316172719, -0.02555587701499462, -0.11378983408212662, 0.05243935436010361, -0.011876286007463932, -0.010053997859358788, 0.0499800443649292, 0.04745157063007355, 0.002136237919330597, 0.004476298112422228, -0.0682300478219986, 0.017317619174718857, -0.05083901435136795, 0.0505896620452404, -0.08770047873258591, 0.0066836318001151085, 0.003089204430580139, -0.043480902910232544, -0.0058733513578772545, -0.0014816693728789687, -0.05514712631702423, -0.010583908297121525, 0.018574925139546394, -0.009667990729212761, -0.026114340871572495, -0.026826171204447746, 0.023008154705166817, -0.010287280194461346, 0.0018066593911498785, -0.0048768543638288975, 0.007465451490134001, -0.006495558191090822, -0.023260705173015594, 0.06514468789100647, -0.011954061686992645, 0.039243631064891815, 0.020145388320088387, -0.035665594041347504, 0.026669586077332497, -0.04204519838094711, -0.059308506548404694, 0.01103742141276598, -0.013910296373069286, -0.010905128903687, -0.03948952257633209, 0.005574028939008713, 0.022595055401325226, 0.024617822840809822, -0.0028391005471348763, 0.013732848688960075, -0.04895426705479622, 0.016806934028863907, -0.024052776396274567, 0.030605174601078033, -0.007495037745684385, -0.009271921589970589, 0.03735429048538208, -0.001232643029652536, 0.01779658906161785, 0.01573799178004265, -0.02582559920847416, -0.0060117533430457115, -0.02929137460887432, -0.013698914088308811, 0.0022306819446384907, 0.025898976251482964, 0.0022017306182533503, 0.023738719522953033, 0.0016781188314780593, -0.02923799306154251, 0.003559034550562501, -0.00425898190587759, 0.01718774251639843, 0.0002999190182890743, -0.010390227660536766, 0.006516518536955118, 0.032916754484176636, 0.03200162947177887, -0.019682757556438446, 0.02294841594994068, -0.02167908102273941, 0.008866597898304462, -0.04040691629052162, -0.05254031717777252, -0.07754629105329514, -0.05074847862124443, 0.03230354189872742, 0.011215093545615673, -0.0015538043808192015, -0.012040039524435997, -0.04318045452237129, 0.0034094664733856916, 0.015046614222228527, 0.033016834408044815, 0.003577461000531912, 0.04670819640159607, -0.0025864054914563894, 0.0014990635681897402, -0.003089186269789934, -0.039428070187568665, 0.0424078144133091, 0.03028123825788498, 0.0001394590362906456, 0.016517184674739838, -0.012059230357408524, 0.01178815308958292, 0.0285393875092268, 0.024142228066921234, -0.015381098724901676, -0.045921579003334045, 0.018862390890717506, -0.06241825222969055, 0.0034187408164143562, 0.02696828916668892, -0.018836606293916702, 0.011454219929873943, -0.012053940445184708, 0.055129799991846085, -0.005556917749345303, 0.03053371049463749, 0.023210670799016953, -0.012891937047243118, -0.004250889178365469, 0.035140592604875565, 0.004758902825415134, 0.050915881991386414, 0.023624034598469734, 0.0490230992436409, -0.047511760145425797, 0.025309506803750992, -0.04160171374678612, -0.025398429483175278, -0.04842006042599678, -0.08857610076665878, -0.02136942930519581, 0.02529117465019226, -0.002727797254920006, -0.01707148365676403, 0.021819088608026505, -0.0033610044047236443, 0.004055672790855169, -0.002435487462207675, -0.08344875276088715, 0.008408358320593834, -0.046949487179517746, -0.018620144575834274, 0.0176865104585886, 0.052256952971220016, 0.03838573023676872, 0.02974502369761467, -0.004889087751507759, -0.01780775561928749, 0.01393831055611372, -0.03152715787291527, 0.012170159257948399, 0.011570729315280914, -0.019157730042934418, 0.022757690399885178, -0.03563021868467331, 0.025947030633687973, -0.026096075773239136, -0.028922732919454575, -0.014721381478011608, 0.017598498612642288, -0.02382175251841545, -0.017659582197666168, 0.062250953167676926, 0.01372176967561245, 0.061479195952415466, 0.056064024567604065, -0.029258716851472855, -0.002294606063514948, 0.04987592250108719, 0.04639509320259094, -0.024404272437095642, -0.01123003102838993, 0.026266761124134064, -0.030160587280988693, 0.023725833743810654, -0.02243235893547535, 0.029286133125424385, -0.029444018378853798, -0.020294763147830963, 0.025819994509220123, -0.03245408460497856, -0.030702220275998116, 0.013379091396927834, 0.016284257173538208, -0.006590985227376223, 0.06616609543561935, 0.02222886122763157, 0.012451307848095894, 0.03496437892317772, 0.028045499697327614, 0.0017820574576035142, 0.025569956749677658, 0.0537739060819149, -0.06350380927324295, 0.030063336715102196, 0.002188293496146798, 0.013232910074293613, 0.060796644538640976, -0.0441078320145607, -0.01657112129032612, -0.018644167110323906, -0.06123778223991394, -0.06934922933578491, -0.020480113103985786, 0.02410433255136013, 0.0033597275614738464, 0.013460827060043812, 0.02303181029856205, -0.03778413310647011, -0.006990818306803703, 0.011418553069233894, -0.029907405376434326, -0.03139588609337807, -0.0007963487878441811, 0.016213063150644302, 0.005794018507003784, 0.056165631860494614, -0.021004382520914078, -0.11936920881271362, 0.01854090578854084, -0.010757364332675934, 0.02629357948899269, -0.03291069343686104, 0.05034733563661575, -6.358939572237432e-05, 0.02412295527756214, 0.0034036696888506413, 0.013727438636124134, -0.013667788356542587, -0.024372432380914688, 0.012844063341617584, -0.054651178419589996, -0.009083849377930164, 0.024013863876461983, 0.021185582503676414, 0.026496201753616333, 0.025542616844177246, -0.03746683523058891, 0.009397253394126892, -0.04535337910056114, -0.03391917422413826, -0.00936873722821474, 0.013614112511277199, -0.0016725355526432395, -0.029693275690078735, -0.03586164489388466, -0.059751834720373154, -0.044121868908405304, 0.05495944619178772, 0.02891121804714203, -0.02997732348740101, 0.024743076413869858, 0.013044921681284904, 0.014512385241687298, -0.10045439004898071, -0.015020789578557014, -0.03296886384487152, -0.06361409276723862, -0.055554069578647614, 0.0022038896568119526, 0.013085179030895233, -0.02019527181982994, -0.0718015804886818, 0.035604555159807205, -0.012984802015125751, -0.01257609948515892, -0.02489885874092579, -0.02614232711493969, -0.014105643145740032, 0.02489342726767063, -0.047318603843450546, 0.01969865709543228, -0.023020638152956963, 0.03427227959036827, 0.01072755828499794, 0.018109071999788284, -0.01955418288707733, -0.0017292485572397709, 0.034635379910469055, -0.015681175515055656, 0.008984196931123734, 0.07800416648387909, 0.0022325082682073116, -0.02172534167766571, -0.05862962082028389, 0.023787444457411766, 0.024720849469304085, 0.06091999262571335, 0.030771760269999504, -0.057371269911527634, -0.0010396643774583936, -0.05608970671892166, -0.004281970672309399, 0.04821702465415001, -0.0037934244610369205, -0.06124940887093544, 0.005374805070459843, -0.0007419751491397619, 0.015347233973443508, 0.048449937254190445, -0.024691052734851837, -0.020089097321033478, 0.02014036476612091, 0.02985052391886711, 0.009236263111233711, -0.020294496789574623, 0.04675879701972008, 0.07058269530534744, -0.07996714860200882, -0.0515887513756752, 0.04228392243385315, 0.04974103718996048, 0.010901959612965584, -0.048213765025138855, 0.030642224475741386, -0.017239345237612724, 0.09797148406505585, -0.023653190582990646, 0.014248580671846867, 0.00824244599789381, -0.007755480241030455, 0.01518283411860466, -0.007318983785808086, 0.00585096376016736, 0.01791687309741974, 0.0032003363594412804, 0.0014327801764011383, 0.0775325745344162, 0.03406320512294769, 0.02800864726305008, -0.02783866412937641, 0.034228261560201645, 0.02636953815817833, -0.009171283803880215, 0.005456398241221905, -0.0032528885640203953, -0.0015650829300284386, -0.00033876695670187473, -0.05314106494188309, -0.006154621485620737, -0.033668890595436096, 0.06774692982435226, 0.00734354555606842, -0.02563011646270752, -0.09444714337587357, 0.02788769267499447, 0.033268071711063385, -6.005809288598338e-33, 0.013481443747878075, -0.0027901814319193363, -0.0032127515878528357, 0.03992942348122597, -0.0028783571906387806, 0.03950260579586029, -0.045457515865564346, 0.014025615528225899, -0.002784091979265213, -0.00852979812771082, 0.02032293938100338, -0.01771607995033264, -0.0006676711491309106, -0.014843267388641834, 0.03141676262021065, 0.009237643331289291, 0.006124916020780802, 0.04286941513419151, 0.009030104614794254, 0.011528005823493004, 0.05444955825805664, 0.025821203365921974, 0.06519513577222824, -0.026824846863746643, 0.021823611110448837, 0.010568879544734955, 0.05169261619448662, 0.020643364638090134, 0.003352452302351594, 0.01948663778603077, -0.06893640011548996, -0.008192433044314384, -0.008800626732409, 0.028119025751948357, 0.024556275457143784, 0.08153413236141205, -0.02007417380809784, -0.02810928225517273, 0.017325591295957565, 0.048614464700222015, 0.04176666960120201, -0.021442044526338577, -0.04314463958144188, 0.00551755353808403, 0.02084052748978138, -0.010872140526771545, -0.009976848028600216, -0.002781626069918275, 0.00749701913446188, 0.05957883968949318, 0.08656346052885056, 0.002093738876283169, 0.015387814491987228, -0.059068746864795685, -0.052352145314216614, 0.03015620820224285, -0.013204007409512997, -0.03208576887845993, -0.03500783070921898, -0.003605092642828822, -0.010337804444134235, -0.009903793223202229, -0.04107897728681564, 0.016658540815114975, 0.03997492790222168, 0.05025316774845123, -0.00567657221108675, 0.010687688365578651, -0.0008847703575156629, 0.01382436417043209, -0.03096950054168701, 0.014575271867215633, 0.011700809933245182, -0.06983262300491333, 0.13018645346164703, 0.0064337365329265594, 0.05113186314702034, 0.0076494780369102955, 0.03889205679297447, 0.07752769440412521, -0.021163281053304672, 0.07605694979429245, -0.0143186179921031, 0.020606208592653275, -0.037960562855005264, -0.04248727858066559, 0.02145131304860115, -0.03306246176362038, 0.029829664155840874, -0.024875663220882416, -0.046328190714120865, 0.05160975828766823, 0.0009849636116996408, -0.006365853361785412, 0.014574272558093071, -0.027613943442702293, 0.06299158930778503, -0.059006694704294205, -0.016435060650110245, 0.002111404202878475, -0.006819961126893759, 0.03182017803192139, 0.015904167667031288, -0.02137165702879429, -0.026681626215577126, -0.03867410495877266, 0.02291349694132805, 0.025756580755114555, -0.03158695623278618, -0.0026412461884319782, -0.02371929958462715, -0.04626019299030304, -0.033323824405670166, 0.024623146280646324, 0.019292356446385384, 0.006624080706387758, -0.008055685088038445, 0.03368464857339859, -0.007252003997564316, -0.09915965795516968, 0.016488350927829742, -0.024144675582647324, -0.0031430297531187534, -0.045554183423519135, -0.020772242918610573, 0.008528723381459713, -0.03782251849770546, 0.031252581626176834, 0.04944978654384613, 0.062149934470653534, 0.017669053748250008, 0.03127576783299446, 2.740456466199248e-07, 0.028015349060297012, -0.011681107804179192, 0.021049238741397858, -0.10756285488605499, 0.0356648713350296, -0.03295591101050377, -0.013658407144248486, 0.006013105623424053, 0.015675216913223267, -0.0915825217962265, 0.03363985940814018, 0.06912649422883987, 0.005972273647785187, -0.005033326335251331, 0.08093082904815674, -0.09652877599000931, 0.09508591145277023, -0.011783364228904247, -0.024405714124441147, 0.03364326059818268, 0.018731122836470604, -0.03607621416449547, 0.020142322406172752, 0.012708023190498352, 0.00012174867879366502, -0.0747184082865715, 0.01472694892436266, -0.01257386989891529, 0.08114106953144073, -0.08392636477947235, 0.044135794043540955, 0.02059311419725418, -0.008509892970323563, -0.03529706597328186, 0.04999183118343353, 0.020254328846931458, 0.03630027547478676, 0.05900327488780022, 0.03287690505385399, 0.053338244557380676, -0.03038042038679123, -0.019375266507267952, -0.030716372653841972, 0.014294737949967384, -0.003698919899761677, 0.0036310344003140926, 0.0004533477476797998, 0.001357500208541751, -0.04284657537937164, 0.010346878319978714, -0.032278429716825485, 0.01529064029455185, 0.04020051285624504, -0.021463919430971146, -0.011694921180605888, -0.02390051819384098, -0.02059181034564972, -0.01217392086982727, 0.000330130074871704, -0.004377659410238266, -0.015769433230161667, 0.006926853209733963, 0.02546507492661476, -0.0703875944018364, 0.001236459007486701, -0.03513628616929054, -0.018057014793157578, 2.115755292478246e-34, -0.03675573319196701, -0.007320411503314972, -0.012133876793086529, -0.026501508429646492, 0.05036897957324982, 0.0018643717048689723, 0.013458617962896824, 0.032043229788541794, 0.01617986336350441, -0.05441289767622948, -0.023397069424390793], "parsedContent": {"infobox": {}, "categories": [], "links": ["crystal bow", "toxic blowpipe", "blessing", "Decorative armour (quiver)", "<PERSON><PERSON><PERSON>'s quiver", "Bolt pouch", "minigame", "Items Kept on Death", "Barbarian Assault", "Clan Wars", "team cape", "Soul Wars", "Blue icon", "Red icon", "hairstyle", "Equipment Stats", "Armour/Highest bonuses", "Calculator:Armoury", "Death", "Items Kept on Death", "Protect Item", "Guide Prices", "Weight", "Inventory", ":Category:Slot tables"], "plainText": "The worn equipment interface. left The Worn Equipment tab, also called the equipped tab, the worn items screen, or the equipped inventory, shows all the equipment that the player is wearing or wielding. The equipped inventory is divided into 11 slots for each area of the body that some piece of equipment can be worn on. Since the 11 slots can each be equipped with an item which then no longer appears in the backpack inventory, a player can effectively carry 39 items with them at once. The slots and what they can be equipped with are listed below. Head slot link=Head slot table This slot tends to confer low defensive bonuses, but high offensive ones. Off-style helmets tend to have the least penalizing offensive drawbacks as well, making this slot a good choice to lean into the advantages of another combat style that doesn't match the one of your weapon. Cape slot link=Cape slot table Capes provide relatively small bonuses, but they typically provide them without any drawbacks to other combat styles. A variety of capes provide utility functions, making this slot valuable for non-combat purposes as well. Neck slot link=Neck slot table Neck slot items tend to provide strong offensive bonuses for their appropriate combat style, or a wide variety of non-combat functions, similar to rings. Ammunition slot link=Ammunition slot table When using bows, crossbows, or ballistae, a player needs ammunition to fire from their weapon; arrows, bolts, or javelins. Ammunition cannot be used from the inventory; it must be equipped to the ammunition slot. When not using ranged, or using ranged weapons that provide their own ammo such as the crystal bow or toxic blowpipe, you can instead equip various blessings for a small prayer bonus and other perks. It is common to refer to this slot as the 'quiver', despite actual quivers that exist being equipped to the cape slot. Extra ammo slot The Dizana's quiver and Bolt pouch provide an extra ammo slot, which may be clicked to switch the ammo being used. Weapon slot link=Weapon slot table Your choice of weapon in the main hand. Determines your combat style, affecting what type of damage you deal and what offensive advantages apply from the rest of your equipment. Some skilling tools can be equipped in this slot as well. Shield slot link=Shield slot table Shield slot equipment often provides specific combat utility required for fighting particular monsters, such as protection against the fiery breath of dragons. If no specific utility is required, there are powerful offensive or defensive options. Two-handed items link=Two-handed slot table This type of equipment occupies both the weapon and shield slots, so when equipped, both items will be removed. This means you cannot equip a two-handed item if your inventory is full and both of those slots are occupied. Body slot link=Body slot table The body item of an armour set tends to provide both the highest defensive benefits for its combat style as well as the highest offensive penalties for off-style attacks. Items in this slot very rarely have non-combat utility. Legs slot link=Legs slot table Leg items tend to fill extremely similar roles as body items, with the same pronounced defensive strengths and offensive weaknesses, though generally somewhat weaker. Because they are two different slots, you can mix and match equipment sets to reach more balanced bonuses. Hands slot link=Hand slot table The hand slot tend to be a great source of offensive bonuses, and commonly benefits all combat styles at once. Feet slot link=Feet slot table The gear available for this slot is very similar to hand slot gear, but tends to have more emphasis on defence than offence. It also provides less equipment with combat style flexibility than the hand slot. Similar to the shield slot, equipment necessary for negating penalizing effects are often feet slot items as well. Ring slot link=Ring slot table The ring slot has many equipment choices with abilities not available any other way; this tends to make this slot very competitive. Utility functions such as teleportation are also very frequently available via the ring slot. The rings that do provide statistical bonuses tend to be similar in magnitude to the neck slot, though typically must be imbued to reach their full potential. Jaw slot 36px The jaw slot (or beard slot) is a hidden slot in which facial hair is placed. Items in the jaw slot are typically used to achieve various benefits within a minigame without having to sacrifice a worn item, such as a cape. Jaw slot items may be seen on the Items Kept on Death interface. Examples of jaw slot items include Barbarian Assault role icons, which were once worn in the cape slot, but moved to the jaw slot to allow capes to be equipped within the minigame. Another example can be seen in Clan Wars, where imaginary team capes are placed in the jaw slot so that a player's team mate will have their left-click \"Attack\" option moved to a right-click option without having to actually wear a team cape. Jaw slot items are also used in Soul Wars, where Blue icon or Red icon are placed in the jaw slot. Items in the jaw slot also remove existing facial hair from the player's model. To prevent players from being affected, a fake beard is built into the equipped model to appear as if it was never removed. For example, each Barbarian Assault role icon has 16 different variants, one for each facial hairstyle that currently exists. Trivia *When items are equipped, players used to have the option to \"Operate\" them, regardless of them having a secondary function or not. For items that did not have a special function, the player would receive a message saying, \"There is no way to operate that item,\" if they attempted to do so. See also *Equipment Stats *Armour/Highest bonuses *Armoury calculator *Death **Items Kept on Death **Protect Item *Guide Prices *Weight *Inventory *:Category:Slot tables References"}, "originalContent": "{{External|rs}}\n{{Redirect|Equipment|a table of best-in-slot equipment|Armour/Highest bonuses}}\n[[File:Worn Equipment tab.png|frame|The worn equipment interface.]]\n[[File:Worn Equipment.png|left]]\nThe '''Worn Equipment''' tab, also called the '''equipped''' tab, the '''worn items''' screen, or the '''equipped inventory''', shows all the equipment that the player is wearing or wielding. The equipped inventory is divided into 11 slots for each area of the body that some piece of equipment can be worn on. Since the 11 slots can each be equipped with an item which then no longer appears in the backpack inventory, a player can effectively carry 39 items with them at once. The slots and what they can be equipped with are listed below.\n\n==Head slot==\n{{Otheruses|def=no|a list of items in this slot|Head slot table}}\n[[File:Head slot.png|left|link=Head slot table]]\nThis slot tends to confer low defensive bonuses, but high offensive ones. Off-style helmets tend to have the least penalizing offensive drawbacks as well, making this slot a good choice to lean into the advantages of another combat style that doesn't match the one of your weapon.\n\n==Cape slot==\n{{Otheruses|def=no|a list of items in this slot|Cape slot table}}\n[[File:Cape slot.png|left|link=Cape slot table]]\nCapes provide relatively small bonuses, but they typically provide them without any drawbacks to other combat styles. A variety of capes provide utility functions, making this slot valuable for non-combat purposes as well.\n\n==Neck slot==\n{{Otheruses|def=no|a list of items in this slot|Neck slot table}}\n[[File:Neck slot.png|left|link=Neck slot table]]\nNeck slot items tend to provide strong offensive bonuses for their appropriate combat style, or a wide variety of non-combat functions, similar to rings.\n\n==Ammunition slot==\n{{Otheruses|def=no|a list of items in this slot|Ammunition slot table}}\n[[File:Ammo slot.png|left|link=Ammunition slot table]]\nWhen using bows, crossbows, or ballistae, a player needs ammunition to fire from their weapon; arrows, bolts, or javelins. Ammunition cannot be used from the inventory; it must be equipped to the ammunition slot. When not using ranged, or using ranged weapons that provide their own ammo such as the [[crystal bow]] or [[toxic blowpipe]], you can instead equip various [[blessing]]s for a small prayer bonus and other perks. It is common to refer to this slot as the 'quiver', despite actual [[Decorative armour (quiver)|quivers]] that exist being equipped to the cape slot.\n\n===Extra ammo slot===\nThe [[Dizana's quiver]] and [[Bolt pouch]] provide an extra ammo slot, which may be clicked to switch the ammo being used.\n\n==Weapon slot==\n{{Otheruses|def=no|a list of items in this slot|Weapon slot table}}\n[[File:Weapon slot.png|left|link=Weapon slot table]]\nYour choice of weapon in the main hand. Determines your combat style, affecting what type of damage you deal and what offensive advantages apply from the rest of your equipment. Some skilling tools can be equipped in this slot as well.\n\n==Shield slot==\n{{Otheruses|def=no|a list of items in this slot|Shield slot table}}\n[[File:Shield slot.png|left|link=Shield slot table]]\nShield slot equipment often provides specific combat utility required for fighting particular monsters, such as protection against the fiery breath of dragons. If no specific utility is required, there are powerful offensive or defensive options.\n\n==Two-handed items==\n{{Otheruses|def=no|a list of items in this slot|Two-handed slot table}}\n[[File:2h slot.png|left|link=Two-handed slot table]]\nThis type of equipment occupies both the weapon and shield slots, so when equipped, both items will be removed. This means you cannot equip a two-handed item if your inventory is full and both of those slots are occupied.\n\n==Body slot==\n{{Otheruses|def=no|a list of items in this slot|Body slot table}}\n[[File:Body slot.png|left|link=Body slot table]]\nThe body item of an armour set tends to provide both the highest defensive benefits for its combat style as well as the highest offensive penalties for off-style attacks. Items in this slot very rarely have non-combat utility.\n\n==Legs slot==\n{{Otheruses|def=no|a list of items in this slot|Legs slot table}}\n[[File:Legs slot.png|left|link=Legs slot table]]\nLeg items tend to fill extremely similar roles as body items, with the same pronounced defensive strengths and offensive weaknesses, though generally somewhat weaker. Because they are two different slots, you can mix and match equipment sets to reach more balanced bonuses.\n\n==Hands slot==\n{{Otheruses|def=no|a list of items in this slot|Hand slot table}}\n[[File:Hands slot.png|left|link=Hand slot table]]\nThe hand slot tend to be a great source of offensive bonuses, and commonly benefits all combat styles at once.\n\n==Feet slot==\n{{Otheruses|def=no|a list of items in this slot|Feet slot table}}\n[[File:Boots slot.png|left|link=Feet slot table]]\nThe gear available for this slot is very similar to hand slot gear, but tends to have more emphasis on defence than offence. It also provides less equipment with combat style flexibility than the hand slot. Similar to the shield slot, equipment necessary for negating penalizing effects are often feet slot items as well.\n\n==Ring slot==\n{{Otheruses|def=no|a list of items in this slot|Ring slot table}}\n[[File:Ring slot.png|left|link=Ring slot table]]\nThe ring slot has many equipment choices with abilities not available any other way; this tends to make this slot very competitive. Utility functions such as teleportation are also very frequently available via the ring slot. The rings that do provide statistical bonuses tend to be similar in magnitude to the neck slot, though typically must be imbued to reach their full potential.\n\n==Jaw slot==\n[[File:Monkey dentures detail.png|left|36px]]\nThe jaw slot (or beard slot) is a hidden slot in which facial hair is placed.{{CiteForum|author = Mod Wolf|url = https://archive.fo/X9ifv|title = Poll 67 Updates and Hosidius Improvements|forum = Reddit|postdate = 30 May 2019}} Items in the jaw slot are typically used to achieve various benefits within a [[minigame]] without having to sacrifice a worn item, such as a cape. Jaw slot items may be seen on the [[Items Kept on Death]] interface.\n\nExamples of jaw slot items include [[Barbarian Assault]] role icons, which were once worn in the cape slot, but moved to the jaw slot to allow capes to be equipped within the minigame. Another example can be seen in [[Clan Wars]], where imaginary [[team cape]]s are placed in the jaw slot so that a player's team mate will have their left-click \"Attack\" option moved to a right-click option without having to actually wear a team cape.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1134141975995781120|date=30 May 2019|archiveurl=https://archive.fo/7FnBw|archivedate=6 June 2019|quote=Clan Wars [uses the jaw slot] too, to give a team cape effect without you needing the cape.}} Jaw slot items are also used in [[Soul Wars]], where [[Blue icon]] or [[Red icon]] are placed in the jaw slot.\n\nItems in the jaw slot also remove existing facial hair from the player's model. To prevent players from being affected, a fake beard is built into the equipped model to appear as if it was never removed.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1134035660447330304|date=30 May 2019|archiveurl=https://archive.fo/jzRA8|archivedate=6 June 2019|quote=This deletes your beard if you have one, so the [Barbarian Assault] icons have fake beards built into them so that your appearance doesn't change.}} For example, each Barbarian Assault role icon has 16 different variants, one for each facial [[hairstyle]] that currently exists.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1134141975995781120|date=30 May 2019|archiveurl=https://archive.vn/7FnBw|archivedate=6 June 2019|quote=[A jaw slot item] actually does replace your beard - so the 4 [Barbarian Assault] icons now have multiple alternate versions, one for each beard style, so that the model is of icon+beard.}}\n\n==Trivia==\n*When items are equipped, players used to have the option to \"Operate\" them, regardless of them having a secondary function or not. For items that did not have a special function, the player would receive a message saying, \"There is no way to operate that item,\" if they attempted to do so.\n\n==See also==\n*[[Equipment Stats]]\n*[[Armour/Highest bonuses]]\n*[[Calculator:Armoury|Armoury calculator]]\n*[[Death]]\n**[[Items Kept on Death]]\n**[[Protect Item]]\n*[[Guide Prices]]\n*[[Weight]]\n*[[Inventory]]\n*[[:Category:Slot tables]]\n\n==References==\n{{Reflist}}\n\n{{Interface}}\n{{Equipment class}}"}