{"title": "Chinchompa (weapon)", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [], "relationships": [{"from": "Chinchompa (weapon)", "to": "Maniacal monkey/Strategies", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Chinchompa (weapon)", "to": "members", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Chinchompa (weapon)", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Chinchompa (weapon)", "to": "box trap", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Chinchompa (weapon)", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Chinchompa (weapon)", "to": "<PERSON><PERSON><PERSON> spells", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Chinchompa (weapon)", "to": "barrage spells", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Chinchompa (weapon)", "to": "successful hit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Chinchompa (weapon)", "to": "Defence", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Chinchompa (weapon)", "to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}], "embedding": [1.9634475393104367e-05, -0.09637770056724548, 0.021349960938096046, -0.008023742586374283, -0.008019580505788326, 0.0057640098966658115, -0.02052580565214157, -0.005705744959414005, 0.052931543439626694, -0.027003372088074684, -0.005511956289410591, 0.01910203881561756, 0.03940459340810776, -0.03281387686729431, 0.007157295010983944, -0.05066268891096115, 0.015837812796235085, 0.022284824401140213, -0.040296461433172226, 0.004911340307444334, 0.02473589964210987, -0.06541675329208374, -0.055520981550216675, 0.0782759040594101, 0.014830312691628933, -0.07976935803890228, -0.02434464544057846, -0.051854632794857025, -0.010320713743567467, -0.07396526634693146, 0.11038736999034882, -0.04932386800646782, 0.007378098089247942, 0.038476042449474335, 2.106959755110438e-06, 0.003069002879783511, 0.042687028646469116, 0.015092169865965843, -0.031787194311618805, 0.027245983481407166, 0.03313637897372246, -0.040530186146497726, -0.024119669571518898, 0.027002859860658646, 0.01298210397362709, -0.01900937594473362, 0.003885466605424881, 0.046598177403211594, 0.03065606951713562, -0.0028528813272714615, 0.005428778938949108, -0.06705212593078613, -0.009214668534696102, -0.04148373752832413, 0.04448041319847107, -0.05095740035176277, 0.020531006157398224, 0.013471152633428574, -0.0020546610467135906, 0.04742414131760597, -0.04020564630627632, -0.06332558393478394, -0.058367758989334106, -0.02453220635652542, 0.012380135245621204, -0.024853339418768883, -0.043595992028713226, -0.003249891335144639, -0.019208528101444244, 0.023315398022532463, -0.06527267396450043, -0.04390869662165642, -0.004523203242570162, -0.04541947692632675, -0.007719710003584623, -0.04794618487358093, -0.028904782608151436, -0.008446800522506237, 0.01505223661661148, 0.006999386940151453, 0.09127803891897202, 0.0642571821808815, -0.021009454503655434, -0.029389385133981705, 0.03577308729290962, -0.014709710143506527, -0.004401339218020439, 0.022544723004102707, -0.030283765867352486, -0.02914588339626789, 0.07762888073921204, -0.010236016474664211, -0.0114954374730587, 0.051294464617967606, -0.020206894725561142, 0.0025857482105493546, 0.006245135795325041, -0.008191509172320366, 0.050395529717206955, -0.054323237389326096, 0.02268344908952713, 0.017440276220440865, -0.001318755792453885, 0.02278743125498295, 0.003412957303225994, -0.01806802675127983, 0.0482918918132782, 0.009507841430604458, -0.02435421384871006, 0.006883700378239155, -0.02956063114106655, -0.018924063071608543, 0.0832701027393341, -0.03401061147451401, 0.03471451252698898, -0.00882155355066061, -0.037357013672590256, 0.008145391009747982, -0.036821067333221436, 0.01941775530576706, -0.003865349106490612, 0.03701793774962425, 0.006771158427000046, 0.009089244529604912, 0.007156130857765675, 0.08169884979724884, -0.02071242965757847, 0.0029039806686341763, -0.01957247219979763, 0.012406928464770317, -0.04008617624640465, 0.012284614145755768, -0.05651528015732765, 8.819648064672947e-05, 0.003290950320661068, 0.06379300355911255, -0.040615811944007874, -0.011342059820890427, 0.030734455212950706, 0.01873200759291649, 0.035817526280879974, -0.0008048776653595269, 0.006091727409511805, 0.012939868494868279, -0.01211061142385006, -0.021127719432115555, 0.024464886635541916, -0.01614491455256939, -0.00797379668802023, -0.017324447631835938, -0.017698824405670166, -0.014161975122988224, 0.02764749899506569, 0.03703295812010765, 0.015296315774321556, 0.030662555247545242, -0.051034778356552124, 0.03712250664830208, -0.04560716450214386, 0.03378638997673988, 0.04844816029071808, -0.01089601032435894, 0.031117429956793785, -0.03133430704474449, -0.04952141270041466, -0.004493681248277426, -0.018013548105955124, 0.004873636644333601, 0.0351577028632164, -0.08387108147144318, 0.008575156331062317, -0.025259628891944885, -0.029131513088941574, 0.009258373640477657, -0.031102683395147324, 0.04266147315502167, 0.031201111152768135, -0.055146727710962296, -0.04517999663949013, -0.00980133656412363, 0.013116509653627872, 0.026876995339989662, 0.047423817217350006, -0.025288740172982216, -0.02385753206908703, -0.04978172481060028, -0.02568463608622551, -0.00014796576579101384, -0.054574042558670044, -0.026872552931308746, 0.01467060949653387, -0.02953074499964714, -0.013883812353014946, -0.020024487748742104, -0.02010580711066723, -0.018899554386734962, 0.10379499197006226, -0.022682027891278267, -0.016142727807164192, 0.06675074994564056, -0.030656836926937103, -0.01492885872721672, 0.01013725996017456, 0.011741738766431808, -0.05762812867760658, -0.0201726071536541, -0.0478237085044384, 0.05886182561516762, -0.09602401405572891, -0.028378447517752647, -0.029640858992934227, -0.041206225752830505, 0.00902382843196392, 0.05288785696029663, 0.024448689073324203, 0.0572764091193676, 0.03725617751479149, -0.014682027511298656, 0.02075684629380703, -0.0929696261882782, 0.03518616408109665, 0.08801522105932236, 0.029041901230812073, 0.022929903119802475, -0.044908151030540466, 0.0009964293567463756, -0.013609596528112888, 0.009144134819507599, -0.02214076742529869, -0.030753478407859802, 0.0380118303000927, 0.01422981545329094, 0.0066027091816067696, 0.028151651844382286, -0.03473775088787079, -0.02626449428498745, 0.10847412794828415, -0.008339738473296165, 0.024128317832946777, -0.034804247319698334, 0.040542878210544586, -0.051040008664131165, 0.010543319396674633, -0.02838178165256977, 0.03713103011250496, 0.04672330245375633, -0.06649231910705566, 0.007177260704338551, 0.02061242237687111, -0.03788096830248833, 0.02676866576075554, 0.017947595566511154, 0.0007463947404175997, 0.029679356142878532, -0.04104889929294586, -0.023298561573028564, -0.010139710269868374, -0.005613036919385195, -0.018721427768468857, -0.03774983808398247, 0.016790231689810753, 0.005976508371531963, 0.012800153344869614, -0.057204946875572205, 0.061844270676374435, -0.012758234515786171, 0.007478728890419006, -0.037654243409633636, 0.059979815036058426, 0.00963131245225668, 0.0048868414014577866, -0.005291637498885393, -0.03776814043521881, -0.03250102326273918, 0.00733698857948184, -0.03924176096916199, -0.03214123845100403, -0.029177922755479813, -0.010357464663684368, 0.02692253701388836, -0.02247581258416176, 0.028817662969231606, -0.0418105386197567, -0.013636643998324871, 0.06407592445611954, 0.020788483321666718, 0.0011991545325145125, -0.0018697690684348345, -0.0044682081788778305, -0.06755002588033676, -0.007339890114963055, 0.004382689017802477, -0.013502722606062889, -0.029705209657549858, -0.003863465739414096, 0.02241653762757778, -0.030657371506094933, 0.07095351070165634, -0.003557994496077299, 0.04916204884648323, 0.02728230692446232, 0.03998367860913277, 0.003379669040441513, -0.024818146601319313, -0.027556821703910828, -0.010208368301391602, 0.04284806177020073, 0.008186505176126957, 0.014682123437523842, 0.060169897973537445, 0.005897895898669958, -0.024131128564476967, -0.00037348843761719763, -0.041445594280958176, 0.033510852605104446, 0.014782015234231949, -0.0075482819229364395, -0.04080083221197128, -0.01811891421675682, -0.08397231996059418, -0.004444740246981382, 0.04441864788532257, -0.027788309380412102, -0.037558626383543015, 0.014172869734466076, 0.016448795795440674, -0.010389374569058418, -0.058291856199502945, 0.039081376045942307, 0.02945137210190296, -0.01592790149152279, 0.003481539897620678, 0.028640899807214737, 0.0632089301943779, 0.03953069821000099, -0.011426819488406181, -0.048980798572301865, -0.040745045989751816, -0.010853667743504047, -0.01551735494285822, -0.04415765777230263, 0.024893391877412796, -0.03387147933244705, -0.0023924652487039566, -0.025958042591810226, 0.04790598154067993, 0.020447542890906334, 0.03493243828415871, 0.07297533005475998, -0.001967690885066986, 0.028383078053593636, -0.05646664276719093, -0.041131969541311264, 0.054403092712163925, -0.06748213618993759, -0.02568570338189602, -0.01347626093775034, 0.006093690637499094, -0.010438922792673111, 0.045413821935653687, 0.09985722601413727, 0.033240314573049545, 0.0755961686372757, -0.0037044542841613293, 0.009576469659805298, -0.020252589136362076, -0.011439194902777672, 0.03828242048621178, -0.07372519373893738, -0.018576908856630325, 0.02452724613249302, 0.0067571052350103855, 0.010238216258585453, 0.003724731272086501, 0.014902342110872269, 0.02560054138302803, -0.01997491531074047, 0.03653843328356743, 0.028495240956544876, 0.03312411531805992, 0.013774171471595764, -0.014975386671721935, -0.029418956488370895, -0.02056875079870224, -0.0024785965215414762, -0.01544016133993864, -0.010668524540960789, -0.004759105388075113, -0.07684273272752762, 0.00698234885931015, 0.026246190071105957, 0.005446522496640682, -0.038891587406396866, 0.02620764635503292, 0.029973601922392845, -0.01387233566492796, 0.0905836671590805, 0.06652335822582245, -0.0076591214165091515, 0.031013572588562965, 0.007950808852910995, -0.009418919682502747, 0.0014621693408116698, 0.0286382008343935, -0.03935641795396805, 0.055372755974531174, -0.0406733974814415, -0.03497793525457382, 0.02830156870186329, 0.027804777026176453, -0.026288973167538643, -0.039462000131607056, 0.0037260253448039293, 0.03361760079860687, -0.042768292129039764, 0.0007863916689530015, 0.025904597714543343, 0.03639606386423111, -0.01065394189208746, 0.011226237751543522, -0.038152944296598434, 0.021617937833070755, 0.04271750897169113, -0.052254658192396164, 0.03872980177402496, -0.029542284086346626, -0.002375848824158311, -0.02332117222249508, 0.03226224705576897, 0.04816975072026253, -0.06964100152254105, -0.010303557850420475, -0.03772560879588127, -0.06451810151338577, 0.04793965071439743, -0.049214743077754974, 0.07606279104948044, -0.07566589117050171, -0.04785099998116493, -0.08090326189994812, -0.011999892070889473, 0.011696402914822102, 0.027673201635479927, 0.04055771604180336, 0.022712968289852142, -0.06570051610469818, -0.016783611848950386, 0.03371071815490723, -0.0822862908244133, -0.000662861333694309, -0.04041126370429993, -0.011746089905500412, 0.10956312716007233, 0.045427653938531876, 0.023121830075979233, 0.009797410108149052, 0.014089000411331654, -0.08745872229337692, -0.02552684396505356, -0.004716519266366959, -0.016401736065745354, 0.02502799592912197, -0.04796825721859932, -0.026432009413838387, -0.028631454333662987, -0.05554194748401642, -0.009904547594487667, 0.006802866235375404, 0.017933513969182968, -0.011723451316356659, -0.033065713942050934, -0.009978996589779854, -0.028929168358445168, 0.018651405349373817, 0.022683821618556976, -0.02138092927634716, 0.017621083185076714, -0.06884580105543137, 0.05466242879629135, 0.02584279514849186, -0.0014349237317219377, 0.03153582289814949, 0.044625286012887955, -0.00663656136021018, 0.0025590339209884405, 0.0036444691941142082, -0.028651846572756767, -0.02002709172666073, 0.031180448830127716, -0.02475564554333687, -0.04233613982796669, -0.024500099942088127, 0.0010985883418470621, 0.0017579467967152596, 0.03014625422656536, 0.10457441955804825, 0.01581556163728237, -0.02088540606200695, -0.021221041679382324, -0.01202512439340353, -0.07856743037700653, 0.030194956809282303, 0.025707101449370384, 0.010244760662317276, -0.022157223895192146, -0.012032674625515938, 0.009159590117633343, 0.01687915250658989, 0.005428504664450884, 0.05565350502729416, 0.05947108194231987, -0.05768173933029175, -0.04711849242448807, 0.016003744676709175, 0.039049096405506134, 0.007251331117004156, 0.027659578248858452, 0.021272502839565277, -0.014552829787135124, 0.0008167382911778986, -0.007882115431129932, -0.01405642181634903, 0.00700400723144412, -0.038363050669431686, -0.01699906401336193, -0.0031913090497255325, -9.610012966732029e-06, -0.010298904962837696, -0.010600965470075607, 0.047551229596138, -0.001794841606169939, 0.001205962267704308, 0.0465875007212162, -0.020830553025007248, -0.016624068841338158, -0.004667629487812519, 0.02681686542928219, -0.029215946793556213, 0.04930498078465462, -0.05761989206075668, -0.03165673837065697, -0.02749248966574669, 0.007544906809926033, 0.0064873527735471725, 0.012274726293981075, 0.014636890031397343, -0.02741004340350628, -0.004330395720899105, 0.023810535669326782, 0.04788375645875931, -0.0174719151109457, 0.01124302577227354, -0.0032328800298273563, 0.039696212857961655, 0.03758517652750015, -6.605616301364949e-33, -0.030340515077114105, -0.001877910690382123, 0.0056743742898106575, -0.07227261364459991, 0.012446566484868526, -0.018303681164979935, -0.014985225163400173, 0.05069781467318535, -0.015959441661834717, -0.03339196369051933, -0.02060307003557682, 0.0054170419462025166, 0.011975265108048916, 0.00882756058126688, 0.0468955859541893, -0.0318278968334198, -0.04427722841501236, -0.021707993000745773, 0.015646066516637802, -0.07842215895652771, 0.06094488129019737, 0.01022733747959137, -0.01818314753472805, 0.03866996243596077, 0.019793298095464706, 0.0029603647999465466, -0.02566428668797016, 0.0004571302270051092, -0.03441973403096199, 0.007406045217067003, -0.031242575496435165, -0.03527508303523064, 0.017356667667627335, 0.06387539207935333, -0.0032804382499307394, -0.028811270371079445, 0.02126125805079937, 0.0012993979034945369, -0.031038282439112663, 0.03124728426337242, 0.03683874383568764, -0.04176883399486542, 0.021239515393972397, 0.0055937799625098705, 0.004235060419887304, -0.05151303857564926, -0.041583068668842316, -0.0846414864063263, 0.017093416303396225, -0.027004938572645187, 0.08967845886945724, -0.014899383299052715, 0.0077268402092158794, -0.011877574026584625, -0.10427415370941162, -0.003957205452024937, -0.01085542980581522, 0.013331941328942776, 0.027239682152867317, -0.0017526166047900915, 0.12597833573818207, 0.07032209634780884, -0.03041895106434822, 0.029223090037703514, -0.01500757597386837, 0.036435067653656006, -0.0002961298741865903, -0.024135766550898552, -0.00902937725186348, -0.026422854512929916, 0.013623139820992947, 0.04142562672495842, 0.02651466429233551, -0.05033891648054123, 0.10950955003499985, -0.038517776876688004, -0.05595387518405914, 0.03923444077372551, 0.05384611338376999, -0.021259117871522903, -0.04838213697075844, 0.058721356093883514, -0.04532795026898384, 0.010457070544362068, 0.006055870093405247, -0.0005322822835296392, -0.005009328480809927, -0.021949527785182, -0.023173389956355095, 0.045380089432001114, 0.016363097354769707, -0.026581471785902977, -0.04407040402293205, 0.008710630238056183, 0.06780315935611725, 0.027128148823976517, -0.01781703345477581, 0.00777436513453722, 0.017915790900588036, -0.013558347709476948, 0.028020424768328667, -0.007907003164291382, 0.007001751102507114, -0.0974109098315239, -0.017109114676713943, -0.020639577880501747, -0.022726651281118393, 0.05285157635807991, -0.010624313727021217, -0.013150987215340137, 0.01854969933629036, -0.003691592486575246, 0.01796625182032585, -1.8234964954899624e-05, 0.019624659791588783, 0.009737200103700161, -0.022529706358909607, -0.04910577833652496, 0.03629597648978233, -0.017554091289639473, -0.0075420960783958435, 0.03752721846103668, -0.015419022180140018, 9.510518430033699e-05, -0.05651570484042168, 0.04508066922426224, 0.02614561840891838, 0.050052475184202194, 0.0025715879164636135, -0.08668890595436096, 0.0036336693447083235, -0.023672880604863167, 2.75890016609992e-07, 0.031641170382499695, 0.021415619179606438, 0.03061741590499878, 0.07349605858325958, 0.09386986494064331, 0.021478435024619102, -0.07675967365503311, 0.05342492833733559, -0.005161926615983248, -0.03466003015637398, -0.017085161060094833, 0.012183250859379768, 0.02164035104215145, 0.0281553715467453, 0.005936323199421167, 0.0628712996840477, 0.050699882209300995, -0.004976355936378241, 0.0025276050437241793, -0.04643400013446808, -0.014577663503587246, 0.03282200172543526, 0.001705801929347217, -0.0043970076367259026, -0.033397089689970016, 0.051115334033966064, 0.023264914751052856, 0.01722172647714615, -0.0009638259653002024, -0.013711844570934772, 0.02900628186762333, 0.04643562436103821, -0.02357678860425949, -0.007135231513530016, 0.040478210896253586, 0.014890757389366627, 0.032365694642066956, 0.030142970383167267, 0.03230009600520134, 0.005628421902656555, 0.0033496336545795202, -0.049774639308452606, 0.01799044944345951, 0.04515496641397476, 0.034336548298597336, 0.041427791118621826, 0.021654563024640083, 0.002788369543850422, 0.014294872991740704, 0.007056740578263998, -0.00963238812983036, -0.015339314006268978, -0.01064545288681984, -0.0025590003933757544, -0.020537959411740303, -0.0011588671477511525, 0.030982933938503265, -0.007399503607302904, -0.039222244173288345, -0.032591812312603, -0.041488584131002426, 0.03624624386429787, 0.02277485281229019, -0.020033307373523712, -0.023168284446001053, -0.0039118132553994656, -0.03846805542707443, 2.586034995987482e-34, -0.013801665045320988, -0.0020829380955547094, -0.060155853629112244, -0.0013307910412549973, 0.016204610466957092, 0.03219076246023178, 0.09270741790533066, -0.011022075079381466, 0.052548848092556, -0.01683717593550682, -0.0021910478826612234], "parsedContent": {"infobox": {}, "categories": [], "links": ["Maniacal monkey/Strategies", "members", "Ranged", "box trap", "<PERSON>", "<PERSON><PERSON><PERSON> spells", "barrage spells", "successful hit", "Defence", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "plainText": "|ammunition = Thrown |twohanded = No |speed = 4 }} Chinchompas are a type of members Ranged throwing weapon. They must either be bought from other players or caught in a box trap using the Hunter skill. They are thrown and explode on impact, hitting any targets in a 3x3 area, similar to burst and barrage spells. Unlike other throwing weapons, they cannot be retrieved, even when doing no damage. Chinchompas make use of unique mechanics, where their accuracy depends on how many squares they travel until they reach the target and the combat style being used by the player. The initial target also serves as the accuracy roll for surrounding targets - if the initial hit is a successful hit, then all surrounding targets will be hit. As such, it can be useful to target an opponent with lower Defence in order to damage a nearby higher Defence opponent; this is commonly used against Kree'arra. The accuracy roll made on the initial target uses the 'heavy' ranged defence for determining the opponent's defence roll.<ref></ref> Below lists all the accuracy rates of possible combinations. {| class=\"wikitable\" style=\"text-align: center;\" ! Style !! 0-3 Squares !! 4-6 Squares !! 7+ Squares |- |Short fuse |100% |75% |50% |- |Medium fuse |75% |100% |75% |- |Long fuse |50% |75% |100% |} Note: 100% accuracy does not mean a successful hit — it takes into consideration the player's Ranged attack bonus. For example, if the player had +100 Ranged attack, and used the short fuse style on an opponent one square away, it would roll the full +100 Ranged attack against the opponent's Defence. However, if the player used long fuse with the same distance and Ranged attack bonus, then their Ranged attack effectively becomes +50 instead. Combat styles Chinchompas References"}, "originalContent": "{{External|rs}}\n{{Redirect3|Chinning|For the ranged training method using Chinchompas, see [[Maniacal monkey/Strategies]]}}\n{{Otheruses|def=no|other uses|Chinchompa (disambiguation)}}\n{{Infobox Ranged Weapon Group\n|name = Chinchompa\n|images = {{plinkp|Chinchompa}}{{plinkp|Red chinchompa}}{{plinkp|Black chinchompa}}\n|ammunition = Thrown\n|twohanded = No\n|speed = 4\n}}\n'''Chinchompas''' are a type of [[members]] [[Ranged]] throwing weapon. They must either be bought from other players or caught in a [[box trap]] using the [[Hunter]] skill.\n\nThey are thrown and explode on impact, hitting any targets in a 3x3 area, similar to [[Burst spells|burst]] and [[barrage spells]]. Unlike other throwing weapons, they cannot be retrieved, even when doing no damage.\n\nChinchompas make use of unique mechanics, where their accuracy depends on how many squares they travel until they reach the target and the combat style being used by the player. The initial target also serves as the accuracy roll for surrounding targets - if the initial hit is a [[successful hit]], then all surrounding targets will be hit. As such, it can be useful to target an opponent with lower [[Defence]] in order to damage a nearby higher Defence opponent; this is commonly used against [[Kree'arra]]. The accuracy roll made on the initial target uses the 'heavy' ranged defence for determining the opponent's defence roll.<ref>{{PlainCiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1817838437292032420|date=29 July 2024|archiveurl=https://archive.is/dS7ny|archivedate=30 July 2024|quote=I believe those are 'heavy'.}}</ref>\n\nBelow lists all the accuracy rates of possible combinations.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/761964699646828544|date=6 August 2016|archiveurl=https://archive.is/kEm36|archivedate=26 May 2020|quote=If you chose the short fuse, 0-3 gives you full accuracy, 4-6 gives reduced, 7+ gives bad, etc.}}{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/993196171677126661|date=6 May 2018|archiveurl=https://archive.is/CpnIZ|archivedate=26 May 2020|quote=Medium fuses 0-3 tiles and 7+ tiles are 75%, not 50%. It's applied to the accuracy/defence roll when the attack is cast, like armour bonuses are.}}{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/865225157736095744|date=18 May 2017|archiveurl=https://archive.is/ELFlD|archivedate=26 May 2020|quote=No, they work with the same mechanics but not with the same numbers. It's like you guessed.}}\n\n{| class=\"wikitable\" style=\"text-align: center;\"\n! Style !! 0-3 Squares !! 4-6 Squares !! 7+ Squares\n|-\n|Short fuse\n|100%\n|75%\n|50%\n|-\n|Medium fuse\n|75%\n|100%\n|75%\n|-\n|Long fuse\n|50%\n|75%\n|100%\n|}\n\n'''Note:''' 100% accuracy does not mean a successful hit — it takes into consideration the player's Ranged attack bonus. For example, if the player had +100 Ranged attack, and used the short fuse style on an opponent one square away, it would roll the full +100 Ranged attack against the opponent's Defence. However, if the player used long fuse with the same distance and Ranged attack bonus, then their Ranged attack effectively becomes +50 instead.\n\n==Combat styles==\n{{CombatStyles|Chinchompas|speed=4|attackrange=9}}\n\n==Chinchompas==\n{{Infotable Bonuses|Chinchompa|Red chinchompa|Black chinchompa|sort=arange|nototals=yes}}\n\n==References==\n{{reflist}}\n\n{{Weapon}}\n{{Ranged weapons}}"}