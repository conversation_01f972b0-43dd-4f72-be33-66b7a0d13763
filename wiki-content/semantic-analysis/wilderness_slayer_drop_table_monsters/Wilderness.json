{"title": "Wilderness", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [{"type": "category", "value": "Player vs Player arenas", "confidence": 0.9, "source": "category"}], "relationships": [{"from": "Wilderness", "to": "13 August", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "2001", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Human", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Gielinor", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "PvP", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Mage Arena bank", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Corporeal Beast's Lair", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "King Black Dragon Lair", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Hardcore ironmen", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "#Wilderness levels", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "achievement diaries", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Carrallanger Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Graveyard of Shadows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Desert Treasure I", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dareeyak Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ruins (west)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ghorrock Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Frozen Waste Plateau", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Annakarl Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Cemetery Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Forgotten Cemetery", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Tyss", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ice Plateau Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Frozen Waste Plateau", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Lunar Diplomacy", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness crabs teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dark crab", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Fishing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Respawn Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Tyss", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Minigame teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Revenant cave teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Revenant Caves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Burning amulet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chaos Temple (Wilderness)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bandit Camp", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Hunter cape", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Black chinchompa", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Canoe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Obelisk", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ring of dueling", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON> (Ardougne)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Deserted Keep", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON> (Edgeville)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness sword 4", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Fountain of Rune", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Diary#Elite", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness sword 3", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Diary#Hard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Forinthry", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Guthix", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "First Age", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Second Age", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Zarosian Empire", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Asgarnia", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Morytania", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Zamorak", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Thammar<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "vampyre", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Lord <PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Viggora", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Zemouregal", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Staff of Armadyl", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "God Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Saradomin", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Armadyl", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bandos", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Edicts of Guthix", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Gielinor", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "aviansie (race)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "icyene", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "ourg", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "wyrms", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "revenants", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Fourth Age", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "giant", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dragon (race)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "bandit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "dark warrior", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "rogue", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "pirate", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness ditch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "combat level", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "PvP world", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness fence", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "members", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Death", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Items Kept on Death", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "spawning#respawn point", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON> Necklace", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Grave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "skull (status)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Abyss", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Emblem Trader", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Protect Item", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "prayer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "high-risk world", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "void armour", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "fire cape", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "fighter torso", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "coins", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Perdu", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "graceful clothing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Teleportation", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "members", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Teleport Block", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Protect from Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "special attacks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "chronicle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Black chinchompa", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dinh's bulwark", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "justiciar armour", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "elysian spirit shield", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Anglerfish", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "blighted anglerfish", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "toxic blowpipe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Powered staves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON>'s sceptre", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "accursed sceptre", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "serpentine helm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Barrows equipment", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Items Kept on Death#Unsafe item mechanics", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "spellbook", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Old School RuneScape Mobile", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "autocast", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "player killing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "multicombat areas", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "clan", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "player-versus-player", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "single-way combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "PJ timer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "loot key", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "player-versus-player", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "combat pure", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "combat level", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "combat pure", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "free-to-play combat pure guide", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Server#Themed servers/worlds", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Player-versus-player", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Single-way_combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Edgeville", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "free-to-play", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "members", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "protection prayers", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Singles-plus_combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "multicombat area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "clan", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "protection prayers", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "safespotting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Edgeville", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "PvP world", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "PvP", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "flaming", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "ragging", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "clan", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "skiller", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "PvM", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Tele Block", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "clan", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bount<PERSON> Hunter", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "player killing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "minigame", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bounty Hunter Shop", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "combat level", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "clan", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "player-versus-player", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Edgeville Dungeon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Obelisk of Air", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Black demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Earth warrior", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chaos druid", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Obelisk of Earth", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Obelisk of Air", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "South Wilderness mine", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Edgeville", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "River Lum", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "coal rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "iron rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ma<PERSON> of Zamorak", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Abyss", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "South-west Wilderness mine", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dark Warriors' Fortress", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "coal rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chaos Temple (Wilderness)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Graveyard of Shadows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Elder <PERSON> druid", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dark Warriors' Fortress", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bandit Camp (Wilderness)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Graveyard of Shadows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Revenant Caves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dark Warriors' Fortress", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bandit Camp (Wilderness)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Revenant", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Slayer Cave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Multicombat area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dark Warriors' Fortress", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Silk Chasm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness God Wars Dungeon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Multicombat area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "The Forgotten Cemetery", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "ecumenical key", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "aviansie", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "adamantite bar", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bone Yard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chaos Temple", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bones", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Big bones", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bandit Camp mine", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Forgotten Cemetery", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "iron rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "coal rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "mithril rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "adamantite rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "free-to-play", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "mining site", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Lava Dragon Isle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Lava dragon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "white berries", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chaos Temple (hut)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Frozen Waste Plateau", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Prayer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "P<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Mage Arena", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Lava Dragon Isle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "gold necklace", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "steel platebody", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "staff of earth", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Muddy chest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "lava eel", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Greater demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Black dragon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "runite rock", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Frozen Waste Plateau", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chaos Temple (hut)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "water rune", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "cosmic rune", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "runite rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Multicombat area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Rogues' Castle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Prayer points", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Greater demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "blood rune", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "burnt bones", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Lava Maze runite mine", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "runite ore", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Fountain of Rune", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Multicombat area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Volcano", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "spells", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "runes", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dragonstone#Enchanted jewellery", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Rogues' Castle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Mage Arena", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Deserted Keep", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "rogue", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "pickpocket", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chest (Rogues' Castle)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Resource Area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Deserted Keep", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Scorpion Pit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Mining", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Fishing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Woodcutting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "fishing spot", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "dark crab", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Runite Golem", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Volcano", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Rogues' Castle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Odium ward", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "malediction ward", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dark crab", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Deserted Keep#Teleport lever", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "King Black Dragon Lair", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Mage Arena", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Minigame Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Minigame Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Clan Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Last Man Standing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dareeyak Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ancient Magicks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ruins (west)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Carrallanger Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Graveyard of Shadows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Annakarl Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ghorrock Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Frozen Waste Plateau", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ice Plateau Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Lunar spells", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Tele Group Ice Plateau", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Cemetery Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON>euus spellbook", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "The Forgotten Cemetery", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Corporeal Beast Cave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "black chinchompa", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "cavern (Revenant Caves)#Level 40", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Fountain of Rune", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Obelisk", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Obelisk (Construction)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ardougne teleport lever", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Edgeville teleport lever", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Deserted Keep", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Woodcutting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "canoe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Obelisk", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "magic door", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "chaos dwarf", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Vet'ion's Rest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Trollheim Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Trollheim teleport (tablet)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Mind Altar Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Mind Altar teleport (tablet)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Edgeville Monastery", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Edgeville", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Lesser Fanatic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Paddewwa Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Paddewwa teleport (tablet)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Edgeville Dungeon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Canoe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Fairy ring", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Grand Exchange", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Varrock Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Varrock Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Spirit Tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Keldagrim minecart system", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Balloon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "bosses", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "malediction ward", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "odium ward", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "v<PERSON><PERSON><PERSON>'s chainmace", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "thammaron's sceptre", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "craw's bow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "ring of the gods", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "tyrannical ring", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "treasonous ring", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "voidwaker", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "King Black Dragon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Callisto", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Vet'ion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Multicombat area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Escape Caves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Artio", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Singles-plus combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Diary#Medium", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Diary#Hard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Player_killing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Items_Kept_on_Death", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness#Combat_types", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Callisto", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Crush", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Callisto's Den", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Multicombat_area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Vet'ion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Slash", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Vet'ion's Rest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Multicombat_area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Skeleton Hellhound (Vet'ion)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Greater Skeleton Hellhound (Vet'ion)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Stab", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Silk Chasm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Multicombat_area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON>' <PERSON><PERSON> (monster)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Prayer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Artio", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Crush", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Hunter's End", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Singles-plus_combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Slash", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Skeletal Tomb", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Singles-plus_combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Stab", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Web Chasm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Singles-plus_combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Revenant maledictus", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Revenant Caves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Singles-plus_combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "revenant", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "area of effect", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chaos Elemental", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Rogues' Castle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Multicombat_area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "combat styles", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chaos Fanatic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Single-way_combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "special attack", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Crazy archaeologist", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Crush", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ruins (west)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "The Forgotten Cemetery", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Single-way_combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "special attack", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Scorpia", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Scorpion Pit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Multicombat_area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "poison", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "King Black Dragon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Dragonfire", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "King Black Dragon Lair", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Single-way_combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "black dragon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "poison", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Stun (status)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Resource Area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "ores", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "magic tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "yew tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "runite golem", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "runite ore", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "dark crab", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "fishing spots", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "lobster pot", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "dark fishing bait", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "coins", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "player killers", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chaos Temple (hut)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "gilded altar", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "player-owned house", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "chapel", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "prayer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON> druid (NPC)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Abyss", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "RuneScape", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ma<PERSON> of Zamorak", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Edgeville", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "risk", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Runecraft", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Cosmic rune", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chaos Altar", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "chaos talisman", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "chaos tiara", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "chaos runes", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Runite ore", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Mining", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "free player", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Members", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Heroes' Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Mourner Tunnels", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "runite rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Update:Rejuvenating the Wilderness: More risk, more reward", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Frozen Waste Plateau", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "mines", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Skeleton Mine", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "coal rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "skeleton", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bandit Camp Mine", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "iron rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "coal rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Mithril rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "adamantite rocks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "fishing spot", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bandit Camp (Wilderness)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Fishing spot (small net, bait)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "shrimps", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "anchovies", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "sardine", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "herring", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Lava eel", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "oily fishing rod", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "fishing bait", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Fountain of Rune", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Resource Area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "dark crab", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "dark fishing bait", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "lobster pot", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Fishing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Hitpoints", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "hunting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Black chincho<PERSON> (Hunter)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Black salamander (Hunter)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "player-killing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ent", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Woodcutting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Thieving", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chest (Rogues' Castle)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Rogues' Castle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "rogue", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "money making", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Agility", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Agility Course", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "experience", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness agility ticket", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "marks of grace", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Agility dispenser", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Slayer task", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "monster", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "combat level", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness bosses", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON><PERSON><PERSON>'s key", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Slayer's enchantment", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "ring of wealth scroll", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "magic shortbow scroll", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "drops", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "ring of wealth (i)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "clue scroll", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Family Crest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chronozon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Edgeville dungeon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Enter the Abyss", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Devious Minds", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ma<PERSON> of Zamorak", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Curse of the Empty Lord", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "miniquest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Kolodion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "god cape", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "god staff", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "bank", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Pool of refreshment", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Clan Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Last Man Standing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "minigame", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "teleblock", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness sword", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "dragon bone", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Resource Area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Chaos Temple (Wilderness)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Cape merchants", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Team capes", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Minimap", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "clans", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bandit Duty Free", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "general store", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Bandit Camp (Wilderness)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "High Alchemy", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Clue scroll (hard)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Clue scroll (elite)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Clue scroll (master)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Treasure Trail", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "player killers", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "clue box", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "ring of wealth (i)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "13 August", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "2001", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "PvP", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness ditch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "18 April", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "2007", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "scam", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON>sayer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Army of Darkness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Everlasting Fire", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Faithless", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Forbidden", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Inspiration", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Pirates of Peril", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Regal", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Scape Sad", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Scape Wild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Shining", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Troubled", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Undercurrent", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Underground (music track)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wild Isle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wild Side", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness (music track)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness 2", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness 3", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Witching", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wonder", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "dragon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Elite Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Patch Notes (13 March 2014)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Skull (status)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Items Kept on Death", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Forinthry", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness ditch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Sign", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness Slayer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Wilderness boss", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "High-risk world", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Death", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness", "to": "Player vs Player arenas", "type": "belongs_to_category", "confidence": 0.9, "source": "category"}], "embedding": [0.0357278548181057, 0.02704835869371891, 0.023828472942113876, 0.012691955082118511, -0.016959065571427345, 0.012460731901228428, -0.051576897501945496, -0.003533937269821763, 0.024884117767214775, -0.013775722123682499, 0.0010586905991658568, 0.059620220214128494, -0.014374440535902977, -0.01712549850344658, -0.020161062479019165, -0.0018812279449775815, -0.009177972562611103, -0.05474436655640602, 0.0176705252379179, 0.017826881259679794, -0.033110808581113815, 0.008203700184822083, 0.0016619893722236156, 0.013096633367240429, 0.05565211921930313, -0.04541324824094772, 0.010784981772303581, 0.008225593715906143, -0.01326290424913168, -0.050053272396326065, 0.021765951067209244, -0.07498034834861755, 0.008261413313448429, 0.0075132520869374275, 2.336330453545088e-06, -0.017589928582310677, 0.025768723338842392, 0.013272910378873348, 0.019465848803520203, -0.0031446944922208786, -0.009823024272918701, 0.12305354326963425, 0.051269471645355225, 0.013167321681976318, -0.003774951444938779, -0.05543353781104088, 0.0016410675598308444, 0.039343882352113724, 0.01900036819279194, -0.015693698078393936, 0.022316984832286835, -0.07692664116621017, -0.04083487391471863, -0.02008807472884655, -0.004943236242979765, 0.06413541734218597, 0.05145265907049179, -0.0050689950585365295, -0.006299837492406368, -0.0458194836974144, -0.020700156688690186, -0.03250982612371445, -0.024043329060077667, 0.016827646642923355, -0.0017292554257437587, -0.002389214001595974, -0.04363856837153435, -0.021180635318160057, 0.009582770988345146, 0.023483827710151672, -0.0610053688287735, -0.02025517076253891, 0.005308695137500763, 0.04382359981536865, -0.04776887223124504, -0.04001772403717041, 0.017994046211242676, 0.0632617399096489, -0.02203674614429474, 0.020688850432634354, 0.006708307657390833, 0.07390912622213364, 0.009450211189687252, 0.012899218127131462, -0.009472067467868328, 0.034366674721241, 0.012313736602663994, 0.04045240953564644, -0.032270632684230804, -0.016588859260082245, -0.13302761316299438, 0.025184348225593567, 0.002509078709408641, 0.0270925872027874, -0.06799940764904022, -0.03611171990633011, -0.028173677623271942, 0.044867049902677536, 0.07533467561006546, 0.016598789021372795, -0.04772580787539482, -0.026699239388108253, -0.07634355872869492, 0.052326638251543045, 0.005509010516107082, -0.10948258638381958, 0.04433098062872887, 0.01988259144127369, 0.052324388176202774, -0.03189755231142044, 0.021985892206430435, -0.016551559790968895, -0.011194742284715176, -0.016494059935212135, -0.04141818359494209, 0.016369733959436417, 0.08095728605985641, -0.008579161949455738, -0.03605268895626068, 0.007180028595030308, 0.055118970572948456, 0.0225276667624712, -0.03140505403280258, -0.013743716292083263, -0.00434174295514822, -0.07479678094387054, -0.06597137451171875, 0.04630676284432411, 0.010988052934408188, 0.024241913110017776, 0.029791871085762978, -0.013951252214610577, 0.03424021974205971, -0.033804599195718765, -0.006245194002985954, 0.059668879956007004, 0.025861123576760292, 0.03322552517056465, -0.08153045177459717, 0.018104329705238342, 0.04088746756315231, -0.004030370153486729, -0.04037060961127281, -0.013644197955727577, -0.010973370634019375, -0.03670254349708557, 0.023806098848581314, -0.034388333559036255, -0.019877532497048378, 0.026165412738919258, -0.026035821065306664, 0.04793054983019829, 0.00704192416742444, -0.012770338915288448, 0.023937342688441277, 0.03444815054535866, 0.08192332834005356, -0.016554374247789383, 0.02107802778482437, -0.03003520518541336, 0.017445771023631096, 0.029832100495696068, 0.028257621452212334, -0.06354771554470062, -0.06483647227287292, 0.021316638216376305, -0.03835982829332352, 0.04468663036823273, 0.009177414700388908, -0.02816784754395485, 0.003942111507058144, 0.019151480868458748, -0.02854190766811371, -0.00023419235367327929, 0.028020104393363, 0.09078802168369293, 0.08483288437128067, 0.0010063641238957644, -0.06059251353144646, 0.020522555336356163, -0.058017320930957794, -0.03357842564582825, 0.038759972900152206, -0.009401417337357998, 0.04687771573662758, -0.013548742048442364, 0.03005838952958584, 0.05117412656545639, 0.026029665023088455, -0.023051707074046135, -0.027134686708450317, 0.00886869803071022, 0.0181716438382864, -0.017919417470693588, 0.01861025206744671, -0.03060065023601055, 0.017815914005041122, -0.1251024305820465, 0.0435534343123436, -0.045601263642311096, 0.04748283699154854, 0.024048931896686554, 0.01950010284781456, 0.006626430433243513, 0.0028792214579880238, -0.027356170117855072, 0.01746400073170662, -0.01418998185545206, 0.004499248694628477, -0.0027558449655771255, -0.020509731024503708, 0.05031311511993408, 0.06607332080602646, -0.029492942616343498, -0.08129961788654327, 0.023902863264083862, -0.026401791721582413, 0.02655315026640892, -0.05687783285975456, -0.054135579615831375, -0.051575951278209686, -0.014297785237431526, -0.06859417259693146, 0.003405617782846093, -0.03341148421168327, -0.012537681497633457, -0.011162273585796356, 0.00453640753403306, -0.01797342300415039, -0.05503898113965988, -0.024415872991085052, -0.006678258068859577, -0.0013895387528464198, -0.004437930416315794, -0.024663789197802544, -0.02873484045267105, 0.05468587577342987, 0.0892026498913765, -0.024548325687646866, -0.07485189288854599, 0.002903089625760913, 0.0027350224554538727, 0.025914400815963745, 0.029221726581454277, -0.042054228484630585, -0.03256317973136902, 0.015751715749502182, -0.01962062157690525, -0.03836165368556976, -0.010496798902750015, 0.009625650942325592, 0.004625715781003237, 0.01843089982867241, 0.019031012430787086, -0.009977763518691063, -0.03695596754550934, -0.021313179284334183, 0.0424027293920517, 0.013970492407679558, -0.012353871949017048, -0.010464731603860855, -0.02084817737340927, 0.01376781053841114, 0.03874683380126953, 0.02622118592262268, 0.01795705407857895, -0.04139340668916702, 0.05691341683268547, 0.06624089926481247, -0.010814858600497246, 0.037774018943309784, 0.01655907742679119, -0.013938650488853455, -0.022939452901482582, 0.01224568672478199, 5.100078851683065e-05, -0.019313547760248184, 0.030422696843743324, -0.011598080396652222, 0.017937352880835533, 0.007182273082435131, 0.03647185489535332, 0.054930612444877625, 0.035411685705184937, 0.03237500041723251, 0.049605000764131546, -0.0032496985513716936, -0.00727530661970377, 0.00461007421836257, -0.017636215314269066, -0.012692926451563835, 0.02237599901854992, 0.0037292190827429295, -0.08347827941179276, -0.00615648040547967, 0.018865518271923065, 0.07151127606630325, -0.048142824321985245, 0.050505056977272034, 0.039394307881593704, 0.054246872663497925, -0.028257474303245544, 0.049719229340553284, -0.0132516510784626, 0.009333217516541481, -0.0965690016746521, 0.028558896854519844, -0.02316761203110218, -0.001399802858941257, 0.022529030218720436, 0.025276580825448036, 0.00021387363085523248, -0.022055184468626976, 0.03702971711754799, 0.033239543437957764, 0.03222202509641647, 0.030845381319522858, -0.020449897274374962, 0.0017970886547118425, -0.02715921401977539, -0.014467665925621986, 0.024865103885531425, 0.02211604081094265, 0.01302409078925848, -0.002911789808422327, 0.014994282275438309, 0.050467267632484436, 0.017356643453240395, 0.007248094771057367, 0.026705682277679443, -0.009308548644185066, 0.027559705078601837, -0.05316013842821121, -0.001246784464456141, -0.08360309898853302, 0.011067626997828484, -0.004240166395902634, -0.12027677893638611, -0.05351774021983147, 0.028232913464307785, 0.025500338524580002, 0.045632775872945786, 0.04953284189105034, -0.02893040142953396, 0.02858218364417553, 0.022223223000764847, 0.013437771238386631, -0.01917289011180401, 0.09512157738208771, -0.005639536771923304, -0.007539697457104921, -0.02853519283235073, 0.0013406152138486505, -0.00041965325362980366, -0.02461041510105133, 0.045775048434734344, 0.012099512852728367, 0.021579081192612648, 0.02550431340932846, 0.018305467441678047, -0.017185701057314873, 0.06614725291728973, -0.07226119190454483, -0.06014818698167801, 0.060972873121500015, 0.034577708691358566, 0.010886906646192074, -0.009411312639713287, 0.02364031784236431, 0.015535418875515461, 0.007155303843319416, 0.03183196857571602, 0.0271398164331913, 0.0245902631431818, 0.014920187182724476, 0.008651791140437126, 0.015075228177011013, -0.08422008901834488, 0.04530900716781616, 0.037974338978528976, -0.014719861559569836, 0.00255402410402894, 0.01934683509171009, -0.011908574029803276, 0.021603204309940338, -0.005127002485096455, -0.017397094517946243, 0.05981212109327316, 0.006207338068634272, 0.008439593017101288, 0.005929671693593264, -0.05929882079362869, -0.027516495436429977, 0.061659976840019226, -0.008466687053442001, -0.021243782714009285, -0.038461677730083466, 0.00510470662266016, -0.0052737342193722725, 0.014081898145377636, 0.007204040884971619, 0.015031584538519382, 0.09404950588941574, 0.015757709741592407, -0.07106146216392517, 0.10170608758926392, 0.035311248153448105, 0.012632561847567558, -0.011177062056958675, 0.016674771904945374, -0.059862080961465836, 0.004571238532662392, -0.002259335946291685, -0.003988934680819511, -0.009269784204661846, 0.0647265836596489, -0.036415256559848785, 0.028003495186567307, -0.060510020703077316, 0.03973451629281044, -0.0340568870306015, 0.017291810363531113, -0.027775151655077934, -0.027022846043109894, 0.027249271050095558, 0.023845667019486427, 0.01960090547800064, 0.0635327473282814, 0.012837516143918037, 0.00741557264700532, 0.02098892629146576, -0.061735183000564575, -0.05202620103955269, 0.02103813923895359, -0.022970017045736313, -0.06884001195430756, 0.013010143302381039, 0.020895304158329964, 0.04959488287568092, -0.03517897054553032, -0.028254231438040733, 0.018055178225040436, 0.05508895218372345, -0.035900384187698364, 0.010643313638865948, -0.0718885138630867, 0.01031331717967987, 0.014579269103705883, 0.02978239580988884, -0.0016341920709237456, -0.05372747778892517, 0.016204053536057472, 0.03814646601676941, -0.03126461058855057, -0.02784680761396885, 0.00266354251652956, 0.03408246487379074, -0.03946471959352493, -0.000902072642929852, 0.026819510385394096, 0.028434202075004578, 0.019995087757706642, -0.013326363638043404, 0.017055416479706764, -0.04410490766167641, 0.02809400111436844, -0.021092025563120842, -0.06340393424034119, 0.0485638752579689, -0.04845571517944336, -0.018576303496956825, 0.011193828657269478, -0.002735424553975463, 0.020997723564505577, 0.0078288484364748, 0.04880719631910324, 0.01735714264214039, -0.026584770530462265, -0.023729760199785233, -0.029834337532520294, -0.0314020961523056, 0.05206519365310669, 0.022542543709278107, -0.026401374489068985, -0.00030806229915469885, -0.027983354404568672, -0.006987303029745817, -0.018637539818882942, -0.02705823816359043, -0.0055672177113592625, -0.004792146384716034, 0.03758338838815689, -0.013498043641448021, 0.12469593435525894, 0.04448169097304344, 0.05670744553208351, -0.025951940566301346, -0.08546296507120132, 0.05632929131388664, -0.07496075332164764, 0.012947997078299522, 0.04462451487779617, -0.033093567937612534, 0.03637588024139404, -0.006530244834721088, 0.018889950588345528, -0.006679838988929987, -0.004562933463603258, -0.019000671803951263, 0.003873484907671809, 0.03674203157424927, -0.01991284266114235, 0.005029860883951187, -0.06910920888185501, 0.005820547696202993, 0.05013125017285347, -0.034191761165857315, 0.01680493727326393, 0.007448679301887751, -0.007259027566760778, 0.024069426581263542, -0.021546706557273865, -0.009077664464712143, -0.03067772276699543, -0.006680591031908989, -0.015362540259957314, -0.0031469794921576977, 0.00435560243204236, 0.013959159143269062, -0.01504445169121027, 0.09735145419836044, 0.014991730451583862, 0.01890370436012745, -0.004365378059446812, 0.003742288099601865, 0.029307130724191666, 0.051832523196935654, 0.0034164548851549625, -0.031615499407052994, 0.02960110269486904, 0.028757955878973007, 0.0071743628941476345, -0.026344332844018936, 0.029065627604722977, -0.009348025545477867, -0.019523881375789642, -0.029884792864322662, -0.005036919843405485, -0.02316751517355442, -0.0006952249095775187, -0.025183741003274918, -0.004692286252975464, -0.020167797803878784, 0.007799983024597168, -0.039125099778175354, -6.796229322506506e-33, -0.027085080742836, -0.06429379433393478, 0.036231812089681625, -0.05901686102151871, -0.06321542710065842, -0.07166238874197006, 0.04270399734377861, 0.021964728832244873, -0.02482624724507332, 0.020514879375696182, 0.014691908843815327, -0.015048174187541008, -0.0058641592040658, 0.034724682569503784, -0.013702933676540852, -0.02259397879242897, -0.040185682475566864, 0.01384771429002285, 0.016604185104370117, -0.00060533412033692, 0.05008873715996742, -0.020645570009946823, 0.004775507841259241, -0.0002142667945008725, -0.004644460044801235, 0.021538792178034782, -0.021556945517659187, -0.010652170516550541, -0.0521080419421196, -0.0710444301366806, -0.029978366568684578, -0.05648615211248398, 0.006230826489627361, 0.03286193683743477, 0.005531372502446175, -0.0017055259086191654, -0.024275656789541245, -0.0020184863824397326, -0.05126475170254707, 0.012878751382231712, -0.015866069123148918, -0.010868223384022713, 0.05371507629752159, 0.004785384517163038, 0.05258416384458542, -0.06028347462415695, 0.005451540928333998, -0.00816209428012371, -0.05570901930332184, 0.0017384805250912905, 0.03733820095658302, -0.008729266934096813, -0.03225954249501228, -0.04657122865319252, -0.06983117014169693, 0.011403817683458328, -0.0291058998554945, 0.013112020678818226, 0.0007371727260760963, -0.03535635396838188, 0.013094599358737469, 0.0055500720627605915, -0.01022877637296915, 0.0317898690700531, -0.0071152313612401485, 0.01000625267624855, -0.10400935262441635, -0.017026780173182487, -0.047893427312374115, -0.022347677499055862, -0.04017525538802147, 0.040259454399347305, -0.020467964932322502, 0.003548420500010252, -0.017199061810970306, -0.015833240002393723, -0.03537894785404205, 0.033054862171411514, -0.05434974282979965, 0.0030610808171331882, 0.0026064999401569366, 0.023916466161608696, 0.04414002597332001, -0.004786801524460316, 0.028887085616588593, -0.029546404257416725, -0.02783121168613434, 0.0922928974032402, -7.515348261222243e-05, -0.043954361230134964, 0.01192901749163866, 0.026937520131468773, -0.03332144021987915, -0.0027423242572695017, 0.0653626099228859, 0.035259779542684555, 0.05681949853897095, 0.00826281774789095, -0.003682043170556426, -0.033621203154325485, 0.08494623005390167, 0.01900140382349491, -0.017696907743811607, 0.0005936607485637069, -0.017988543957471848, 0.016510983929038048, -0.029752248898148537, -0.015244523994624615, -0.04776575416326523, -0.013655941933393478, 0.022015580907464027, -0.0016363657778128982, -0.015489745885133743, 0.016838092356920242, 0.018751943483948708, -0.08307833969593048, 0.02254360169172287, -0.025961440056562424, 0.021816235035657883, -0.05270456150174141, 0.02056209184229374, -0.01068884413689375, -0.026084793731570244, -0.02896500751376152, 0.01698474958539009, 0.019481508061289787, -0.06767440587282181, 0.019670646637678146, 0.012753581628203392, -0.002800390589982271, 0.001404781243763864, -0.039701543748378754, 2.9562491477008734e-07, 0.028142718598246574, 0.019618183374404907, 0.01907375454902649, -0.07790052145719528, -0.007525695953518152, 0.002085234737023711, -0.0018957171123474836, 0.06499722599983215, -0.018151327967643738, 0.04240034148097038, 0.0022234260104596615, -0.03262702375650406, -0.016231298446655273, 0.0015218750340864062, 0.022598566487431526, 0.030184121802449226, -0.06429728120565414, -0.02015962265431881, -0.02158639021217823, -0.037566471844911575, 0.06068094074726105, -0.040850408375263214, -0.014009728096425533, 0.0016807623906061053, -0.019563497975468636, -0.07006308436393738, 0.018281077966094017, -0.008445682004094124, 0.016702093183994293, -0.005134884733706713, 0.03853989765048027, -0.02017778716981411, -0.0053728315979242325, -0.08277788758277893, 0.02405601553618908, -0.04245602712035179, -0.0031309525948017836, 0.021854009479284286, 0.03066076897084713, 0.03586599975824356, -0.04302569478750229, -0.005300241056829691, -0.012480407021939754, 0.007727254647761583, 0.006114867515861988, -0.07617367804050446, -0.029549477621912956, -0.00615922175347805, -0.002732127206400037, -0.04228566586971283, -0.013059353455901146, 0.018538882955908775, -0.0026131323538720608, -0.012754923664033413, 0.021728061139583588, -0.005031948443502188, -0.01263453345745802, -0.0033586756326258183, 0.026935290545225143, -0.0089107695966959, -0.03182313218712807, 0.011905340477824211, 0.01508820429444313, 0.0884457677602768, -0.003063549753278494, 0.013286594301462173, -0.*****************, 2.6589624818415045e-34, -0.027399711310863495, 0.011773396283388138, -0.*****************, -0.*****************, 0.052249666303396225, 0.016511861234903336, 0.*****************, -0.005964784417301416, -0.016730351373553276, 0.*****************, 0.*****************], "parsedContent": {"infobox": {}, "categories": ["Player vs Player arenas"], "links": ["13 August", "2001", "<PERSON><PERSON><PERSON>", "Human", "Gielinor", "PvP", "Ferox Enclave", "Mage Arena bank", "Corporeal Beast's Lair", "King Black Dragon Lair", "Hardcore ironmen", "#Wilderness levels", "achievement diaries", "Wilderness Diary", "Carrallanger Teleport", "Graveyard of Shadows", "Desert Treasure I", "Dareeyak Teleport", "Ruins (west)", "Ghorrock Teleport", "Frozen Waste Plateau", "Annakarl Teleport", "<PERSON><PERSON>", "Cemetery Teleport", "Forgotten Cemetery", "Tyss", "Ice Plateau Teleport", "Frozen Waste Plateau", "Lunar Diplomacy", "Wilderness crabs teleport", "Dark crab", "Fishing", "<PERSON><PERSON><PERSON><PERSON>", "Respawn Teleport", "Ferox Enclave", "Tyss", "Minigame teleport", "Ferox Enclave", "Revenant cave teleport", "<PERSON><PERSON>", "Revenant Caves", "Burning amulet", "Chaos Temple (Wilderness)", "Bandit Camp", "<PERSON><PERSON>", "Hunter cape", "Black chinchompa", "Canoe", "Ferox Enclave", "Wilderness Obelisk", "Ring of dueling", "Ferox Enclave", "<PERSON><PERSON> (Ardougne)", "Deserted Keep", "<PERSON><PERSON> (Edgeville)", "Wilderness sword 4", "Fountain of Rune", "Wilderness Diary#Elite", "Wilderness sword 3", "Wilderness Diary#Hard", "Forinthry", "Guthix", "<PERSON><PERSON><PERSON>", "First Age", "Second Age", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zarosian Empire", "Asgarnia", "<PERSON><PERSON><PERSON><PERSON>", "Morytania", "<PERSON><PERSON><PERSON><PERSON>", "Zamorak", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Thammar<PERSON>", "vampyre", "Lord <PERSON><PERSON><PERSON>", "Viggora", "Zemouregal", "<PERSON><PERSON><PERSON>", "Staff of Armadyl", "God Wars", "Saradomin", "Armadyl", "Bandos", "Edicts of Guthix", "Gielinor", "aviansie (race)", "icyene", "ourg", "wyrms", "revenants", "Fourth Age", "giant", "demon", "Dragon (race)", "bandit", "dark warrior", "rogue", "pirate", "Wilderness ditch", "combat level", "combat", "PvP world", "Wilderness fence", "members", "Death", "Items Kept on Death", "spawning#respawn point", "<PERSON> Necklace", "Grave", "skull (status)", "Abyss", "Emblem Trader", "Protect Item", "prayer", "high-risk world", "void armour", "fire cape", "fighter torso", "coins", "Perdu", "graceful clothing", "Teleportation", "members", "Teleport Block", "Protect from Magic", "special attacks", "chronicle", "Black chinchompa", "Dinh's bulwark", "justiciar armour", "elysian spirit shield", "Anglerfish", "blighted anglerfish", "toxic blowpipe", "Powered staves", "<PERSON><PERSON><PERSON><PERSON>'s sceptre", "accursed sceptre", "serpentine helm", "Barrows equipment", "Items Kept on Death#Unsafe item mechanics", "spellbook", "Old School RuneScape Mobile", "autocast", "player killing", "multicombat areas", "clan", "player-versus-player", "single-way combat", "PJ timer", "loot key", "player-versus-player", "combat pure", "combat level", "combat pure", "free-to-play combat pure guide", "Server#Themed servers/worlds", "Player-versus-player", "Single-way_combat", "Edgeville", "Ferox Enclave", "free-to-play", "members", "protection prayers", "Singles-plus_combat", "multicombat area", "clan", "protection prayers", "safespotting", "Edgeville", "PvP world", "PvP", "flaming", "ragging", "clan", "skiller", "PvM", "Tele Block", "clan", "Bount<PERSON> Hunter", "player killing", "minigame", "Bounty Hunter Shop", "combat level", "clan", "player-versus-player", "Edgeville Dungeon", "Obelisk of Air", "Black demon", "Earth warrior", "Chaos druid", "Obelisk of Earth", "Obelisk of Air", "South Wilderness mine", "Edgeville", "River Lum", "coal rocks", "iron rocks", "Ma<PERSON> of Zamorak", "Abyss", "South-west Wilderness mine", "Dark Warriors' Fortress", "coal rocks", "Chaos Temple (Wilderness)", "Graveyard of Shadows", "Elder <PERSON> druid", "Dark Warriors' Fortress", "Bandit Camp (Wilderness)", "Ferox Enclave", "Graveyard of Shadows", "Revenant Caves", "Dark Warriors' Fortress", "Bandit Camp (Wilderness)", "<PERSON><PERSON>", "Revenant", "Wilderness Slayer Cave", "Multicombat area", "Dark Warriors' Fortress", "Silk Chasm", "<PERSON><PERSON><PERSON><PERSON>", "Wilderness God Wars Dungeon", "Multicombat area", "The Forgotten Cemetery", "ecumenical key", "aviansie", "adamantite bar", "Bone Yard", "Chaos Temple", "<PERSON><PERSON><PERSON>", "Bones", "Big bones", "Bandit Camp mine", "Forgotten Cemetery", "iron rocks", "coal rocks", "mithril rocks", "adamantite rocks", "free-to-play", "mining site", "Lava Dragon Isle", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lava dragon", "white berries", "Chaos Temple (hut)", "Frozen Waste Plateau", "Prayer", "P<PERSON><PERSON>", "<PERSON><PERSON>", "Mage Arena", "Lava Dragon Isle", "gold necklace", "steel platebody", "staff of earth", "Muddy chest", "lava eel", "<PERSON><PERSON>", "Greater demon", "Black dragon", "runite rock", "Frozen Waste Plateau", "Chaos Temple (hut)", "water rune", "cosmic rune", "runite rocks", "<PERSON><PERSON>", "Multicombat area", "Rogues' Castle", "Prayer points", "Greater demon", "blood rune", "burnt bones", "Lava Maze runite mine", "<PERSON><PERSON>", "runite ore", "Fountain of Rune", "Multicombat area", "Wilderness Volcano", "spells", "runes", "Dragonstone#Enchanted jewellery", "Rogues' Castle", "Mage Arena", "Deserted Keep", "rogue", "pickpocket", "Chest (Rogues' Castle)", "Resource Area", "Deserted Keep", "Scorpion Pit", "Mining", "Fishing", "Woodcutting", "fishing spot", "dark crab", "Runite Golem", "Wilderness Volcano", "Rogues' Castle", "Odium ward", "malediction ward", "Dark crab", "Deserted Keep#Teleport lever", "King Black Dragon Lair", "Mage Arena", "Minigame Teleport", "Minigame Teleport", "Clan Wars", "Last Man Standing", "Ferox Enclave", "Dareeyak Teleport", "Ancient Magicks", "Ruins (west)", "Carrallanger Teleport", "Graveyard of Shadows", "Annakarl Teleport", "<PERSON><PERSON>", "Ghorrock Teleport", "Frozen Waste Plateau", "Ice Plateau Teleport", "Lunar spells", "Tele Group Ice Plateau", "Cemetery Teleport", "<PERSON>euus spellbook", "The Forgotten Cemetery", "Corporeal Beast Cave", "Ferox Enclave", "black chinchompa", "<PERSON>", "cavern (Revenant Caves)#Level 40", "Fountain of Rune", "Wilderness Obelisk", "Wilderness Diary", "Obelisk (Construction)", "Ardougne teleport lever", "Edgeville teleport lever", "Deserted Keep", "Woodcutting", "canoe", "Ferox Enclave", "Wilderness Obelisk", "magic door", "<PERSON><PERSON><PERSON>", "chaos dwarf", "Vet'ion's Rest", "Trollheim Teleport", "Trollheim teleport (tablet)", "Mind Altar Teleport", "Mind Altar teleport (tablet)", "Edgeville Monastery", "Edgeville", "Lesser Fanatic", "Paddewwa Teleport", "Paddewwa teleport (tablet)", "Edgeville Dungeon", "Canoe", "Fairy ring", "Grand Exchange", "Varrock Teleport", "Varrock Diary", "Spirit Tree", "Keldagrim minecart system", "Balloon", "bosses", "malediction ward", "odium ward", "v<PERSON><PERSON><PERSON>'s chainmace", "thammaron's sceptre", "craw's bow", "ring of the gods", "tyrannical ring", "treasonous ring", "voidwaker", "King Black Dragon", "Wilderness", "Callisto", "<PERSON><PERSON><PERSON><PERSON>", "Vet'ion", "Multicombat area", "Escape Caves", "Artio", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Singles-plus combat", "Wilderness Diary#Medium", "Wilderness Diary#Hard", "Player_killing", "Items_Kept_on_Death", "Wilderness#Combat_types", "Callisto", "Crush", "Magic", "Ranged", "Callisto's Den", "Multicombat_area", "Vet'ion", "Slash", "Magic", "Vet'ion's Rest", "Multicombat_area", "Skeleton Hellhound (Vet'ion)", "Greater Skeleton Hellhound (Vet'ion)", "<PERSON><PERSON><PERSON><PERSON>", "Stab", "Magic", "Ranged", "Silk Chasm", "Multicombat_area", "<PERSON><PERSON><PERSON><PERSON>' <PERSON><PERSON> (monster)", "Prayer", "Artio", "Crush", "Magic", "Ranged", "Hunter's End", "Singles-plus_combat", "<PERSON><PERSON><PERSON><PERSON>", "Slash", "Magic", "Skeletal Tomb", "Singles-plus_combat", "<PERSON><PERSON>", "Stab", "Magic", "Ranged", "Web Chasm", "Singles-plus_combat", "Revenant maledictus", "Ranged", "Magic", "Revenant Caves", "Singles-plus_combat", "revenant", "area of effect", "Chaos Elemental", "Magic", "Ranged", "<PERSON><PERSON>", "Rogues' Castle", "Multicombat_area", "combat styles", "Chaos Fanatic", "Magic", "<PERSON><PERSON>", "Single-way_combat", "special attack", "Crazy archaeologist", "Crush", "Ranged", "Ruins (west)", "The Forgotten Cemetery", "Single-way_combat", "special attack", "Scorpia", "<PERSON><PERSON>", "Scorpion Pit", "Multicombat_area", "poison", "King Black Dragon", "<PERSON><PERSON>", "Dragonfire", "King Black Dragon Lair", "<PERSON><PERSON>", "Single-way_combat", "black dragon", "poison", "Stun (status)", "Resource Area", "ores", "magic tree", "yew tree", "runite golem", "runite ore", "dark crab", "fishing spots", "lobster pot", "dark fishing bait", "coins", "Wilderness Diary", "player killers", "Chaos Temple (hut)", "gilded altar", "player-owned house", "chapel", "prayer", "<PERSON> druid (NPC)", "Abyss", "RuneScape", "Ma<PERSON> of Zamorak", "Edgeville", "risk", "Runecraft", "Cosmic rune", "Chaos Altar", "chaos talisman", "chaos tiara", "chaos runes", "Runite ore", "Mining", "free player", "Members", "Heroes' Guild", "Mourner Tunnels", "runite rocks", "Update:Rejuvenating the Wilderness: More risk, more reward", "Frozen Waste Plateau", "mines", "Skeleton Mine", "coal rocks", "skeleton", "Bandit Camp Mine", "iron rocks", "coal rocks", "Mithril rocks", "adamantite rocks", "fishing spot", "Bandit Camp (Wilderness)", "Fishing spot (small net, bait)", "shrimps", "anchovies", "sardine", "herring", "<PERSON><PERSON>", "Lava eel", "oily fishing rod", "fishing bait", "Fountain of Rune", "Wilderness Resource Area", "dark crab", "dark fishing bait", "lobster pot", "Fishing", "Hitpoints", "hunting", "Black chincho<PERSON> (Hunter)", "<PERSON>", "Black salamander (Hunter)", "<PERSON>", "player-killing", "Ent", "logs", "Woodcutting", "Axe", "Thieving", "Chest (Rogues' Castle)", "Rogues' Castle", "rogue", "money making", "Agility", "Wilderness Agility Course", "experience", "Wilderness agility ticket", "marks of grace", "Agility dispenser", "<PERSON><PERSON><PERSON><PERSON>", "Slayer task", "monster", "combat level", "Wilderness bosses", "<PERSON><PERSON><PERSON>'s key", "Slayer's enchantment", "ring of wealth scroll", "magic shortbow scroll", "drops", "ring of wealth (i)", "clue scroll", "Family Crest", "Chronozon", "Edgeville dungeon", "Enter the Abyss", "Devious Minds", "Ma<PERSON> of Zamorak", "Curse of the Empty Lord", "miniquest", "Magic", "Kolodion", "god cape", "god staff", "Magic", "Ferox Enclave", "bank", "Pool of refreshment", "Clan Wars", "Last Man Standing", "minigame", "teleblock", "Wilderness Diary", "Wilderness sword", "dragon bone", "Resource Area", "<PERSON>", "Chaos Temple (Wilderness)", "Cape merchants", "Team capes", "Minimap", "clans", "Bandit Duty Free", "general store", "Bandit Camp (Wilderness)", "High Alchemy", "Clue scroll (hard)", "Clue scroll (elite)", "Clue scroll (master)", "Treasure Trail", "player killers", "clue box", "ring of wealth (i)", "13 August", "2001", "PvP", "combat", "Wilderness ditch", "18 April", "2007", "scam", "<PERSON>sayer", "Army of Darkness", "Everlasting Fire", "Faithless", "Forbidden", "Inspiration", "<PERSON>", "Pirates of Peril", "Regal", "Scape Sad", "Scape Wild", "Shining", "Troubled", "Undercurrent", "Underground (music track)", "Wild Isle", "Wild Side", "Wilderness (music track)", "Wilderness 2", "Wilderness 3", "Witching", "Wonder", "dragon", "Wilderness Elite Diary", "Patch Notes (13 March 2014)", "Skull (status)", "Items Kept on Death", "Forinthry", "Wilderness ditch", "Wilderness Diary", "Wilderness Sign", "Wilderness Slayer", "Wilderness boss", "High-risk world", "Death"], "plainText": "The Wilderness, also known as the Wild or Wildy, is a large and dangerous wasteland that makes up most of north-eastern Gielinor.  '''''' <br> When the player is killed by another player, all unprotected items will be lost to the killer. Hardcore ironmen will lose their status if they die to a player, just like they would in the event of any other unsafe death. Two players or multiple players (within multi-combat zone) may fight if their combat levels are within a certain range of each other, increasing the deeper into the Wilderness they go. This, along with very high level monsters and bosses, makes the Wilderness one of the most dangerous places in the game. The area has its own set of achievement diaries, the Wilderness Diaries with tasks relating to the Wilderness and its surrounding area. Transportation {| class=\"wikitable\" |+ !Icon !Name !Transport location !Requirements and notes |- | |Carrallanger Teleport |Graveyard of Shadows | rowspan=\"4\" |frameless Desert Treasure I |- | |Dareeyak Teleport |Ruins (west) |- | |Ghorrock Teleport |Frozen Waste Plateau |- | |Annakarl Teleport |Demonic Ruins |- | |Cemetery Teleport |Forgotten Cemetery |Spoken to Tyss |- | |Ice Plateau Teleport |Frozen Waste Plateau |frameless Lunar Diplomacy |- | |Wilderness crabs teleport |Dark crab Fishing spot near Venenatis |None |- | |Respawn Teleport |Ferox Enclave |Spoken to Tyss |- |frameless |Minigame teleport |Ferox Enclave |None |- | |Revenant cave teleport |Lava Maze Revenant Caves entrance |None |- | |Burning amulet |Chaos Temple, Bandit Camp, Lava Maze |None |- | |Hunter cape teleport |Black chinchompa Hunter area | |- |width=21x21 |Canoe |Ferox Enclave, Wilderness pond (one-way) | |- |width=21x21 |Wilderness Obelisk |5 locations; see main page |None |- | |Ring of dueling |Ferox Enclave |None |- | rowspan=\"2\" |width=21x21 |Lever (Ardougne) | rowspan=\"2\" |Deserted Keep | rowspan=\"2\" |None |- |Lever (Edgeville) |- | |Wilderness sword 4 | rowspan=\"2\" |Fountain of Rune |frameless Elite Wilderness diary |- | |Wilderness sword 3 |frameless Hard Wilderness diary |} In-game history The area that is now the Wilderness was formerly known as Forinthry. It was a lush and green land by the time Gielinor was discovered by Guthix. Forinthry was very rich in resources, and so a perfect place to build settlements. Zaros, a god who entered Gielinor after Guthix entered his sleeping state at the end of the First Age, fully conquered this area during the Second Age. Many settlements were built, whose most powerful were Dareeyak, Carrallanger, Annakarl, and Ghorrock. Forinthry formed a very big part of Zaros' mighty empire, which was the most powerful of Gielinor at that time, and stretched from northern Forinthry, through Asgarnia and Misthalin, to the north-east of Morytania. Near the end of the second age, Zaros' Mahjarrat general Zamorak wanted to overthrow Zaros and was able to collect a group of followers. Those included (but were not limited to) the Elder Demon Thammaron, the vampyre Lord Drakan, the human warrior Viggora, and the Mahjarrat Zemouregal and Hazeel. When Zamorak got his hands on the Staff of Armadyl, he attacked Zaros' palace and after an intense battle, a large part of Zaros' powers were transferred via the staff to Zamorak, leading to his banishment. After that, Zamorak supposedly disappeared. Years later, he returned to Gielinor with an army so big that he could conquer the world and declared war against all the other gods, starting the God Wars. During the God Wars, Forinthry was under constant attack from the armies of Zamorak and Saradomin and soon started to crumble. The fortresses were dominated, and many of Zaros' remaining followers sided with Zamorak to not be his victims, and soon, the remaining settlements of Forinthry were destroyed, with the exception of Ghorrock. In the last years of the war Saradomin forged a temporary and fragile alliance with Armadyl and Bandos to defeat Zamorak, and in a following battle Zamorak brought destruction on the entire land by causing a massive explosion that swept across the continent turning it into the cursed wasteland today known as the Wilderness. The massive destruction caused Guthix to awake from his long slumber. Once awoken, Guthix led his army to the scarred land of Forinthry to end the war, which led him to establish the Edicts of Guthix, which banned all gods from ever directly influencing Gielinor again. Guthix went deep into the depths of Gielinor and wept, saddened by the effects the God Wars had wrought on the world that he had shaped and loved. The Wars ended, but the damage was done; many races like the aviansie, icyene, ourgs, and wyrms were almost wiped out of existence, and all of Gielinor suffered from the effects of the wars, but not one kingdom suffered as heavily as Forinthry. Forinthry is today known as the Wilderness and is now nothing but a cursed wasteland feared by adventurers. Today, many ruins of the once mighty cities still remain in these lands, barely recognisable as the great cities they were but rather piles of bricks and spirits of the creatures who died during the God Wars, who were forced to look after the Wilderness. They started wandering the Wilderness, attacking anything alive they would encounter; these spirits became known as the revenants. During the Fourth Age, there were many attempts to recolonise the Wilderness, none known to be particularly successful. There are still many creatures wandering around in the Wilderness, like many species of giants and demons. Due to the Wilderness being a site of ancient battles, it is also a place where dragons reside. Some outlaws, mostly Zamorakians, have also managed to set up their bases there, like bandits, dark warriors, rogues, and pirates. Unique mechanics Various gameplay mechanics that are present in the game may work differently when within the Wilderness, such as item behaviour, item-dropping behaviour, etc. Wilderness levels The Wilderness contains different levels, starting at level 1 at the Wilderness ditch, and increasing to level 56 in the far north. These levels are only indicated by a small screen overlay; there is no physical separation between levels. Players within combat level range of each other can attack each other in the Wilderness. This range is calculated by taking the combat level and adding or subtracting the current Wilderness level. For example, in level 1 Wilderness, a player who is level 100 can attack players who are level 99, level 101, or the same level. If a player is level 100 in level 24 Wilderness, they can attack players between level 76 and level 124 combat. The first two steps (squares/tiles) of the Wilderness are \"safe\" zones, in which players can not attack or be attacked, thus making the Wilderness ditch not a strict reminder of where the Wilderness starts. Both players (or the player and monsters) must be outside the safe zone before any PVP or Wilderness Slayer tasks can take effect. In PvP worlds, anywhere outside of a safe zone (except for the Wilderness itself) is level 15 Wilderness. Inside the Wilderness, this is further increased by the regular Wilderness level; e.g., being in level 20 Wilderness in a PvP world will essentially act as if the player is in level 35 Wilderness. Death mechanics are still based on the actual Wilderness level without this 15 level modifier, however. Deep Wilderness The deep Wilderness is separated from the lower part of the Wilderness by the Wilderness fence. Unlike the rest of the Wilderness, the deep Wilderness is only available to members. Death mechanics Upon dying in the Wilderness of any cause, the player will keep up to three (3) items upon respawning. Dying in the wilderness will not send food, potions, or the Phoenix Necklace to a Grave, and they are instead deleted. left If the player obtains a skull (either by attacking other player(s), entering the Abyss, or requesting one from NPCs like the Emblem Trader), they will lose all items on death. If the player is attacked by another player(s), they can fight them back without obtaining a skull.  The Protect Item prayer allows the player to keep one more item than usual if activated, meaning up to four (4) items are kept normally, but only one (1) is kept if skulled. Protect Item can NOT be activated on a high-risk world. If the player dies to other players, all tradeable items will only be visible to the player who dealt the most damage, lasting for one minute before becoming visible to other players. If the player dies to a monster, they are visible only to the victim for a minute. After this, all other players can see the dropped items for 59 minutes. Upon death, certain untradeable items (such as void armour, fire capes or fighter torso) will be converted into coins above level 20 Wilderness, and players must obtain the item again from its original source. Below level 20 Wilderness, any items deemed useful in combat turn into broken items instead, which can be repaired back into their original state with coins at Perdu. Other untradeable items, such as graceful clothing, will go to the gravestone. Teleport restrictions Teleports will not work above level 20 Wilderness, except for those methods listed below, which may be used by members up to level 30 Wilderness. However, the Teleport Block spell will prevent using nearly any means of teleportation for five minutes, or two and a half minutes if it is successfully cast on a player with Protect from Magic active. Also, you cannot teleport after using your special attacks on another player for a short duration. The chronicle does not work in the Wilderness. Member teleportation up to level 30 Wilderness * * * * * * * * * * * * * (acting as a ring of life when below 10% hitpoints) * (acting as a ring of life when below 10% hitpoints) Dropping items Except for raw food, cooked food, and potions, all dropped items become visible to other players immediately. This is to discourage players from dropping their items before dying and quickly making it back to pick them up. Cooked foods and potions never appear to other players, which is to prevent clans and teams from dropping supplies for each other during a fight. Additionally, manually dropped food and potions despawn in 15 seconds rather than 3 minutes, which is to prevent players from juggling food to give themselves an advantage in a fight. Black chinchompas cannot be released in combat. This was changed to prevent chinchompa hunters from releasing them to leave nothing for the player killer out of spite. Item behaviour * The damage reduction properties of Dinh's bulwark and the set effect of justiciar armour do not work. However, the passive effect of the elysian spirit shield does work. * Anglerfish and blighted anglerfish do not provide an overheal when eaten if you are in combat with another player or a non-player character. * The toxic blowpipe will have a base speed of 4 instead of 3 when fighting players; it is unaffected when fighting monsters in the Wilderness. * Powered staves cannot be used against other players, except Thammaron's sceptre and the accursed sceptre. * The serpentine helm will not venom players when used with a poisoned weapon. * Barrows equipment is protected upon a PvP death based on the broken version's value, not the value of the working version. See Items Kept on Death for more information. UI changes * Spell icons will not grow in size to reduce empty space in the spellbook while filters are enabled. This does not apply to the mobile client. * The autocast setting does not remember your selected spell if you change weapons, even if you have the required runes in your inventory. <!-- Not included: Teleport delay at revs, pjing inconsistencies (e.g. hitting someone in multi while you're in singles), probably a ton more --> Player killing left Since players can engage in combat within the Wilderness, player killing - or \"PKing\" - is a common activity. When a player is killed within the Wilderness, all of their lost tradeable items will be dropped for the opponent that has dealt the most damage, and some of the lost untradeable items will be dropped as coins. Note that this a dangerous activity. A clan of players killing an opponent. In multicombat areas, multiple players can attack the same target simultaneously. This allows players to form clans and engage in player-versus-player combat together, increasing the chances of successful knockouts. In single-way combat areas, there is an extended PJ timer: a combat-interruption prevention and temporary trade restriction mechanic that somewhat lessens teams' abilities to dominate individual players. However, to avoid getting attacked while looting in the dangerous areas, players are advised to unlock and turn on the loot keys. Many players create specialised accounts for player-versus-player combat called combat pures. The goal of these accounts is to gain an advantage over other players within their respective combat level brackets. Combat pures will often minimise or neglect defensive combat skills to maximise the damage output. See the combat pure and free-to-play combat pure guides for more details. Official worlds The following official worlds are assigned to Wilderness PKing: {| class=\"wikitable sortable\" style=\"text-align: left;\" !World !Location !Members !Activity |} Combat types Player-versus-player combat activities can be divided into the following types: * 1-vs-1 combat: arranged duels between willing players in single combat, usually within low-to-mid levels of the Wilderness north of Edgeville or around the Ferox Enclave. Players participating in this form of combat adhere to some unofficial rules that make the fights more dynamic and fair. These will vary between free-to-play and members worlds, but in general players tend not to use protection prayers, are expected to risk similar wealth in either items or cash, safing (healing too much in combat) and player jacking (interrupting fights) are frowned upon, and players are free to run or teleport when out of supplies. ** Risk fighting: a form of 1-vs-1 combat where players bring expensive items or coins to increase the carried risk; a players' experience and abilities in combat greatly affect the outcome. ** Deathmatches: a form of 1-vs-1 combat where players agree to fight to the death, even if they run out of supplies. * Singles-plus: functions similarly to single-way (1-vs-1) combat but in singles-plus players attacking other players will take priority over a monster or boss. * Multicombat: fights between groups of players within multicombat areas where any prayers, spells and items can be used to gain advantage in combat. None of the unofficial rules of arranged 1-vs-1 combat apply here. These fights are more common within the deeper Wilderness between individual players and small teams, as well as clans. * No honour combat: a type of combat where using any means necessary to kill the opponent is allowed. This usually includes techniques that are frowned upon and rarely utilised in typical arranged 1-vs-1 combat, breaking its unofficial set of rules. Examples of these include using freezing spells, protection prayers, safespotting, and all combat styles (tribridding). No honour (or nh) fights can occur in the deep Wilderness, as well as in arranged locations such as Edgeville in the PvP worlds. In a wider context, no honour can also be used to describe behaviours that are disapproved of among the PvP community, such as flaming, ragging or cheating. Unfair clan-related behaviours also fall under this category, examples of which include regrouping with a team when the fight was already over, or taking a fake ending picture at the end of a fight. * Hunting: some player killers target unwilling skillers or PvMers to kill them for loot. As in no honour combat, these players will use any technique they can to score the kill. For example, usage of Tele Block and binding or freezing spells is common. This type of combat is common around locations that provide high experience rates or high profit.  * Clan wars: arranged wars between clans, usually much larger in scale than regular multicombat fights. Bounty Hunter Bounty Hunter was a player killing-based minigame that could only be played on the Bounty Hunter world 318. Players were assigned a target to kill to earn points, which were used to be cashed in for rewards at the Bounty Hunter Shop. Players were matched based on their combat levels. Looting Players, especially those with low-level accounts, can cross player-killing hotspots with relatively few player killers being able to attack them. This allows them to pick up items dropped by other players, especially leftover food and ammunition. Looters pick up the remains of a drop after the killer finished taking the most expensive items. Some looters even tailgate player-killing clans, which can be especially profitable during wars. Looters are most common around the 1-vs-1 combat hotspots and the locations of arranged clan wars. Even though this is a relatively profitable activity for the looters, their presence may inconvenience players participating in player-versus-player battles by making them accidentally click to attack them. It is recommended that players turn off their left-click attack options after a successful kill to avoid attacking the looters. Notable locations A detailed map of the Wilderness, with levels, notable locations, and typical NPC spawns. {| class=\"wikitable\" !Place !Wilderness Level !On the Map !Other Information |- |Northern Edgeville Dungeon |1 - 5 |Underground, with exit to Obelisk of Air in level 5 Wilderness |Monsters include Black demons, Earth warriors, and Chaos druids. The Obelisk of Earth and exit to the Obelisk of Air are located here. |- |South Wilderness mine |6 - 7 |North of the Edgeville bank, near the tip of the River Lum |Contains coal and iron rocks. The Mage of Zamorak provides teleport access to the Abyss. |- |South-west Wilderness mine |9 |South of the Dark Warriors' Fortress |Contains 34 coal rocks. |- |Chaos Temple |13 |South-east of the Graveyard of Shadows, marked by the altar icon |This is one of only three altars in the Wilderness. Elder Chaos druids inhabit the area. |- |Dark Warriors' Fortress |13 - 16 |South of the Bandit Camp |Spawns all runes chaos and lower. |- |Ferox Enclave |13 - 16 |South of the Graveyard of Shadows |A safe haven surrounded by barriers in which players cannot attack each other. |- |Revenant Caves |17 - 40 |Three entrances - one east of the Dark Warriors' Fortress (low level), one north of the Bandit Camp (mid level), and one south-east of the Lava Maze (high level) |Home to Revenants. Requires a  coin entrance fee, which needs to be repaid if the player dies inside. |- |Wilderness Slayer Cave |17- 31<br/>(multicombat) |Two entrances - one east of the Dark Warriors' Fortress (low level), one south-west of the Silk Chasm (high level) |Home to various monsters assigned by Krystilia, who have slightly improved drop tables over their standard variants. |- |Wilderness God Wars Dungeon |23 - 29<br/>(multicombat) |East of The Forgotten Cemetery |Monsters drop ecumenical keys, and aviansie drop noted adamantite bars. |- |Bone Yard |27 - 30 |North of the Chaos Temple and east of the Ruins |Bones and Big bones spawn here, making it a popular Prayer training location. |- |Bandit Camp mine |29 - 35 |East of the Forgotten Cemetery |Contains 16 iron rocks, 31 coal rocks, 19 mithril rocks, 8 adamantite rocks. Most rocks of any free-to-play mining site. |- |Lava Dragon Isle |36 - 42 |West of the Demonic Ruins, east of the Lava Maze |The only location of Lava dragons. Two white berries spawn here. |- |Chaos Temple (hut) |38 |The small building just south of the Frozen Waste Plateau |Altar allows for Prayer training. A hotspot for PKers recharging their prayer or killing players training here. |- |Lava Maze |40 - 45 |South of the Mage Arena, west of Lava Dragon Isle |Many item spawns (gold necklace, steel platebody and staff of earth), and the Muddy chest can be looted. Has a single lava eel fishing spot. The dungeon contains Greater demons, Black dragons, and a runite rock. |- | Frozen Waste Plateau | 43 - 56 | North-western most part of the Wilderness, north of the Chaos Temple (hut) | Spawns water and cosmic runes, and features three runite rocks.  |- |Demonic Ruins |45 - 48<br/>(multicombat) |North-east corner, south of the Rogues' Castle |Prayer points are slowly recharged while within range. The only F2P location with Greater demons. Also features a blood rune and several burnt bones spawns. |- |Lava Maze runite mine |46 |North of the Lava Maze |The only free-to-play vein of runite ore. Player-killing hotspot. |- |Fountain of Rune |47 - 48<br/>(multicombat) |North-east corner, south of the Wilderness Volcano |Players can cast spells without runes here, though no experience is given. Dragonstone jewellery can gain extra charges from the fountain. |- |Rogues' Castle |51 |East of the Mage Arena and Deserted Keep |Contains rogues, which can be pickpocketed, and chests, which can be looted. |- |Resource Area |51 - 53 |East of the Deserted Keep, west of the Scorpion Pit |Contains Mining, Fishing and Woodcutting resources. One of the few fishing spots to contain dark crabs. Two Runite Golems are here. |- |Wilderness Volcano |53 - 56 |North-east of the Rogues' Castle |Odium wards and malediction wards are forged here. Dark crab fishing spots are nearby. |} Transportation methods Note: Some methods in this section may allow the player to immediately teleport into deep sections of the Wilderness. However, nothing except members' Deserted Keep, KBD and Mage Arena levers allow directly leaving the Wilderness above level 30, and pk-ers may tele-block the player below level 30. Always come prepared, as easy escape may not be readily available in an emergency. Spells {| class=\"wikitable sortable\" !Icon !Spell !<br/>level !Spellbook !Location !Wilderness<br/>level |- |File:Minigame icon.png |Minigame Teleport | - |Minigame Teleport |Clan Wars / Last Man Standing (Ferox Enclave) |13-16 |- |File:Dareeyak Teleport.png |Dareeyak Teleport |78 | rowspan=\"4\" |Ancient |Western Ruins |23 |- |File:Carrallanger Teleport.png |Carrallanger Teleport |84 |Graveyard of Shadows |19 |- |File:Annakarl Teleport.png |Annakarl Teleport |90 |Demonic Ruins |47 |- |File:Ghorrock Teleport.png |Ghorrock Teleport |96 | rowspan=\"3\" |Frozen Waste Plateau |45 |- |File:Ice Plateau Teleport.png |Ice Plateau Teleport |89 | rowspan=\"2\" |Lunar | rowspan=\"2\" |53 |- |File:Tele Group Ice Plateau.png |Tele Group Ice Plateau |90 |- |File:Cemetery Teleport.png |Cemetery Teleport |71 |Arceuus |The Forgotten Cemetery |31 |} Items * - has teleports to various locations around the Wilderness * - teleports players to Corporeal Beast Cave, where exiting the cave places players in the Wilderness * - teleports player to the Ferox Enclave * - offers five daily teleports to the black chinchompa Hunter area * - teleports the player to the Revenant Caves entrance at level 40 Wilderness * - offers one daily teleport to the Fountain of Rune * - offers unlimited teleports to the Fountain of Rune * - teleports the player to the dark crab fishing spots in level 33 Wilderness Obelisks A Wilderness Obelisk There are several obelisks scattered throughout the whole Wilderness that players can use to randomly teleport to particular locations within the Wilderness. If players have completed the hard Wilderness Diary, they can choose their teleport location. Also, players with level  can build their own obelisk that teleports them to the others, but this is a one-way teleport to the Wilderness. Like all Teleports, they will not teleport the player if they are Tele-Blocked. Other Players can use the Ardougne or Edgeville teleport lever to teleport to the Deserted Keep. Players with level Woodcutting can create a waka canoe at any canoe station, which transports the player to either the Ferox Enclave or the pond in level 35 Wilderness, east of a Wilderness Obelisk. The magic door found within the Entrana Dungeon teleports the player to level 32 Wilderness, south of the chaos dwarves near Vet'ion's Rest. Teleports close to the ditch The following methods can be used to safely teleport relatively close to the ditch (or other kind of border), but not yet into Wilderness: * File:Trollheim_Teleport.png Trollheim Teleport (tablet) is relatively close to  Agility shortcut to Wilderness (One-way shortcut) * File:Mind Altar Teleport.png Mind Altar Teleport (tablet) *  to Edgeville Monastery *  to Edgeville *  to Lesser Fanatic * File:Paddewwa Teleport.png Paddewwa Teleport (tablet) for access to the Edgeville Dungeon * Canoe to Edgeville * Fairy ring  *  Agility shortcut from Grand Exchange accessible by Varrock Teleport after completion of medium tier of Varrock Diary or via Spirit Tree or minecart *  * Balloon to Lumberyard Bosses The Wilderness is a host of many powerful bosses, who are able to drop rare items that can't be obtained anywhere else such as the malediction & odium ward, viggora's chainmace, thammaron's sceptre & craw's bow, the ring of the gods, tyrannical ring & treasonous ring and the voidwaker. Although the lair itself is not in the Wilderness, KBD is still considered a Wilderness boss by designation, as players must enter the Wilderness to fight him.  There are a plethora of other bosses in the Wilderness; all of whom reside above level 20 Wilderness, making escape choices very limited. The \"Big Three\" bosses of the Wilderness are considered to be Callisto, Venenatis, and Vet'ion. They all reside in their individual lairs, all of which are Multicombat areas, though players who leave them will be placed in the Escape Caves, a small but still dangerous dungeon. These bosses also have weaker variants; Artio, Spindel and Calvar'ion. As with their stronger counterparts, they have their own individual lairs, though these are in Singles-plus combat areas and are fairly far apart from each other. To access these weaker bosses, the player must have completed the Medium Wilderness tasks. While inside the lairs of any of these six bosses, players that have not completed the Hard Wilderness tasks will have to wait a 3 tick (1.8 second) delay before being able to teleport away. As the original three are above level 30 Wilderness, they cannot be teleported out of anyway. Logging out in the boss' lairs will either place them in the Escape Caves (original three) or outside the entrance (weaker variants) upon their return. These lairs can also be peeked into with 20 kill count at the boss to check if anyone is inside the lair. If the lair is empty, the resulting message will likewise state so. If the lair is occupied, the resulting message will inform the player that there is \"movement\" inside, and does not specify the amount of player(s) inside. Warning: All of these bosses are accessed through the Wilderness where other players can try to engage in player killing. It is advised to check items kept on death and only bring acceptable risk. {| class=\"wikitable sortable\" !Name !Image !Level !Hitpoints !Attack style !Location (combat type) !Notes |- |Callisto |170px |470 |1,000 |Crush, Magic, Ranged |Callisto's Den (multicombat) |A powerful bear that can deal massive AoE damage with his melee attacks, can deploy traps to limit the players' movement, and a shockwave attack that can deal up to 50 damage if not protected against. Unique drops: <br/>  <br/>  <br/>  <br/>  <br/>  <br/>  |- |Vet'ion |125px |454 |255 |Slash, Magic |Vet'ion's Rest (multicombat) |Uses attacks that can't be mitigated by prayer (but can be dodged), and when he reaches half health, it will summon two level 194 skeleton hellhounds to aid it, becoming invulnerable until they are killed. Also has a second form that is dealt with the same way as the first, except it summons level 231 Greater Skeleton Hellhounds instead. Unique drops: <br/>  <br/>  <br/>  <br/>  <br/>  <br/>  |- |Venenatis |200px |464 |850 |Stab, Magic, Ranged |Silk Chasm (multicombat) |Attacks with all three combat styles, but switches ranged and magic attacks after a few attacks, and are only used at a distance. Can summon spiderlings that drain Prayer and launch a sticky web that drains run energy and prayer points. Unique drops: <br/>  <br/>  <br/>  <br/>  <br/>  <br/>  |- |Artio |170px |320 |450 |Crush, Magic, Ranged |Hunter's End (singles-plus) |A scaled-down, solo-able version of Callisto. Unique drops: <br/>  <br/>  <br/>  <br/>  <br/>  <br/>  |- |Calvar'ion |125px |264 |150 |Slash, Magic |Skeletal Tomb (singles-plus) |A scaled-down, solo-able version of Vet'ion. Unique drops: <br/>  <br/>  <br/>  <br/>  <br/>  <br/>  |- |Spindel |200px |302 |515 |Stab, Magic, Ranged |Web Chasm (singles-plus) |A scaled-down, solo-able version of Venenatis. Unique drops: <br/>  <br/>  <br/>  <br/>  <br/>  <br/>  |- |Revenant maledictus |200px |397 |1250 |Ranged, Magic |Revenant Caves (singles-plus) |Has a small chance to spawn when killing revenants, can hit up to 30 with powerful Magic, and has a 5x5 area of effect attack. Unique drops: <br/>  <br/>  <br/>  <br/>  <br/>  <br/>  |- |Chaos Elemental |175px |305 |250 |Magic, Ranged, Melee |West of the Rogues' Castle (multicombat) |It can hit up to 28 in all three combat styles. Unique drops: <br/>  <br/>  <br/>  <br/>   |- |Chaos Fanatic |150px |202 |225 |Magic |West of the Lava Maze (single combat)  |Has a special attack that must be avoided in order to prevent damage. Unique drops: <br/>  <br/>  <br/>  <br/>  |- |Crazy archaeologist |75px |204 |225 |Crush, Ranged |Ruins south of The Forgotten Cemetery (single combat)  |Has a multi-area special attack that must be avoided by running away. Unique drops: <br/>  <br/>  <br/>  <br/>  |- |Scorpia |200px |225 |200 |Melee |Cavern beneath Scorpion Pit (multicombat) |Has a powerful poison that can damage up to 20. Unique drops: <br/>  <br/>  <br/>    <br />  |- |King Black Dragon\t |200px |276 |255 |Melee, Dragonfire (long-ranged) |His lair is accessed by pulling the lever in the western entrance of the Lava Maze Dungeon (single combat) \t |A particularly strong black dragon that is capable of various dragonfire attacks that can poison, stun and freeze. Unique drops: <br/>  <br/>  <br/>  <br/>  |} Skill training Throughout the Wilderness, there are many locations where players can train their skills. Many of these training methods are more profitable or faster than other options, though they all carry with them increased risk due to the threat of hostile player killers. Resource Area The Resource Area, located very deep within the Wilderness, provides a wide assortment of resources, such as various amounts of ores, magic trees, yew trees, and two runite golems, which can be killed and then mined for runite ore. There are static dark crab fishing spots that can be fished with a lobster pot and dark fishing bait. Players must pay 7,500 coins to enter the Resource Area, with free entry as a reward for completing the elite Wilderness Diary. While it is relatively safe, player killers can also pay the fee and attack people training in the area.  Prayer The Chaos Temple in level 38 Wilderness acts as a gilded altar with two burners lit, granting the same Prayer experience bonus when used in a player-owned house chapel. However, offering bones here gives a 50% chance for a bone offered to not be consumed, potentially saving lots of money if training prayer here in bulk. Outside, the Elder Chaos druid can unnote a player's bones for 50 coins per bone.  Runecraft Abyss Players can use the Abyss via Wilderness to access the various altars throughout RuneScape to craft runes; the Mage of Zamorak that teleports players to the Abyss lurks around level 5 Wilderness, just north of Edgeville. Many players will risk being killed to craft runes here as it is the fastest way available to train Runecraft for profit; normally, only runes above cosmic are crafted here as any rune below this is not considered worth the risk, and/or it takes less time to reach the appropriate altar by other means. Chaos Altar The Chaos Altar is located approximately north of Edgeville in level 9 Wilderness, and as with most other runic altars, requires a chaos talisman or tiara to enter. Here, players can craft chaos runes; however, entering conventionally requires players to traverse a multi-floor maze to reach the altar itself.  Mining Runite ore, which requires 85 Mining, is found in the Wilderness and is the only area where free players may mine it. Members can also mine here, although there are much safer locations such as the Heroes' Guild and Mourner Tunnels, which have some runite rocks. The Wilderness Rejuvenation update added several runite rocks at the Frozen Waste Plateau, making the Wilderness have the most runite rocks in-game. There are also other mines that can be visited, as there is usually no one around. The Skeleton Mine is a mine containing a large amount of coal rocks, although skeletons wander around the mine. The Bandit Camp Mine contains a relatively large amount of iron, coal, mithril, and adamantite rocks. However, given their extreme proximity to a bank and the dangers of the Wilderness, these mines are rarely frequented.  Fishing Some fishing spots are located throughout the Wilderness. * In the Bandit Camp, players can use the net/bait fishing spot to fish for shrimps, anchovies, sardines, and herring. * In the Lava Maze, members can fish for Lava eels using an oily fishing rod and fishing bait. * South of the Fountain of Rune and in the Wilderness Resource Area, players can fish dark crabs with dark fishing bait and a lobster pot. Dark crabs can be fished with 85 Fishing and heal 22 Hitpoints each once cooked, making it one of the best foods for players to use.  Hunter There are two hunting areas in the Wilderness: one for hunting black chinchompas (requires 73 Hunter), and another for hunting black salamanders (requires 67 Hunter). While in the Wilderness, the player can place one extra trap above their maximum amount, thus making hunting quicker and more profitable. The black chinchompa hunting location is particularly dangerous, as the location is a popular player-killing spot.  Woodcutting A player chops an Ent trunk after killing an Ent. Ents roam around the forests in low-level Wilderness, which if killed can be cut for noted logs. The type of logs is based on the Woodcutting level of the player and the Axe used. The Wilderness ents give double the amount of logs compared to the ents in the Woodcutting Guild, making it reasonably profitable at a high Woodcutting level if players are paying attention, as the ents are below level 20 Wilderness.  Thieving At 84 Thieving, players can loot the Chest (Rogues' Castle)s. Three chests are at the base of Rogues' Castle. The chest contains some decent rewards ranging, all of which are stackable, which can yield high profit over time. However, when a player loots from a chest, rogues within range will become aggressive, making this a dangerous but profitable method of money making.  Agility Requiring 52 Agility, there is the Wilderness Agility Course in the deep Wilderness. Although it can yield up to 40,000 experience per hour (without accounting for the agility tickets) it does not drop marks of grace. Players can deposit 150,000 coins into the Agility dispenser if they choose to, which grants gradually increasing rewards the more laps the player completes around the course.  Slayer Krystilia assigns an array of Slayer tasks that must be completed within the Wilderness. These monsters can range from combat level 1 to 470, assuming Wilderness bosses, and they all have a chance at dropping Larran's keys, Slayer's enchantments, ring of wealth scrolls and magic shortbow scrolls in addition to their regular drops. Also, whilst wearing a ring of wealth (i), players double their chances of receiving a clue scroll. Quests and Miniquests Family Crest The Family Crest quest requires you to kill Chronozon, who is located in the Wilderness part of Edgeville dungeon. Enter the abyss & Devious minds The miniquest Enter the Abyss and the quest Devious Minds require you to talk to the Mage of Zamorak, who is located in low-level Wilderness. Curse of the Empty Lord The Curse of the Empty Lord miniquest requires players to enter the Wilderness, though the exact location varies per player. Mage Arena This is where players with at least level 60 Magic can attempt Kolodion's challenge. Successful players receive the god cape and the god staff of their chosen God. Mage Arena II After completing the Mage Arena, players with at least level 75 Magic and the ability to cast the god spells outside the Mage Arena can attempt Kolodion's second challenge. Successful players receive an imbued god cape of their chosen god. Other features Ferox Enclave Ferox Enclave The Ferox Enclave is a safe haven in the central Wilderness (level 13-16). Players can access one of the only two banks in the Wilderness here and use the Pool of refreshment to restore hitpoints, prayer points, run energy, and stats. Ferox Enclave is also the location of the Clan Wars and Last Man Standing minigames.  There are gates on all four sides of the settlement, which allow players in and out of the Enclave, and there are numerous means of transportation to get there. Players who are teleblocked will be denied access to the Enclave. Wilderness Diary The Wilderness Diary can be completed by doing a set of tasks related to the Wilderness. Completing a tier gives a Wilderness sword and variety of useful rewards and benefits. For example, completion of all tasks grants noted dragon bone drops within the Wilderness and free access to the Resource Area. Team capes cape merchant Simon, found near the Chaos Temple.]] A number of Cape merchants are scattered across the Wilderness, each of them offering five colourful capes for sale. When worn, other players with the same cape will appear as blue dots on the Minimap, rather than the usual white. The attack option for players with the same cape will also be moved to a right-click option, rather than the usual left-click default for the Wilderness. These capes are popular for clans or other groups of players in the Wilderness, both to identify their allies and avoid accidentally attacking each other. Shop selling Many players have items that are of very little value to merchants and skill specialists. The Bandit Duty Free, a general store in the Bandit Camp, buys items for close to High Alchemy prices because the \"tax collectors do not visit his store.\" Clue scrolls Hard, elite, and master clue scrolls often require players to traverse through the Wilderness to complete a step of the Treasure Trail. In addition, players can still be attacked by player killers while fighting off any of the wizards that attack when in the process of digging for a clue or a reward casket. It is recommended to only bring what is necessary to complete the clue and prevent the loss of the clue scroll. A clue box can be brought along to protect the clue scroll or casket in case of death within the Wilderness. Wearing the ring of wealth (i) doubles the chance of clue scroll drops in the Wilderness by all monsters. Development history The Wilderness was introduced into the game on 13 August 2001, and quickly became famous as the only place where players could engage in Player-versus-Player (PvP) combat, to gain all items that the defeated dropped. A ditch was added on 18 April 2007 to prevent players from getting accidentally killed, tricked and lured into the Wilderness through tree luring. Currently, players passing the ditch are presented with a screen confirming that they want to enter the Wilderness. This can be toggled at the Doomsayer. Music * Army of Darkness * Everlasting Fire * Faithless * Forbidden * Inspiration * Moody * Pirates of Peril * Regal * Scape Sad * Scape Wild * Shining * Troubled * Undercurrent * Underground * Wild Isle * Wild Side * Wilderness * Wilderness 2 * Wilderness 3 * Witching * Wonder Changes See also *Skull (status) *Items Kept on Death *Forinthry *Wilderness ditch *Wilderness Diary *Wilderness Sign *Wilderness Slayer *Wilderness boss *High-risk world *Death Category:Player vs Player arenas"}, "originalContent": "{{External|rsc|rs}}\n{{Infobox Location\n|name = Wilderness\n|image = [[File:Wilderness.png|300px]]\n|release = [[13 August]] [[2001]]\n|update = Wilderness system online\n|aka = Wildy, Wild, Forinthry\n|members = No\n|capital = N/A (previously [[Senntisten]])\n|map = [[File:The Wilderness.png|300px]]\n|type = region\n|race=[[Human]]s\n|leagueRegion = Wilderness\n}}\n{{Relativelocation\n|location = Wilderness\n|west = Troll Country\n|south =Kingdom of Misthalin\n|east = Fossil Island\n}}\nThe '''Wilderness''', also known as '''the Wild''' or '''Wildy''', is a large and dangerous wasteland that makes up most of north-eastern [[Gielinor]]. \n\n'''{{Colour|red|Warning:}}''' {{Colour|red|The entirety of the Wilderness is a [[PvP]] area, except for [[Ferox Enclave]], [[Mage Arena bank]], [[Corporeal Beast's Lair]], and [[King <PERSON> Dragon Lair]].}}<br>\nWhen the player is killed by another player, all unprotected items will be lost to the killer. [[Hardcore ironmen]] will lose their status if they die to a player, just like they would in the event of any other unsafe death.\n\nTwo players or multiple players (within multi-combat zone) may fight if their combat levels are within a certain range of each other, increasing the [[#Wilderness levels|deeper into the Wilderness they go]]. This, along with very high level monsters and bosses, makes the Wilderness one of the most dangerous places in the game.\n\nThe area has its own set of [[achievement diaries]], the [[Wilderness Diary|Wilderness Diaries]] with tasks relating to the Wilderness and its surrounding area.\n\n==Transportation==\n{| class=\"wikitable\"\n|+\n!Icon\n!Name\n!Transport location\n!Requirements and notes\n|-\n|{{Plinkp|Carrallanger Teleport}}{{Plinkp|Carrallanger teleport (tablet)}}\n|[[Carrallanger Teleport|Carrallanger Teleport]]\n|[[Graveyard of Shadows]]\n| rowspan=\"4\" |[[File:Quest point icon.png|frameless]] [[Desert Treasure I]]\n|-\n|{{Plinkp|Dareeyak Teleport}}{{Plinkp|Dareeyak teleport (tablet)}}\n|[[Dareeyak Teleport]]\n|[[Ruins (west)]]\n|-\n|{{Plinkp|Ghorrock Teleport}}{{Plinkp|Ghorrock teleport (tablet)}}\n|[[Ghorrock Teleport]]\n|[[Frozen Waste Plateau]]\n|-\n|{{Plinkp|Annakarl Teleport}}{{Plinkp|Annakarl teleport (tablet)}}\n|[[Annakarl Teleport]]\n|[[Demonic Ruins]]\n|-\n|{{Plinkp|Cemetery Teleport}}{{Plinkp|Cemetery teleport (tablet)}}\n|[[Cemetery Teleport]]\n|[[Forgotten Cemetery]]\n|Spoken to [[Tyss]]\n|-\n|{{Plinkp|Ice Plateau Teleport}}{{Plinkp|Ice plateau teleport (tablet)}}\n|[[Ice Plateau Teleport]]\n|[[Frozen Waste Plateau]]\n|[[File:Quest point icon.png|frameless]] [[Lunar Diplomacy]]\n|-\n|{{Plinkp|Wilderness crabs teleport}}\n|[[Wilderness crabs teleport]]\n|[[Dark crab]] [[Fishing]] spot near [[Venenatis]]\n|None\n|-\n|{{Plinkp|Respawn Teleport}}\n|[[Respawn Teleport]]\n|[[Ferox Enclave]]\n|Spoken to [[Tyss]]\n|-\n|[[File:Minigame icon.png|frameless]]\n|[[Minigame teleport]]\n|[[Ferox Enclave]]\n|None\n|-\n|{{Plinkp|Revenant cave teleport}}\n|[[Revenant cave teleport]]\n|[[Lava Maze]] [[Revenant Caves]] entrance\n|None\n|-\n|{{Plinkp|Burning amulet}}\n|[[Burning amulet]]\n|[[Chaos Temple (Wilderness)|Chaos Temple]], [[Bandit Camp]], [[Lava Maze]]\n|None\n|-\n|{{Plinkp|Hunter cape}}\n|[[Hunter cape]] teleport\n|[[Black chinchompa]] Hunter area\n|{{SCP|Hunter|99|link=yes}}\n|-\n|[[File:Waka canoe.png|frameless|width=21x21]]\n|[[Canoe]]\n|[[Ferox Enclave]], Wilderness pond (one-way)\n|{{SCP|Woodcutting|57|link=yes}}\n|-\n|[[File:Obelisk (Construction) built.png|frameless|width=21x21]]\n|[[Wilderness Obelisk]]\n|5 locations; see main page\n|None\n|-\n|{{Plinkp|Ring of dueling}}\n|[[Ring of dueling]]\n|[[Ferox Enclave]]\n|None\n|-\n| rowspan=\"2\" |[[File:Lever (Mage Arena bank).png|frameless|width=21x21]]\n|[[Lever (Ardougne)]]\n| rowspan=\"2\" |[[Deserted Keep]]\n| rowspan=\"2\" |None\n|-\n|[[Lever (Edgeville)]]\n|-\n|{{Plinkp|Wilderness sword 4}}\n|[[Wilderness sword 4]]\n| rowspan=\"2\" |[[Fountain of Rune]]\n|[[File:Achievement Diaries icon.png|frameless]] [[Wilderness Diary#Elite|Elite Wilderness diary]]\n|-\n|{{Plinkp|Wilderness sword 3}}\n|[[Wilderness sword 3]]\n|[[File:Achievement Diaries icon.png|frameless]] [[Wilderness Diary#Hard|Hard Wilderness diary]]\n|}\n\n==In-game history==\nThe area that is now the Wilderness was formerly known as [[Forinthry]]. It was a lush and green land by the time Gielinor was discovered by [[Guthix]].\n\nForinthry was very rich in resources, and so a perfect place to build settlements. [[Zaros]], a god who entered Gielinor after Guthix entered his sleeping state at the end of the [[First Age]], fully conquered this area during the [[Second Age]]. Many settlements were built, whose most powerful were [[Dareeyak]], [[Carrallanger]], [[Annakarl]], and [[Ghorrock]]. Forinthry formed a very big part of [[Zarosian Empire|Zaros' mighty empire]], which was the most powerful of Gielinor at that time, and stretched from northern Forinthry, through [[Asgarnia]] and [[Misthalin]], to the north-east of [[Morytania]].\n\nNear the end of the second age, Zaros' [[Mahjarrat]] general [[Zamorak]] wanted to overthrow Zaros and was able to collect a group of followers. Those included (but were not limited to) the [[Tsutsaroth|Elder Demon]] [[Thammaron]], the [[vampyre]] [[Lord Drakan]], the human warrior [[Viggora]], and the Mahjarrat [[Zemouregal]] and [[Hazeel]]. When Zamorak got his hands on the [[Staff of Armadyl]], he attacked Zaros' palace and after an intense battle, a large part of Zaros' powers were transferred via the staff to Zamorak, leading to his banishment. After that, Zamorak supposedly disappeared. Years later, he returned to Gielinor with an army so big that he could conquer the world and declared war against all the other gods, starting the [[God Wars]].\n\nDuring the God Wars, Forinthry was under constant attack from the armies of Zamorak and [[Saradomin]] and soon started to crumble. The fortresses were dominated, and many of Zaros' remaining followers sided with Zamorak to not be his victims, and soon, the remaining settlements of Forinthry were destroyed, with the exception of Ghorrock.\n\nIn the last years of the war Saradomin forged a temporary and fragile alliance with [[Armadyl]] and [[Bandos]] to defeat Zamorak, and in a following battle Zamorak brought destruction on the entire land by causing a massive explosion that swept across the continent turning it into the cursed wasteland today known as the Wilderness. The massive destruction caused Guthix to awake from his long slumber. Once awoken, Guthix led his army to the scarred land of Forinthry to end the war, which led him to establish the [[Edicts of Guthix]], which banned all gods from ever directly influencing [[Gielinor]] again. Guthix went deep into the depths of Gielinor and wept, saddened by the effects the God Wars had wrought on the world that he had shaped and loved. The Wars ended, but the damage was done; many races like the [[aviansie (race)|aviansie]], [[icyene]], [[ourg]]s, and [[wyrms]] were almost wiped out of existence, and all of Gielinor suffered from the effects of the wars, but not one kingdom suffered as heavily as Forinthry.\n\nForinthry is today known as the Wilderness and is now nothing but a cursed wasteland feared by adventurers. Today, many ruins of the once mighty cities still remain in these lands, barely recognisable as the great cities they were but rather piles of bricks and spirits of the creatures who died during the God Wars, who were forced to look after the Wilderness. They started wandering the Wilderness, attacking anything alive they would encounter; these spirits became known as the [[revenants]].\n\nDuring the [[Fourth Age]], there were many attempts to recolonise the Wilderness, none known to be particularly successful. There are still many creatures wandering around in the Wilderness, like many species of [[giant]]s and [[demon]]s. Due to the Wilderness being a site of ancient battles, it is also a place where [[Dragon (race)|dragons]] reside. Some outlaws, mostly Zamorakians, have also managed to set up their bases there, like [[bandit]]s, [[dark warrior]]s, [[rogue]]s, and [[pirate]]s.\n\n==Unique mechanics==\nVarious gameplay mechanics that are present in the game may work differently when within the Wilderness, such as item behaviour, item-dropping behaviour, etc.\n\n===Wilderness levels===\nThe Wilderness contains different levels, starting at level 1 at the [[Wilderness ditch]], and increasing to level 56 in the far north. These levels are only indicated by a small screen overlay; there is no physical separation between levels.\n\nPlayers within [[combat level]] range of each other can attack each other in the Wilderness. This range is calculated by taking the [[combat]] level and adding or subtracting the current Wilderness level.\n\nFor example, in level 1 Wilderness, a player who is level 100 can attack players who are level 99, level 101, or the same level. If a player is level 100 in level 24 Wilderness, they can attack players between level 76 and level 124 combat.\n\nThe first two steps (squares/tiles) of the Wilderness are \"safe\" zones, in which players can not attack or be attacked, thus making the Wilderness ditch not a strict reminder of where the Wilderness starts. Both players (or the player and monsters) must be outside the safe zone before any PVP or Wilderness Slayer tasks can take effect.\n\nIn [[PvP world|PvP worlds]], anywhere outside of a safe zone (except for the Wilderness itself) is level 15 Wilderness. Inside the Wilderness, this is further increased by the regular Wilderness level; e.g., being in level 20 Wilderness in a PvP world will essentially act as if the player is in level 35 Wilderness. Death mechanics are still based on the actual Wilderness level without this 15 level modifier, however.\n\n====Deep Wilderness====\nThe deep Wilderness is separated from the lower part of the Wilderness by the [[Wilderness fence]]. Unlike the rest of the Wilderness, the deep Wilderness is only available to [[members]].\n\n===Death mechanics===\nUpon [[Death|dying]] in the Wilderness of any cause, the player will [[Items Kept on Death|keep up to three (3) items]] upon [[spawning#respawn point|respawning]].\n\nDying in the wilderness will not send food, potions, or the [[Phoenix Necklace]] to a [[Grave]], and they are instead deleted.\n\n[[File:Skull (status) icon.png|left]]\nIf the player obtains a [[skull (status)|skull]] (either by attacking other player(s), entering the [[Abyss]], or requesting one from NPCs like the [[Emblem Trader]]), they will '''lose all items''' on death. If the player is attacked by another player(s), they can fight them back without obtaining a skull. \n\nThe [[Protect Item]] [[prayer]] allows the player to keep one more item than usual if activated, meaning up to four (4) items are kept normally, but only one (1) is kept if skulled. Protect Item can '''NOT''' be activated on a [[high-risk world]].\n\nIf the player dies to other players, all tradeable items will only be visible to the player who dealt the most damage, lasting for one minute before becoming visible to other players. If the player dies to a monster, they are visible only to the victim for a minute. After this, all other players can see the dropped items for 59 minutes.\n\nUpon death, certain untradeable items (such as [[void armour]], [[fire cape]]s or [[fighter torso]]) will be converted into [[coins]] above level 20 Wilderness, and players must obtain the item again from its original source. Below level 20 Wilderness, any items deemed useful in combat turn into broken items instead, which can be repaired back into their original state with coins at [[Perdu]]. Other untradeable items, such as [[graceful clothing]], will go to the gravestone.\n\n===Teleport restrictions===\n[[Teleportation|Teleports]] will not work above level 20 Wilderness, except for those methods listed below, which may be used by [[members]] up to level 30 Wilderness. However, the [[Teleport Block]] spell will prevent using nearly any means of teleportation for five minutes, or two and a half minutes if it is successfully cast on a player with [[Protect from Magic]] active. Also, you cannot teleport after using your [[special attacks]] on another player for a short duration. The [[chronicle]] does not work in the Wilderness.\n\n====Member teleportation up to level 30 Wilderness====\n*{{plink|Amulet of glory|pic=Amulet of glory(4)}}\n*{{plink|Amulet of eternal glory}}\n*{{plink|Combat bracelet|pic=Combat bracelet(4)}}\n*{{plink|Skills necklace|pic=Skills necklace(4)}}\n*{{plink|Ring of wealth}}\n*{{plink|Pharaoh's sceptre}}\n*{{plink|Grand seed pod}}\n*{{plink|Royal seed pod}}\n*{{plink|Slayer ring}}\n*{{Plink|Slayer ring (eternal)}}\n*{{plink|Ring of life}}\n*{{plink|Escape crystal}}\n*{{plink|Defence cape}} (acting as a ring of life when below 10% hitpoints)\n*{{plink|Max cape}} (acting as a ring of life when below 10% hitpoints)\n\n===Dropping items===\nExcept for raw food, cooked food, and potions, all dropped items become visible to other players immediately. This is to discourage players from dropping their items before dying and quickly making it back to pick them up. Cooked foods and potions never appear to other players, which is to prevent clans and teams from dropping supplies for each other during a fight. Additionally, manually dropped food and potions despawn in 15 seconds rather than 3 minutes, which is to prevent players from juggling food to give themselves an advantage in a fight.\n\n[[Black chinchompa]]s cannot be released in combat. This was changed to prevent chinchompa hunters from releasing them to leave nothing for the player killer out of spite.\n\n===Item behaviour===\n{{Under Construction|nocat=yes}}\n* The damage reduction properties of [[Dinh's bulwark]] and the set effect of [[justiciar armour]] do not work. However, the passive effect of the [[elysian spirit shield]] '''does''' work.\n* [[Anglerfish]] and [[blighted anglerfish]] do not provide an overheal when eaten if you are in combat with another player or a non-player character.\n* The [[toxic blowpipe]] will have a base speed of 4 instead of 3 when fighting players; it is unaffected when fighting monsters in the Wilderness.\n* [[Powered staves]] cannot be used against other players, except [[Thammaron's sceptre]] and the [[accursed sceptre]].\n* The [[serpentine helm]] will not venom players when used with a poisoned weapon.\n* [[Barrows equipment]] is protected upon a PvP death based on the broken version's value, not the value of the working version. See [[Items Kept on Death#Unsafe item mechanics|Items Kept on Death]] for more information.\n\n===UI changes===\n{{Under Construction|nocat=yes}}\n* Spell icons will not grow in size to reduce empty space in the [[spellbook]] while filters are enabled. This does not apply to [[Old School RuneScape Mobile|the mobile client]].\n* The [[autocast]] setting does not remember your selected spell if you change weapons, even if you have the required runes in your inventory.\n<!-- Not included: Teleport delay at revs, pjing inconsistencies (e.g. hitting someone in multi while you're in singles), probably a ton more -->\n\n==Player killing==\n{{main|Player killing}}\n[[File:Skull (status) icon.png|left]]\n\nSince players can engage in combat within the Wilderness, [[player killing]] - or \"PKing\" - is a common activity. When a player is killed within the Wilderness, all of their lost tradeable items will be dropped for the opponent that has dealt the most damage, and some of the lost untradeable items will be dropped as coins. Note that this a '''dangerous''' activity.\n\n[[File:Player killing.gif|thumb|A clan of players killing an opponent.]]\n\nIn [[multicombat areas]], multiple players can attack the same target simultaneously. This allows players to form [[clan]]s and engage in [[player-versus-player]] combat together, increasing the chances of successful knockouts. In [[single-way combat]] areas, there is an extended [[PJ timer]]: a combat-interruption prevention and temporary trade restriction mechanic that somewhat lessens teams' abilities to dominate individual players. However, to avoid getting attacked while looting in the dangerous areas, players are advised to unlock and turn on the [[loot key]]s.\n\nMany players create specialised accounts for [[player-versus-player]] combat called [[combat pure]]s. The goal of these accounts is to gain an advantage over other players within their respective [[combat level]] brackets. Combat pures will often minimise or neglect defensive combat skills to maximise the damage output. See the [[combat pure]] and [[free-to-play combat pure guide]]s for more details.\n\n===Official worlds===\nThe following [[Server#Themed servers/worlds|official worlds]] are assigned to Wilderness PKing:\n\n{| class=\"wikitable sortable\" style=\"text-align: left;\"\n!World\n!Location\n!Members\n!Activity\n{{WorldLine|308|United Kingdom|mems=no|Wilderness PK - Free}}\n{{WorldLine|316|United Kingdom|mems=no|Wilderness PK - Free}}\n{{WorldLine|369|United States (east)|mems=yes|Wilderness PK - Members}}\n|}\n\n===Combat types===\n\n{{see also|Free-to-play PvP culture#Combat types}}\n\n[[Player-versus-player]] combat activities can be divided into the following types:\n\n* '''1-vs-1 combat''': arranged duels between willing players in [[Single-way_combat|single combat]], usually within low-to-mid levels of the Wilderness north of [[Edgeville]] or around the [[Ferox Enclave]]. Players participating in this form of combat adhere to some unofficial rules that make the fights more dynamic and fair. These will vary between [[free-to-play]] and [[members]] worlds, but in general players tend not to use [[protection prayers]], are expected to risk similar wealth in either items or cash, ''safing'' (healing too much in combat) and ''player jacking'' (interrupting fights) are frowned upon, and players are free to run or teleport when out of supplies.\n** '''Risk fighting''': a form of 1-vs-1 combat where players bring expensive items or coins to increase the carried ''risk''; a players' experience and abilities in combat greatly affect the outcome.\n** '''Deathmatches''': a form of 1-vs-1 combat where players agree to fight to the death, even if they run out of supplies.\n* '''Singles-plus''': functions similarly to single-way (1-vs-1) combat but in [[Singles-plus_combat|singles-plus]] players attacking other players will take priority over a monster or boss.\n* '''Multicombat''': fights between groups of players within [[multicombat area]]s where any prayers, spells and items can be used to gain advantage in combat. None of the unofficial rules of arranged 1-vs-1 combat apply here. These fights are more common within the deeper Wilderness between individual players and small teams, as well as [[clan]]s.\n\n* '''No honour combat''': a type of combat where using any means necessary to kill the opponent is allowed. This usually includes techniques that are frowned upon and rarely utilised in typical arranged 1-vs-1 combat, breaking its unofficial set of rules. Examples of these include using freezing spells, [[protection prayers]], [[safespotting]], and all combat styles (''tribridding''). No honour (or ''nh'') fights can occur in the deep Wilderness, as well as in arranged locations such as [[Edgeville]] in the [[PvP world]]s. In a wider context, ''no honour'' can also be used to describe behaviours that are disapproved of among the [[PvP]] community, such as [[flaming]], [[ragging]] or cheating. Unfair [[clan]]-related behaviours also fall under this category, examples of which include regrouping with a team when the fight was already over, or taking a fake ending picture at the end of a fight.\n* '''Hunting''': some player killers target unwilling [[skiller]]s or [[PvM]]ers to kill them for loot. As in no honour combat, these players will use any technique they can to score the kill. For example, usage of [[Tele Block]] and binding or freezing spells is common. This type of combat is common around locations that provide high experience rates or high profit. \n* '''Clan wars''': arranged wars between [[clan]]s, usually much larger in scale than regular multicombat fights.\n\n===Bounty Hunter===\n{{main|Bounty Hunter}}\n[[Bounty Hunter]] was a [[player killing]]-based [[minigame]] that could only be played on the Bounty Hunter world 318. Players were assigned a target to kill to earn points, which were used to be cashed in for rewards at the [[Bounty Hunter Shop]]. Players were matched based on their [[combat level]]s.\n\n===Looting===\nPlayers, especially those with low-level accounts, can cross player-killing hotspots with relatively few player killers being able to attack them. This allows them to pick up items dropped by other players, especially leftover food and ammunition. Looters pick up the remains of a drop after the killer finished taking the most expensive items. Some looters even tailgate player-killing [[clan]]s, which can be especially profitable during wars. Looters are most common around the 1-vs-1 combat hotspots and the locations of arranged clan wars.\n\nEven though this is a relatively profitable activity for the looters, their presence may inconvenience players participating in [[player-versus-player]] battles by making them accidentally click to attack them. It is recommended that players turn off their left-click attack options after a successful kill to avoid attacking the looters.\n\n==Notable locations==\n[[File:Wilderness map.png|thumb|358px|A detailed map of the Wilderness, with levels, notable locations, and typical NPC spawns.]]\n{| class=\"wikitable\"\n!Place\n!Wilderness Level\n!On the Map\n!Other Information\n|-\n|Northern [[Edgeville Dungeon]]\n|1 - 5\n|Underground, with exit to [[Obelisk of Air]] in level 5 Wilderness\n|Monsters include [[Black demon]]s, [[Earth warrior]]s, and [[Chaos druid]]s. The [[Obelisk of Earth]] and exit to the [[Obelisk of Air]] are located here.\n|-\n|[[South Wilderness mine]]\n|6 - 7\n|North of the [[Edgeville]] bank, near the tip of the [[River Lum]]\n|Contains [[coal rocks|coal]] and [[iron rocks]]. The [[Mage of Zamorak]] provides teleport access to the [[Abyss]].\n|-\n|[[South-west Wilderness mine]]\n|9\n|South of the [[Dark Warriors' Fortress]]\n|Contains 34 [[coal rocks]].\n|-\n|[[Chaos Temple (Wilderness)|Chaos Temple]]\n|13\n|South-east of the [[Graveyard of Shadows]], marked by the altar icon\n|This is one of only three altars in the Wilderness. [[Elder Chaos druid]]s inhabit the area.\n|-\n|[[Dark Warriors' Fortress]]\n|13 - 16\n|South of the [[Bandit Camp (Wilderness)|Bandit Camp]]\n|Spawns all runes chaos and lower.\n|-\n|[[Ferox Enclave]]\n|13 - 16\n|South of the [[Graveyard of Shadows]]\n|A safe haven surrounded by barriers in which players cannot attack each other.\n|-\n|[[Revenant Caves]]\n|17 - 40\n|Three entrances - one east of the [[Dark Warriors' Fortress]] (low level), one north of the [[Bandit Camp (Wilderness)|Bandit Camp]] (mid level), and one south-east of the [[Lava Maze]] (high level)\n|Home to [[Revenant]]s. Requires a {{Coins|100,000}} coin entrance fee, which needs to be repaid if the player dies inside.\n|-\n|[[Wilderness Slayer Cave]]\n|17- 31<br/>([[Multicombat area|multicombat]])\n|Two entrances - one east of the [[Dark Warriors' Fortress]] (low level), one south-west of the [[Silk Chasm]] (high level)\n|Home to various monsters assigned by [[Krystilia]], who have slightly improved drop tables over their standard variants.\n|-\n|[[Wilderness God Wars Dungeon]]\n|23 - 29<br/>([[Multicombat area|multicombat]])\n|East of [[The Forgotten Cemetery]]\n|Monsters drop [[ecumenical key]]s, and [[aviansie]] drop noted [[adamantite bar]]s.\n|-\n|[[Bone Yard]]\n|27 - 30\n|North of the [[Chaos Temple]] and east of the [[Ruins]]\n|[[Bones]] and [[Big bones]] spawn here, making it a popular Prayer training location.\n|-\n|[[Bandit Camp mine]]\n|29 - 35\n|East of the [[Forgotten Cemetery]]\n|Contains 16 [[iron rocks]], 31 [[coal rocks]], 19 [[mithril rocks]], 8 [[adamantite rocks]]. Most rocks of any [[free-to-play]] [[mining site]].\n|-\n|[[Lava Dragon Isle]]\n|36 - 42\n|West of the [[Demonic Ruins]], east of the [[Lava Maze]]\n|The only location of [[Lava dragon]]s. Two [[white berries]] spawn here.\n|-\n|[[Chaos Temple (hut)]]\n|38\n|The small building just south of the [[Frozen Waste Plateau]]\n|Altar allows for [[Prayer]] training. A hotspot for [[PKers]] recharging their prayer or killing players training here.\n|-\n|[[Lava Maze]]\n|40 - 45\n|South of the [[Mage Arena]], west of [[Lava Dragon Isle]]\n|Many item spawns ([[gold necklace]], [[steel platebody]] and [[staff of earth]]), and the [[Muddy chest]] can be looted. Has a single [[lava eel]] fishing spot. The [[Lava Maze Dungeon|dungeon]] contains [[Greater demon]]s, [[Black dragon]]s, and a [[runite rock]].\n|-\n| [[Frozen Waste Plateau]]\n| 43 - 56\n| North-western most part of the Wilderness, north of the [[Chaos Temple (hut)]]\n| Spawns [[water rune|water]] and [[cosmic rune]]s, and features three [[runite rocks]]{{(m)}}. \n|-\n|[[Demonic Ruins]]\n|45 - 48<br/>([[Multicombat area|multicombat]])\n|North-east corner, south of the [[Rogues' Castle]]\n|[[Prayer points]] are slowly recharged while within range. The only F2P location with [[Greater demon]]s. Also features a [[blood rune]] and several [[burnt bones]] spawns.\n|-\n|[[Lava Maze runite mine]]\n|46\n|North of the [[Lava Maze]]\n|The only free-to-play vein of [[runite ore]]. Player-killing hotspot.\n|-\n|[[Fountain of Rune]]\n|47 - 48<br/>([[Multicombat area|multicombat]])\n|North-east corner, south of the [[Wilderness Volcano]]\n|Players can cast [[spells]] without [[runes]] here, though no experience is given. [[Dragonstone#Enchanted jewellery|Dragonstone jewellery]] can gain extra charges from the fountain.\n|-\n|[[Rogues' Castle]]\n|51\n|East of the [[Mage Arena]] and [[Deserted Keep]]\n|Contains [[rogue]]s, which can be [[pickpocket]]ed, and [[Chest (Rogues' Castle)|chests]], which can be looted.\n|-\n|[[Resource Area]]\n|51 - 53\n|East of the [[Deserted Keep]], west of the [[Scorpion Pit]]\n|Contains [[Mining]], [[Fishing]] and [[Woodcutting]] resources. One of the few [[fishing spot]]s to contain [[dark crab]]s. Two [[Runite Golem]]s are here.\n|-\n|[[Wilderness Volcano]]\n|53 - 56\n|North-east of the [[Rogues' Castle]]\n|[[Odium ward]]s and [[malediction ward]]s are forged here. [[Dark crab]] fishing spots are nearby.\n|}\n\n==Transportation methods==\n'''Note''': Some methods in this section may allow the player to immediately teleport into deep sections of the Wilderness. However, nothing except members' [[Deserted Keep#Teleport lever|Deserted Keep]], [[King Black Dragon Lair|KBD]] and [[Mage Arena]] levers allow directly leaving the Wilderness above level 30, and pk-ers may tele-block the player below level 30. Always come prepared, as easy escape may not be readily available in an emergency.\n\n===Spells===\n{| class=\"wikitable sortable\"\n!Icon\n!Spell\n!{{SCP|Magic}}<br/>level\n!Spellbook\n!Location\n!Wilderness<br/>level\n|-\n|[[File:Minigame icon.png]]\n|[[Minigame Teleport]]\n| -\n|[[Minigame Teleport]]\n|[[Clan Wars]] / [[Last Man Standing]] ([[Ferox Enclave]])\n|13-16\n|-\n|[[File:Dareeyak Teleport.png]]\n|[[Dareeyak Teleport]]\n|78\n| rowspan=\"4\" |[[Ancient Magicks|Ancient]]\n|[[Ruins (west)|Western Ruins]]\n|23\n|-\n|[[File:Carrallanger Teleport.png]]\n|[[Carrallanger Teleport]]\n|84\n|[[Graveyard of Shadows]]\n|19\n|-\n|[[File:Annakarl Teleport.png]]\n|[[Annakarl Teleport]]\n|90\n|[[Demonic Ruins]]\n|47\n|-\n|[[File:Ghorrock Teleport.png]]\n|[[Ghorrock Teleport]]\n|96\n| rowspan=\"3\" |[[Frozen Waste Plateau]]\n|45\n|-\n|[[File:Ice Plateau Teleport.png]]\n|[[Ice Plateau Teleport]]\n|89\n| rowspan=\"2\" |[[Lunar spells|Lunar]]\n| rowspan=\"2\" |53\n|-\n|[[File:Tele Group Ice Plateau.png]]\n|[[Tele Group Ice Plateau]]\n|90\n|-\n|[[File:Cemetery Teleport.png]]\n|[[Cemetery Teleport]]\n|71\n|[[Arceuus spellbook|Arceuus]]\n|[[The Forgotten Cemetery]]\n|31\n|}\n\n===Items===\n*{{Plink|Burning amulet}} - has teleports to various locations around the Wilderness\n*{{Plink|Games necklace}} - teleports players to [[Corporeal Beast Cave]], where exiting the cave places players in the Wilderness\n*{{Plink|Ring of dueling}} - teleports player to the [[Ferox Enclave]]\n*{{Plink|Hunter cape}} - offers five daily teleports to the [[black chinchompa]] [[Hunter]] area\n*{{Plink|Revenant cave teleport}} - teleports the player to the Revenant Caves [[cavern (Revenant Caves)#Level 40|entrance]] at level 40 Wilderness\n*{{Plink|Wilderness sword 3}} - offers one daily teleport to the [[Fountain of Rune]]\n*{{Plink|Wilderness sword 4}} - offers unlimited teleports to the Fountain of Rune\n*{{Plink|Wilderness crabs teleport}} - teleports the player to the dark crab fishing spots in level 33 Wilderness\n\n===Obelisks===\n[[File:Wilderness Obelisk.png|right|thumb|200px|A Wilderness Obelisk]]\n{{Main|Wilderness Obelisk}}\nThere are several [[Wilderness Obelisk|obelisks]] scattered throughout the whole Wilderness that players can use to randomly teleport to particular locations within the Wilderness. If players have completed the hard [[Wilderness Diary]], they can choose their teleport location. Also, players with level {{SCP|Construction|80}} can build their own [[Obelisk (Construction)|obelisk]] that teleports them to the others, but this is a one-way teleport to the Wilderness. Like all Teleports, they will '''not''' teleport the player if they are Tele-Blocked.\n\n===Other===\nPlayers can use the [[Ardougne teleport lever|Ardougne]] or [[Edgeville teleport lever]] to teleport to the [[Deserted Keep]].\n\nPlayers with level {{SCP|Woodcutting|57}}[[Woodcutting]] can create a [[canoe|waka canoe]] at any canoe station, which transports the player to either the [[Ferox Enclave]] or the pond in level 35 Wilderness, east of a [[Wilderness Obelisk]].\n\nThe [[magic door]] found within the [[Entrana Dungeon]] teleports the player to level 32 Wilderness, south of the [[chaos dwarf|chaos dwarves]] near [[Vet'ion's Rest]].\n\n===Teleports close to the ditch===\nThe following methods can be used to safely teleport relatively close to the ditch (or other kind of border), but not yet into Wilderness:\n\n* [[File:Trollheim_Teleport.png]] [[Trollheim Teleport]] ([[Trollheim teleport (tablet)|tablet]]) is relatively close to {{SCP|Agility|64}} Agility shortcut to Wilderness (One-way shortcut)\n* [[File:Mind Altar Teleport.png]] [[Mind Altar Teleport]] ([[Mind Altar teleport (tablet)|tablet]])\n* {{plink|Combat bracelet}} to [[Edgeville Monastery]]\n* {{plink|Amulet of glory}} to [[Edgeville]]\n* {{plink|Achievement diary cape}} to [[Lesser Fanatic]]\n* [[File:Paddewwa Teleport.png]] [[Paddewwa Teleport]] ([[Paddewwa teleport (tablet)|tablet]]) for access to the [[Edgeville Dungeon]]\n* [[Canoe]] to Edgeville\n* [[Fairy ring]] {{Fairycode|DKR}}\n* {{SCP|Agility|21}} Agility shortcut from [[Grand Exchange]] accessible by [[Varrock Teleport]] after completion of medium tier of [[Varrock Diary]] or via [[Spirit Tree]] or [[Keldagrim minecart system|minecart]]\n* {{plink|Lumberyard teleport}}\n* [[Balloon]] to Lumberyard\n\n==Bosses==\nThe Wilderness is a host of many powerful [[bosses]], who are able to drop rare items that can't be obtained anywhere else such as the [[malediction ward|malediction]] & [[odium ward]], [[viggora's chainmace]], [[thammaron's sceptre]] & [[craw's bow]], the [[ring of the gods]], [[tyrannical ring]] & [[treasonous ring]] and the [[voidwaker]].\n\nAlthough the lair itself is not in the Wilderness, [[King Black Dragon|KBD]] is still considered a [[Wilderness]] boss by designation, as players must enter the Wilderness to fight him. \n\nThere are a plethora of other bosses in the Wilderness; all of whom reside above level 20 Wilderness, making escape choices very limited.\n\nThe \"Big Three\" bosses of the Wilderness are considered to be [[Callisto]], [[Venenatis]], and [[Vet'ion]]. They all reside in their individual lairs, all of which are [[Multicombat area]]s, though players who leave them will be placed in the [[Escape Caves]], a small but still dangerous dungeon.\n\nThese bosses also have weaker variants; [[Artio]], [[Spindel]] and [[Calvar'ion]]. As with their stronger counterparts, they have their own individual lairs, though these are in [[Singles-plus combat]] areas and are fairly far apart from each other. To access these weaker bosses, the player must have completed the [[Wilderness Diary#Medium|Medium Wilderness tasks]].\n\nWhile inside the lairs of any of these six bosses, players that have not completed the [[Wilderness Diary#Hard|Hard Wilderness tasks]] will have to wait a 3 tick (1.8 second) delay before being able to teleport away. As the original three are above level 30 Wilderness, they cannot be teleported out of anyway. Logging out in the boss' lairs will either place them in the Escape Caves (original three) or outside the entrance (weaker variants) upon their return. These lairs can also be peeked into with 20 kill count at the boss to check if anyone is inside the lair. If the lair is empty, the resulting message will likewise state so. If the lair is occupied, the resulting message will inform the player that there is \"movement\" inside, and '''does not''' specify the amount of player(s) inside.\n\n'''Warning''': All of these bosses are accessed through the Wilderness where other players can try to engage in [[Player_killing|player killing]]. It is advised to check [[Items_Kept_on_Death|items kept on death]] and only bring acceptable risk.\n{| class=\"wikitable sortable\"\n!Name\n!Image\n!Level\n!Hitpoints\n!Attack style\n!Location ([[Wilderness#Combat_types|combat type]])\n!Notes\n|-\n|[[Callisto]]\n|[[File:Callisto.png|centre|170px]]\n|470\n|1,000\n|[[Crush]], [[Magic]], [[Ranged]]\n|[[Callisto's Den]] ([[Multicombat_area|multicombat]])\n|A powerful bear that can deal massive AoE damage with his melee attacks, can deploy traps to limit the players' movement, and a shockwave attack that can deal up to 50 damage if not protected against.\nUnique drops: <br/> {{plink|Claws of callisto}} <br/> {{plink|Tyrannical ring}} <br/> {{plink|Voidwaker hilt}} <br/> {{plink|Dragon pickaxe}} <br/> {{plink|Dragon 2h sword}} <br/> {{plink|Callisto cub}}\n|-\n|[[Vet'ion]]\n|[[File:Vet'ion.png|centre|125px]]\n|454\n|255\n|[[Slash]], [[Magic]]\n|[[Vet'ion's Rest]] ([[Multicombat_area|multicombat]])\n|Uses attacks that can't be mitigated by prayer (but can be dodged), and when he reaches half health, it will summon two level 194 [[Skeleton Hellhound (Vet'ion)|skeleton hellhounds]] to aid it, becoming invulnerable until they are killed. Also has a second form that is dealt with the same way as the first, except it summons level 231 [[Greater Skeleton Hellhound (Vet'ion)|Greater Skeleton Hellhound]]s instead.\nUnique drops: <br/> {{plink|Skull of vet'ion}} <br/> {{plink|Ring of the gods}} <br/> {{plink|Voidwaker blade}} <br/> {{plink|Dragon pickaxe}} <br/> {{plink|Dragon 2h sword}} <br/> {{plink|Vet'ion jr.}}\n|-\n|[[Venenatis]]\n|[[File:Venenatis.png|centre|200px]]\n|464\n|850\n|[[Stab]], [[Magic]], [[Ranged]]\n|[[Silk Chasm]] ([[Multicombat_area|multicombat]])\n|Attacks with all three combat styles, but switches ranged and magic attacks after a few attacks, and are only used at a distance. Can summon [[Venenatis' Spiderling (monster)|spiderlings]] that drain [[Prayer]] and launch a sticky web that drains run energy and prayer points.\nUnique drops: <br/> {{plink|Fangs of venenatis}} <br/> {{plink|Treasonous ring}} <br/> {{plink|Voidwaker gem}} <br/> {{plink|Dragon pickaxe}} <br/> {{plink|Dragon 2h sword}} <br/> {{plink|Venenatis spiderling}}\n|-\n|[[Artio]]\n|[[File:Artio.png|centre|170px]]\n|320\n|450\n|[[Crush]], [[Magic]], [[Ranged]]\n|[[Hunter's End]] ([[Singles-plus_combat|singles-plus]])\n|A scaled-down, solo-able version of Callisto.\nUnique drops: <br/> {{plink|Claws of callisto}} <br/> {{plink|Tyrannical ring}} <br/> {{plink|Voidwaker hilt}} <br/> {{plink|Dragon pickaxe}} <br/> {{plink|Dragon 2h sword}} <br/> {{plink|Callisto cub}}\n|-\n|[[Calvar'ion]]\n|[[File:Calvar'ion.png|centre|125px]]\n|264\n|150\n|[[Slash]], [[Magic]]\n|[[Skeletal Tomb]] ([[Singles-plus_combat|singles-plus]])\n|A scaled-down, solo-able version of Vet'ion.\nUnique drops: <br/> {{plink|Skull of vet'ion}} <br/> {{plink|Ring of the gods}} <br/> {{plink|Voidwaker blade}} <br/> {{plink|Dragon pickaxe}} <br/> {{plink|Dragon 2h sword}} <br/> {{plink|Vet'ion jr.}}\n|-\n|[[Spindel]]\n|[[File:Spindel.png|centre|200px]]\n|302\n|515\n|[[Stab]], [[Magic]], [[Ranged]]\n|[[Web Chasm]] ([[Singles-plus_combat|singles-plus]])\n|A scaled-down, solo-able version of Venenatis.\nUnique drops: <br/> {{plink|Fangs of venenatis}} <br/> {{plink|Treasonous ring}} <br/> {{plink|Voidwaker gem}} <br/> {{plink|Dragon pickaxe}} <br/> {{plink|Dragon 2h sword}} <br/> {{plink|Venenatis spiderling}}\n|-\n|[[Revenant maledictus]]\n|[[File:Revenant maledictus.png|centre|200px]]\n|397\n|1250\n|[[Ranged]], [[Magic]]\n|[[Revenant Caves]] ([[Singles-plus_combat|singles-plus]])\n|Has a small chance to spawn when killing [[revenant]]s, can hit up to 30 with powerful Magic, and has a 5x5 [[area of effect]] attack.\nUnique drops: <br/> {{plink|Amulet of avarice}} <br/> {{plink|Craw's bow (u)}} <br/> {{plink|Thammaron's sceptre (u)}} <br/> {{plink|Viggora's chainmace (u)}} <br/> {{plink|Ancient crystal}} <br/> {{plink|Ancient statuette}}\n|-\n|[[Chaos Elemental]]\n|[[File:Chaos Elemental.png|centre|175px]]\n|305\n|250\n|[[Magic]], [[Ranged]], [[Melee]]\n|West of the [[Rogues' Castle]] ([[Multicombat_area|multicombat]])\n|It can hit up to 28 in all three [[combat styles]].\nUnique drops: <br/> {{plink|Dragon pickaxe}} <br/> {{plink|Dragon 2h sword}} <br/> {{plink|Dragon dagger}} <br/> {{plink|Pet chaos elemental}} \n|-\n|[[Chaos Fanatic]]\n|[[File:Chaos Fanatic.png|centre|150px]]\n|202\n|225\n|[[Magic]]\n|West of the [[Lava Maze]] ([[Single-way_combat|single combat]]) \n|Has a [[special attack]] that must be avoided in order to prevent damage.\nUnique drops: <br/> {{plink|Malediction shard 1}} <br/> {{plink|Odium shard 1}} <br/> {{plink|Ancient staff}} <br/> {{plink|Pet chaos elemental}}\n|-\n|[[Crazy archaeologist]]\n|[[File:Crazy archaeologist.png|centre|75px]]\n|204\n|225\n|[[Crush]], [[Ranged]]\n|[[Ruins (west)|Ruins]] south of [[The Forgotten Cemetery]] ([[Single-way_combat|single combat]]) \n|Has a multi-area [[special attack]] that must be avoided by running away.\nUnique drops: <br/> {{plink|Malediction shard 2}} <br/> {{plink|Odium shard 2}} <br/> {{plink|Rune crossbow}} <br/> {{plink|Fedora}}\n|-\n|[[Scorpia]]\n|[[File:Scorpia.png|centre|200px]]\n|225\n|200\n|[[Melee]]\n|Cavern beneath [[Scorpion Pit]] ([[Multicombat_area|multicombat]])\n|Has a powerful [[poison]] that can damage up to 20.\nUnique drops: <br/> {{plink|Malediction shard 3}} <br/> {{plink|Odium shard 3}} <br/> {{plink|Dragon scimitar}} \n\n{{plink|Dragon 2h sword}} <br /> {{plink|Scorpia's offspring}}\n|-\n|[[King Black Dragon]]\t\n|[[File:King Black Dragon.png|centre|200px]]\n|276\n|255\n|[[Melee]], [[Dragonfire]] (long-ranged)\n|[[King Black Dragon Lair|His lair]] is accessed by pulling the lever in the western entrance of the [[Lava Maze Dungeon]] ([[Single-way_combat|single combat]]) \t\n|A particularly strong [[black dragon]] that is capable of various dragonfire attacks that can [[poison]], [[Stun (status)|stun]] and freeze.\nUnique drops: <br/> {{plink| Draconic visage}} <br/> {{plink|Dragon pickaxe}} <br/> {{plink|Kbd heads}} <br/> {{plink|Prince black dragon}}\n|}\n\n==Skill training==\nThroughout the Wilderness, there are many locations where players can train their skills. Many of these training methods are more profitable or faster than other options, though they all carry with them increased risk due to the threat of hostile player killers.\n===Resource Area===\n{{main|Resource Area}}\nThe [[Resource Area]], located very deep within the Wilderness, provides a wide assortment of resources, such as various amounts of [[ores]], [[magic tree]]s, [[yew tree]]s, and two [[runite golem]]s, which can be killed and then mined for [[runite ore]]. There are static [[dark crab]] [[fishing spots]] that can be fished with a [[lobster pot]] and [[dark fishing bait]].\n\nPlayers must pay 7,500 [[coins]] to enter the Resource Area, with free entry as a reward for completing the elite [[Wilderness Diary]]. While it is relatively safe, [[player killers]] can also pay the fee and attack people training in the area.\n\n==={{plinkp|Prayer|pic=Prayer icon}} Prayer===\nThe [[Chaos Temple (hut)|Chaos Temple]] in level 38 Wilderness acts as a [[gilded altar]] with two burners lit, granting the same Prayer experience bonus when used in a [[player-owned house]] [[chapel]]. However, offering bones here gives a '''50% chance''' for a bone offered to '''not be consumed''', potentially saving lots of money if training [[prayer]] here in bulk. Outside, the [[Elder Chaos druid (NPC)|Elder Chaos druid]] can unnote a player's bones for 50 coins per bone.\n\n==={{plinkp|Runecraft|pic=Runecraft icon}} Runecraft===\n====Abyss====\n{{main|Abyss}}\nPlayers can use the [[Abyss]] via Wilderness to access the various altars throughout ''[[RuneScape]]'' to craft runes; the [[Mage of Zamorak]] that teleports players to the Abyss lurks around level 5 Wilderness, just north of [[Edgeville]]. Many players will [[risk]] being killed to craft runes here as it is the fastest way available to train [[Runecraft]] for profit; normally, only runes above [[Cosmic rune|cosmic]] are crafted here as any rune below this is not considered worth the risk, and/or it takes less time to reach the appropriate altar by other means.\n\n====Chaos Altar====\n{{Main|Chaos Altar}}\n\nThe [[Chaos Altar]] is located approximately north of Edgeville in level 9 Wilderness, and as with most other runic altars, requires a [[chaos talisman]] or [[chaos tiara|tiara]] to enter. Here, players can craft [[chaos runes]]; however, entering conventionally requires players to traverse a multi-floor maze to reach the altar itself.\n\n==={{plinkp|Mining|pic=Mining icon}} Mining===\n[[Runite ore]], which requires 85 [[Mining]], is found in the Wilderness and is the only area where [[free player]]s may mine it. [[Members]] can also mine here, although there are much safer locations such as the [[Heroes' Guild]] and [[Mourner Tunnels]], which have some [[runite rocks]]. The [[Update:Rejuvenating the Wilderness: More risk, more reward|Wilderness Rejuvenation]] update added several runite rocks at the [[Frozen Waste Plateau]], making the Wilderness have the most runite rocks in-game.\n\nThere are also other [[mines]] that can be visited, as there is usually no one around. The [[Skeleton Mine]] is a mine containing a large amount of [[coal rocks]], although [[skeleton]]s wander around the mine. The [[Bandit Camp Mine]] contains a relatively large amount of [[iron rocks|iron]], [[coal rocks|coal]], [[Mithril rocks|mithril]], and [[adamantite rocks]]. However, given their extreme proximity to a bank and the dangers of the Wilderness, these mines are rarely frequented.\n\n==={{plinkp|Fishing|pic=Fishing icon}} Fishing===\nSome [[fishing spot]]s are located throughout the Wilderness.\n* In the [[Bandit Camp (Wilderness)|Bandit Camp]], players can use the [[Fishing spot (small net, bait)|net/bait fishing spot]] to fish for [[shrimps]], [[anchovies]], [[sardine]]s, and [[herring]].\n* In the [[Lava Maze]], members can fish for [[Lava eel]]s using an [[oily fishing rod]] and [[fishing bait]].\n* South of the [[Fountain of Rune]] and in the [[Wilderness Resource Area]], players can fish [[dark crab]]s with [[dark fishing bait]] and a [[lobster pot]]. Dark crabs can be fished with 85 [[Fishing]] and heal 22 [[Hitpoints]] each once cooked, making it one of the best foods for players to use.\n\n==={{plinkp|Hunter|pic=Hunter icon}} Hunter===\nThere are two [[hunting]] areas in the Wilderness: one for hunting [[Black chinchompa (Hunter)|black chinchompa]]s (requires 73 [[Hunter]]), and another for hunting [[Black salamander (Hunter)|black salamanders]] (requires 67 [[Hunter]]). While in the Wilderness, the player can place one extra trap above their maximum amount, thus making hunting quicker and more profitable. The black chinchompa hunting location is particularly dangerous, as the location is a popular [[player-killing]] spot.\n\n==={{plinkp|Woodcutting|pic=Woodcutting icon}} Woodcutting===\n[[File:Chopping ent trunk.png|thumb|right|244px|A player chops an Ent trunk after killing an Ent.]]\n[[Ent]]s roam around the forests in low-level Wilderness, which if killed can be cut for noted [[logs]]. The type of logs is based on the [[Woodcutting]] level of the player and the [[Axe]] used.\n\nThe Wilderness ents give double the amount of logs compared to the ents in the Woodcutting Guild, making it reasonably profitable at a high Woodcutting level if players are paying attention, as the ents are below level 20 Wilderness.\n\n==={{plinkp|Thieving|pic=Thieving icon}} Thieving===\nAt 84 [[Thieving]], players can loot the [[Chest (Rogues' Castle)]]s. Three chests are at the base of [[Rogues' Castle]]. The chest contains some decent rewards ranging, all of which are stackable, which can yield high profit over time. However, when a player loots from a chest, [[rogue]]s within range will become aggressive, making this a dangerous but profitable method of [[money making]].\n\n==={{plinkp|Agility|pic=Agility icon}} Agility===\n{{main|Wilderness Agility Course}}\nRequiring 52 [[Agility]], there is the [[Wilderness Agility Course]] in the deep Wilderness. Although it can yield up to 40,000 [[experience]] per hour (without accounting for the [[Wilderness agility ticket|agility tickets]]) it does not drop [[marks of grace]]. Players can deposit 150,000 coins into the [[Agility dispenser]] if they choose to, which grants gradually increasing rewards the more laps the player completes around the course.\n\n==={{plinkp|Slayer|pic=Slayer icon}} Slayer===\n{{main|Krystilia|Krystilia/Strategies}}\n[[Krystilia]] assigns an array of [[Slayer task]]s that must be completed within the Wilderness. These [[monster]]s can range from [[combat level]] 1 to 470, assuming [[Wilderness bosses]], and they all have a chance at dropping [[Larran's key]]s, [[Slayer's enchantment]]s, [[ring of wealth scroll]]s and [[magic shortbow scroll]]s in addition to their regular [[drops]]. Also, whilst wearing a [[ring of wealth (i)]], players double their chances of receiving a [[clue scroll]].\n\n==Quests and Miniquests==\n===Family Crest===\nThe [[Family Crest]] quest requires you to kill [[Chronozon]], who is located in the Wilderness part of [[Edgeville dungeon]].\n\n===Enter the abyss & Devious minds===\nThe miniquest [[Enter the Abyss]] and the quest [[Devious Minds]] require you to talk to the [[Mage of Zamorak]], who is located in low-level Wilderness.\n\n===Curse of the Empty Lord===\nThe [[Curse of the Empty Lord]] [[miniquest]] requires players to enter the Wilderness, though the exact location varies per player.\n\n===Mage Arena===\n{{Main|Mage Arena}}\nThis is where players with at least level 60 [[Magic]] can attempt [[Kolodion]]'s challenge. Successful players receive the [[god cape]] and the [[god staff]] of their chosen God.\n\n===Mage Arena II===\n{{Main|Mage Arena II}}\nAfter completing the Mage Arena, players with at least level 75 [[Magic]] and the ability to cast the god spells outside the Mage Arena can attempt Kolodion's second challenge. Successful players receive an imbued god cape of their chosen god.\n\n==Other features==\n===Ferox Enclave===\n{{Main|Ferox Enclave}}\n[[File:Ferox Enclave.png|right|200px|thumb|[[Ferox Enclave]]]]\nThe [[Ferox Enclave]] is a '''safe''' haven in the central Wilderness (level 13-16). Players can access one of the only two [[bank]]s in the Wilderness here and use the [[Pool of refreshment]] to restore hitpoints, prayer points, run energy, and stats. Ferox Enclave is also the location of the [[Clan Wars]] and [[Last Man Standing]] [[minigame]]s. \nThere are gates on all four sides of the settlement, which allow players in and out of the Enclave, and there are numerous means of transportation to get there. Players who are [[teleblock]]ed will be denied access to the Enclave.\n\n===Wilderness Diary===\n{{Main|Wilderness Diary}}\nThe [[Wilderness Diary]] can be completed by doing a set of tasks related to the Wilderness. Completing a tier gives a [[Wilderness sword]] and variety of useful rewards and benefits. For example, completion of all tasks grants noted [[dragon bone]] drops within the Wilderness and free access to the [[Resource Area]].\n\n===Team capes===\n[[File:Simon.png|thumb|160px|The [[Cape merchants|cape merchant]] [[Simon]], found near the [[Chaos Temple (Wilderness)|Chaos Temple]].]]\n{{Main|Team cape}}\nA number of [[Cape merchants]] are scattered across the Wilderness, each of them offering five colourful [[Team capes|capes]] for sale. When worn, other players with the same cape will appear as blue dots on the [[Minimap]], rather than the usual white. The attack option for players with the same cape will also be moved to a right-click option, rather than the usual left-click default for the Wilderness. These capes are popular for [[clans]] or other groups of players in the Wilderness, both to identify their allies and avoid accidentally attacking each other.\n\n===Shop selling===\nMany players have items that are of very little value to merchants and skill specialists. The [[Bandit Duty Free]], a [[general store]] in the [[Bandit Camp (Wilderness)|Bandit Camp]], buys items for close to [[High Alchemy]] prices because the \"tax collectors do not visit his store.\"\n\n===Clue scrolls===\n[[Clue scroll (hard)|Hard]], [[Clue scroll (elite)|elite]], and [[Clue scroll (master)|master clue scroll]]s often require players to traverse through the Wilderness to complete a step of the [[Treasure Trail]]. In addition, players can still be attacked by [[player killers]] while fighting off any of the wizards that attack when in the process of digging for a clue or a reward casket. It is recommended to only bring what is necessary to complete the clue and prevent the loss of the clue scroll. A [[clue box]] can be brought along to protect the clue scroll or casket in case of death within the Wilderness.\n\nWearing the [[ring of wealth (i)]] doubles the chance of clue scroll drops in the Wilderness by all monsters.\n\n==Development history==\nThe Wilderness was introduced into the game on [[13 August]] [[2001]], and quickly became famous as the only place where players could engage in Player-versus-Player ([[PvP]]) [[combat]], to gain all items that the defeated dropped.\n\nA [[Wilderness ditch|ditch]] was added on [[18 April]] [[2007]] to prevent players from getting accidentally killed, [[scam|tricked and lured]] into the Wilderness through tree luring.\n\nCurrently, players passing the ditch are presented with a screen confirming that they want to enter the Wilderness. This can be toggled at the [[Doomsayer]].\n\n==Music==\n\n* [[Army of Darkness]]\n* [[Everlasting Fire]]\n* [[Faithless]]\n* [[Forbidden]]\n* [[Inspiration]]\n* [[Moody]]\n* [[Pirates of Peril]]\n* [[Regal]]\n* [[Scape Sad]]\n* [[Scape Wild]]\n* [[Shining]]\n* [[Troubled]]\n* [[Undercurrent]]\n* [[Underground (music track)|Underground]]\n* [[Wild Isle]]\n* [[Wild Side]]\n* [[Wilderness (music track)|Wilderness]]\n* [[Wilderness 2]]\n* [[Wilderness 3]]\n* [[Witching]]\n* [[Wonder]]\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 8 January 2025\n|update = Run Energy Changes\n|change = Food, potions, and the Phoenix Necklace no longer go to the Gravestone in the Wilderness, preventing unintended access to an unlimited supply.\n}}\n{{Subject changes\n|date = 10 April 2024\n|update = Undead Pirates, Colosseum Changes & more!\n|change =\n*Reduced respawn timers for most NPCs in the Wilderness (including bosses, but excluding Revenants) to 9 seconds.\n*Raw food no longer appears to other players when dropped in the Wilderness.\n*Manually dropped food or potions will now despawn faster in the Wilderness.\n}}\n{{Subject changes\n|date = 31 March 2016\n|update = Bugfixes & Combining Tentacles\n|change = Players can now attack one another when both stood on the tile outside of the Wilderness resource area.\n}}\n{{Subject changes\n|date = 10 September 2015\n|update = Slayer Assignment Rework\n|change = \n*All trail combat NPCs now cannot prevent a player from being attacked by a player inside a single combat zone of the Wilderness.\n*All trail combat NPCs now also do not allow a player to be attacked by several players at once in a single combat zone.\n*It is no longer possible to use the Jutting Wall entrance to the Wilderness GWD to avoid damage\n}}\n{{Subject changes\n|date = 20 August 2015\n|update = Boss slayer & total worlds\n|change = The welcome screen will no longer appear when logging in within the Wilderness.\n}}\n{{Subject changes\n|date = 6 August 2015\n|update = XP Drops, Jad Pet & Slayer\n|change = It is now possible to be attacked by a player in single combat Wilderness zones if you are fighting a Zamorak Mage when attempting to dig for your clue scroll.\n}}\n{{Subject changes\n|date = 5 March 2015\n|update = Achievement Diaries\n|poll = Old School Achievement Diary Rewards Poll\n|change = All [[dragon]] bone drops in the Wilderness are now noted if you have completed the [[Wilderness Elite Diary]].\n}}\n{{Subject changes\n|date = 13 March 2014\n|update = Rejuvenating the Wilderness: More risk, more reward\n|poll = Old School feature poll 15: Rejuvenating the Wilderness\n|change = The Wilderness Rejuvenation update has been released [[Patch Notes (13 March 2014)|(patch notes)]].\n}}\n{{Subject changes footer}}\n\n==See also==\n*[[Skull (status)]]\n*[[Items Kept on Death]]\n*[[Forinthry]]\n*[[Wilderness ditch]]\n*[[Wilderness Diary]]\n*[[Wilderness Sign]]\n*[[Wilderness Slayer]]\n*[[Wilderness boss]]\n*[[High-risk world]]\n*[[Death]]\n\n{{Wilderness}}\n{{Regions}}\n{{Free-to-play PvP}}\n[[Category:Player vs Player arenas]]"}