{"title": "Wilderness Slayer Cave", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [], "relationships": [{"from": "Wilderness Slayer Cave", "to": "21 October", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "2020", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Wilderness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "A Dangerous Game", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Wilderness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Slayer task", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Slayer Master", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Krystilia#Tasks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Trouver parchments", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Blighted", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Krystilia/Strategies#Requirements", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "<PERSON><PERSON><PERSON>'s key", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Dwarf multicannon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Slayer master", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Wilderness weapons", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Slayer helmet(i)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Ancient magicks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Ice Barrage", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Ice Burst", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Blighted ancient ice sack", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Blighted super restore", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Agility", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Summer pie", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "player killer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "<PERSON><PERSON><PERSON>'s small chest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Silk Chasm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Games necklace", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Corporeal Beast", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Player vs Player", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Pool of Refreshment", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Carrallanger teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Carrallanger teleport (tablet)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Graveyard of Shadows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Wilderness crabs teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Player killing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Hunter cape", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Black chincho<PERSON> (Hunter)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Burning amulet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Abyssal demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Ankou#Level 98", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Black demon#Level 188", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Black dragon#Level 247", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Dust devil", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Greater demon#Level 104", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Greater Nechryael", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Green dragon#Level 88", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Hellhound#Level 136", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Ice giant#Level 67", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "<PERSON>lly", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Lesser demon#Level 94", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Bandit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Slayer reward point#Extend", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Abyssal demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Priest in Peril", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Black demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Black dragon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Dust devil", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Greater demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Green dragon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Greater Nechryael", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Hellhound", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Ice giant", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "<PERSON>lly", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Lesser demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Hellhounds", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "amulet of avarice", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Green dragon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "abyssal demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "dust devil", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "jelly", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Greater Nechryael", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "green dragon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "blighted", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "<PERSON><PERSON>'s stuff for the Last Shopper Standing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "A Dangerous Game", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wilderness Slayer Cave", "to": "Revenants (music track)", "type": "references", "confidence": 0.7, "source": "wiki_link"}], "embedding": [0.05031181126832962, -0.033377040177583694, -0.0010772034293040633, 0.01831529289484024, -0.06605873256921768, 0.0025665017310529947, -0.07381318509578705, 0.010757519863545895, -0.016247116029262543, -0.024256300181150436, -0.013478981330990791, 0.06645935773849487, -0.013453432358801365, -0.06219393387436867, 0.00012213543232064694, -0.0003503953048493713, 0.02437436208128929, -0.03138439357280731, -0.059322211891412735, -0.005854623857885599, -0.03744681179523468, -0.016240976750850677, -0.018303584307432175, -0.0262374896556139, -0.016858557239174843, -0.03251543641090393, -0.016398001462221146, 0.034161925315856934, -0.0021435378585010767, -0.06319783627986908, 0.011382072232663631, -0.06565655767917633, 0.06421998143196106, 0.04817136749625206, 2.3423604034178425e-06, 0.005199796985834837, 0.007448094896972179, 0.01853262074291706, -0.042428575456142426, 0.016457732766866684, -0.08671269565820694, 0.0335923433303833, 0.023284070193767548, 0.007126927375793457, 0.007087824400514364, -0.0817931517958641, 0.024050921201705933, 0.038746774196624756, 0.01926940120756626, -0.026690825819969177, 0.0050201709382236, -0.04756810516119003, -0.03311413154006004, 0.004775531589984894, 0.03607039898633957, 0.050127577036619186, 0.035024672746658325, -0.03304961323738098, -0.012679467909038067, -0.0021511814557015896, -0.010782822035253048, 0.012681784108281136, 0.031121084466576576, 0.0042266822420060635, 0.03162373974919319, -0.033316295593976974, -0.09921745955944061, -0.09748614579439163, -0.0008088790345937014, -0.01319151185452938, -0.060685694217681885, -0.01964438520371914, 0.0009725380805321038, 0.06215544044971466, -0.04407251253724098, -0.011818597093224525, 0.005579693708568811, 0.037024419754743576, -0.03669136390089989, -0.0026340866461396217, 0.02189607359468937, 0.02960459142923355, 0.02777419425547123, -0.0043154810555279255, 0.012962915934622288, -0.005339931696653366, 0.01232406310737133, -0.013718548230826855, -0.022358888760209084, 0.03074558451771736, -0.042367853224277496, 0.020464664325118065, -0.020384185016155243, -0.004393430892378092, -0.0847688764333725, -0.023577148094773293, 0.014585435390472412, 0.030136656016111374, 0.034908048808574677, 0.07945223152637482, -0.02790750004351139, -0.027546050027012825, -0.09983234852552414, 0.03364820033311844, 0.0023408771958202124, -0.06365291029214859, 0.036259669810533524, -0.04389037936925888, 0.017682436853647232, -0.0046507553197443485, 0.0005079770344309509, -0.003386803437024355, -0.019624318927526474, -0.06604510545730591, 0.08999480307102203, 0.04583932086825371, 0.043611012399196625, 0.003096256172284484, 0.0035698360297828913, -0.0033867883030325174, -0.09371232986450195, -0.0034386031329631805, 0.03382840007543564, -0.0016809109365567565, 0.027845129370689392, -0.05898270383477211, -0.027867799624800682, 0.0016861716285347939, 0.02055951952934265, -0.018095683306455612, -0.026341605931520462, 0.017979903146624565, 0.017814824357628822, -0.016375796869397163, 0.007025877013802528, 0.0019867292139679193, 0.019630122929811478, -0.006623953115195036, -0.05177782475948334, 0.06192424148321152, 0.02873547375202179, 0.00703527731820941, -0.030142759904265404, 0.010422330349683762, 0.01942402496933937, 0.0042897420935332775, -0.03154638782143593, 0.01235396321862936, 0.003487470094114542, 0.025807473808526993, 0.02884303778409958, 0.04044394567608833, 0.0514645054936409, -0.021686038002371788, 0.022451482713222504, 0.007880338467657566, -0.02491459622979164, -0.0005359207862056792, 0.0288824662566185, 0.10930774360895157, -0.036581311374902725, -0.0188900213688612, -0.01722194254398346, -0.028467902913689613, -0.03361091390252113, -0.019951412454247475, -0.06090159714221954, 0.07699689269065857, 0.011026899330317974, -0.024747410789132118, -0.0234341062605381, 0.038408152759075165, -0.025419333949685097, -0.007849873043596745, 0.016667282208800316, 0.1450727880001068, 0.1064801812171936, 0.08870719373226166, -0.05200415104627609, -0.050543855875730515, -0.0020190232899039984, -0.04937136918306351, 0.011336036957800388, -0.02560431882739067, -0.007136058993637562, -0.04914400354027748, -0.00437551736831665, -0.0002400629746261984, 0.05608478933572769, -0.04827115312218666, -0.037889543920755386, 0.05551627650856972, -0.00447242334485054, -0.013644104823470116, 0.02262224815785885, 0.0011412623571231961, -0.010323853231966496, -0.10113844275474548, -0.021937062963843346, -0.0474073588848114, 0.04170782491564751, 0.06090621277689934, 0.039299990981817245, -0.04714943841099739, 0.010348567739129066, -0.03990651294589043, 0.01570233143866062, 0.024266423657536507, -0.05944083258509636, 0.02839171141386032, -0.011995750479400158, 0.02572435326874256, 0.011432684026658535, -0.00337769091129303, -0.07274162024259567, 0.014176342636346817, -0.00609599007293582, 0.038238439708948135, -0.0029709660448133945, -0.03747814521193504, -0.020517924800515175, -0.0669240728020668, -0.03594737499952316, 0.02849290706217289, 0.03315130993723869, 0.055287353694438934, 0.00716072553768754, 0.027929574251174927, 0.012430243194103241, -0.06170593947172165, -0.01137465424835682, 0.0027160330209881067, 0.02792860008776188, -0.04045766219496727, -0.03508027270436287, -0.03998942673206329, 0.023738157004117966, -0.018509233370423317, 0.004046959802508354, -0.04300034046173096, -0.0010118820937350392, -0.015203149057924747, 0.01801854930818081, 0.030057983472943306, 0.0174525436013937, -0.0841749757528305, 0.03662008047103882, -0.013503167778253555, -0.03387226164340973, -0.05094265937805176, -0.02811271697282791, -0.009372844360768795, 0.037322334945201874, 0.034066472202539444, -0.0032270862720906734, 0.00072578463004902, 0.008147582411766052, 0.012040679343044758, 0.007874030619859695, 0.03337685391306877, -0.06157519668340683, -0.025684531778097153, 0.013629263266921043, 0.0012384511064738035, -0.014390002936124802, 0.030163167044520378, -0.036522336304187775, 0.06781164556741714, 0.02216545306146145, -0.011451968923211098, 0.056075673550367355, -0.01510706078261137, -0.011298656463623047, 0.015118466690182686, 0.0036069368943572044, -0.0004038238257635385, -0.024411439895629883, 0.004952515475451946, -0.006907664239406586, 0.0034522395581007004, -0.009936046786606312, 0.06144155189394951, -0.010203490033745766, 0.03746108338236809, 0.028611015528440475, 0.029747003689408302, -0.024551240727305412, -0.09247355163097382, -0.0021892855875194073, 0.024971777573227882, 0.03105114959180355, -0.001564075704663992, 0.000526872172486037, -0.07447822391986847, -0.0017348864348605275, 0.02102186158299446, 0.10963024944067001, 0.0030947159975767136, 0.04518968239426613, 0.05088306963443756, 0.026966167613863945, 0.0228615403175354, 0.00036417419323697686, 0.008276381529867649, 0.05622733756899834, -0.030885882675647736, 0.010357348248362541, 0.02372587099671364, 0.03753587231040001, -0.018113920465111732, 0.006995937321335077, -0.0165599025785923, -0.010121500119566917, -0.01617417484521866, 0.01621965318918228, 0.014969665557146072, 0.011230917647480965, -0.030099082738161087, -0.005229454953223467, 0.021193720400333405, -0.0297659020870924, -0.00837203860282898, -0.019136423245072365, -0.01371182780712843, -0.01543188001960516, -0.010364257730543613, 0.04085123538970947, 0.017578184604644775, -0.06467001140117645, 0.04050388187170029, 0.04373568668961525, 0.050588272511959076, -0.03802168369293213, -0.011178489774465561, 0.03485424816608429, 0.012891625985503197, -0.016741979867219925, -0.10710940510034561, 0.030606906861066818, 0.015834810212254524, 0.03170916065573692, 0.00947549194097519, -0.036760613322257996, -0.002117268042638898, 0.06096683442592621, 0.004779215902090073, -0.0395638570189476, 0.01835898496210575, -0.010859247297048569, 0.006622439716011286, 0.037937674671411514, -0.020155349746346474, 0.002030112547799945, -0.02287590689957142, -0.0028395301196724176, -0.007471155375242233, -0.02832881361246109, 0.03651756793260574, 0.0779571384191513, 0.01885150372982025, 0.013685474172234535, 0.056043244898319244, -0.020638493821024895, -0.020023265853524208, 0.030902830883860588, 0.05506005510687828, 0.004058692138642073, -0.02234736457467079, -0.00522827310487628, -0.0020875525660812855, -0.029013723134994507, 0.024879248812794685, 0.03301134333014488, -0.007616288959980011, 0.034318435937166214, -0.03632831200957298, 0.017779076471924782, -0.06602349877357483, 0.05303969606757164, 0.05505983531475067, -0.028887374326586723, -0.02282383292913437, 0.028316009789705276, 0.004776301793754101, 0.05146397650241852, -0.01313190907239914, 0.020209772512316704, 0.009220847859978676, 0.05026671662926674, 0.023826010525226593, 0.03234132379293442, -0.03370751440525055, 0.038857679814100266, 0.0035752183757722378, -0.031598079949617386, -0.020219583064317703, -0.049555640667676926, 0.056227393448352814, -0.02476678416132927, 0.004060161300003529, 0.020287416875362396, -0.006099027115851641, 0.024032190442085266, 0.02314726822078228, -0.04604625701904297, 0.01415120530873537, 0.04772457852959633, -0.012206438928842545, 0.04606045410037041, 0.14527347683906555, -0.10786262154579163, -0.011130687780678272, -0.017796717584133148, -0.022920994088053703, 0.02232312597334385, 0.0327468179166317, -0.035453349351882935, 0.0034276533406227827, -0.04367152974009514, -0.01053580641746521, 0.0032571337651461363, -0.024553779512643814, -0.015057612210512161, -0.017483318224549294, 0.03297329694032669, 0.005977757275104523, 0.006433004047721624, -0.026735585182905197, 0.02592167630791664, 0.03924401104450226, 0.030380763113498688, 0.010776794515550137, -0.020655956119298935, -0.0166944433003664, 0.0834706574678421, -0.022952230647206306, 0.029559355229139328, -0.03131256625056267, -0.001713626435957849, -0.025496136397123337, 0.020487910136580467, 0.04574250802397728, 0.04996776953339577, -0.026056645438075066, -0.008047103881835938, 0.0002802749222610146, -0.014550364576280117, -0.031070806086063385, 0.04126117751002312, 0.04261421784758568, -0.04237009212374687, 0.01105890516191721, 0.02284933812916279, 0.017101500183343887, -0.041093237698078156, 0.016265852376818657, 0.041162602603435516, -0.055068034678697586, -0.02963555045425892, -0.03715179115533829, 0.027584780007600784, 0.005526284221559763, -0.019341517239809036, -0.029031263664364815, -0.023746203631162643, 0.051068805158138275, -0.0753684788942337, -0.07200770080089569, -0.0006357337697409093, -0.03909715265035629, 0.034119706600904465, -0.005416841246187687, -0.0021434880327433348, -0.01558646745979786, -0.0062372381798923016, 0.009261399507522583, 0.006547803059220314, 0.000862868269905448, 0.029838599264621735, 0.028452076017856598, -0.022075273096561432, -0.01996017061173916, 0.039422668516635895, 0.015495935454964638, 0.030574820935726166, 0.011231837794184685, 0.047428570687770844, -0.037506818771362305, -0.017757920548319817, 0.024346917867660522, 0.007037075702100992, 0.01695266179740429, -0.004686031490564346, 0.058283910155296326, 0.05075782537460327, 0.039650820195674896, -0.08264534920454025, -0.05052293464541435, 0.10196803510189056, -0.0687260702252388, 0.02607796899974346, 0.038741905242204666, -0.02721458114683628, -0.01156165823340416, 0.03297186642885208, 0.00397228728979826, -0.04290959611535072, -0.0016158956568688154, -0.0055900742299854755, 0.0003176270693074912, 0.005211067385971546, -0.00883492548018694, -0.011112275533378124, -0.02412993833422661, 0.034895267337560654, -0.021384919062256813, 0.013221938163042068, 0.05678481608629227, 0.046312760561704636, -0.02464853785932064, -0.008436844684183598, 1.0542438758420758e-05, 0.07225391268730164, -0.017418064177036285, 3.745974026969634e-06, 0.016460657119750977, 0.01953318528831005, 0.00841672532260418, -0.02946716547012329, -0.024558978155255318, 0.1206837072968483, 0.053904939442873, 0.019662657752633095, -0.011698686517775059, -0.008372722193598747, 0.04300409555435181, 0.011249552480876446, 0.07055434584617615, 0.023777039721608162, 0.003642341820523143, 0.03297583758831024, -0.034736715257167816, -0.02867022529244423, -0.03131195157766342, -0.008061039261519909, -0.030728736892342567, -0.03662734478712082, 0.004609490744769573, -0.03279304876923561, -0.06225365400314331, 0.015490604564547539, -0.023701008409261703, 0.014063484035432339, -0.015173817053437233, -0.05066518113017082, -6.37023972066011e-33, -0.010218115523457527, -0.06582034379243851, -0.001553426729515195, -0.10572420060634613, -0.017745105549693108, -0.036119524389505386, 0.03449392691254616, 0.003917925525456667, -0.03678319603204727, -0.012041972950100899, 0.0042998599819839, 0.010189327411353588, -0.008388679474592209, 0.021431775763630867, -0.01319975033402443, 0.03509923443198204, -0.02596050687134266, -0.014554849825799465, 0.01705046556890011, -0.04222480207681656, -0.028874952346086502, -0.0007311683730222285, 0.06378822773694992, 0.03171323984861374, -0.037581849843263626, -0.00014184312021825463, 0.007617376744747162, 0.0011760072084143758, -0.0067700608633458614, -0.0343855619430542, -0.02532445453107357, 0.031683530658483505, -0.007427250035107136, 0.06600256264209747, 0.0232276301831007, -0.040936827659606934, -0.007701224647462368, -0.0027473322115838528, -0.0013643805868923664, 0.04770735651254654, -0.0035811096895486116, -0.006368318106979132, 0.04475408047437668, 0.004293100908398628, 0.05903090536594391, -0.008889256045222282, -0.005269234534353018, 0.015378070063889027, -0.019946416839957237, 0.016674762591719627, 0.007929727435112, -0.020596982911229134, -0.031437747180461884, -0.08158468455076218, 0.009317809715867043, 0.030139992013573647, -0.02573433518409729, -0.05577422305941582, 0.03908636048436165, 0.006833238527178764, 0.025043966248631477, -0.02060682512819767, 0.014609617181122303, 0.027363626286387444, 0.0006994411814957857, 0.026413559913635254, -0.05125218257308006, -0.009297894313931465, 0.018239492550492287, -0.017202874645590782, -0.042203113436698914, 0.021179741248488426, -0.0005285023944452405, -0.03480404615402222, -0.044781848788261414, 0.039877377450466156, -0.027081208303570747, -0.006201264914125204, 0.03669356554746628, -0.002799069043248892, 0.03430575877428055, 0.008965621702373028, -0.0011174051323905587, -0.04194120690226555, 0.019003193825483322, -0.031534384936094284, -0.001827190862968564, 0.049193985760211945, 0.040955208241939545, -0.01006350852549076, -0.02582678757607937, -0.06790794432163239, -0.04183144122362137, -0.012565594166517258, 0.09748925268650055, 0.0887412577867508, 0.03589848428964615, -0.008406921289861202, 0.009923956356942654, -0.01679919846355915, 0.06771073490381241, -0.0017031432362273335, 0.0047667366452515125, -0.009648792445659637, -0.036060743033885956, 0.010075616650283337, -0.07122935354709625, -0.0008490132167935371, -0.014817772433161736, -0.016498813405632973, 0.005512467119842768, -0.042570825666189194, -0.03880036249756813, 0.03825574368238449, 0.03681883588433266, -0.08433520048856735, 0.0159490667283535, 0.008209680207073689, -0.00846165232360363, -0.029686331748962402, -0.01480709109455347, 0.0004588918818626553, -6.428641790989786e-05, -0.00893142819404602, 0.0047120326198637486, 0.0048494902439415455, -0.011903497390449047, 0.02360399067401886, 0.03213179111480713, 0.043542638421058655, -0.00778990238904953, -0.03169252723455429, 3.0222724944906076e-07, 0.012937026098370552, 0.09328040480613708, 0.008355711586773396, -0.04313847795128822, 0.02035965770483017, -0.01015515811741352, -0.002327066846191883, 0.011349140666425228, -0.015166154131293297, -0.006080505903810263, -0.03539930656552315, -0.018722431734204292, -0.03125966340303421, 0.028604868799448013, 0.06566590070724487, -0.009314864873886108, -0.05228251963853836, -0.03854254633188248, -0.040031228214502335, -0.0409289225935936, 0.033746760338544846, -0.004394158720970154, -0.02265843376517296, 0.00785216037184, -0.016927076503634453, -0.04059021919965744, 0.01609748974442482, 0.0046665784902870655, -0.023115741088986397, 0.04429509490728378, -0.010200145654380322, 0.052515529096126556, -0.008577489294111729, -0.098504938185215, 0.055306363850831985, -0.008588057942688465, -0.024682113900780678, -0.008110671304166317, 0.018571265041828156, -0.004891580902040005, -0.03369314223527908, 0.055095937103033066, -0.009954043664038181, -0.05337774381041527, -0.04958126321434975, 0.006081526633352041, -0.016518449410796165, -0.024184968322515488, 0.060023773461580276, -0.06638945639133453, -0.03494017571210861, 0.013359105214476585, 0.02380738966166973, -0.017543544992804527, 0.0007962726522237062, -0.02938094735145569, -0.007000135723501444, 0.004232271574437618, -0.009222828783094883, -0.020753292366862297, -0.025008145719766617, -0.025963185355067253, -0.020219607278704643, 0.06107348948717117, -0.005082223564386368, 0.08496086299419403, -0.05212213471531868, 2.747177363563467e-34, -0.03905748575925827, -0.006914861965924501, -0.00782361626625061, 0.014619770459830761, 0.031062278896570206, 0.037559136748313904, 0.057069893926382065, -0.01665322855114937, -0.0008418780635111034, -0.007539106532931328, -0.0031765466555953026], "parsedContent": {"infobox": {}, "categories": [], "links": ["21 October", "2020", "Wilderness", "A Dangerous Game", "Wilderness", "Slayer task", "<PERSON><PERSON><PERSON><PERSON>", "Slayer Master", "Krystilia#Tasks", "Trouver parchments", "Blighted", "Krystilia/Strategies#Requirements", "<PERSON><PERSON><PERSON>'s key", "Dwarf multicannon", "<PERSON><PERSON><PERSON><PERSON>", "Slayer master", "Wilderness weapons", "Slayer helmet(i)", "Ancient magicks", "Ice Barrage", "Ice Burst", "Blighted ancient ice sack", "Blighted super restore", "Agility", "Summer pie", "player killer", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>'s small chest", "Silk Chasm", "Games necklace", "Corporeal Beast", "Ferox Enclave", "Player vs Player", "Pool of Refreshment", "Carrallanger teleport", "Carrallanger teleport (tablet)", "Graveyard of Shadows", "Wilderness crabs teleport", "Player killing", "Hunter cape", "Black chincho<PERSON> (Hunter)", "Burning amulet", "Abyssal demon", "Ankou#Level 98", "Black demon#Level 188", "Black dragon#Level 247", "Dust devil", "Greater demon#Level 104", "Greater Nechryael", "Green dragon#Level 88", "Hellhound#Level 136", "Ice giant#Level 67", "<PERSON>lly", "Lesser demon#Level 94", "Bandit", "Slayer reward point#Extend", "Abyssal demon", "Priest in Peril", "<PERSON><PERSON><PERSON>", "Black demon", "Black dragon", "Dust devil", "Greater demon", "Green dragon", "<PERSON><PERSON><PERSON><PERSON>", "Greater Nechryael", "Hellhound", "Ice giant", "<PERSON>lly", "Lesser demon", "Hellhounds", "amulet of avarice", "Green dragon", "abyssal demon", "dust devil", "jelly", "Greater Nechryael", "green dragon", "blighted", "<PERSON><PERSON>", "<PERSON><PERSON>'s stuff for the Last Shopper Standing", "A Dangerous Game", "Revenants (music track)"], "plainText": "The Wilderness Slayer Cave is a cave located beneath the Wilderness. The chambers contain a diverse selection of monsters, located between level 17 to 32 Wilderness. All of the monsters in this cave are commonly assigned by Krystilia as well as other Slayer masters, although the full task list is extensive and most are not found in the cave. Often, monsters in the cave are stronger than their other Wilderness counterparts, and their drop tables are significantly improved. Loot from monsters includes a variety of items from <PERSON><PERSON>'s shop, such as Trouver parchments and Blighted items. While on a Wilderness Slayer task they also have a 15% increased drop rate of <PERSON><PERSON><PERSON>'s key.  The area is desirable to players wishing to quickly complete slayer tasks, as it's possible to use the Dwarf multicannon inside the dungeon. The cave is the only multi-combat spot where the cannon can be used for some monsters. Additionally, as the monsters drop better loot in the cave than in the overworld, it is a popular slayer location despite the added risk; many players use the cave to complete tasks for Kryst<PERSON> but also for Slayer masters in general. It's usually profitable enough in the cave to justify the cost of deaths (providing the player doesn't risk too much and die too often). The ability to use Wilderness weapons and Slayer helmet(i) means the player has the potential for substantially boosted damage and therefore players can enjoy high experience rates in the cave. Ancients such as the Ice Barrage and Ice Burst spells are effective in this dungeon; many of the slayer tasks are best killed with Ancients in multi-combat. As it's the Wilderness, Blighted ancient ice sack and Blighted super restore are usable here and are dropped by the monsters, allowing for cheaper tasks and training. There are agility shortcuts connecting the Abyssal demons, Lesser demons, and Greater Nechryael, and shortcuts connecting the Dust devils, Jellies, and Hellhounds. These all require 77 Agility and are useful for players wishing to escape a player killer. Abyssal demons, Nechryaels, Dust devils, and Jellies can only be killed while on Slayer task. If a player attempts to kill these monsters while not on the corresponding task, they will be stopped by Peeve. Transportation  Entrances to the Wilderness Slayer Cave  *Level 18 Wilderness — west of Larran's small chest *Level 29 Wilderness — south-west of the Silk Chasm  Teleports near the cave's entrances  *Games necklace offers a teleport to the Corporeal Beast — players can then run east to the level 18 entrance. This is usually the most convenient location for the southern entrance, as the Corporeal Beast cave is not classed as the Wilderness despite leading directly to it giving the player added security. *Teleporting to Ferox Enclave and running east to the level 18 entrance. This is preferred by many players, as here they are able to bank and it is not a PvP area. It's also possible to restore health, prayer, and run energy here with the Pool of Refreshment. Be aware of others players, you may be more likely to be scouted if running from the Ferox Enclave. *Using either the Carrallanger teleport or Carrallanger teleport tablet, leading to the Graveyard of Shadows and again running east to the level 18 entrance. *Breaking a Wilderness crabs teleport tablet and running west to the level 29 entrance — This is not recommended not only due to the high cost of this teleport, but also because it teleports the player to level 33 Wilderness. This means it inherently carries more risk as the player cannot instantly teleport out if a PKer is in the area. *Hunter cape has a teleport to Black chinchompa (Hunter), from here players can run east to arrive at the north entrance. This teleport takes the player to level 32 Wilderness in an active area, so again is not recommended. *Using a Burning Amulet to Chaos Temple and running north to the level 18 entrance. Inhabitants Monsters that can be found in the Wilderness Slayer Cave include: {| class=\"wikitable sortable align-left-2\" style=\"text-align:center;\" ! colspan=\"2\" |Monster !Combat<br/>level !Quantity !Wilderness level !Killable on Task only? !Average gold per kill (GE)  Krystilia task !Average gold per kill (GE) Non-Krystilia task or no task |- |100x100px |Abyssal demon |124 |8 |29-33 |Yes |}} |}} |- |100x100px |Ankou |98 |16 |19-22 |No |}} |}} |- |100x100px |Black demon |188 |4 |24-27 |No |}} |}} |- |100x100px |Black dragon |247 |3 |30-32 |No |}} |}} |- |100x100px |Dust devil |93 |9 |25-28 |Yes |}} |}} |- |100x100px |Greater demon |104 |10 |29-32 |No |}} |}} |- |100x100px |Greater Nechryael |200 |8 |22-25 |Yes |}} |}} |- |100x100px |Green dragon |88 |6 |19-27 |No |}} |}} |- |100x100px |Hellhound |136 |9 |21-24 |No |}} |}} |- |100x100px |Ice giant |67 |10 |17-19 |No |}} |}} |- |100x100px |Jelly |78 |14 |23-25 |Yes |}} |}} |- |100x100px |Lesser demon |94 |7 |25-28 |No |}} |}} |} There is also a single level 130 Bandit roaming the cave. Wilderness slayer tasks in this cave {| class=\"wikitable sortable lighttable\" |- ! Monster ! Amount !Extended amt. ! Slayer Exp !Task Weight |- | Abyssal demons<ref group=\"n\">Only assigned to players if they have completed the Priest in Peril quest and have at least .</ref><ref group=\"n\" name=\"barrage\">Slayer task assignments of abyssal demons, dust devils, jellies, and nechryaels from Krystilia can be enabled or disabled for free.</ref> | 75-125 |200-250 | 150 |5 |- | Ankou | 75-125 |91-150 | 70, 100 |6 |- | Black demons | 100-150 |200-250 | 157, 200 |7 |- | Black dragons | 8-16 |40-60 | 199.4 |4 |- | Dust devils<ref group=\"n\" name=\"barrage\" /><ref group=\"n\">Only assigned to players if they have at least .</ref> | 75-125 |200-250 | 105 |5 |- | Greater demons | 100-150 |200-250 | 87, 120 |8 |- | Green dragons | 65-100 |  | 75, 100 |4 |- | Nechryael (In the cave as Greater Nechryael)<ref group=\"n\" name=\"barrage\" /><ref group=\"n\">Only assigned to players if they have at least .</ref> | 75-125 |200-250 | 105 |5 |- | Hellhounds | 75-125 |  | 116, 150 |7 |- | Ice giants | 100-150 |  | 70, 100 |6 |- | Jellies<ref group=\"n\" name=\"barrage\" /><ref group=\"n\">Only assigned to players if they have at least .</ref> | 100-150 |  | 75 |5 |- | Lesser demons | 80-120 |  | 79, 110 |6 |} <references group=\"n\" /> Changes References"}, "originalContent": "{{Infobox Location\n|name = Wilderness Slayer Cave\n|image = [[File:Wilderness Slayer Cave.png|300px]]\n|release = [[21 October]] [[2020]]\n|update = Halloween 2020\n|members = Yes\n|floors = One\n|location = [[Wilderness]]\n|race = Varies\n|music = [[A Dangerous Game]]\n|map = [[File:Wilderness Slayer Cave map.png|300px]]\n|type = dungeon\n|leagueRegion = Wilderness\n}}\n\nThe '''Wilderness Slayer Cave''' is a cave located beneath the [[Wilderness]]. The chambers contain a diverse selection of monsters, located between level 17 to 32 Wilderness. All of the monsters in this cave are commonly [[Slayer task|assigned]] by [[<PERSON>ryst<PERSON>]] as well as other [[Slayer Master|Slayer masters]], although the full [[Krystilia#Tasks|task list]] is extensive and most are not found in the cave. Often, monsters in the cave are stronger than their other Wilderness counterparts, and their drop tables are significantly improved. Loot from monsters includes a variety of items from <PERSON><PERSON>'s shop, such as [[Trouver parchments]] and [[Blighted]] items. While on a [[Krystilia/Strategies#Requirements|Wilderness Slayer task]] they also have a 15% increased drop rate of [[<PERSON><PERSON><PERSON>'s key]].{{CiteTwitter|author=Mod <PERSON>|url=https://twitter.com/JagexAsh/status/1326275335969796096|date=11 November 2020|archiveurl=https://archive.is/dtbEO|archivedate=10 November 2020|quote=15%}} \n\nThe area is desirable to players wishing to quickly complete slayer tasks, as it's possible to use the [[Dwarf multicannon]] inside the dungeon. The cave is the only multi-combat spot where the cannon can be used for some monsters. Additionally, as the monsters drop better loot in the cave than in the overworld, it is a popular slayer location despite the added risk; many players use the cave to complete tasks for [[Krystilia]] but also for [[Slayer master]]s in general. It's usually profitable enough in the cave to justify the cost of deaths (providing the player doesn't risk too much and die too often).\n\nThe ability to use [[Wilderness weapons]] and [[Slayer helmet(i)]]{{efn|While on a Slayer task.}} means the player has the potential for substantially boosted damage and therefore players can enjoy high experience rates in the cave. [[Ancient magicks|Ancients]] such as the [[Ice Barrage]] and [[Ice Burst]] spells are effective in this dungeon; many of the slayer tasks are best killed with Ancients in multi-combat. As it's the Wilderness, [[Blighted ancient ice sack]] and [[Blighted super restore]] are usable here and are dropped by the monsters, allowing for cheaper tasks and training.\n\nThere are agility shortcuts connecting the Abyssal demons, Lesser demons, and Greater Nechryael, and shortcuts connecting the Dust devils, Jellies, and Hellhounds. These all require 77 [[Agility]]{{efn|Boostable — [[Summer pie|Summer pies]] can be incredibly useful if the player is 72-76 Agility as they provide a +5 boost to agility.}} and are useful for players wishing to escape a [[player killer]].\n\nAbyssal demons, Nechryaels, Dust devils, and Jellies can '''only''' be killed while on Slayer task.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1504059116284780547|date=16 March 2022|archiveurl=https://archive.ph/qOvZt|archivedate=8 April 2022}} If a player attempts to kill these monsters while not on the corresponding task, they will be stopped by [[Peeve]].\n\n==Transportation==\n{{Map|3293:3748,title:Northern entrance|3260,3664,title:Southern entrance|mtype=pin|width=300|height=200|align=left|caption=The two entrances to the Wilderness Slayer Cave.}}\n\n==== Entrances to the Wilderness Slayer Cave ====\n*Level 18 Wilderness — west of [[Larran's small chest]]\n*Level 29 Wilderness — south-west of the [[Silk Chasm]]\n\n==== Teleports near the cave's entrances ====\n*[[Games necklace]] offers a teleport to the [[Corporeal Beast]] — players can then run east to the level 18 entrance. This is usually the most convenient location for the southern entrance, as the Corporeal Beast cave is not classed as the Wilderness despite leading directly to it giving the player added security.\n*Teleporting to [[Ferox Enclave]] and running east to the level 18 entrance. This is preferred by many players, as here they are able to bank and it is not a [[Player vs Player|PvP]] area. It's also possible to restore health, prayer, and run energy here with the [[Pool of Refreshment]]. Be aware of others players, you may be more likely to be scouted if running from the Ferox Enclave.\n*Using either the [[Carrallanger teleport]] or [[Carrallanger teleport (tablet)|Carrallanger teleport tablet]], leading to the [[Graveyard of Shadows]] and again running east to the level 18 entrance.\n*Breaking a [[Wilderness crabs teleport|Wilderness crabs teleport tablet]] and running west to the level 29 entrance — This is not recommended not only due to the high cost of this teleport, but also because it teleports the player to level 33 Wilderness. This means it inherently carries more risk as the player cannot instantly teleport out if a [[Player killing|PKer]] is in the area.\n*[[Hunter cape]] has a teleport to [[Black chinchompa (Hunter)]], from here players can run east to arrive at the north entrance. This teleport takes the player to level 32 Wilderness in an active area, so again is not recommended.\n*Using a [[Burning amulet|Burning Amulet]] to Chaos Temple and running north to the level 18 entrance.\n{{clear|left}}\n{{notelist}}\n\n==Inhabitants==\nMonsters that can be found in the Wilderness Slayer Cave include:\n\n{| class=\"wikitable sortable align-left-2\" style=\"text-align:center;\"\n! colspan=\"2\" |Monster\n!Combat<br/>level\n!Quantity\n!Wilderness level\n!Killable on Task only?\n!'''Average gold per kill (GE)''' \n'''Krystilia task'''\n!Average gold per kill (GE)\nNon-Krystilia task or no task\n|-\n|[[File:Abyssal demon.png|100x100px]]\n|[[Abyssal demon]]\n|124\n|8\n|29-33\n|'''Yes'''\n|{{Coins|{{Average drop value|mob=Abyssal demon#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Abyssal demon#Wilderness Slayer Cave|raw=y}}}}{{Efn|Can only be killed if assigned as a task by another slayer master|name=slayeronly}}\n|-\n|[[File:Ankou.png|100x100px]]\n|[[Ankou#Level 98|Ankou]]\n|98\n|16\n|19-22\n|No\n|{{Coins|{{Average drop value|mob=Ankou#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Ankou#Wilderness Slayer Cave|raw=y}}}}\n|-\n|[[File:Black demon.png|100x100px]]\n|[[Black demon#Level 188|Black demon]]\n|188\n|4\n|24-27\n|No\n|{{Coins|{{Average drop value|mob=Black demon#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Black demon#Wilderness Slayer Cave|raw=y}}}}\n|-\n|[[File:Black dragon.png|100x100px]]\n|[[Black dragon#Level 247|Black dragon]]\n|247\n|3\n|30-32\n|No\n|{{Coins|{{Average drop value|mob=Black dragon#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Black dragon#Wilderness Slayer Cave|raw=y}}}}\n|-\n|[[File:Dust devil (Wilderness Slayer Cave).png|100x100px]]\n|[[Dust devil]]\n|93\n|9\n|25-28\n|'''Yes'''\n|{{Coins|{{Average drop value|mob=Dust devil#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Dust devil#Wilderness Slayer Cave|raw=y}}}}{{Efn|name=slayeronly}}\n|-\n|[[File:Greater demon.png|100x100px]]\n|[[Greater demon#Level 104|Greater demon]]\n|104\n|10\n|29-32\n|No\n|{{Coins|{{Average drop value|mob=Greater demon#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Greater demon#Wilderness Slayer Cave|raw=y}}}}\n|-\n|[[File:Greater Nechryael.png|100x100px]]\n|[[Greater Nechryael]]\n|200\n|8\n|22-25\n|'''Yes'''\n|{{Coins|{{Average drop value|mob=Greater Nechryael#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Greater Nechryael#Wilderness Slayer Cave|raw=y}}}}{{Efn|name=slayeronly}}\n|-\n|[[File:Green dragon.png|100x100px]]\n|[[Green dragon#Level 88|Green dragon]]\n|88\n|6\n|19-27\n|No\n|{{Coins|{{Average drop value|mob=Green dragon#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Green dragon#Wilderness Slayer Cave|raw=y}}}}\n|-\n|[[File:Hellhound.png|100x100px]]\n|[[Hellhound#Level 136|Hellhound]]\n|136\n|9\n|21-24\n|No\n|{{Coins|{{Average drop value|mob=Hellhound#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Hellhound#Wilderness Slayer Cave|raw=y}}}}\n|-\n|[[File:Ice giant.png|100x100px]]\n|[[Ice giant#Level 67|Ice giant]]\n|67\n|10\n|17-19\n|No\n|{{Coins|{{Average drop value|mob=Ice giant#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Ice giant#Wilderness Slayer Cave|raw=y}}}}\n|-\n|[[File:Jelly.png|100x100px]]\n|[[Jelly]]\n|78\n|14\n|23-25\n|'''Yes'''\n|{{Coins|{{Average drop value|mob=Jelly#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Jelly#Wilderness Slayer Cave|raw=y}}}}{{Efn|name=slayeronly}}\n|-\n|[[File:Lesser demon.png|100x100px]]\n|[[Lesser demon#Level 94|Lesser demon]]\n|94\n|7\n|25-28\n|No\n|{{Coins|{{Average drop value|mob=Lesser demon#Wilderness Slayer Cave|wildernessslayer=yes|raw=y}}}}\n|{{Coins|{{Average drop value|mob=Lesser demon#Wilderness Slayer Cave|raw=y}}}}\n|}\n{{Notelist}}\nThere is also a single level 130 [[Bandit]] roaming the cave.\n\n==Wilderness slayer tasks in this cave==\n\n{| class=\"wikitable sortable lighttable\"\n|-\n! Monster\n! Amount\n![[Slayer reward point#Extend|Extended amt.]]\n! Slayer Exp\n!Task Weight\n|-\n| [[Abyssal demon]]s<ref group=\"n\">Only assigned to players if they have completed the [[Priest in Peril]] quest and have at least {{SCP|Slayer|85|link=yes}}.</ref><ref group=\"n\" name=\"barrage\">Slayer task assignments of abyssal demons, dust devils, jellies, and nechryaels from Krystilia can be enabled or disabled for free.</ref>\n| 75-125\n|200-250\n| 150\n|5\n|-\n| [[Ankou]]\n| 75-125\n|91-150\n| 70, 100\n|6\n|-\n| [[Black demon]]s\n| 100-150\n|200-250\n| 157, 200\n|7\n|-\n| [[Black dragon]]s\n| 8-16\n|40-60\n| 199.4\n|4\n|-\n| [[Dust devil]]s<ref group=\"n\" name=\"barrage\" /><ref group=\"n\">Only assigned to players if they have at least {{SCP|Slayer|65|link=yes}}.</ref>\n| 75-125\n|200-250\n| 105\n|5\n|-\n| [[Greater demon]]s\n| 100-150\n|200-250\n| 87, 120\n|8\n|-\n| [[Green dragon]]s\n| 65-100\n| {{NA}}\n| 75, 100\n|4\n|-\n| [[Nechryael]] (In the cave as [[Greater Nechryael]])<ref group=\"n\" name=\"barrage\" /><ref group=\"n\">Only assigned to players if they have at least {{SCP|Slayer|80|link=yes}}.</ref>\n| 75-125\n|200-250\n| 105\n|5\n|-\n| [[Hellhound]]s\n| 75-125\n| {{NA}}\n| 116, 150\n|7\n|-\n| [[Ice giant]]s\n| 100-150\n| {{NA}}\n| 70, 100\n|6\n|-\n| [[Jelly|Jellies]]<ref group=\"n\" name=\"barrage\" /><ref group=\"n\">Only assigned to players if they have at least {{SCP|Slayer|52|link=yes}}.</ref>\n| 100-150\n| {{NA}}\n| 75\n|5\n|-\n| [[Lesser demon]]s\n| 80-120\n| {{NA}}\n| 79, 110\n|6\n|}\n<references group=\"n\" />\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 8 May 2024\n|update = Project Rebalance: Skilling & Poll 81 MTA Changes\n|change = Shortcuts were added on the east and west sides of the cave, requiring level 77 Agility.\n}}\n{{Subject changes\n|date = 18 January 2023\n|update = Secrets of the North Improvements\n|change = The max range of [[Hellhounds]] has been increased in the Wilderness Slayer Dungeon.\n}}\n{{Subject changes\n|date = 16 February 2022\n|update = 9th Birthday Event\n|change = Green dragons in the Wilderness Slayer Cave now drop noted loot for players wearing an [[amulet of avarice]].\n}}\n{{Subject changes\n|date = 9 February 2022\n|update = Land of the Goblins\n|change = [[Green dragon]]s now also drop additional loot alongside the other monsters in the dungeon.\n}}\n{{Subject changes\n|date = 2 February 2022\n|update = Revenant Maledictus & Wilderness Changes\n|change = Added [[abyssal demon]]s, [[dust devil]]s, [[jelly|jellies]] and [[Greater Nechryael]]s to the caves. Enemies in the Wilderness Slayer Cave, with the exception of [[green dragon]]s, now also have a chance to drop items from a new loot table containing consumable [[blighted]] items and items from [[Justine]]'s [[Justine's stuff for the Last Shopper Standing|Last Man Standing rewards shop]].\n}}\n{{Subject changes\n|date = 5 November 2020\n|update = Revenant Cave Tweaks and Leagues II Fixes\n|change = A new track, [[A Dangerous Game]], now plays in the cave instead of [[Revenants (music track)|Revenants]].\n}}\n{{Subject changes\n|date = 28 October 2020\n|update = Leagues II - Trailblazer Launch\n|poll =\n|change = Hellhounds now have higher respawn rates, as well as roam in a slightly wider area.\n}}\n{{Subject changes\n|date = 21 October 2020\n|update = Halloween 2020\n|poll =\n|change = All the creatures in the Wilderness Slayer Cave will have a boosted chance to drop Larran's Keys.\n}}\n{{Subject changes footer}}\n\n==References==\n{{Reflist}}\n\n{{Slayer locations}}\n{{Dungeons}}\n{{Wilderness}}"}