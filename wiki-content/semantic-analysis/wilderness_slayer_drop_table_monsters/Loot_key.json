{"title": "Loot key", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [{"type": "category", "value": "Wilderness", "confidence": 0.9, "source": "category"}], "relationships": [{"from": "Loot key", "to": "Wilderness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "<PERSON><PERSON><PERSON>'s key", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Muddy key", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "23 February", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "2022", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Loot Chest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "PvP", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Wilderness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "PvP world", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "free-to-play", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "member", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Loot Chest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "bank PIN", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "bank", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "bank note", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "coins", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Wilderness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "High Level Alchemy", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Skull (status)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "choose option", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Skull (status)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "bones", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Skull (status)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Protect Item", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Ironman Mode", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Loot Chest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Wilderness Loot Key interface", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Loot Chest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Skull (status)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Forinthry Surge", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Choose option", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Loot Chest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Grand Exchange", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Edgeville", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Mage Arena", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Revenant Caves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Loot Chest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Loot key", "to": "Wilderness", "type": "belongs_to_category", "confidence": 0.9, "source": "category"}], "embedding": [0.06093648821115494, -0.03637984022498131, 0.02692754566669464, 0.08310715109109879, -0.04750489816069603, 0.04524612054228783, -0.027384381741285324, 0.018579378724098206, 0.001587199280038476, 0.015487209893763065, -0.022970277816057205, 0.0617581307888031, 0.021697746589779854, 0.06559593230485916, 0.0643603503704071, -0.029731426388025284, -0.019505450502038002, -7.515270408475772e-05, 0.042728204280138016, -0.028012879192829132, -0.032166946679353714, -0.010618537664413452, -0.051036424934864044, 0.0015810496406629682, 0.04781636968255043, -0.037316903471946716, -0.02641407772898674, 0.01511844340711832, 0.0057797846384346485, -0.028192071244120598, 0.046159110963344574, -0.07095630466938019, 0.015228347852826118, -0.0808800533413887, 2.406283329037251e-06, -0.02455112524330616, 0.023769671097397804, 0.00987255945801735, -0.06764596700668335, 0.024985268712043762, -0.02277054823935032, 0.024037720635533333, 0.002330875489860773, -0.020768295973539352, -0.0005397316417656839, -0.11589621007442474, 0.003692824859172106, 0.08792192488908768, 0.028345802798867226, -0.03406449407339096, 0.005585917737334967, 0.06418538838624954, 0.0036498233675956726, -0.02476167120039463, 0.08947896957397461, -0.09737648069858551, -0.0024749182630330324, -0.03977251052856445, -0.0035176773089915514, 0.017829548567533493, 0.03260071575641632, 0.008031757548451424, 0.0023072008043527603, 0.024241410195827484, 0.01786242611706257, 0.02826416678726673, -0.02700965106487274, -0.05594095215201378, -0.015055512078106403, 0.01063684094697237, -0.05893827974796295, 0.03251912444829941, 0.0037213927134871483, 0.043761420994997025, -0.02832191064953804, 0.017194222658872604, -0.046826768666505814, 0.011312278918921947, 0.012880755588412285, -0.01124928891658783, 0.06913436949253082, 0.00355014530941844, 0.005888499319553375, 0.015111659653484821, 0.017457595095038414, -0.0028144761454313993, 0.012606359086930752, 0.007128075696527958, 0.025255776941776276, 0.0003408070479054004, -0.030482370406389236, -0.014291524887084961, -0.04502734914422035, 0.019683457911014557, 0.019057881087064743, -0.008517703041434288, 0.04294072836637497, 0.13629287481307983, 0.024016939103603363, -0.06945344805717468, -0.05606136471033096, -0.026064123958349228, -0.05694885551929474, 0.014490115456283092, -0.05048178508877754, -0.07914567738771439, -0.0010111165465787053, 0.021064074710011482, 0.049336932599544525, 0.003825549269095063, 0.012274786829948425, -0.016454821452498436, -0.03450598567724228, 0.018850117921829224, -3.203276719432324e-05, 0.011080688796937466, 0.019775215536355972, -0.035873617976903915, -0.03516511991620064, 0.011833514086902142, -0.04102984815835953, 0.0005662111798301339, 0.014042818918824196, 0.011492544785141945, 0.04080667719244957, -0.01399095170199871, -0.04835675656795502, 0.00667359447106719, -0.001753087854012847, 0.008149906992912292, -0.06931952387094498, 0.005110749509185553, 0.0007654556538909674, -0.03902063146233559, 0.023600716143846512, -0.002132561756297946, -0.01567123644053936, 0.011226390488445759, -0.03931429609656334, 0.0255950465798378, 0.036530494689941406, 0.031607065349817276, -0.003913848195225, -0.028019683435559273, -0.026095839217305183, -0.02407738007605076, 0.016701150685548782, 0.03400377556681633, 0.006266502663493156, -0.016007371246814728, -0.046337489038705826, 0.0016557608032599092, 0.06361695379018784, 0.010983576066792011, 0.030375586822628975, -0.010095890611410141, 0.06545041501522064, -0.02506258897483349, -0.05301674082875252, 0.0531412698328495, 0.011621172539889812, -0.015902157872915268, -0.0398060642182827, -0.003357812063768506, -0.01170472614467144, 0.03312927484512329, -0.03883838653564453, 0.012126952409744263, 0.06315886229276657, -0.049925774335861206, -0.004553443286567926, -0.018154369667172432, -0.06191979721188545, 0.002779909875243902, -0.006616094149649143, 0.08247110992670059, 0.033660247921943665, -0.08671746402978897, -0.10094931721687317, -0.03996738791465759, -0.011312456801533699, -0.10480319708585739, 0.0160722266882658, -0.0056777955032885075, 0.030091779306530952, -0.011039942502975464, -0.00918933842331171, -0.028738465160131454, -0.03542080149054527, -0.007677895482629538, -0.024517612531781197, 0.00965199712663889, -0.00870355125516653, 0.03672851622104645, 0.037197113037109375, -0.000375398580217734, 0.02948460727930069, 0.055521659553050995, -0.019027724862098694, -0.006091398652642965, 0.00633996119722724, -0.0228118859231472, 0.07554241269826889, 0.018567156046628952, -0.0014600149588659406, 0.039727915078401566, -0.0373447984457016, 0.007912881672382355, -0.02168886363506317, -0.009995423257350922, 0.0074660093523561954, 0.018522555008530617, 0.02377866767346859, 0.02722766064107418, -0.046349041163921356, 0.032703887671232224, -0.03651142120361328, 0.0075264279730618, 0.006165888626128435, -0.05073048174381256, -0.026643220335245132, -0.021232206374406815, 0.0641639456152916, -0.024723857641220093, -0.01755712367594242, -0.022560201585292816, -0.040663011372089386, 0.048895083367824554, -0.010824928060173988, 0.016420554369688034, 0.021413229405879974, -0.016846109181642532, 0.01121631171554327, 0.006094896234571934, 0.006633512210100889, -0.030953409150242805, 0.029194418340921402, 0.09182290732860565, 0.07363513857126236, -0.03287801519036293, 0.039644476026296616, 0.008573250845074654, 0.026413466781377792, 0.032258290797472, -0.035288047045469284, -0.02814542129635811, 0.032808274030685425, -0.041123244911432266, -0.08368558436632156, -0.02394033968448639, -0.004771866369992495, -0.02495519630610943, -0.01719040796160698, -0.003659810870885849, -0.006752569694072008, -0.09325730055570602, -0.015286036767065525, 0.07277607917785645, 0.01103606540709734, 0.056945253163576126, 0.04805803671479225, 0.0062737539410591125, 0.027347803115844727, 0.008526042103767395, 0.04390442371368408, -0.006473483983427286, -0.04935765266418457, 0.10953852534294128, 0.03421975299715996, 0.0015152650885283947, 0.02373441867530346, 0.018377335742115974, -0.02461787685751915, -0.015532135032117367, -0.002660398604348302, -0.05181780830025673, -0.034220293164253235, 0.025670615956187248, 0.007101865950971842, 0.017610907554626465, 0.01697108894586563, 0.06499669700860977, -0.0013034612638875842, 0.01619015447795391, 0.017465127632021904, -0.0010807974031195045, -0.023946791887283325, -0.0011444998672232032, 0.024689268320798874, 0.05960821732878685, 0.02292889542877674, -0.03839181363582611, -0.005299638491123915, -0.05845586210489273, 0.01677359826862812, -0.026098912581801414, -0.0538802333176136, 0.013991675339639187, -0.014748429879546165, 0.04492655023932457, -0.029187971726059914, -0.06512396037578583, -0.011780349537730217, -0.04779618978500366, -0.03933437168598175, -0.06846263259649277, -0.026649214327335358, 0.010741922073066235, 0.014300402253866196, 0.015835914760828018, 0.017084918916225433, 0.01588807813823223, -0.0066379462368786335, -0.07482867687940598, 0.05013206601142883, 0.0015638732584193349, -0.0057321698404848576, 0.03342025354504585, 0.04469381645321846, -0.012562504038214684, -0.05499792844057083, -0.00021117262076586485, -0.012114142999053001, -0.01581110619008541, 0.0014949386240914464, -0.009973889216780663, -0.029884466901421547, 0.0010127512505277991, -0.03563323989510536, -0.03778059780597687, -0.00038298493018373847, -0.02194434404373169, -0.060231659561395645, 0.022562412545084953, -0.03497473895549774, -0.04742131754755974, -0.033476825803518295, -0.05208510905504227, 0.009409889578819275, -0.006162841338664293, -0.03261006996035576, -0.033953335136175156, 0.013898590579628944, -0.08262304216623306, 0.033066850155591965, -0.03919682279229164, -0.038657598197460175, -0.008410022594034672, 0.06126056984066963, 0.0118350088596344, 0.0856764167547226, -0.009150441735982895, -0.004539934452623129, -0.010286477394402027, -0.012128201313316822, -0.016884397715330124, 0.048197705298662186, 0.024556603282690048, 0.022048097103834152, -0.007027748506516218, -0.01347829308360815, 0.009567988105118275, 0.039692651480436325, -0.019758084788918495, 0.02250687964260578, 0.045296236872673035, 0.011250736191868782, 0.009752719663083553, -0.02334868349134922, -0.026315584778785706, -0.0018570625688880682, 0.039746370166540146, 0.05678684636950493, 0.004932540003210306, 0.009861782193183899, -0.032087694853544235, 0.0016968967393040657, -0.011259348131716251, -0.03917069733142853, 0.02451644279062748, -0.011611482128500938, -0.058900136500597, 0.042829953134059906, 0.0306199099868536, 0.004512010607868433, 0.020512908697128296, -0.0007907524122856557, 0.044365815818309784, -0.06259405612945557, -0.004745728801935911, 0.05765405297279358, -0.004775627050548792, -0.06565993279218674, 0.03446650877594948, 0.016882706433534622, -0.03581347316503525, -0.02059742622077465, 0.030850499868392944, -0.034791748970746994, 0.016178930178284645, -0.015413050539791584, 0.07093847543001175, 0.05811941251158714, 0.03105025179684162, -0.0428176186978817, 0.04627596586942673, 0.012836730107665062, -0.004460890311747789, -0.012520933523774147, 5.156559382157866e-06, -0.11910796910524368, 0.017369301989674568, -0.023384101688861847, 0.013555568642914295, -0.013238059356808662, 0.0016585927223786712, -0.03421648591756821, 0.01014735922217369, 0.04359816014766693, -0.04999378323554993, -0.0335681214928627, 0.003174756420776248, -0.05943635478615761, 0.013912483118474483, 0.02744831144809723, -0.02466687001287937, -0.012669837102293968, -0.012583065778017044, -0.007116669788956642, 0.04823324456810951, -0.0003482151951175183, 0.012586218304932117, -0.0035787392407655716, 0.004756808280944824, 0.051744602620601654, -0.020638640969991684, -0.01489763893187046, 0.023817703127861023, 0.0987347960472107, -0.025747766718268394, -0.023521842435002327, -0.01249730959534645, 0.0415380485355854, -0.01109989732503891, -0.014428609050810337, -0.034375742077827454, -0.015372350811958313, -0.027738697826862335, -0.028768332675099373, 0.004864859394729137, -0.021382616832852364, 0.03133682906627655, 0.024921778589487076, -0.0771537497639656, -0.10374636948108673, -0.02226637303829193, 0.05154558643698692, 0.03193956986069679, 0.015681346878409386, 0.041153572499752045, 0.010286780074238777, 0.020578254014253616, -0.05227440595626831, -0.004889211151748896, -0.035171736031770706, 0.014557819813489914, -0.015034404583275318, -0.05932028964161873, -0.008772670291364193, -0.04076514393091202, 0.02720368653535843, 0.03762591630220413, 0.00573249626904726, -0.027836082503199577, 0.056349173188209534, 0.05259464681148529, 0.02574743516743183, -0.034650299698114395, -0.028962600976228714, -0.010789433494210243, -0.009196778759360313, 0.01961340196430683, -0.00803086906671524, 0.0014977301470935345, 0.009008407592773438, 0.015736853703856468, 0.07578478753566742, -0.017365388572216034, 0.03299957886338234, 0.013884207233786583, -0.02423202432692051, -0.036004338413476944, 0.015970638021826744, -0.012240279465913773, 0.07422813028097153, 0.06770601868629456, -0.015059150755405426, -0.07922405004501343, 0.0634431540966034, -0.028130915015935898, 0.047872018069028854, 0.05013379454612732, 0.0071621248498559, 0.03398754075169563, -0.01942797750234604, 0.037227846682071686, -0.018818169832229614, -0.020341552793979645, 0.008190588094294071, -0.058231815695762634, 0.006091913674026728, -0.0009643907542340457, -0.03231789544224739, -0.045964352786540985, 0.0533631332218647, 0.018419958651065826, 0.02365247718989849, -0.03306721895933151, 0.031042076647281647, 0.004542635288089514, 0.006238212808966637, -0.004375563934445381, 0.026416804641485214, -0.01279439777135849, 0.011688819155097008, -0.052049145102500916, 0.027218658477067947, 0.0028038870077580214, -0.04936134070158005, -0.05227545648813248, 0.0807417556643486, 0.04688311368227005, -0.005320103373378515, 0.02030092291533947, -0.04007844254374504, 0.0628674179315567, 0.05684054270386696, -0.02438867650926113, 0.0027816740330308676, 0.03658105432987213, 0.021299375221133232, -0.03144830837845802, 0.0009251743322238326, 0.028776532039046288, 0.011253665201365948, 0.00810018740594387, -0.040463533252477646, 0.03436833992600441, -0.001265413942746818, 0.007100362330675125, 0.007728366181254387, 0.02198592573404312, -0.03835730254650116, -0.02538837306201458, 0.022239698097109795, -6.684990088036285e-33, 0.01594526693224907, -0.047485899180173874, -0.023922210559248924, 0.08687693625688553, -0.0014027601573616266, 0.02313445694744587, 0.004623047076165676, 0.033894363790750504, -0.006596891675144434, -0.006532564293593168, 0.020618272945284843, -0.044532448053359985, 0.027787823230028152, 0.03148912265896797, 0.009197579696774483, 0.0026767312083393335, -0.041249848902225494, -0.007553690113127232, 0.05701310187578201, -0.027586352080106735, 0.04452889412641525, 0.02160429023206234, 0.08351992815732956, 0.00020370174024719745, 0.06766728311777115, -0.020247627049684525, 0.06044730916619301, 0.03660260885953903, 0.011039230972528458, -0.0031439445447176695, -0.01785481721162796, -0.0025561347138136625, -0.00654821190983057, -0.02271726168692112, 0.007116003427654505, 0.02276393584907055, -0.022000722587108612, -0.0019088927656412125, 0.013825553469359875, 0.034353259950876236, 0.019188079982995987, -0.032010894268751144, -0.0052063120529055595, 0.043934740126132965, 0.047276537865400314, -0.02358582615852356, 0.0035888226702809334, -0.015103274025022984, -0.020893653854727745, -0.008644300512969494, 0.03405078127980232, -0.00916165392845869, 0.02339937537908554, -0.024063415825366974, 0.03469666838645935, -0.021116560325026512, -0.0756036639213562, -0.07780146598815918, 0.050991594791412354, -0.02360968105494976, 0.05556792765855789, 0.022950956597924232, -0.030234798789024353, 0.020724164322018623, -0.013578367419540882, 0.0067055546678602695, -0.042562492191791534, -0.006567168049514294, -0.03603155538439751, 0.06189486011862755, 0.020341545343399048, 0.0023428648710250854, -0.026210961863398552, 0.055772267282009125, 0.02347642369568348, 0.01922588422894478, -0.034524090588092804, -0.014848767779767513, -0.013221092522144318, 0.009511972777545452, 0.029957838356494904, -0.034884851425886154, 0.016973748803138733, 0.036911629140377045, -0.08474181592464447, -0.007210498675704002, -0.022591864690184593, -0.040966060012578964, 0.022947141900658607, -0.006254706997424364, 0.013042494654655457, -0.010604954324662685, -0.00705802533775568, 0.007772267796099186, -0.01513194665312767, 0.001439085346646607, 0.09024445712566376, 0.002106975531205535, -0.030099594965577126, -0.03909846767783165, 0.02838141657412052, 0.006694784853607416, 0.01312080305069685, -0.05256577953696251, 0.0019384954357519746, -0.0228564590215683, -0.03772962838411331, 0.051772378385066986, -0.018296565860509872, 0.005911169573664665, -0.016675451770424843, -0.044690556824207306, -0.015503919683396816, 0.04895385354757309, 0.011454620398581028, -0.05914526805281639, -0.007396727800369263, -0.09669355303049088, 0.00039579623262397945, -0.04180218279361725, 0.07248526811599731, 0.023252472281455994, -0.0032727979123592377, 0.029255058616399765, -0.029976507648825645, 0.07477083802223206, -0.050582125782966614, -0.012080095708370209, -0.004364157561212778, 0.03748335689306259, 0.02424064837396145, -0.03732042759656906, 3.003269171131251e-07, -0.037173815071582794, 0.07238505035638809, 0.060890182852745056, -0.012270629405975342, -0.02957586757838726, -0.01697414182126522, -0.006567410193383694, 0.041417960077524185, -0.05052866041660309, -0.0909186601638794, -0.042476847767829895, 0.034861791878938675, 0.018321538344025612, 0.008932053111493587, 0.012397526763379574, -0.00039016877417452633, 0.05286071076989174, 0.014928996562957764, 0.01108593586832285, -0.018661269918084145, 0.048798006027936935, -0.02335367165505886, 0.0034745046868920326, -0.010754970833659172, 0.013398902490735054, 0.044892892241477966, 0.003136672545224428, -0.024539131671190262, -0.02775539457798004, -0.04907883331179619, -0.04448636993765831, 0.0051037962548434734, -0.00588171137496829, -0.06052042171359062, 0.06564323604106903, 0.004556227941066027, 0.006581859663128853, 0.02480403520166874, -0.039414677768945694, -0.0566667765378952, -0.039255429059267044, -0.04540492594242096, 0.008929334580898285, -0.054091744124889374, -0.007412682753056288, 0.02676345966756344, 0.00024727985146455467, -0.04065726324915886, -0.006561577320098877, -0.02850124053657055, -0.019880743697285652, -0.04803576320409775, 0.06464327871799469, 0.010639380663633347, 0.017459843307733536, 0.005369086749851704, -0.004041047766804695, -0.020462743937969208, 0.0015176195884123445, 0.026132600381970406, 0.022581921890378, 0.03032330796122551, 0.01102761086076498, -0.027558304369449615, -0.02890147641301155, 0.06115839630365372, -0.008876302279531956, 2.940132235774801e-34, -0.0626060888171196, 0.00021130537788849324, -0.028949331492185593, -0.023608677089214325, 0.****************, 0.028999287635087967, -0.*****************, -0.024597274139523506, 0.010957101359963417, -0.007751482538878918, 0.030238622799515724], "parsedContent": {"infobox": {}, "categories": ["Wilderness"], "links": ["Wilderness", "<PERSON><PERSON><PERSON>'s key", "Muddy key", "23 February", "2022", "Loot Chest", "PvP", "<PERSON><PERSON>", "Ferox Enclave", "Wilderness", "PvP world", "free-to-play", "member", "Loot Chest", "bank PIN", "bank", "bank note", "coins", "Wilderness", "High Level Alchemy", "<PERSON><PERSON>", "Ferox Enclave", "Skull (status)", "choose option", "Skull (status)", "bones", "Skull (status)", "Protect Item", "Ironman Mode", "Loot Chest", "<PERSON><PERSON>", "Wilderness Loot Key interface", "Loot Chest", "Skull (status)", "Forinthry Surge", "Choose option", "Loot Chest", "Grand Exchange", "Edgeville", "Mage Arena", "Revenant Caves", "Loot Chest"], "plainText": "120px Loot keys allow the player to retrieve the loot stored in the Loot Chest after defeating an opponent in a dangerous PvP activity. In order to receive loot keys instead of having the items dropped on the ground by the defeated opponents, the player must pay 1,000,000 coins to <PERSON><PERSON> in the Ferox Enclave and turn on the associated setting. Mechanics Loot keys can be obtained both in the regular worlds within the Wilderness, as well as the PvP worlds. They are available to both free-to-play and member players. Opening the Loot Chest requires entering a bank PIN. If the player attempts to withdraw items from a loot key which contains members-only items while on a free-to-play world, they will be unable to withdraw the members-only items, but may withdraw non-members-only items. Loot keys always take up 1 inventory space, regardless of the value or number of the looted items. They do not stack and cannot be banked or converted to bank notes. Additionally, loot keys can only be normally destroyed in the safe areas. Attempting to destroy a key with a total value above 1,000,000 coins within a dangerous area such as the Wilderness will fail with the following game chat message:  However, regardless of the location, the keys can be alched, yielding no coins. The value of the items deleted by disposing of the key through other means than the default Destroy option does not count towards the total value of the destroyed items reported by <PERSON><PERSON>. Note that in the area around Ferox Enclave, , and thus it should not considered a safe area. The skull of a player holding loot keys and the icon next to their name in the choose option menu changes accordingly to display the current number of held keys. However, loot key value has no effect on the appearance of the skull. If the player is unskulled, there is no way to tell how many keys they are holding. Items dropped on the ground By default, loot key allows to retrieve all items that would be normally dropped by the opponent. Depending on the individual settings chosen by the player, the loot stored in the chest might contain all items except for food, all items below a specific value threshold, or a combination of those two options instead. The rest of the items filtered by the individual settings will be dropped to the ground as regular loot. Note that the bones of the killed opponent are always dropped on the ground, regardless of whether a loot key was received. If the player's inventory is not full, a loot key will appear in the player's inventory immediately after killing the opponent along with the kill message. If the inventory is full, the key will drop to the ground instead. The key's loot value cannot be checked without picking it up. A dropped loot key can only be picked up by the player who has killed the opponent; otherwise the loot is lost. If the player already has 5 loot keys in the inventory, the loot will drop to the ground as if the option was turned off, and the player will receive the following message:  If the opponent drops no loot, no key is given to the player or dropped on the ground. Maximum number of keys Each player can only have up to 5 loot keys in the inventory at any given time. After killing an opponent that has enough loot keys to put the player over the 5 key limit, some loot keys will be deleted so that the player can only obtain up to 5 keys. Note that as long as the player does not have 5 keys in the inventory, the opponent's actual loot will also be received as a loot key, which might cause to delete an additional key. This is why defeating an opponent that has 5 keys in the inventory with the loot keys enabled will always cause one key to be removed, even if the killer has none. Death and key transfer When a player is killed by an opponent that is either in the process of dying or has died recently, their loot keys will be destroyed along with their contents and their loot will drop to the floor regardless of the loot key settings of the other player. In case player A has disengaged from combat with player B, and is consequently finished off by player C, the loot key will go to player C if more than 30 ticks (18 seconds) have passed since the moment of disengagement, even if player B has dealt a majority of the damage total. The effects of being unskulled and the Protect Item prayer apply only to the items in the inventory, but not associated with the loot keys themselves. As a consequence, loot keys are always dropped on death–in both PvP and PvM. If a player dies with loot keys in the inventory, they will either be transferred to the opponent if they are eligible to receive them, or removed instead. Ironman Mode Players in the Ironman Mode can unlock and receive loot keys, but they may not retrieve the loot themselves. However, they can still destroy or refuse to pick up the keys, effectively removing the loot from the game and preventing the opponents from getting back their items. They might also preview the looted items with the Loot Chest with the only option to destroy them. Ironmen can die to a regular player to transfer their previously obtained loot keys. This is currently the only way for the Ironman players to transfer the looted items. Gallery <gallery> Loot key destroy prompt.png|Prompt displayed when attempting to Destroy a loot key within a safe area. Nex Achievements and Loot Keys (6).png|The loot key settings interface that can be accessed via Skully. Wilderness Loot Key interface.png|The Wilderness Loot Key interface displayed after a player uses a loot key on the Loot Chest. File:Skull (Loot key) icon (1).png|Skull status whilst holding 1 loot key. File:Skull (Loot key) icon (2).png|Skull status whilst holding 2 loot keys. File:Skull (Loot key) icon (3).png|Skull status whilst holding 3 loot keys. File:Skull (Loot key) icon (4).png|Skull status whilst holding 4 loot keys. File:Skull (Loot key) icon (5).png|Skull status whilst holding 5 loot keys. File:Skull (Forinthry Surge Loot key) icon (1).png|Skull status with the Forinthry Surge whilst holding 1 loot keys. File:Skull (Forinthry Surge Loot key) icon (2).png|Skull status with the Forinthry Surge whilst holding 2 loot keys. File:Skull (Forinthry Surge Loot key) icon (3).png|Skull status with the Forinthry Surge whilst holding 3 loot keys. File:Skull (Forinthry Surge Loot key) icon (4).png|Skull status with the Forinthry Surge whilst holding 4 loot keys. File:Skull (Forinthry Surge Loot key) icon (5).png|Skull status with the Forinthry Surge whilst holding 5 loot keys. File:Choose option skull 1 loot key.png|Choose option icon whilst holding 1 loot key. File:Choose option skull 2 loot key.png|Choose option icon whilst holding 2 loot keys. File:Choose option skull 3 loot key.png|Choose option icon whilst holding 3 loot keys. File:Choose option skull 4 loot key.png|Choose option icon whilst holding 4 loot keys. File:Choose option skull 5 loot key.png|Choose option icon whilst holding 5 loot keys. </gallery> Changes References Category:Wilderness"}, "originalContent": "{{otheruses|the [[Wilderness]] loot keys|loot keys related to the other activities|Loot key (disambiguation)}}\n{{Redirect3|Wilderness Loot Key|For other uses, see [[<PERSON><PERSON><PERSON>'s key]] and [[Muddy key]]}}\n{{Infobox Item\n|name = Loot key\n|image = [[File:Loot key.png]]\n|release = [[23 February]] [[2022]]\n|update = Nex Achievements and Loot Keys\n|members = No\n|quest = No\n|tradeable = No\n|bankable = No\n|placeholder = Yes\n|equipable = No\n|stackable = No\n|noteable = No\n|options = Check, Destroy\n|destroy = The loot inside will be destroyed with it!\n|examine = Contains the loot from a fallen enemy. Use it at the Loot Chest in the Ferox Enclave.\n|value = 0\n|weight = 0.010\n|id = 26651,26652,26653,26654,26655\n}}\n[[File:Loot key detail.png|left|120px]]\n'''Loot keys''' allow the player to retrieve the loot stored in the [[Loot Chest]] after defeating an opponent in a dangerous [[PvP]] activity. In order to receive loot keys instead of having the items dropped on the ground by the defeated opponents, the player must pay 1,000,000 coins to [[<PERSON><PERSON>]] in the [[Ferox Enclave]] and turn on the associated setting.\n\n==Mechanics==\n\nLoot keys can be obtained both in the regular worlds within the [[Wilderness]], as well as the [[PvP world]]s. They are available to both [[free-to-play]] and [[member]] players. Opening the [[Loot Chest]] requires entering a [[bank PIN]]. If the player attempts to withdraw items from a loot key which contains members-only items while on a free-to-play world, they will be unable to withdraw the members-only items, but may withdraw non-members-only items.\n\nLoot keys always take up 1 inventory space, regardless of the value or number of the looted items. They do not stack and cannot be [[bank]]ed or converted to [[bank note]]s. Additionally, loot keys can only be normally destroyed in the safe areas. Attempting to destroy a key with a total value above 1,000,000 [[coins]] within a dangerous area such as the [[Wilderness]] will fail with the following game chat message: {{mes|The loot is worth too much for you to destroy it here. Go somewhere safe first.}} However, regardless of the location, the keys can be [[High Level Alchemy|''alched'']], yielding no coins. The value of the items deleted by disposing of the key through other means than the default ''Destroy'' option does not count towards the total value of the destroyed items reported by [[Skully]]. Note that in the area around [[Ferox Enclave]], {{Colour|Red|even if you are not teleblocked, other players who '''are''' teleblocked '''can''' attack you}}, and thus it should not considered a safe area.\n\nThe [[Skull (status)|skull]] of a player holding loot keys and the icon next to their name in the [[choose option]] menu changes accordingly to display the current number of held keys. However, loot key value has no effect on the appearance of the skull. If the player is [[Skull (status)|unskulled]], there is no way to tell how many keys they are holding.\n\n===Items dropped on the ground===\n\nBy default, loot key allows to retrieve all items that would be normally dropped by the opponent. Depending on the individual settings chosen by the player, the loot stored in the chest might contain all items except for food, all items below a specific value threshold, or a combination of those two options instead. The rest of the items filtered by the individual settings will be dropped to the ground as regular loot. Note that the [[bones]] of the killed opponent are always dropped on the ground, regardless of whether a loot key was received.\n\nIf the player's inventory is not full, a loot key will appear in the player's inventory immediately after killing the opponent along with the kill message. If the inventory is full, the key will drop to the ground instead. The key's loot value cannot be checked without picking it up. A dropped loot key can only be picked up by the player who has killed the opponent; otherwise the loot is lost. If the player already has 5 loot keys in the inventory, the loot will drop to the ground as if the option was turned off, and the player will receive the following message: {{mes|You have reached the limit of 5 loot keys.}} If the opponent drops no loot, no key is given to the player or dropped on the ground.\n\n===Maximum number of keys===\n\nEach player can only have up to 5 loot keys in the inventory at any given time.\n\nAfter killing an opponent that has enough loot keys to put the player over the 5 key limit, some loot keys will be deleted so that the player can only obtain up to 5 keys. Note that as long as the player does not have 5 keys in the inventory, the opponent's actual loot will also be received as a loot key, which might cause to delete an additional key. This is why defeating an opponent that has 5 keys in the inventory with the loot keys enabled will always cause one key to be removed, even if the killer has none.\n\n===Death and key transfer===\n\nWhen a player is killed by an opponent that is either in the process of dying or has died recently, their loot keys will be destroyed along with their contents and their loot will drop to the floor regardless of the loot key settings of the other player.\n\nIn case player A has disengaged from combat with player B, and is consequently finished off by player C, the loot key will go to player C if more than 30 ticks (18 seconds) have passed since the moment of disengagement, even if player B has dealt a majority of the damage total.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1630213577549160448|date=27 February 2023|archiveurl=https://archive.ph/IkrB6|archivedate=27 February 2023}}\n\nThe effects of being [[Skull (status)|unskulled]] and the [[Protect Item]] prayer apply only to the items in the inventory, but not associated with the loot keys themselves. As a consequence, loot keys are always dropped on death–in both PvP and PvM. If a player dies with loot keys in the inventory, they will either be transferred to the opponent if they are eligible to receive them, or removed instead.\n\n===Ironman Mode===\n\nPlayers in the [[Ironman Mode]] can unlock and receive loot keys, but they may not retrieve the loot themselves. However, they can still destroy or refuse to pick up the keys, effectively removing the loot from the game and preventing the opponents from getting back their items. They might also preview the looted items with the [[Loot Chest]] with the only option to destroy them.\n\nIronmen can die to a regular player to transfer their previously obtained loot keys. This is currently the only way for the Ironman players to transfer the looted items.\n\n==Gallery==\n<gallery>\nLoot key destroy prompt.png|Prompt displayed when attempting to ''Destroy'' a loot key within a safe area.\nNex Achievements and Loot Keys (6).png|The loot key settings interface that can be accessed via [[Skully]].\nWilderness Loot Key interface.png|The [[Wilderness Loot Key interface]] displayed after a player uses a loot key on the [[Loot Chest]].\nFile:Skull (Loot key) icon (1).png|[[Skull (status)|Skull status]] whilst holding 1 loot key.\nFile:Skull (Loot key) icon (2).png|Skull status whilst holding 2 loot keys.\nFile:Skull (Loot key) icon (3).png|Skull status whilst holding 3 loot keys.\nFile:Skull (Loot key) icon (4).png|Skull status whilst holding 4 loot keys.\nFile:Skull (Loot key) icon (5).png|Skull status whilst holding 5 loot keys.\nFile:Skull (Forinthry Surge Loot key) icon (1).png|Skull status with the [[Forinthry Surge]] whilst holding 1 loot keys.\nFile:Skull (Forinthry Surge Loot key) icon (2).png|Skull status with the Forinthry Surge whilst holding 2 loot keys.\nFile:Skull (Forinthry Surge Loot key) icon (3).png|Skull status with the Forinthry Surge whilst holding 3 loot keys.\nFile:Skull (Forinthry Surge Loot key) icon (4).png|Skull status with the Forinthry Surge whilst holding 4 loot keys.\nFile:Skull (Forinthry Surge Loot key) icon (5).png|Skull status with the Forinthry Surge whilst holding 5 loot keys.\nFile:Choose option skull 1 loot key.png|[[Choose option]] icon whilst holding 1 loot key.\nFile:Choose option skull 2 loot key.png|Choose option icon whilst holding 2 loot keys.\nFile:Choose option skull 3 loot key.png|Choose option icon whilst holding 3 loot keys.\nFile:Choose option skull 4 loot key.png|Choose option icon whilst holding 4 loot keys.\nFile:Choose option skull 5 loot key.png|Choose option icon whilst holding 5 loot keys.\n</gallery>\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 13 November 2024\n|update = Guardians of the Rift Changes Out Now!\n|change = Loot keys containing less than 1,000,000 coins worth of loot can now be destroyed, up from 30,000 coins.\n}}\n{{Subject changes\n|date = 1 March 2023\n|update = Theatre of Blood: Entry Mode Improvements\n|change = Players can no longer receive a loot key if they are in a trade screen. The key will now be dropped where the player died.\n}}\n{{Subject changes\n|date = 29 June 2022\n|update = Unranked Group Iron Changes and More!\n|change = Loot Keys can now be destroyed within the Wilderness if they contain less than 30,000 coins worth of loot. There is a small delay to this process to discourage players from destroying loot during combat.\n}}\n{{Subject changes\n|date = 30 March 2022\n|update = Guardians of the Rift Improvements\n|change = Loot keys' despawn timer has been extended to two minutes, for consistency with other drops.\n}}\n{{Subject changes\n|date = 9 March 2022\n|update = PJ Timer & New Broadcast Settings \n|change =\n* The cost of unlocking the loot key was decreased from 5,000,000 coins to 1,000,000 coins. Players who have already unlocked the loot keys with the original price were reimbursed 4,000,000 coins deposited into their banks.\n* [[Loot Chest]]s were added to the [[Grand Exchange]], [[Edgeville]] bank and [[Mage Arena]] bank.\n* When players kill each other in a duel, one of the players no longer receives the entire loot. Instead, when a player is killed by an opponent that is either in the process of dying or has died recently, their loot keys will be destroyed and their loot will drop to the floor regardless of the loot key settings of the other player.\n}}\n{{Subject changes\n|date = 23 February 2022\n|type = Hotfix\n|change = The [[Revenant Caves]] fee is now included in the loot within the [[Loot Chest]].\n}}\n{{Subject changes\n|date = 23 February 2022\n|type = Hotfix\n|change = If the player's inventory is full, the loot key will drop to the floor and the player will be able to pick it up normally without it being removed.\n}}\n{{Subject changes footer}}\n\n==References==\n{{Reflist}}\n\n{{Account build}}\n{{Free-to-play PvP}}\n{{Keys}}\n\n[[Category:Wilderness]]"}