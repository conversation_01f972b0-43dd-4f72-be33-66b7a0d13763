{"title": "<PERSON><PERSON><PERSON>", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [{"type": "category", "value": "Weapon types", "confidence": 0.9, "source": "category"}], "relationships": [{"from": "<PERSON><PERSON><PERSON>", "to": "Slash", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "combat style", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Strength", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "net trapping", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Herblore", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "black salamander", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "<PERSON><PERSON> brothers", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "safespot", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Lava dragon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "safespotting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "halberd", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "bows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "The Worst Ranged Weapon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "More than just a ranged weapon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Combat Achievements", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Protect Item", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Death", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Swamp lizard (Hunter)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Orange salamander (Hunter)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Red salamander (Hunter)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Black salamander (Hunter)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "<PERSON><PERSON> sa<PERSON> (Hunter)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON><PERSON>", "to": "Weapon types", "type": "belongs_to_category", "confidence": 0.9, "source": "category"}], "embedding": [0.05830145999789238, -0.05862521380186081, 0.023939037695527077, -0.00988003984093666, -0.05509088933467865, -0.002131607849150896, 0.0215874332934618, 0.037995852530002594, -0.03832269087433815, -0.06397233158349991, -0.0765230804681778, 0.06580270826816559, 0.052126094698905945, -0.05118729919195175, 0.06295588612556458, -0.08351957052946091, -0.004186683334410191, 0.008092022500932217, -0.039741404354572296, -0.02047753520309925, 0.013845162466168404, 0.0017687996150925756, -0.03888405114412308, 0.0039888666942715645, -0.04341987520456314, -0.045069459825754166, -0.020432889461517334, 0.013184362091124058, 0.037612657994031906, -0.08420897275209427, 0.08783309161663055, -0.04860832542181015, 0.032060492783784866, 0.05731964111328125, 2.0841339392063674e-06, -0.00880979374051094, 0.03706255555152893, 0.025034651160240173, 0.0295253898948431, 0.025838544592261314, 0.01663968525826931, -0.010521083138883114, 0.02468729205429554, 0.036436695605516434, 0.006606310606002808, -0.08365093916654587, 0.02001163363456726, 0.013882671482861042, 0.020368821918964386, -0.016812797635793686, 0.020986735820770264, -0.08419213443994522, 0.025483574718236923, 0.003853700589388609, 0.041198357939720154, -0.02473646216094494, -0.01940608210861683, -0.10989357531070709, 0.04605095461010933, 0.042823467403650284, -0.050178539007902145, -0.02919822558760643, -0.036636173725128174, -0.016590967774391174, 0.12646207213401794, -0.02783539518713951, -0.054357413202524185, -0.009195726364850998, -0.009887080639600754, 0.008924005553126335, -0.06155189871788025, -0.019629623740911484, -0.006430457346141338, 0.030516711995005608, -0.005035403184592724, 0.04468519985675812, 0.0024767450522631407, -0.01443568430840969, -0.03285706788301468, 0.019292255863547325, -0.018671266734600067, 0.0059631033800542355, 0.021916061639785767, -0.012364033609628677, 0.010612105019390583, -5.833660907228477e-05, -0.0005985480383969843, 0.008686011657118797, 0.02897990681231022, -0.012894758954644203, -0.014628928154706955, -0.036340564489364624, 0.03394898772239685, 0.053067538887262344, -0.027555588632822037, 0.006975147873163223, 0.012577996589243412, -0.06452638655900955, 0.07406871020793915, -0.028700662776827812, 0.0031491003464907408, 0.010587197728455067, -0.050216540694236755, -0.018654758110642433, -0.05474162846803665, -0.05668647214770317, 0.03849570080637932, 0.050573572516441345, 0.013293404132127762, 0.006321461405605078, -0.02467525750398636, 0.011786437593400478, -0.004569354932755232, -0.02003145031630993, 0.06890371441841125, -0.03774825856089592, 0.041939616203308105, -0.03264494240283966, 0.010719827376306057, 0.018004806712269783, -0.04220758005976677, 0.002482146956026554, 0.0201491117477417, 0.015292001888155937, -0.0015299820806831121, -0.0697563961148262, -0.05358950421214104, 0.03669218346476555, -0.005644362419843674, 0.007058667950332165, -0.02838231436908245, 0.024968218058347702, -0.021123863756656647, -0.04887379705905914, 0.00473139900714159, -0.010805101133883, -0.024161653593182564, -0.04599648341536522, -0.014839785173535347, 0.03305608779191971, -0.0036045231390744448, 0.0032041065860539675, -0.011968364007771015, 0.048958033323287964, 0.0195132028311491, -0.003436699276790023, 0.017215367406606674, 0.03574274480342865, -0.011303839273750782, -0.013525114394724369, 0.03176850080490112, 0.007736132480204105, 0.031965501606464386, 0.005766092333942652, 0.06948574632406235, -0.0009397226967848837, -0.018975302577018738, -0.011730420403182507, -0.031257953494787216, 0.06713611632585526, 0.012247194536030293, 0.014710815623402596, -0.05624012649059296, -0.027263153344392776, 0.0028154205065220594, -0.05624300241470337, 0.007770542521029711, 0.049589212983846664, -0.006375106051564217, -0.05844726786017418, -0.003466819180175662, -0.03154987096786499, -0.06216730549931526, 0.008893451653420925, 0.020509852096438408, 0.1203857809305191, 0.09393524378538132, 0.038966625928878784, -0.007003608625382185, -0.007521621882915497, 0.001899585360661149, 0.00570478942245245, 0.023563507944345474, 0.04190196469426155, 0.009680496528744698, -0.005039446987211704, 0.07084698975086212, -0.019138716161251068, -0.022796064615249634, -0.020945418626070023, -0.010782288387417793, -0.02661413885653019, -0.07401672750711441, -0.007287685759365559, 0.06387020647525787, 0.04327904433012009, 0.0010826826328411698, -0.004423219244927168, -0.06533968448638916, -0.01069473847746849, -0.02354227937757969, 0.0557083934545517, -0.02435145154595375, 0.05563654750585556, -0.02491029165685177, -0.016341960057616234, -0.08481330424547195, 0.040063582360744476, -0.04377784952521324, 0.017307352274656296, -0.024055246263742447, 0.010242011398077011, -0.02815711498260498, 0.004277692176401615, -0.02485571801662445, 0.005773411598056555, 0.00039351650048047304, -0.03553882986307144, -0.010725308209657669, -0.026005612686276436, 0.029274486005306244, -0.03649891912937164, 0.07319531589746475, 0.018237829208374023, 0.043281182646751404, 0.017685646191239357, 0.01576472446322441, 0.004478112794458866, 0.016651950776576996, -0.046520356088876724, -0.0003354518848937005, 0.00970835518091917, 0.039370667189359665, -0.006889883894473314, 0.001943844137713313, -0.005708782467991114, 0.03619709610939026, 0.09231226146221161, -0.03580166772007942, -0.021393055096268654, 0.056789640337228775, 0.02420308068394661, 0.02043752372264862, -0.013003751635551453, 0.011676457710564137, -0.005134842824190855, -0.08101441711187363, -0.017537619918584824, -0.020016390830278397, -0.03909657150506973, -0.031508952379226685, 0.039124540984630585, -0.011691553518176079, 0.028541045263409615, -0.04046729579567909, -0.07043754309415817, 0.026599042117595673, -0.0047456566244363785, -0.04788481444120407, -0.04875515401363373, 0.025498896837234497, -0.030652403831481934, 0.02040930837392807, -0.04158550873398781, -0.007008622866123915, 0.027338353917002678, 0.002555900951847434, -0.026351483538746834, 0.06981112062931061, 0.020645523443818092, 0.06416589766740799, 0.028844209387898445, -0.03808443993330002, -0.008715946227312088, 0.012836075387895107, 0.012667544186115265, 0.009748083539307117, -0.04069884866476059, -0.01382515113800764, 0.04377998411655426, -0.0068879141472280025, 0.01176353171467781, -0.007614208851009607, 0.030505819246172905, 0.007827378809452057, 0.074714295566082, -0.020777758210897446, 0.020697712898254395, -0.011258408427238464, -0.0407724529504776, -0.02061968669295311, 0.017855042591691017, 0.01356165949255228, 0.019547298550605774, 0.053342487663030624, 0.035288527607917786, 0.004439582582563162, 0.011808858253061771, 0.00950308982282877, -0.04110763594508171, 0.03656861186027527, 0.0633685514330864, -0.0310556348413229, 0.018795739859342575, 0.02232145518064499, -0.04024374857544899, 0.008416198194026947, -0.06002115085721016, 0.009716764092445374, 0.0495043620467186, 0.01745540089905262, 0.015993844717741013, 0.011007904075086117, -0.018891649320721626, 0.08498284220695496, -5.3569310694001615e-05, -0.016750061884522438, -0.053300369530916214, 0.0035927919670939445, -0.038438137620687485, -0.014977102167904377, 0.02347981370985508, -0.012512973509728909, -0.009445557370781898, 0.01326802559196949, 0.025317342951893806, 0.06561250239610672, -0.012363724410533905, -0.02647237479686737, -0.008370110765099525, 0.007709880825132132, -0.03691113740205765, 0.0012491758679971099, 0.05643262341618538, -0.03562096506357193, 0.0930294543504715, -0.016043463721871376, -0.08604887872934341, 0.002195662586018443, 0.019825300201773643, -0.0233014989644289, 0.00830498244613409, -0.009895593859255314, 0.01655368134379387, -0.022115761414170265, 0.024471571668982506, -0.04554159566760063, 0.04159008711576462, 0.06829360127449036, -0.008965902961790562, 0.0034659167286008596, 0.002705585677176714, -0.017928840592503548, 0.005407951772212982, -0.02800404466688633, -0.03654835745692253, -0.03345877304673195, -0.10553663969039917, 0.013067355379462242, -0.013455076143145561, 0.06358455866575241, 0.04721831530332565, 0.04449466988444328, -0.04023417830467224, -0.03706463798880577, -0.022247634828090668, -0.014864899218082428, -0.029144776985049248, -0.05066725239157677, -0.053775936365127563, -0.028460828587412834, 0.022132771089673042, -0.00829644501209259, 0.042548518627882004, 0.02126249670982361, 0.04475622624158859, 0.020049624145030975, -0.0397912859916687, 0.02150154486298561, -0.011099006980657578, 0.011981376446783543, -0.06553931534290314, -0.004132257774472237, -0.010234926827251911, 0.020788200199604034, -0.005071691237390041, 0.011183619499206543, 0.016461580991744995, 0.010586440563201904, -0.08290624618530273, -0.0011110760970041156, -0.009250713512301445, -0.0035942629911005497, 0.05976054072380066, -0.015443175099790096, -0.008842426352202892, -0.007864672690629959, 0.0321781150996685, -0.004385508596897125, 0.022885898128151894, 0.04167220741510391, 0.03154292702674866, 0.05798547342419624, -0.001836952636949718, 0.00043904149788431823, -0.03661865368485451, 0.09370333701372147, -0.04143139719963074, 0.03958209231495857, 0.034770455211400986, -0.006414356641471386, 0.0029236346017569304, -0.056871797889471054, -0.010646708309650421, -0.02772076614201069, -0.03897700086236, 0.03038865327835083, 0.026789961382746696, -0.01924138516187668, 0.05099521204829216, -0.010218843817710876, 0.035502418875694275, 0.03211596608161926, -0.013680987991392612, 0.02958298660814762, -0.023496760055422783, -0.050637971609830856, -0.024038802832365036, 0.033600978553295135, 0.035019177943468094, -0.03713230788707733, 0.0002527580363675952, 0.0267779678106308, -0.008959253318607807, -0.019223958253860474, -0.0014756839955225587, -0.014703240245580673, -0.04225696623325348, 0.014147543348371983, -0.04467090219259262, -0.004503573756664991, 0.06082576513290405, 6.102098632254638e-05, -0.019610408693552017, 0.003945788834244013, -0.032157015055418015, -0.04763002693653107, 0.024519389495253563, -0.07523950189352036, 0.0498337559401989, -0.023749468848109245, 0.004693008027970791, 0.0026016649790108204, 0.004300227388739586, 0.0007209903560578823, 0.02473030984401703, -0.04652823135256767, -0.03572221100330353, -0.011029038578271866, -0.08910426497459412, 0.00198248284868896, 0.016690216958522797, -0.07600454986095428, -0.0008245733915828168, -0.024798046797513962, 0.02003655955195427, -0.0001760215382091701, -0.0044099404476583, -0.002834137761965394, -0.0029726573266088963, -0.019580619409680367, 0.014811057597398758, 0.02613980509340763, 0.025666093453764915, 0.00672110915184021, 0.03575519472360611, 0.029708033427596092, -0.017056316137313843, 0.012314441613852978, 0.014422813430428505, -0.07030435651540756, 0.0268667284399271, 0.017421439290046692, -0.10523957759141922, 0.009839536622166634, 0.01767856255173683, 0.01570976711809635, 0.002557614352554083, 0.014471864327788353, 0.04398534074425697, -0.0031439343001693487, 0.006796571891754866, 0.007112466264516115, 0.020807744935154915, 0.05913702771067619, 0.0792730301618576, -0.025861214846372604, 0.017142703756690025, 0.040190115571022034, -0.041219934821128845, -0.0022680910769850016, 0.02009519748389721, -0.020847784355282784, -0.0033590304665267467, 0.024869436398148537, 0.006352433934807777, -0.009657284244894981, 0.04816681146621704, -0.01064165960997343, 0.012249945662915707, 0.06018327921628952, -0.016880158334970474, -0.01684756763279438, -0.005234599579125643, 0.051956310868263245, 0.11442562192678452, -0.00994561892002821, 0.003363346913829446, -0.018274305388331413, 0.0373595766723156, -0.02653731405735016, -0.015616941265761852, 0.043759748339653015, -0.0024426644667983055, 0.01464362908154726, 0.027492156252264977, 0.014055708423256874, 0.021163666620850563, 0.0005363462842069566, -0.029875049367547035, 0.027101077139377594, 0.024998152628540993, 0.04220915213227272, -0.01566327176988125, -0.005273234099149704, 5.880315075046383e-05, 0.010813763365149498, 0.04460708424448967, -0.001957535045221448, -0.057105012238025665, -0.025466904044151306, -0.01308547705411911, 0.053930945694446564, -0.0586998350918293, -0.015011143870651722, -0.018536580726504326, -0.03242088481783867, 0.006574420258402824, -0.02209477312862873, 0.012788742780685425, -0.006641383282840252, 0.021585596725344658, 0.03753851354122162, -0.059768885374069214, -0.02247043512761593, -6.305738141578583e-33, -0.028263742104172707, -0.027760930359363556, 0.015322945080697536, -0.1051037535071373, 0.0023765757214277983, 0.0024149364326149225, -0.012722412124276161, 0.01542555633932352, -0.05264783650636673, -0.012984618544578552, -0.02809896320104599, -0.03028789348900318, 0.008418947458267212, -0.027293015271425247, 0.006778878625482321, -0.01584223099052906, -0.01139844674617052, -0.005729687865823507, 0.009411603212356567, -0.055181242525577545, 0.057336241006851196, 0.029014838859438896, 0.05791056901216507, 0.09925499558448792, 0.00841972790658474, -0.02200862020254135, -0.053484462201595306, -0.013791725039482117, -0.03545413166284561, -0.015126192942261696, -0.035213958472013474, -0.04974706470966339, -0.024972759187221527, 0.0009215115569531918, -0.0022544546518474817, 0.04494493827223778, 0.009287660010159016, 0.018759161233901978, 0.00553200813010335, 0.059353336691856384, -0.01573462411761284, -0.04442385584115982, 0.05347519367933273, 0.007685498334467411, 0.0031775389797985554, -0.011292342096567154, -0.02628105692565441, -0.004358988720923662, 0.011291708797216415, -0.039771851152181625, 0.049189239740371704, 0.005565226543694735, -0.01492526475340128, -0.0852779969573021, 0.028670839965343475, -0.003403565613552928, 0.014940834604203701, -0.04406815022230148, 0.04603405296802521, -0.009055241011083126, 0.06336700916290283, -0.03011801280081272, 0.018937690183520317, 0.08090654015541077, -0.02353993058204651, 0.02336074411869049, -0.0016610113671049476, -0.07611151784658432, 0.06167222559452057, -0.029449664056301117, -0.047011490911245346, 0.04429612681269646, 0.07224025577306747, -0.035558488219976425, 0.06430531293153763, -0.03703340142965317, -0.016090039163827896, 0.033329762518405914, 0.02531834878027439, 0.007378370966762304, 0.0037298500537872314, 0.018010837957262993, 0.030948327854275703, 0.011512840166687965, -0.022224506363272667, 0.0407821387052536, 0.009491736069321632, -0.009268936701118946, 0.0071419039741158485, 0.013373952358961105, 0.046473924070596695, 0.020646249875426292, -0.019665664061903954, 0.025275185704231262, 0.04230329021811485, 0.05802743136882782, 0.04006210342049599, -0.0004135682829655707, 0.009999429807066917, -0.05494241043925285, 0.0633523091673851, 0.012551012448966503, 0.02284090593457222, -0.06440676003694534, -0.012069864198565483, 0.007019373122602701, 0.021071016788482666, -0.053750645369291306, -0.06532123684883118, 0.0036814070772379637, -0.06268727034330368, -0.02926424890756607, -0.007260131649672985, 0.01458960771560669, 0.027962476015090942, -0.036722175776958466, -0.010757052339613438, 0.013398602604866028, 0.024899188429117203, -0.05321573093533516, -0.01711486279964447, 0.08397844433784485, -0.06323129683732986, -0.023436352610588074, -0.016602085903286934, 0.011387338861823082, 0.07634930312633514, -0.007345161400735378, -0.04407327622175217, -0.03471057489514351, -0.022325584664940834, -0.06101962924003601, 2.708265469664184e-07, 0.04655713215470314, 0.01741018332540989, -0.02526499517261982, -0.013893729075789452, 0.014757988043129444, 0.042861197143793106, -0.007357857655733824, 0.05177697166800499, -0.05706595629453659, -0.016051851212978363, -0.0008592663798481226, 0.044713906943798065, -0.02551959827542305, 0.023661328479647636, -0.00513397715985775, -0.023189108818769455, -0.06385848671197891, 0.025650624185800552, 0.01821332611143589, -0.051935575902462006, -0.023977765813469887, -0.03885092958807945, -0.002177151618525386, -0.02125343680381775, -0.028383703902363777, -0.02579336054623127, -0.015144933946430683, 0.04487074166536331, 0.014740994200110435, 0.04014694690704346, 0.02153877727687359, 0.038576770573854446, -0.04043705761432648, -0.015758991241455078, 0.032881200313568115, -0.07168983668088913, -0.028056208044290543, -0.02033597230911255, 0.03285333514213562, 0.030171751976013184, -0.042075589299201965, 0.021625027060508728, 0.004997303243726492, 0.02360296994447708, 0.0412881076335907, 0.09283386915922165, 0.008336033672094345, -0.05732171609997749, -0.04355554282665253, 0.008653050288558006, -0.03578699752688408, 0.009391131810843945, -0.008459345437586308, 0.015288140624761581, -0.031762346625328064, -0.00815015658736229, 0.02369387447834015, -0.01000757422298193, 0.025824327021837234, 0.011443893425166607, -0.012594112195074558, -0.010236618109047413, -0.032753463834524155, 0.0606069415807724, -0.039131853729486465, 0.005524719599634409, -0.07796131074428558, 2.3507345469089578e-34, -0.014718864113092422, 0.03519405424594879, -0.019597724080085754, 0.024828635156154633, 0.01313421968370676, -0.0037649988662451506, -0.029634075239300728, -0.030859198421239853, 0.039893537759780884, 0.001963176764547825, -0.026171671226620674], "parsedContent": {"infobox": {}, "categories": ["Weapon types"], "links": ["Slash", "Ranged", "Magic", "combat style", "Ranged", "Magic", "Strength", "net trapping", "Herblore", "black salamander", "<PERSON><PERSON> brothers", "safespot", "Lava dragon", "<PERSON><PERSON>", "safespotting", "halberd", "bows", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "The Worst Ranged Weapon", "More than just a ranged weapon", "Combat Achievements", "Protect Item", "Death", "Swamp lizard (Hunter)", "Orange salamander (Hunter)", "Red salamander (Hunter)", "Black salamander (Hunter)", "<PERSON><PERSON> sa<PERSON> (Hunter)"], "plainText": "Salamanders are unique and versatile two-handed weapons that facilitate the training of all combat styles, namely the Ranged, Magic and Strength skills. Salamanders may be obtained through net trapping and must be fuelled using a variety of herb-tars made with the Herblore skill. The black salamander is commonly used by players with a low budget to kill the <PERSON> brothers or to safespot Lava dragons. Due to their ability to output multiple types of damage, salamanders also have a niche use in PvP against bot accounts which use auto-prayer macros. They are also able to attack diagonally over some obstacles and corners similar to most ranged or magic attacks, which allows it to be used in melee safespotting without having to flinch or use a halberd. Salamanders can also be used to flinch an enemy by while dealing magic or ranged damage such as the Kalphite Queen's second phase, because spells and other ranged weapons have a delay that makes flinching unsafe. When using the Scorch (Slash) and Blaze (Magic) attack styles, the attack speed is 5. When using the Flare (Range) attack style, the attack speed is 4, which is similar in concept to the Rapid style of most Ranged weapons, and it will use standard ranged defence, similar to bows. Killing Kree'arra and the Phantom Mu<PERSON><PERSON> with a salamander are grandmaster and master Combat Achievement tasks. Warning: All salamanders are not retrievable if they are unprotected items when a player dies. This is because it is a live creature and it will simply run away once released. Combat styles Salamanders and fuel {| class=\"wikitable align-left-4 align-left-6\" style=\"text-align:center;\" ! ! !colspan=2|Salamander !colspan=2|Ammunition |- |30 |29 | <sup>(Hunter)</sup> | |- |50 |47 | <sup>(Hunter)</sup> | |- |60 |59 | <sup>(Hunter)</sup> | |- |70 |67 | <sup>(Hunter)</sup> | |- |80 |79 | <sup>(Hunter)</sup> | |} Bonuses Trivia * Salamanders have a unique sound when equipping them. References Category:Weapon types"}, "originalContent": "{{External|rs}}\n{{Infobox Weapon Group\n|name = Salamander\n|images = [[File:Swamp lizard.png]][[File:Orange salamander.png]][[File:Red salamander.png]][[File:Black salamander.png]][[File:Tecu salamander.png]]\n|primary = [[Slash]]\n|secondary = [[Ranged|Range]]\n|tertiary = [[Magic]]\n|speed = Varies\n}}\n'''Salamanders''' are unique and versatile two-handed weapons that facilitate the training of all [[combat style]]s, namely the [[Ranged]], [[Magic]] and [[Strength]] skills.\n\nSalamanders may be obtained through [[net trapping]] and must be fuelled using a variety of herb-tars made with the [[Herblore]] skill. The [[black salamander]] is commonly used by players with a low budget to kill the [[<PERSON><PERSON> brothers]] or to [[safespot]] [[Lava dragon]]s. Due to their ability to output multiple types of damage, salamanders also have a niche use in PvP against [[Botting|bot]] accounts which use auto-prayer macros. They are also able to attack diagonally over some obstacles and corners similar to most ranged or magic attacks, which allows it to be used in melee [[safespotting]] without having to flinch or use a [[halberd]]. Salamanders can also be used to flinch an enemy by while dealing magic or ranged damage such as the Kalphite Queen's second phase, because spells and other ranged weapons have a delay that makes flinching unsafe.\n\nWhen using the Scorch (Slash) and Blaze (Magic) attack styles, the attack speed is 5. When using the Flare (Range) attack style, the attack speed is 4, which is similar in concept to the Rapid style of most Ranged weapons, and it will use standard{{CiteTwitter|author=Mod Ash|url=https://x.com/JagexAsh/status/1812764898239107397?t=0cPxrFVCvgFX01UUhSnWsg|date=15 July 2024|archiveurl=|archivedate=|quote=\"standard\"|notes=|name=|group=}} ranged defence, similar to [[bows]].\n\nKilling [[Kree'arra]] and the [[Phantom Muspah]] with a salamander are [[The Worst Ranged Weapon|grandmaster]] and [[More than just a ranged weapon|master]] [[Combat Achievements|Combat Achievement]] tasks.\n\nWarning: All salamanders are '''not''' retrievable if they are [[Protect Item|unprotected items]] when a player [[Death|dies]]. This is because it is a live creature and it will simply run away once released.\n\n==Combat styles==\n{{CombatStyles|Salamander}}\n\n==Salamanders and fuel==\n{| class=\"wikitable align-left-4 align-left-6\" style=\"text-align:center;\"\n!\n{{SCP|Attack}}\n{{SCP|Ranged}}\n{{SCP|Magic}}\n!{{SCP|Hunter}}\n!colspan=2|Salamander\n!colspan=2|Ammunition\n|-\n|30\n|29\n|{{plinkt|Swamp lizard}} <sup>([[Swamp lizard (Hunter)|Hunter]])</sup>\n|{{plinkt|Guam tar}}\n|-\n|50\n|47\n|{{plinkt|Orange salamander}} <sup>([[Orange salamander (Hunter)|Hunter]])</sup>\n|{{plinkt|Marrentill tar}}\n|-\n|60\n|59\n|{{plinkt|Red salamander}} <sup>([[Red salamander (Hunter)|Hunter]])</sup>\n|{{plinkt|Tarromin tar}}\n|-\n|70\n|67\n|{{plinkt|Black salamander}} <sup>([[Black salamander (Hunter)|Hunter]])</sup>\n|{{plinkt|Harralander tar}}\n|-\n|80\n|79\n|{{plinkt|Tecu salamander}} <sup>([[Tecu salamander (Hunter)|Hunter]])</sup>\n|{{plinkt|Irit tar}}\n|}\n\n===Bonuses===\n{{Infotable Bonuses|Swamp lizard|Orange salamander|Red salamander|Black salamander|Tecu salamander|sort=aslash|nototals=yes}}\n\n==Trivia==\n{{Listen\n|title = Equip salamander\n|align = right\n|filename = Equip salamander.ogg\n|desc = The unique sound which can be heard when players equip a salamander.\n}}\n* Salamanders have a unique sound when equipping them.\n\n==References==\n{{Reflist}}\n\n{{Salamanders}}\n{{Slash weapons}}\n{{Ranged weapons}}\n{{Magic weapons}}\n{{Weapon}}\n[[Category:Weapon types]]"}