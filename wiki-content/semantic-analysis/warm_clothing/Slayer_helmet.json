{"title": "Slayer helmet", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [{"type": "category", "value": "Warm clothing", "confidence": 0.9, "source": "category"}], "relationships": [{"from": "Slayer helmet", "to": "6 January", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "2014", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "nose peg", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "facemask", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "earmuffs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "spiny helmet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "enchanted gem", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "black mask", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Reinforced goggles", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "A Porcine of Interest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Crafting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "boosts", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Slayer reward points", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Defence", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "nose peg", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Slayer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "rune full helm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Nex", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "warm clothing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Wintertodt", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Slayer task", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Attack", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Strength", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "black mask", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Salve amulet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Salve amulet (e)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Salve amulet(ei)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "berserker necklace", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "obsidian weapons", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Slayer helmet (i)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Soul Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Nightmare Zone", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Emir's Arena", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "scroll of imbuing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "<PERSON> Leviathan", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "<PERSON> Whisperer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "undead", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Salve amulet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Salve amulet (e)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Salve amulet(ei)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "amulet of torture", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "helm of <PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Amulet of fury", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "amulet of glory", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "necklace of anguish", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "occult necklace", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Void Knight equipment", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "ankou", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Vorkath", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "aberrant spectre", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "nose peg", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Malevolent masquerade", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Slayer master", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "A Porcine of Interest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "black mask", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "melee", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Slayer task", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "black mask (i)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Slayer helmet (i)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "earmuffs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "banshee", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "facemask", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Smoke Dungeon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Smoke Devil Dungeon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Dust devil", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Smoke devil", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Nex", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Poison gas cloud", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Volcanic sulphur (rock)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Lovakengj", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "sulphur mine", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "nose peg", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "aberrant spectre", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "spiny helmet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "wall beast", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Lumbridge Swamp Caves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "enchanted gem", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "eternal gem", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "reinforced goggles", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Sour<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "reinforced goggles", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "A Porcine of Interest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Nightmare Zone", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Combat_Achievements/Hard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Combat Achievements", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Soul Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Scroll of imbuing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Slayer helmet (i)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "hard Kourend & Kebos Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Captain <PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "lizardman shaman", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "<PERSON><PERSON><PERSON> helm (5)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Slayer reward point", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Combat Achievements", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "G<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Eye see you", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "King black bonnet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Kal<PERSON><PERSON> khat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Unholy helmet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Dark Mantle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Undead Head", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Use more head", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Twisted Vision", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Warriors' Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "warm clothing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Wintertodt", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "death", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Items Kept on Death", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "graphical updates", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "reinforced goggles", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "A Porcine of Interest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet", "to": "Warm clothing", "type": "belongs_to_category", "confidence": 0.9, "source": "category"}], "embedding": [0.016641156747937202, -0.036272402852773666, 0.02660873904824257, 0.017765583470463753, 0.0037746368907392025, -0.013600755482912064, 0.013824038207530975, 0.0039515248499810696, 0.012557975016534328, 0.012614191509783268, 0.03317105770111084, 0.04626679793000221, 0.018763570114970207, 0.040846142917871475, 0.07197148352861404, -0.029949648305773735, -0.044383708387613297, -0.016220111399888992, -0.01859370805323124, 0.01624598354101181, 0.043449077755212784, 0.0033794709015637636, -0.04014259949326515, -0.009506149217486382, 0.005323400720953941, -0.03468550369143486, 0.028042294085025787, 0.019394321367144585, 0.02752978540956974, -0.03941083326935768, 0.08006664365530014, -0.05761009082198143, -0.03982482850551605, 0.026702987030148506, 2.1826513147971127e-06, -0.016710808500647545, 0.04035690054297447, 0.029191723093390465, -0.035679079592227936, 0.03018265590071678, -0.08029133826494217, -0.0542450025677681, -0.027835628017783165, 0.03309033066034317, 0.026031577959656715, -0.04073109105229378, -0.004296340513974428, 0.07029804587364197, 0.006049942225217819, -0.04889031499624252, -0.02448609471321106, -0.04764537885785103, -0.015929346904158592, -0.02754087559878826, 0.08295746147632599, 0.031935691833496094, -0.026982152834534645, 0.010349652729928493, -0.00330715742893517, 0.00961130391806364, -0.02547481097280979, 0.03366681933403015, -0.035239920020103455, 0.013043619692325592, 0.07910186797380447, 0.008407327346503735, -0.0011823616223409772, -0.04120858386158943, 0.008819419890642166, 0.017710426822304726, -0.05786770209670067, -0.01183883287012577, 0.03155685216188431, 0.015501494519412518, -0.025472821667790413, -0.04031124711036682, 0.005468407645821571, -0.0099743427708745, -0.010167500004172325, -0.018649833276867867, 0.005955666769295931, 0.01008687261492014, -0.027148503810167313, -0.009773100726306438, -0.03338020667433739, -0.038492463529109955, -0.04136025533080101, -0.0059992303140461445, 0.02781982161104679, -0.02847328968346119, -0.0015429321210831404, 0.02543323114514351, -0.03769814595580101, 0.021634593605995178, -0.04986881464719772, -0.017541930079460144, 0.0003054530534427613, -0.06256641447544098, 0.07859703153371811, 0.0018105792114511132, -0.04165888577699661, 0.0077701136469841, 0.0007804305641911924, 0.04694720730185509, -0.06548117846250534, -0.07544662803411484, -0.003456009551882744, -0.114829882979393, 0.007719740737229586, 0.03138910233974457, 0.01955319009721279, 0.024373043328523636, -0.0018203228246420622, -0.05800992250442505, 0.02791529707610607, 0.03304535895586014, 0.01472841203212738, -0.0122073320671916, -0.037174951285123825, 0.015835732221603394, 0.0008978548576124012, -0.026142949238419533, 0.005784251261502504, 0.03836464509367943, 0.025279752910137177, -0.10545061528682709, -0.009605473838746548, -0.008552147075533867, -0.041334107518196106, 0.0032926425337791443, -0.035516880452632904, 0.019457001239061356, 0.01310905534774065, -0.037211984395980835, 0.007761707529425621, 0.04506443440914154, -0.08091217279434204, -0.0013841836480423808, -0.015455642715096474, 0.04663066938519478, 0.01755286008119583, 0.03843872249126434, -0.049784041941165924, 0.002808262361213565, -0.003107369877398014, -0.009381605312228203, -0.018086791038513184, -0.01567838340997696, 0.014659203588962555, -0.029376555234193802, 0.007678637281060219, 0.001515052979812026, 0.07300219684839249, 0.01885278895497322, 0.0029539400711655617, -0.013846369460225105, -0.06835290044546127, 0.03469783812761307, -0.02787395380437374, 0.047395866364240646, -0.008199776522815228, -0.05021698400378227, -0.07952064275741577, 0.02590826340019703, 0.014163265004754066, -0.02072295732796192, -0.07487954199314117, -0.005340615287423134, 0.04974900558590889, -0.005525213200598955, -0.0021882057189941406, 0.005240326747298241, -0.0274213757365942, -0.0648692175745964, 0.03085261583328247, 0.0025583612732589245, 0.004477245733141899, -0.057588621973991394, -0.05864332988858223, -0.006814156658947468, -0.015488840639591217, -0.11447125673294067, 0.07444257289171219, -0.04266553744673729, -0.06526492536067963, -0.04274925962090492, -0.08183419704437256, -0.011211114004254341, -0.03635066747665405, -0.009567723609507084, 0.02493799291551113, 0.0031164574902504683, 0.00210933038033545, 0.02159934490919113, 0.0377328023314476, -0.008596252650022507, -0.015177331864833832, -0.034388381987810135, -0.06415001302957535, -0.047557346522808075, 0.028835110366344452, -0.0048599219880998135, 0.048732828348875046, -0.042027756571769714, -0.007813256233930588, -0.01838359236717224, -0.014334574341773987, 0.0152094392105937, -0.05045107379555702, 0.06505324691534042, 0.019763333722949028, 0.003597857430577278, 0.03184753656387329, -0.01003978680819273, 0.02717847190797329, -0.03888854756951332, -0.011465298011898994, -0.028672516345977783, -0.023827070370316505, -0.0571097731590271, 0.06053566932678223, 0.0689677894115448, 0.033328600227832794, 0.026455555111169815, -0.0543048195540905, 0.02740372344851494, -0.009069224819540977, 0.00344952754676342, 0.04072904959321022, -0.02429182454943657, 0.0017535561928525567, 0.012903192080557346, 0.019185911864042282, -0.05672126263380051, 0.019172249361872673, -0.0035065177362412214, 0.07864487171173096, 0.03434498980641365, -0.015682538971304893, -0.09926047921180725, -0.008953738026320934, 0.008743315003812313, -0.0032511733006685972, 0.019529715180397034, -0.05077638849616051, -0.04789451137185097, 0.004354587756097317, 0.0024897383991628885, -0.05256826430559158, 0.013018281199038029, 0.008527714759111404, -0.018415184691548347, -0.027125410735607147, 0.049557387828826904, -0.07315070927143097, -0.0356757752597332, -0.06352120637893677, 0.012282389216125011, -0.030486062169075012, 0.0036627103108912706, -0.004954575560986996, 0.006876083556562662, 0.025285091251134872, -0.045261625200510025, -0.000718915369361639, -0.009559650905430317, -0.0411764495074749, -0.004325311630964279, -0.004822469316422939, -0.006457531359046698, -0.010130037553608418, 0.009101197123527527, 0.013640659861266613, 0.023436622694134712, -0.002800214570015669, -0.06077107414603233, -0.04296453669667244, -0.02542673610150814, -0.045192647725343704, -0.0008537966059520841, 0.006926984526216984, 0.02269810065627098, 0.008457867428660393, 0.0011352630099281669, 0.005284035112708807, 0.04743102937936783, -0.01616460084915161, -0.015395903959870338, -0.02362365461885929, 0.005228731315582991, -0.03889936953783035, 0.052688732743263245, -0.026065055280923843, 0.0010061305947601795, 0.021684642881155014, 0.034476518630981445, 0.10632592439651489, 0.05374005436897278, 0.09348733723163605, -0.0208651814609766, -0.040133800357580185, 0.053177814930677414, -0.023566564545035362, -0.013653891161084175, -0.01143215224146843, 0.005873428657650948, -0.01782478578388691, 0.020210089161992073, -0.0033564388286322355, -0.011297819204628468, 0.008980666287243366, -0.009638745337724686, -0.01490658987313509, 0.03394710272550583, 0.016390809789299965, -0.08452954143285751, -0.0878087729215622, 0.02327917516231537, 0.028877876698970795, 0.08045139908790588, 0.011144045740365982, 0.035125527530908585, -0.061618782579898834, 0.011106654070317745, -0.011569016613066196, 0.07369336485862732, 0.0015912496019154787, -0.004129564855247736, -0.021640688180923462, 0.02195638045668602, 0.029807208105921745, 0.023380141705274582, -0.015817610546946526, -0.013755980879068375, 0.06193849444389343, -0.013001777231693268, -0.0582926906645298, -0.08720731735229492, -0.005379974842071533, -0.015058617107570171, 0.012097781524062157, -0.054483529180288315, -0.06447835266590118, -0.007300031371414661, 0.02675403468310833, -0.02992972917854786, -0.03276198357343674, 0.04311821609735489, -0.038994595408439636, 0.025667831301689148, 0.02420598268508911, 0.022590510547161102, 0.013326235115528107, -0.008493135683238506, -0.01923881284892559, -0.09180551767349243, 0.007187308743596077, 0.04248414933681488, 0.05109013617038727, 0.008538010530173779, -0.011265859007835388, 0.00783316045999527, 0.05987048149108887, 0.039682917296886444, -0.05507812649011612, -0.0020867816638201475, 0.004780150018632412, -0.0030382119584828615, 0.01621829718351364, -0.047682810574769974, 0.0194690078496933, 0.05063004419207573, 0.060149457305669785, 0.007928457111120224, -0.01720326393842697, 0.018760431557893753, 0.020074237138032913, -0.0062751867808401585, 0.07397191971540451, 0.00176011782605201, -0.03392744064331055, 0.018470823764801025, 0.0485227108001709, 0.009042403660714626, 0.06808554381132126, -0.032809846103191376, 0.00617370568215847, 0.026452364400029182, 0.015576917678117752, 0.005219298414885998, 0.034875914454460144, 0.010061748325824738, 0.05978154018521309, 0.0344201885163784, -0.03425398841500282, -0.041527073830366135, -0.04437827691435814, 0.02611175738275051, 0.020633356645703316, -0.021302126348018646, -0.001859067939221859, 0.05428045243024826, 0.024861713871359825, 0.07526686787605286, 0.04702083393931389, 0.018585918471217155, -0.01077759824693203, 0.016713693737983704, 0.031979307532310486, -0.026994500309228897, -0.020488835871219635, -0.06892795860767365, -0.03855690360069275, 0.01413070596754551, 0.02486465498805046, -0.04350943863391876, -0.04108112305402756, -0.019618509337306023, 0.02465846948325634, 0.007708500139415264, -0.003621144453063607, 0.0697280541062355, 0.012400728650391102, 0.0026374340523034334, 0.10547280311584473, -0.030803820118308067, -0.03961534798145294, -0.060007836669683456, 0.03248552232980728, 0.010588995181024075, -0.03506302088499069, 0.032235581427812576, 0.013385561294853687, -0.009552761912345886, -0.021046997979283333, -0.02887953817844391, 0.044056329876184464, 0.03887869790196419, -0.06284178048372269, -0.01031119842082262, 0.062173161655664444, 0.023839080706238747, 0.049025002866983414, 0.01167117990553379, -0.0011887565487995744, 0.022750724107027054, 0.02877550944685936, -0.051441971212625504, -0.04793797805905342, -0.014196709729731083, 0.03750655800104141, -0.019842596724629402, 0.048033732920885086, -0.007995875552296638, -0.05713239684700966, -0.014683589339256287, 0.05417053401470184, 0.011839265003800392, -0.014124012552201748, -0.027193915098905563, 0.03654240444302559, 0.026618974283337593, -0.04834209010004997, -0.061495598405599594, -0.03070462867617607, -0.04537692666053772, -0.011858182027935982, -0.033563170582056046, 0.017427686601877213, -0.011885743588209152, 0.020269786939024925, 0.017875200137495995, -0.03530488163232803, -0.005843013059347868, 0.054832782596349716, -0.03533637896180153, 0.016138605773448944, 0.06627175211906433, 0.054352570325136185, 0.033231064677238464, 0.030798576772212982, 0.01832210272550583, -0.02683422900736332, 0.07792879641056061, 0.01349148154258728, 0.04004570096731186, 0.035888273268938065, 0.013266343623399734, 0.000895656761713326, 0.03670741990208626, 0.06062737852334976, 0.030849739909172058, 0.015314912423491478, 0.03401932492852211, 0.027248097583651543, 0.08875823020935059, -0.07600774616003036, 0.00176229327917099, -0.020406998693943024, -0.03930588811635971, 0.016905058175325394, -0.012131353840231895, 0.040609925985336304, -0.003745648078620434, 0.002169389510527253, -0.008668528869748116, 0.0007137570646591485, 0.013604775071144104, 0.00398354372009635, -0.020649012178182602, 0.0153340520337224, 0.04623182490468025, -0.0032267544884234667, 0.02910708263516426, 0.0113524179905653, 0.011610213667154312, -0.08779865503311157, 0.02614382468163967, 0.05055803433060646, 0.03574204072356224, 0.006435260642319918, -0.020469719544053078, 0.0768837034702301, -0.041207972913980484, -0.007731418125331402, 0.007507405709475279, 0.04302660748362541, -0.005807979963719845, -0.017677711322903633, 0.033183448016643524, 0.04216355085372925, -0.012792570516467094, 0.044703174382448196, -0.010102863423526287, -0.031945303082466125, -0.02071741595864296, 0.008900200948119164, 0.011214769445359707, 0.022529326379299164, -0.016506431624293327, -0.04972223564982414, -0.009639552794396877, -0.05185876414179802, -0.03781528398394585, 0.0081630302593112, 0.03779128938913345, -0.033738475292921066, -0.017748568207025528, 0.004749298561364412, 0.015757471323013306, -0.0013171297032386065, 0.0002503706200513989, 0.03408542647957802, -0.026245741173624992, 0.05650249496102333, -6.527737596571065e-33, 0.039144840091466904, 0.015491452999413013, -0.003970370162278414, 0.0071729496121406555, -0.04750900715589523, 0.0379057377576828, -0.002387839136645198, -0.0018469352507963777, -0.021225517615675926, -0.06808952242136002, -0.005945499986410141, -0.04054897278547287, -0.008369499817490578, 0.02441498078405857, -0.0008197100250981748, 0.028940565884113312, -0.02240794338285923, 0.013635624200105667, 0.031067198142409325, -0.06664171069860458, 0.012139062397181988, -0.006629146169871092, -0.00849275104701519, 0.01817815750837326, -0.013442599214613438, -0.03582456707954407, -0.03555309772491455, -0.018389835953712463, -0.00014346650277730078, 0.026430297642946243, -0.01536601036787033, 0.014110775664448738, -0.0011228884104639292, 0.06261264532804489, 0.038593027740716934, -0.030574364587664604, 0.0007887862739153206, -0.0027595970313996077, 0.011911606416106224, -0.00933191366493702, -0.03577417880296707, 0.006833204068243504, 0.009393149055540562, -0.011736089363694191, 0.03164660930633545, 0.0072654057294130325, 0.007676905021071434, -0.0028434176929295063, -0.004069085232913494, 0.005888625979423523, -0.012368112802505493, 0.003846077248454094, -0.012746445834636688, -0.02608499862253666, 0.03225558251142502, 0.05891594663262367, 0.006142248399555683, 0.006205730605870485, 0.01891150139272213, -0.012347329407930374, -0.01923321932554245, -0.04981048405170441, -0.03788352012634277, 0.0818256288766861, -0.007250815164297819, -0.006394865922629833, 0.032178353518247604, 0.026165539398789406, -0.03291875496506691, 0.005413690581917763, 0.01573672704398632, 0.02257879637181759, 0.04427781328558922, -0.03338386118412018, 0.02801702544093132, 0.0034427964128553867, -0.04218257963657379, -0.003354392945766449, 0.03623456135392189, 0.06434252113103867, -0.006781762931495905, 0.04798941686749458, -0.024855736643075943, 0.03160422295331955, -0.05256946384906769, -0.00687370216473937, 0.007728145457804203, 0.009210732765495777, 0.02964194491505623, 0.027703728526830673, -0.030507568269968033, 0.028089508414268494, -0.026706866919994354, 0.007133028469979763, 0.11196641623973846, -0.009149297140538692, 0.058502621948719025, -0.03706634044647217, 0.008128825575113297, -0.06812576204538345, 0.026263052597641945, 0.007942457683384418, 0.03858455643057823, -0.004487099125981331, 0.008961336687207222, -0.04106738418340683, 0.021961985155940056, -0.006987726781517267, -0.008293164893984795, -0.00814385898411274, -0.030776290223002434, -0.005043930374085903, -0.03155359625816345, 0.01730298437178135, 0.01972968503832817, -0.08567128330469131, -0.005075717810541391, -0.11906961351633072, 0.01834660768508911, -0.0804196372628212, 0.013622886501252651, -0.0009802252752706409, 0.04976607859134674, -0.015306690707802773, -0.05093590170145035, -0.014336938038468361, 0.08447595685720444, -0.028644274920225143, 0.04368416219949722, -0.03101215325295925, 0.022008351981639862, -0.04211018979549408, 3.010155751326238e-07, -0.02807564288377762, 0.03604521229863167, -0.02339368686079979, -0.0055206394754350185, 0.021260784938931465, 0.018708329647779465, 0.01051167119294405, 0.04074294865131378, -0.01718548685312271, -0.027278218418359756, -0.02444647066295147, 0.027213847264647484, 0.010346662253141403, -0.003901542630046606, 0.024816734716296196, -0.10507343709468842, 0.0718589648604393, 0.01896190084517002, -0.011311941780149937, 0.0015255094040185213, 0.03559236228466034, 0.009386519901454449, 0.051505837589502335, 0.013866868801414967, 0.03124566748738289, 0.04101061820983887, -0.023986883461475372, -0.02555331401526928, -0.002939125755801797, -0.021790314465761185, 0.027961568906903267, 0.08422362804412842, 0.0005677369772456586, 0.015239066444337368, 0.0340743213891983, -0.0371253602206707, 0.06789359450340271, 0.038067322224378586, -0.007132995408028364, 0.07233123481273651, -0.035074640065431595, 0.08296885341405869, -0.00706084817647934, -0.04066157341003418, 0.00788278877735138, 0.08465669304132462, -0.024022793397307396, 0.025305137038230896, -0.06112443283200264, 0.010159317404031754, 0.026840193197131157, -0.005014872644096613, -0.01908024586737156, -0.02395527809858322, -0.0017070724861696362, 0.011939694173634052, 0.0015977841103449464, -0.0532383918762207, -0.02736704982817173, -0.009078312665224075, -0.03539501503109932, -0.046337053179740906, 0.019177770242094994, 0.0434250570833683, -0.004398685414344072, -0.02729938179254532, -0.03718241676688194, 2.7775216482039903e-34, -0.0232252087444067, -0.023964855819940567, -0.02249142900109291, -0.025222131982445717, 0.022487491369247437, 0.031200939789414406, 0.010058500804007053, 0.031669970601797104, -0.004969384055584669, -0.03914913162589073, -0.02310560643672943], "parsedContent": {"infobox": {}, "categories": ["Warm clothing"], "links": ["6 January", "2014", "nose peg", "facemask", "earmuffs", "spiny helmet", "enchanted gem", "black mask", "Reinforced goggles", "A Porcine of Interest", "Crafting", "boosts", "Slayer reward points", "Defence", "nose peg", "Slayer", "rune full helm", "Nex", "warm clothing", "Wintertodt", "Slayer task", "Attack", "Strength", "black mask", "Salve amulet", "Salve amulet (e)", "Salve amulet(ei)", "berserker necklace", "obsidian weapons", "Slayer helmet (i)", "Soul Wars", "Nightmare Zone", "Emir's Arena", "scroll of imbuing", "Ranged", "Magic", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON> Leviathan", "<PERSON> Whisperer", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "undead", "Salve amulet", "Salve amulet (e)", "Salve amulet(ei)", "<PERSON><PERSON>", "Magic", "Ranged", "amulet of torture", "helm of <PERSON><PERSON><PERSON><PERSON>", "Amulet of fury", "amulet of glory", "necklace of anguish", "occult necklace", "Void Knight equipment", "ankou", "Vorkath", "aberrant spectre", "nose peg", "Malevolent masquerade", "Slayer master", "A Porcine of Interest", "black mask", "melee", "Slayer task", "black mask (i)", "Ranged", "Magic", "Slayer helmet (i)", "earmuffs", "banshee", "facemask", "Smoke Dungeon", "Smoke Devil Dungeon", "Dust devil", "Smoke devil", "Nex", "Poison gas cloud", "Volcanic sulphur (rock)", "Lovakengj", "sulphur mine", "nose peg", "aberrant spectre", "spiny helmet", "wall beast", "Lumbridge Swamp Caves", "enchanted gem", "eternal gem", "reinforced goggles", "Sour<PERSON>", "reinforced goggles", "A Porcine of Interest", "Nightmare Zone", "Combat_Achievements/Hard", "Combat Achievements", "Soul Wars", "Scroll of imbuing", "Slayer helmet (i)", "Magic", "Ranged", "hard Kourend & Kebos Diary", "Captain <PERSON><PERSON><PERSON>", "lizardman shaman", "<PERSON><PERSON><PERSON> helm (5)", "Slayer reward point", "Combat Achievements", "G<PERSON><PERSON>", "Eye see you", "King black bonnet", "Kal<PERSON><PERSON> khat", "Unholy helmet", "Dark Mantle", "Undead Head", "Use more head", "Twisted Vision", "Warriors' Guild", "warm clothing", "Wintertodt", "death", "Items Kept on Death", "graphical updates", "reinforced goggles", "A Porcine of Interest"], "plainText": "140px left A Slayer helmet is a piece of headgear made by combining a nose peg, facemask, earmuffs, spiny helmet, enchanted gem, and a black mask together. Reinforced goggles are additionally required only if the quest A Porcine of Interest is completed. The helmet can be created by players with level 55 Crafting (boosts work) and who have unlocked the \"Malevolent masquerade\" ability for 400 Slayer reward points. It requires 10 Defence to equip. Despite the helmet requiring a nose peg, which requires 60 Slayer to equip, the Slayer helmet requires no particular Slayer level to wear. The helmet has the same defensive bonuses as a rune full helm. Additionally, while worn, the helmet provides the special effects of all of the Slayer equipment from which it was made. It will prevent infection from <PERSON><PERSON>'s virus attack, and can be used as warm clothing for <PERSON><PERSON><PERSON>. When worn on a Slayer assignment, the helmet provides an Attack and Strength boost of 16.67% (from the black mask used to make it) against monsters assigned as the player's Slayer task. This does not stack with the boosts given by the Salve amulet or its enchanted/imbued variants against undead Slayer assignments. It does however stack with the 20% damage boost of a berserker necklace when using obsidian weapons. The helmet can be imbued through Soul Wars, the Nightmare Zone, or Emir's Arena (with a scroll of imbuing) to provide a 15% boost to Ranged and Magic damage and accuracy as well, as detailed below. A lone exception to the boost application are the Awakened variants of <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> Leviathan, and The Whisperer if assigned them as a slayer task. When killed by another player, and the helmet is not one of the items protected on death, the Slayer helmet is converted to a black mask and dropped for that player. It does not drop coins and leave the owner with a broken Slayer helmet, like other untradeable gear would. Players can own more than one Slayer helmet at a time. Combat stats Slayer helmet and Konar/Krystilia tasks Konar/Krystilia assigns monsters as well as specific areas they must be killed in. The Slayer helmet's accuracy and damage bonuses do not apply to monsters outside of the designated area, even if the monster type matches the task. Slayer helmet vs. Salve amulet For players with undead Slayer assignments, the boosts given by the Salve amulet and its variants do not stack with the effects of a Slayer helmet. If a player owns the enchanted or enchanted-imbued Salve amulet, it may be worth it to switch to another helmet and wear the amulet instead, since the amulet gives a 20% accuracy and damage bonus, as opposed to the 16.67% Melee, 15% Magic, and 15% Ranged accuracy and damage bonus the helmet grants. However, this 3.33 - 5% margin from the Salve amulet may be less damage overall than if the player were to wear a different amulet with the Slayer helmet, as most helmets provide less offensive bonuses than comparable amulets. For example, the amulet of torture provides +10 Strength bonus, whereas a helm of Neitiznot only provides +3. Overall, the torture and Slayer helmet combination provides more damage output than a Salve amulet and helm of Neitiznot. This is also the case for the amulets of fury and glory, as well as the necklace of anguish and occult necklace for Ranged and Magic respectively. Notably, the bonuses of the Salve amulet and its variants do stack with Void Knight equipment. This means that above certain levels, the Void set and Salve amulet have a greater damage output than a Slayer helmet and a torture/anguish/occult neckpiece at places such as ankou or Vorkath. This is not practical for aberrant spectres though, which will drain stats without a nose peg or Slayer helmet. Creation  Unlocked Malevolent masquerade from a Slayer master |members = Yes |ticks = 0 |mat1 = Black mask |mat2 = Earmuffs |mat3 = Facemask |mat4 = Nose peg |mat5 = Spiny helmet |mat6 = Enchanted gem |mat6cost = 1 |mat7=Reinforced goggles |mat7cost=100 |mat7itemnote = Only required after completion of A Porcine of Interest |output1 = Slayer helmet |output1cost = No }} The items used to assemble the Slayer helmet give the following perks: *A black mask, which provides a 16.67% boost to melee damage and accuracy against the player's current Slayer task. Note that if a black mask with charges is used to make a Slayer helmet, the mask will lose all of its charges. **A black mask (i) can also be used, which provides an additional 15% boost to Ranged and Magic accuracy and damage. This will create a Slayer helmet (i) instead. *A pair of earmuffs, which deafens the screams of banshees. *A facemask, which allows the player to breathe in the Smoke Dungeon and Smoke Devil Dungeon, prevents Dust devils and Smoke devils from decreasing the player's stats, prevents infection from Nex's virus attack, and reduces the damage poison gas clouds inflict on players while mining volcanic sulphur deposits in the Lovakengj sulphur mine. *A nose peg, which blocks the stench of aberrant spectres. *A spiny helmet, which protects the player from wall beasts when traversing the Lumbridge Swamp Caves. *An enchanted gem, which allows the player to contact their Slayer master to ask for tips or check their kills on their assignment. Players can right click the helmet to get the same options as right-clicking an enchanted gem, including checking their task progress and their Slayer monster kill log. An eternal gem cannot be used. *A pair of reinforced goggles (after completion of A Porcine of Interest), which allows players to fight Sourhogs. The helmet is assembled by using any component on another while all components are present in the player's inventory. The helmet can be disassembled with a right-click option, turning it back into its component items. Upgrading Players can attach a pair of reinforced goggles to a Slayer helmet to gain its benefits. If players already own a Slayer helmet upon completion of the quest A Porcine of Interest, the goggles' benefits are automatically added to the helmet. At the cost of 1,250,000 Nightmare Zone reward points or 625,000 with the hard tier of Combat Achievements, 500 Soul Wars Zeal Tokens, or a Scroll of imbuing, the Slayer helmet can be upgraded to a Slayer helmet (i), adding a 15% damage and accuracy boost to both Magic and Ranged. The Slayer helmet (i) also has a +10 magic defence bonus, a +3 magic attack bonus and a +3 ranged attack bonus. Upon completion of the hard Kourend & Kebos Diary, players can speak to Captain Cleive to teach them how to use a Slayer helmet in place of a Shayzien helm when fighting lizardman shamans. The player must have obtained the Shayzien helm (5) before being able to do it. Cosmetics With 1,000 Slayer reward points, the player can learn how to recolour the helmet (or its imbued variant) by using certain special items on it. Each recolour ability is purchased separately, meaning 8,000 Slayer reward points are required to fully access all the recolours, provided the player has the items readily available. Players can disassemble the recoloured helmet to retrieve the items back. All recolours have the same stats and bonuses as the original Slayer helmet and are thus purely cosmetic. With the elite tier or above of Combat Achievements claimed, players may recolour their helm with the visage of certain bosses by bringing a Slayer helmet to Ghommal. The list of recolours offered by Ghommal depends on the tier of rewards claimed. Players may freely change this recolour at any time by speaking with Ghommal, or remove the recolour by disassembling the helm, upon which the un-coloured version will be returned. The variants that can be created are as follows: {| class=\"wikitable lighttable align-center-1\" ! colspan=\"2\" rowspan=\"2\" |Upgraded item ! colspan=\"2\" |Requirements |- !Unlock !Item |- | |Eye see you | |- | |King black bonnet | |- | |Kalphite khat | |- | |Unholy helmet | |- | |Dark Mantle | |- | |Undead Head | |- | |Use more head | |- | |Twisted Vision | |- ! colspan=\"2\" |Upgraded item ! colspan=\"2\" |Tier claimed |- | | colspan=\"2\"| |- | | colspan=\"2\"| |- | | colspan=\"2\"| |} Products Treasure Trails  |item1=Dragon defender|cost=0  |item2=Dragon battleaxe  |item3=Slayer helmet|cost3=}} Used in recommended equipment Changes  now correctly refers to the enchanted gem, rather than the slayer gem. }}  now connects to a female character's neck line. }}  chathead model has had a green tint removed from its jaw. }} Gallery (historical) {|style=\"text-align: center\" cellpadding=\"15\" |300x150px |300x150px |300x150px |300x150px |- |center |center |center |center |- |6 January 2014 –<br/>20 February 2014 |20 February 2014 –<br/>26 May 2016 |26 May 2016 –<br/>13 September 2018 |13 September 2018 –<br/>present |} Trivia *The Slayer helmet was the first item to conform (either by remodelling or conversion [unclear from source]) to the Old School model format (from a later undefined format) and to be introduced to the game. *Despite the fact that reinforced goggles are added as a component of the Slayer helmet after completion of A Porcine of Interest, neither the player model nor the chathead model reflect this, as they both display open eye sockets in the helmet. *The Slayer helmet's examine text is identical to the Spiny helmet. References Category:Warm clothing"}, "originalContent": "{{External|rs}}\n{{Otheruses|the regular slayer helmet|the imbued version|Slayer helmet (i)}}\n{{Infobox Item\n|name = Slayer helmet\n|image = [[File:Slayer helmet.png]]\n|release = [[6 January]] [[2014]]\n|update = Second Slayer Batch: The Helm\n|aka = Slayer helm\n|members = Yes\n|quest = No\n|tradeable = No\n|placeholder = Yes\n|equipable = Yes\n|stackable = No\n|noteable = No\n|options = Wear, Check, Disassemble, Drop\n|wornoptions = Check, Partner, Log\n|examine = You don't want to wear it inside-out.\n|value = 40000\n|weight = 2.267\n|id = 11864\n|leagueRegion = Morytania\n}}\n[[File:Slayer helmet detail.png|left|140px]]\n[[File:Slayer helmet chathead.png|left]]\nA '''Slayer helmet''' is a piece of headgear made by combining a [[nose peg]], [[facemask]], [[earmuffs]], [[spiny helmet]], [[enchanted gem]], and a [[black mask]] together. [[Reinforced goggles]] are additionally required only if the quest [[A Porcine of Interest]] is completed. The helmet can be created by players with level 55 [[Crafting]] ([[boosts]] work) and who have unlocked the \"Malevolent masquerade\" ability for 400 [[Slayer reward points]]. It requires 10 [[Defence]] to equip. Despite the helmet requiring a [[nose peg]], which requires 60 [[Slayer]] to equip, the Slayer helmet requires no particular Slayer level to wear.\n\nThe helmet has the same defensive bonuses as a [[rune full helm]]. Additionally, while worn, the helmet provides the special effects of all of the Slayer equipment from which it was made. It will prevent infection from [[Nex]]'s virus attack, and can be used as [[warm clothing]] for [[Wintertodt]].\n\nWhen worn on a [[Slayer task|Slayer assignment]], the helmet provides an [[Attack]] and [[Strength]] boost of 16.67% (from the [[black mask]] used to make it) against monsters assigned as the player's Slayer task.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/689105142382198784|date=18 January 2016|archiveurl=https://archive.is/xzKBj|archivedate=26 May 2020|name=scuffedBuff}} This does '''not''' stack with the boosts given by the [[Salve amulet]] or its [[Salve amulet (e)|enchanted]]/[[Salve amulet(ei)|imbued]] variants against undead Slayer assignments. It '''does''' however stack with the 20% damage boost of a [[berserker necklace]] when using [[obsidian weapons]]. The helmet can be [[Slayer helmet (i)|imbued]] through [[Soul Wars]], the [[Nightmare Zone]], or [[Emir's Arena]] (with a [[scroll of imbuing]]) to provide a 15% boost to [[Ranged]] and [[Magic]] damage and accuracy as well, as detailed below.\n\nA lone exception to the boost application are the Awakened variants of [[Vardorvis]], [[Duke Sucellus]], [[The Leviathan]], and [[The Whisperer]] if assigned them as a slayer task.\n\nWhen killed by another player, and the helmet is not one of the items protected on death, the Slayer helmet is converted to a black mask and dropped for that player. It does not drop coins and leave the owner with a broken Slayer helmet, like other untradeable gear would.{{CiteGeneral|format=Reddit|url=https://www.reddit.com/r/2007scape/comments/4okkhf/dev_blog_pvp_poh_rooms/d4dcjst|author=Mod Ronan|accessdate=24 August 2018|date=June 17 2016|title=Dev Blog: PvP & POH Rooms}}\n\nPlayers '''can''' own more than one Slayer helmet at a time.\n\n==Combat stats==\n{{Infobox Bonuses\n|astab = 0\n|aslash = 0\n|acrush = 0\n|amagic = -6\n|arange = -2\n|dstab = +30\n|dslash = +32\n|dcrush = +27\n|dmagic = -1\n|drange = +30\n|str = 0\n|rstr = 0\n|mdmg = 0\n|prayer = 0\n|slot = head\n|image = [[File:Slayer helmet equipped.png|130px]]\n|altimage = [[File:Slayer helmet equipped female.png|130px]]\n}}\n\n==Slayer helmet and Konar/Krystilia tasks==\n[[Konar]]/[[Krystilia]] assigns monsters as well as specific areas they must be killed in.\n\nThe Slayer helmet's accuracy and damage bonuses do not apply to monsters outside of the designated area, even if the monster type matches the task.\n\n==Slayer helmet vs. Salve amulet==\nFor players with [[undead]] Slayer assignments, the boosts given by the [[Salve amulet]] and its variants do '''not''' stack with the effects of a Slayer helmet. If a player owns the [[Salve amulet (e)|enchanted]] or [[Salve amulet(ei)|enchanted-imbued Salve amulet]], it may be worth it to switch to another helmet and wear the amulet instead, since the amulet gives a 20% accuracy and damage bonus, as opposed to the 16.67% [[Melee]], 15% [[Magic]], and 15% [[Ranged]] accuracy and damage bonus the helmet grants.\n\nHowever, this 3.33 - 5% margin from the Salve amulet may be less damage overall than if the player were to wear a different amulet with the Slayer helmet, as most helmets provide less offensive bonuses than comparable amulets. For example, the [[amulet of torture]] provides +10 Strength bonus, whereas a [[helm of neitiznot|helm of Neitiznot]] only provides +3. Overall, the torture and Slayer helmet combination provides more damage output than a Salve amulet and helm of Neitiznot. This is also the case for the [[Amulet of fury|amulets of fury]] and [[amulet of glory|glory]], as well as the [[necklace of anguish]] and [[occult necklace]] for Ranged and Magic respectively.\n\nNotably, the bonuses of the Salve amulet and its variants do stack with [[Void Knight equipment]]. This means that above certain levels, the Void set and Salve amulet have a greater damage output than a Slayer helmet and a torture/anguish/occult neckpiece at places such as [[ankou]] or [[Vorkath]]. This is not practical for [[aberrant spectre]]s though, which will drain stats without a [[nose peg]] or Slayer helmet.\n\n==Creation==\n{{Recipe\n|skill1 = Crafting\n|skill1lvl = 55\n|skill1boostable = yes\n|skill1exp = 0\n|notes = {{SCP|Slayer}} Unlocked [[Malevolent masquerade]] from a [[Slayer master]]\n|members = Yes\n|ticks = 0\n|mat1 = Black mask\n|mat2 = Earmuffs\n|mat3 = Facemask\n|mat4 = Nose peg\n|mat5 = Spiny helmet\n|mat6 = Enchanted gem\n|mat6cost = 1\n|mat7=Reinforced goggles\n|mat7cost=100\n|mat7itemnote = Only required after completion of [[A Porcine of Interest]]\n|output1 = Slayer helmet\n|output1cost = No\n}}\n\nThe items used to assemble the Slayer helmet give the following perks:\n*A [[black mask]], which provides a 16.67%{{NamedRef|scuffedBuff}} boost to [[melee]] damage and accuracy against the player's current [[Slayer task]]. Note that if a black mask with charges is used to make a Slayer helmet, the mask will lose all of its charges.\n**A [[black mask (i)]] can also be used, which provides an additional 15% boost to [[Ranged]] and [[Magic]] accuracy and damage. This will create a [[Slayer helmet (i)]] instead.\n*A pair of [[earmuffs]], which deafens the screams of [[banshee]]s.\n*A [[facemask]], which allows the player to breathe in the [[Smoke Dungeon]] and [[Smoke Devil Dungeon]], prevents [[Dust devil|Dust devils]] and [[Smoke devil|Smoke devils]] from decreasing the player's stats, prevents infection from [[Nex|Nex's]] virus attack, and reduces the damage [[Poison gas cloud|poison gas clouds]] inflict on players while mining [[Volcanic sulphur (rock)|volcanic sulphur deposits]] in the [[Lovakengj]] [[sulphur mine]].\n*A [[nose peg]], which blocks the stench of [[aberrant spectre]]s.\n*A [[spiny helmet]], which protects the player from [[wall beast]]s when traversing the [[Lumbridge Swamp Caves]].\n*An [[enchanted gem]], which allows the player to contact their Slayer master to ask for tips or check their kills on their assignment. Players can right click the helmet to get the same options as right-clicking an enchanted gem, including checking their task progress and their Slayer monster kill log. An [[eternal gem]] cannot be used.\n*A pair of [[reinforced goggles]] (after completion of A Porcine of Interest), which allows players to fight [[Sourhog|Sourhogs]].\n\nThe helmet is assembled by using any component on another while all components are present in the player's inventory. The helmet can be disassembled with a right-click option, turning it back into its component items.\n\n==Upgrading==\nPlayers can attach a pair of [[reinforced goggles]] to a Slayer helmet to gain its benefits. If players already own a Slayer helmet upon completion of the quest [[A Porcine of Interest]], the goggles' benefits are automatically added to the helmet.\n\nAt the cost of 1,250,000 [[Nightmare Zone]] reward points or 625,000 with the [[Combat_Achievements/Hard|hard tier]] of [[Combat Achievements]], 500 [[Soul Wars]] Zeal Tokens, or a [[Scroll of imbuing]], the Slayer helmet can be upgraded to a [[Slayer helmet (i)]], adding a 15% damage and accuracy boost to both [[Magic]] and [[Ranged]]. The Slayer helmet (i) also has a +10 magic defence bonus, a +3 magic attack bonus and a +3 ranged attack bonus.\n\nUpon completion of the [[hard Kourend & Kebos Diary]], players can speak to [[Captain Cleive]] to teach them how to use a Slayer helmet in place of a Shayzien helm when fighting [[lizardman shaman]]s. The player must have obtained the [[Shayzien helm (5)]] before being able to do it.\n\n===Cosmetics===\nWith 1,000 [[Slayer reward point]]s, the player can learn how to recolour the helmet (or its imbued variant) by using certain special items on it. Each recolour ability is purchased separately, meaning 8,000 Slayer reward points are required to fully access all the recolours, provided the player has the items readily available. Players can disassemble the recoloured helmet to retrieve the items back. All recolours have the same stats and bonuses as the original Slayer helmet and are thus purely cosmetic.\n\nWith the elite tier or above of [[Combat Achievements]] claimed, players may recolour their helm with the visage of certain bosses by bringing a Slayer helmet to [[Ghommal]]. The list of recolours offered by Ghommal depends on the tier of rewards claimed. Players may freely change this recolour at any time by speaking with Ghommal, or remove the recolour by disassembling the helm, upon which the un-coloured version will be returned.\n\nThe variants that can be created are as follows:\n{| class=\"wikitable lighttable align-center-1\"\n! colspan=\"2\" rowspan=\"2\" |Upgraded item\n! colspan=\"2\" |Requirements\n|-\n!Unlock\n!Item\n|-\n|{{plinkt|Araxyte slayer helmet}}\n|[[Eye see you]]\n|{{plink|Araxyte head|txt=Araxyte head}}\n|-\n|{{plinkt|Black slayer helmet}}\n|[[King black bonnet]]\n|{{plink|Kbd heads|txt=King Black Dragon heads}}\n|-\n|{{plinkt|Green slayer helmet}}\n|[[Kalphite khat]]\n|{{plink|Kq head|txt=Kalphite Queen head}}\n|-\n|{{plinkt|Red slayer helmet}}\n|[[Unholy helmet]]\n|{{plink|Abyssal head|txt=Abyssal demon head}}\n|-\n|{{plinkt|Purple slayer helmet}}\n|[[Dark Mantle]]\n|{{plink|Dark claw}}\n|-\n|{{plinkt|Turquoise slayer helmet}}\n|[[Undead Head]]\n|{{plink|Vorkath's head}}\n|-\n|{{plinkt|Hydra slayer helmet}}\n|[[Use more head]]\n|{{plink|Alchemical hydra heads}}\n|-\n|{{plinkt|Twisted slayer helmet}}\n|[[Twisted Vision]]\n|{{plink|Twisted horns}}\n|-\n! colspan=\"2\" |Upgraded item\n! colspan=\"2\" |Tier claimed\n|-\n|{{plinkt|Tztok slayer helmet}}\n| colspan=\"2\"|{{plink|Elite Combat Achievements|pic=Ghommal's hilt 4}}\n|-\n|{{plinkt|Vampyric slayer helmet}}\n| colspan=\"2\"|{{plink|Master Combat Achievements|pic=Ghommal's hilt 5}}\n|-\n|{{plinkt|Tzkal slayer helmet}}\n| colspan=\"2\"|{{plink|Grandmaster Combat Achievements|pic=Ghommal's hilt 6}}\n|}\n\n==Products==\n{{Uses material list|Slayer helmet}}\n\n==Treasure Trails==\n{{EmoteClueHeader}}\n{{EmoteClue|tier=master|emote=Raspberry|location={{StashLocation|x=2843|y=3540|pin = redPin|text=[[Warriors' Guild]] bank}}\n |item1=Dragon defender|cost=0\n |item2=Dragon battleaxe\n |item3=Slayer helmet|cost3={{GEP|Black mask}}}}\n{{EmoteClueBottom}}\n\n==Used in recommended equipment==\n{{Used in recommended equipment|Slayer helmet (i)}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 15 January 2025\n|update = Leagues V: Raging Echos Rewards Are Here\n|change = The Slayer helmet can now be used as [[warm clothing]] for [[Wintertodt]].\n}}\n{{Subject changes\n|date = 30 August 2023\n|update = Poll 80 ToA Changes & DMM Tweaks\n|change = All slayer helmet variations now correctly show as lost on [[death]] in the [[Items Kept on Death]] interface.\n}}\n{{Subject changes\n|date = 21 June 2023\n|update = More Poll 79 Changes\n|poll = Old School Content Poll 79: Quality of Life\n|change = Wearing a slayer helmet will now prevent players from becoming infected by a player infected from Nex's choke attack.\n}}\n{{Subject changes\n|date = 13 September 2018\n|update = Left Click Range Cooking, Slayer Helm Recolour and Player Survey\n|poll = Old School Content Poll 58: More QoL and a Quest\n|change = The slayer helmet received a [[graphical updates|graphical update]].\n}}\n{{Subject changes \n|date = 26 May 2016\n|update = Maniacal Monkey Hunting\n|change = The item's inventory sprite was adjusted with the release of recoloured helmets.\n}}\n{{Subject changes\n|date = 11 February 2016\n|update = Tournament Stats & Skilling QoL\n|change = The \"Check\" option was added, allowing players to see their current Slayer task.\n}}\n{{Subject changes\n|date = 8 October 2015\n|update = Smoke Battlestaff Buff\n|change = The message shown when trying to create a slayer helm{{sic}} now correctly refers to the enchanted gem, rather than the slayer gem.\n}}\n{{Subject changes\n|date = 16 July 2015\n|update = Duel Arena Rework\n|change = The slayer helm{{sic}} now connects to a female character's neck line.\n}}\n{{Subject changes\n|date = 20 February 2014\n|update = Happy Birthday!\n|change = The slayer helm's{{sic}} chathead model has had a green tint removed from its jaw.\n}}\n{{Subject changes\n|date = 16 January 2014\n|update = Patch Notes (16 January 2014)\n|change = The item's value was increased from 1 to 40,000.\n}}\n{{Subject changes footer}}\n\n===Gallery (historical)===\n{|style=\"text-align: center\" cellpadding=\"15\"\n|[[File:Slayer helmet (v1) detail.png|center|300x150px]]\n|[[File:Slayer helmet (v2) detail.png|center|300x150px]]\n|[[File:Slayer helmet (v3) detail.png|center|300x150px]]\n|[[File:Slayer helmet detail.png|center|300x150px]]\n|-\n|[[File:Slayer helmet v1.png|center]]\n|[[File:Slayer helmet v2.png|center]]\n|[[File:Slayer helmet v3.png|center]]\n|[[File:Slayer helmet.png|center]]\n|-\n|6 January 2014 –<br/>20 February 2014\n|20 February 2014 –<br/>26 May 2016\n|26 May 2016 –<br/>13 September 2018\n|13 September 2018 –<br/>present\n|}\n\n==Trivia==\n*The Slayer helmet was the first item to conform (either by remodelling or conversion [unclear from source]) to the ''Old School'' model format (from a later undefined format) and to be introduced to the game.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/392403275292483586|date=21 October 2013|archiveurl=https://archive.is/MzqCQ|archivedate=10 January 2014}}\n*Despite the fact that [[reinforced goggles]] are added as a component of the Slayer helmet after completion of [[A Porcine of Interest]], neither the player model nor the chathead model reflect this, as they both display open eye sockets in the helmet.\n*The Slayer helmet's examine text is identical to the Spiny helmet.\n\n==References==\n{{Reflist}}\n\n{{Slayer equipment}}\n{{Nightmare Zone}}\n{{Melee headgear}}\n\n[[Category:Warm clothing]]"}