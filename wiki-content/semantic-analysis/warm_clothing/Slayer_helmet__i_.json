{"title": "Slayer helmet (i)", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [{"type": "category", "value": "Warm clothing", "confidence": 0.9, "source": "category"}], "relationships": [{"from": "Slayer helmet (i)", "to": "6 January", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "2014", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "6 January", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "2021", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "6 July", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "2022", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Defence", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Slayer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Slayer helmet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "rune full helm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Magic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Ranged", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Nex", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "warm clothing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Wintertodt", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "slayer helmet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Nightmare Zone", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Combat_Achievements/Hard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Combat Achievements", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Soul Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Zeal Tokens", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "scroll of imbuing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Emir's Arena", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "black mask", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Slayer task", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Salve amulet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Salve amulet (e)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "obsidian weapons", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "berserker necklace", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "crystal armour", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "crystal body", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Crystal legs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Slayer master", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "<PERSON><PERSON> quo Maten", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Malevolent masquerade", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Slayer master", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "A Porcine of Interest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "herb box", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Malevolent masquerade", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Slayer master", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "A Porcine of Interest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Soul Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Zeal Tokens", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "spoils of war", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Malevolent masquerade", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Slayer master", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "A Porcine of Interest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "scroll of imbuing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Malevolent masquerade", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Slayer master", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "A Porcine of Interest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "herb box", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Soul Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Zeal Tokens", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "spoils of war", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "scroll of imbuing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Slayer reward point", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Combat Achievements", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "G<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "King black bonnet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Kal<PERSON><PERSON> khat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Unholy helmet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Dark Mantle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Undead Head", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Eye see you", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Use more head", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Twisted Vision", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Warriors' Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "warm clothing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Wintertodt", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "graphically updated", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "black mask (i)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Slayer helmet (i)", "to": "Warm clothing", "type": "belongs_to_category", "confidence": 0.9, "source": "category"}], "embedding": [0.004644637927412987, -0.08976710587739944, 0.01057736948132515, 0.01031552255153656, -0.020528893917798996, 0.00846712477505207, 0.021106380969285965, 0.011501256376504898, -0.017337141558527946, -0.017022868618369102, 0.016283884644508362, 0.05143054202198982, 0.02324608899652958, -0.052658066153526306, 0.04194086045026779, -0.018976951017975807, -0.024515895172953606, -0.02276608906686306, -0.03659084439277649, 0.031689662486314774, 0.04754797741770744, -0.026216192170977592, -0.08112805336713791, -0.026835812255740166, -0.02098027803003788, -0.027392936870455742, 0.03514115884900093, -0.007244518958032131, 0.03979579731822014, -0.03989194706082344, 0.08843500167131424, -0.03330572694540024, -0.008042564615607262, 0.010548066347837448, 2.128741243723198e-06, -0.016145983710885048, 0.06786540150642395, 0.039089594036340714, -0.039973847568035126, 0.036456961184740067, -0.08686583489179611, 0.02573375403881073, 0.0026060333475470543, 0.05319976434111595, 0.03498683124780655, -0.019769558683037758, -0.005093181040138006, 0.027457457035779953, -0.022802498191595078, -0.05502428486943245, 0.0041442555375397205, -0.04465474933385849, -0.01756674237549305, -0.019568990916013718, 0.06808724254369736, 0.04966265708208084, -0.0036531214136630297, 0.0021280627697706223, -0.010190551169216633, 0.044167660176754, -0.026612890884280205, 0.010287363082170486, -0.04506394639611244, 0.03900104761123657, 0.12088683247566223, -0.021946387365460396, -0.04080494865775108, -0.019369298592209816, 0.03969879075884819, 0.02257169969379902, 0.025016039609909058, -0.03982316330075264, 0.04189465194940567, 0.00938177015632391, -0.0028034227434545755, -0.03829110786318779, -0.024048352614045143, -0.03436049446463585, 0.017304494976997375, -0.04961490258574486, -0.014589013531804085, 0.06760033965110779, -0.022221090272068977, -0.007113167084753513, -0.024184834212064743, -0.005956884007900953, -0.04294683411717415, -0.007988622412085533, 0.014758018776774406, 0.0008078098180703819, 0.02673933655023575, 0.003634790191426873, -0.022127045318484306, 0.041814591735601425, -0.029789941385388374, 0.004797374829649925, 0.015415599569678307, -0.09625089168548584, 0.04361957311630249, 0.01752045936882496, -0.006164039485156536, 0.020972291007637978, -0.05358459800481796, 0.02346920222043991, -0.03861551359295845, -0.0763164684176445, 0.009584719315171242, -0.08135857433080673, 0.0344652496278286, 0.044746361672878265, 0.018552245572209358, 0.019820084795355797, 0.02872699685394764, -0.018737757578492165, 0.06765599548816681, 0.054308537393808365, 0.01648806408047676, -0.013504154048860073, -0.009684878401458263, 0.017242223024368286, 0.0232697781175375, -0.03219756856560707, 0.05302365496754646, 0.018264560028910637, 0.0012333234772086143, -0.0853603184223175, -0.010883854702115059, -0.00364913628436625, -0.028047483414411545, -0.04368607699871063, -0.025785380974411964, 0.016895942389965057, 0.03694973140954971, -0.025664959102869034, -0.013284614309668541, 0.09165444225072861, -0.062932588160038, -0.019127553328871727, 0.0070191179402172565, 0.0270390622317791, -0.006345327012240887, 0.027520189061760902, -0.061334624886512756, 0.004078616388142109, 0.006677629891782999, 0.042478978633880615, -0.03500158712267876, -0.04322841763496399, 0.011951751075685024, -0.012048356235027313, 0.005424113012850285, -0.026524145156145096, 0.03166217729449272, 0.04116892069578171, 0.01680939644575119, -0.03603722155094147, -0.07639984786510468, 0.0398232601583004, -0.010254142805933952, 0.0434374064207077, -0.009943842887878418, -0.054313402622938156, -0.10970298945903778, 0.008901639841496944, 0.02000073343515396, -0.017273150384426117, -0.11253415048122406, 0.00697993952780962, -0.033160500228405, -0.017108730971813202, -0.006384528242051601, -0.027483783662319183, -0.02697470411658287, -0.047398731112480164, 0.05281978100538254, 0.006171416956931353, -0.010971004143357277, 0.00035035351174883544, -0.06376935541629791, -0.041994430124759674, 0.014324122108519077, -0.09809162467718124, 0.05553455650806427, -0.038238491863012314, -0.08107060939073563, -0.03560100868344307, -0.07703088223934174, -0.021060192957520485, -0.016799066215753555, -0.036067068576812744, -0.0031405675690621138, 0.023644404485821724, -0.04252683371305466, 0.045122645795345306, 0.027593890205025673, -0.03030521236360073, 0.0022875117138028145, 0.010575934313237667, -0.05493558943271637, -0.01794305257499218, 0.05052601173520088, 0.037781793624162674, 0.023044122382998466, -0.051071055233478546, -0.025307204574346542, -0.0613788403570652, 0.0037338188849389553, 0.02043675258755684, -0.034783679991960526, 0.04339328035712242, -0.009829577058553696, 0.031898900866508484, -0.0044646309688687325, 0.0018792800838127732, -0.019373703747987747, -0.05606774613261223, 0.0034811042714864016, -0.00997101329267025, -0.04392130300402641, -0.04688131436705589, 0.061103567481040955, 0.026010092347860336, -0.014733969233930111, 0.009773810394108295, -0.005978539120405912, 0.03019118309020996, -0.021060770377516747, -0.0028008173685520887, 0.01273778174072504, -0.009383090771734715, -0.015889456495642662, 0.009473907761275768, 0.00855344533920288, -0.02765592187643051, -0.021174801513552666, -0.013136995024979115, 0.04486784338951111, 0.018082860857248306, 0.019805526360869408, -0.05679494887590408, 0.009595081210136414, 0.0016209782334044576, -0.020810306072235107, 0.005333344917744398, -0.018751973286271095, -0.04358048737049103, -0.01203331258147955, -0.008096989244222641, -0.03621012717485428, -0.015136681497097015, -0.011446530930697918, -0.0030423502903431654, -0.0008818773785606027, 0.03386954590678215, -0.07371006906032562, -0.010401283390820026, -0.026702923700213432, -0.00818895734846592, -0.005422994028776884, 0.01767086610198021, -0.014119831845164299, -0.002894781529903412, 0.03250797465443611, -0.021758658811450005, -0.051503073424100876, 0.007142179645597935, -0.04011419415473938, 0.023505955934524536, 0.006101321894675493, -0.0030472984071820974, 0.011519111692905426, 0.0049325330182909966, 0.004327712114900351, 0.01885298639535904, -0.0089266961440444, -0.030886579304933548, -0.014143500477075577, -0.03151661530137062, -0.04716223105788231, 0.0011349000269547105, -0.00781199149787426, 0.04182741045951843, -0.007740160450339317, 0.004002264700829983, 0.00770902493968606, 0.05414455756545067, -0.054021112620830536, -0.07528917491436005, -0.03968736156821251, 0.015304319560527802, -0.045826178044080734, 0.04009474813938141, -0.01419029664248228, 0.01050136610865593, 0.007768540643155575, 0.04277762025594711, 0.06878525018692017, 0.053777821362018585, 0.09922129660844803, -0.015938561409711838, -0.020921941846609116, 0.06042204052209854, -0.041121963411569595, -0.016337044537067413, 0.007642229087650776, -0.010225718840956688, -0.002769567770883441, 0.04280703142285347, 0.007531898561865091, 0.03393903747200966, 0.03147841989994049, -0.016625331714749336, -0.012806905433535576, -0.029950415715575218, 0.015958087518811226, -0.0652935579419136, -0.0849514976143837, 0.02134314738214016, 0.011517718434333801, 0.06861472874879837, 0.03641907870769501, 0.043996889144182205, -0.059965845197439194, -0.02065194956958294, 0.007029454689472914, 0.06759952008724213, -0.0008896847139112651, -0.0019004689529538155, -0.04759877920150757, 0.03815199062228203, 0.04029940813779831, 0.031850941479206085, -0.01344379410147667, 0.004771535750478506, 0.07578213512897491, -0.002060468541458249, -0.05464417487382889, -0.07573670148849487, 0.0025463663041591644, -0.014230309054255486, 0.006786627694964409, -0.04060760512948036, -0.023271458223462105, 0.018893593922257423, 0.0009028224740177393, -0.0022260555997490883, -0.06137745827436447, 0.03902989625930786, -0.01709381304681301, -0.023001577705144882, 0.03227926045656204, 0.016739496961236, -0.020313287153840065, 0.012996183708310127, -0.03484708443284035, -0.10609375685453415, -0.02144050784409046, -0.007360497489571571, 0.049364130944013596, 0.014166798442602158, -0.03374549001455307, 0.04482771083712578, 0.03999049961566925, 0.029235050082206726, -0.05702495947480202, -0.03790877014398575, 0.009707567282021046, -0.026752227917313576, 0.005816403776407242, -0.04015324264764786, 0.06583590805530548, 0.035476673394441605, 0.06279833614826202, 0.004454507026821375, -0.032874949276447296, 0.009303019382059574, 0.030726468190550804, -0.029288409277796745, 0.09046562016010284, 0.031875718384981155, -0.03893036022782326, 0.0032188796903938055, 0.020283248275518417, 0.0009240361396223307, 0.0558246485888958, -0.007972335442900658, -0.025539804250001907, 0.04363487660884857, 0.005099072121083736, -0.0384589321911335, 0.026125729084014893, 0.00867680087685585, 0.04925471171736717, 0.052890192717313766, -0.010864755138754845, -0.03167467936873436, -0.01615123078227043, 0.049806833267211914, 0.005263920407742262, 0.0058383881114423275, 0.033905211836099625, 0.013428541831672192, -0.01083562895655632, 0.03626600652933121, 0.023228844627738, 0.022332031279802322, -0.042748741805553436, -0.007374006789177656, 0.02939818799495697, 0.024876246228814125, -0.0496659129858017, -0.10572431981563568, 0.007386698853224516, 0.010864836163818836, 0.05990823730826378, -0.0005972662474960089, -0.03332345187664032, -0.013243359513580799, 0.012239830568432808, 0.01669580489397049, 0.007901247590780258, 0.06711680442094803, -0.018224740400910378, -0.017038187012076378, 0.10690122097730637, -0.014434467069804668, -0.013295459561049938, -0.07798878103494644, 0.013733014464378357, 0.02842182293534279, -0.035921577364206314, 0.05158475413918495, -0.030633198097348213, -0.001031136023811996, -0.00948090199381113, -0.04279036819934845, 0.02762375771999359, 0.04060465469956398, -0.08545558154582977, -0.013849923387169838, 0.05061452463269234, 0.00941680558025837, 0.009238975122570992, 0.028773020952939987, -0.016492510214447975, 0.022568508982658386, 0.009228477254509926, -0.06649436801671982, -0.05125592276453972, 0.007827391847968102, 0.01937081106007099, -0.024711517617106438, 0.06436973810195923, 0.011958402581512928, -0.022446434944868088, -0.011912459507584572, -0.0005665597855113447, -0.020627489313483238, -0.030649377033114433, -0.05211173743009567, 0.031185073778033257, 0.01273028738796711, -0.042453669011592865, -0.0671587586402893, -0.06066080182790756, -0.027927996590733528, -0.01024237647652626, -0.04717806354165077, -0.0076741743832826614, -0.03761676698923111, -0.030373338609933853, 0.020481053739786148, -0.04150651767849922, -0.02896006777882576, 0.01907302439212799, -0.01670389622449875, -0.004573374055325985, 0.06941228359937668, 0.07234558463096619, 0.0442790761590004, 0.01534375548362732, 0.03432164341211319, -0.012975160032510757, 0.07921276241540909, 0.02126666158437729, 0.0290596392005682, 0.006965902633965015, 0.026049897074699402, 0.03708275780081749, 0.02643187716603279, 0.06679107993841171, 0.007693642284721136, 0.007501599378883839, 0.056612201035022736, 0.0414237305521965, 0.07233895361423492, -0.02737138420343399, 0.01067870669066906, -0.03553479537367821, -0.016618981957435608, -0.02519444189965725, -0.01414338406175375, 0.0210412610322237, -0.04478907212615013, 0.024721939116716385, -0.013560238294303417, -0.01357614528387785, 0.021806422621011734, 0.002188302343711257, 0.005578421987593174, 0.03635942563414574, 0.05012250319123268, 0.0074428473599255085, 0.007796523626893759, 0.04813111573457718, 0.03619425743818283, -0.06824882328510284, 0.043007366359233856, 0.026852959766983986, 0.027421724051237106, -0.007713841740041971, -0.007763759698718786, 0.04469892755150795, -0.02109180949628353, 0.012772154062986374, 0.02402617037296295, 0.022860992699861526, -0.006822388153523207, -0.010565532371401787, 0.028328752145171165, 0.06749394536018372, 0.005636726971715689, 0.023810992017388344, -0.014195707626640797, 0.0015484929317608476, -0.01034947857260704, 0.01161988452076912, 0.033639077097177505, 0.019108036532998085, -0.03229053318500519, -0.03901785612106323, -0.012569503858685493, -0.04966474324464798, -0.04836156964302063, 0.01774374023079872, -0.020261412486433983, -0.013959594070911407, -0.009251727722585201, -0.02009323053061962, -0.029423238709568977, 0.011850486509501934, -0.0035455720499157906, 0.04743471369147301, -0.0018985054921358824, 0.02302347496151924, -6.056253791953953e-33, 0.0282619446516037, 0.04728338494896889, -0.02068544365465641, 0.0004079308419022709, -0.02366546541452408, -0.004809191450476646, -0.006225746124982834, 0.03168054670095444, -0.029887912794947624, -0.04654448851943016, -0.008852432481944561, -0.029421411454677582, -0.016009023413062096, 0.009829976595938206, 0.0026173987425863743, 0.00020619708811864257, -0.03106149286031723, 0.00667860871180892, 0.02878870628774166, -0.04445207864046097, 0.01980854570865631, -0.009245406836271286, 0.04422285407781601, -0.026254216209053993, -0.024335164576768875, 2.4081226001726463e-05, -0.008301187306642532, -0.026209812611341476, 0.007368149235844612, 0.022530650720000267, 0.003253369824960828, 0.045990873128175735, -0.022493472322821617, 0.04847906157374382, 0.02488419972360134, -0.04771564155817032, -0.010240788571536541, -0.009123324416577816, 0.006015214137732983, 0.031605035066604614, -0.028597572818398476, 0.014189108274877071, -0.019319182261824608, -0.01426034327596426, 0.009758134372532368, -0.03716189041733742, 0.004235696978867054, -0.01990515924990177, 0.00595353776589036, -0.030593248084187508, 0.013268286362290382, -0.0005413336912170053, -0.038599710911512375, -0.009613844566047192, 0.023351717740297318, 0.07673405855894089, 0.004479637835174799, 0.04725644364953041, 0.005866875406354666, -0.014033501967787743, -0.00041889181011356413, -0.005759425926953554, -0.044199418276548386, 0.05002612993121147, 0.011151478625833988, 0.0197175070643425, 0.013378874398767948, 0.028151068836450577, 0.003077192697674036, -0.012982281856238842, 0.0031716208904981613, 0.059002675116062164, 0.0281915832310915, 0.03551243618130684, 0.0576585978269577, 0.003523899707943201, -0.020378924906253815, 0.015595841221511364, 0.033885881304740906, 0.060104429721832275, -0.026920409873127937, 0.05518980696797371, -0.0387304313480854, 0.02076297253370285, -0.0269179530441761, -0.03229271620512009, 0.00989521387964487, -0.022615676745772362, 0.031605277210474014, -0.011159887537360191, 0.012861686758697033, 0.005911002401262522, -0.04212167486548424, -0.006307617761194706, 0.08105847239494324, 0.01163403782993555, 0.023923639208078384, -0.03150109946727753, 0.009827397763729095, -0.0678817629814148, 0.03795389458537102, -0.001538346754387021, 0.05133669450879097, 0.0004788918304257095, -0.015026694163680077, -0.05950688198208809, 0.02571234107017517, 0.003661534283310175, -0.025325443595647812, 0.00352867622859776, -0.019424419850111008, -0.011536039412021637, 0.004960627295076847, -0.03224363178014755, 0.009699488058686256, -0.0465184710919857, 0.011261051520705223, -0.049008823931217194, 0.0342281237244606, -0.09172427654266357, 0.0051714712753891945, -0.04074162617325783, 0.04330950230360031, -0.022755064070224762, -0.046619679778814316, -0.028944924473762512, 0.0920523852109909, 0.00277278502471745, -0.0032690949738025665, -0.02814340405166149, 0.005481195170432329, -0.02202894724905491, 2.93072048407339e-07, -0.0008238045265898108, 0.05652085691690445, -0.026539485901594162, -0.010561172850430012, 0.07450997829437256, 0.003363484749570489, -0.002040273044258356, 0.04747121036052704, 0.012873683124780655, -0.06049393117427826, -0.010778509080410004, 0.018467944115400314, 0.013266007415950298, -0.031710270792245865, 0.07663970440626144, -0.13591821491718292, 0.03624933958053589, -0.01170823909342289, -0.021581526845693588, 0.021276503801345825, 0.02064947970211506, -0.004541810601949692, 0.015293454751372337, 0.02621137723326683, 0.027105920016765594, 0.048474423587322235, -0.0010475184535607696, -0.03462052345275879, 0.021373141556978226, -0.021286172792315483, 0.050911977887153625, 0.05678211525082588, 0.0006590647390112281, 0.009380868636071682, 0.03781229630112648, -0.019162921234965324, 0.035573314875364304, 0.051685016602277756, -0.015988318249583244, 0.1156458929181099, -0.029805218800902367, 0.052638523280620575, 0.029974687844514847, -0.06899529695510864, 0.016379278153181076, 0.08510825037956238, -0.006080950610339642, 0.023450149223208427, -0.05688191205263138, -0.009756164625287056, -0.008524343371391296, 0.023288080468773842, -0.018959952518343925, -0.03249894455075264, 0.003370346035808325, 0.01171575766056776, -0.012917720712721348, -0.03250464051961899, -0.014602882787585258, 0.043027039617300034, -0.020352860912680626, -0.04163367301225662, 0.03175214305520058, 0.010633938945829868, -0.017371520400047302, -0.008143153041601181, -0.06738318502902985, 2.2795200226470223e-34, -0.03159552440047264, -0.006188771687448025, -0.010594789870083332, -0.016080452129244804, 0.03647925332188606, -0.005494820419698954, 0.08767058700323105, 0.015142549760639668, -0.020008014515042305, -0.004423610866069794, -0.013977511785924435], "parsedContent": {"infobox": {}, "categories": ["Warm clothing"], "links": ["6 January", "2014", "6 January", "2021", "6 July", "2022", "Defence", "Slayer", "Slayer helmet", "<PERSON><PERSON>", "Ranged", "Magic", "rune full helm", "Magic", "Ranged", "Nex", "warm clothing", "Wintertodt", "slayer helmet", "Nightmare Zone", "Combat_Achievements/Hard", "Combat Achievements", "Soul Wars", "Zeal Tokens", "scroll of imbuing", "Emir's Arena", "black mask", "Slayer task", "Salve amulet", "Salve amulet (e)", "obsidian weapons", "berserker necklace", "crystal armour", "crystal body", "Crystal legs", "Slayer master", "<PERSON><PERSON> quo Maten", "Malevolent masquerade", "Slayer master", "A Porcine of Interest", "herb box", "Malevolent masquerade", "Slayer master", "A Porcine of Interest", "Soul Wars", "Zeal Tokens", "spoils of war", "Malevolent masquerade", "Slayer master", "A Porcine of Interest", "scroll of imbuing", "Malevolent masquerade", "Slayer master", "A Porcine of Interest", "herb box", "Soul Wars", "Zeal Tokens", "spoils of war", "scroll of imbuing", "Slayer reward point", "Combat Achievements", "G<PERSON><PERSON>", "King black bonnet", "Kal<PERSON><PERSON> khat", "Unholy helmet", "Dark Mantle", "Undead Head", "Eye see you", "Use more head", "Twisted Vision", "Warriors' Guild", "warm clothing", "Wintertodt", "graphically updated", "black mask (i)"], "plainText": "140px left The Slayer helmet (i), requiring a Defence level of 10 to wear, is a piece of equipment that is worn in the helmet slot. It provides the bonuses of multiple pieces of protective and beneficial Slayer equipment. It functions in the same manner as its unimbued variant (with the 16.67% Melee bonus), except that it also provides an extra 15% boost to Ranged accuracy and Ranged damage, and a 15% boost to Magic accuracy and Magic damage, against monsters assigned as the player's Slayer task. It has defensive bonuses equivalent to the rune full helm, with the exception of providing +11 higher relative magic defence (+10 vs. -1) as well as positive Magic and Ranged attack bonuses. It will prevent infection from Nex's virus attack, and can be used as warm clothing for Wintertodt. Players can imbue the slayer helmet using either 1,250,000 Nightmare Zone reward points (625,000 when the hard tier of Combat Achievements are complete), 500 Soul Wars Zeal Tokens, or by using a scroll of imbuing purchased from the Emir's Arena. The scroll can be used on the black mask before assembling the Slayer helmet or on the Slayer helmet itself.  If an imbued slayer helmet is disassembled, then it will return an imbued black mask, along with the other components. The black mask (i) can be uncharged to return (depending on the original imbuing method) the scroll of imbuing, or a full refund of the Nightmare Zone reward points or Soul Wars zeal tokens used to purchase it. For players with undead Slayer tasks, the boosts given by the Salve amulet or Salve amulet (e) do not stack with the effects of a Slayer helmet (i), and the amulet's boosts will take priority over the helmet's. When using obsidian weapons the effect of a berserker necklace does stack with the effects of a slayer helmet (i). The ranged damage bonus from crystal armour pieces is multiplied by the 15% damage boost from the helmet. Assuming players are wearing both the crystal body and legs, this results in a damage bonus of approximately 29.375% (1.15 * 1.125). Combat stats Slayer helmet and Konar tasks The Slayer master Konar quo Maten assigns monsters as well as specific areas they must be killed in. The Slayer helmet's accuracy and damage bonuses do not apply to monsters outside of the designated area, even if the monster type matches the task. Creation Whole process  Unlocked Malevolent masquerade from a Slayer master |members = Yes |ticks = 1 |facilities = Dom Onion's Reward Shop |mat1 = Black mask |mat2 = Earmuffs |mat3 = Facemask |mat4 = Nose peg |mat5 = Spiny helmet |mat6 = Enchanted gem |mat6cost = 1 |mat7 = Reinforced goggles |mat7cost = 100 |mat7itemnote = Only required after completion of A Porcine of Interest. |mat8 = Nightmare Zone points |mat8quantity = 1250000 |mat8pic = Minigame map icon |mat8cost = () / 9500 |mat8itemnote = Value based on the expected average value of spending Nightmare Zone points on herb boxes. |output1 = Slayer helmet (i) |output1cost = no |output1subtxt = Total - Nightmare Zone }} |text2 = Soul Wars |item2 =  Unlocked Malevolent masquerade from a Slayer master |members = Yes |ticks = 1 |facilities = Soul Wars Reward Shop |mat1 = Black mask |mat2 = Earmuffs |mat3 = Facemask |mat4 = Nose peg |mat5 = Spiny helmet |mat6 = Enchanted gem |mat6cost = 1 |mat7 = Reinforced goggles |mat7cost = 100 |mat7itemnote = Only required after completion of A Porcine of Interest. |mat8 = Zeal Tokens |mat8quantity = 500 |mat8pic = Minigame map icon |mat8cost =  / 30 |mat8itemnote = Value based on the expected average value of spending Soul Wars Zeal Tokens on spoils of war. |output1 = Slayer helmet (i) |output1cost = no |output1subtxt = Total - Soul Wars }} |text3 = Emir's Arena |item3 =  Unlocked Malevolent masquerade from a Slayer master |members = Yes |ticks = 1 |mat1 = Black mask |mat2 = Earmuffs |mat3 = Facemask |mat4 = Nose peg |mat5 = Spiny helmet |mat6 = Enchanted gem |mat6cost = 1 |mat7 = Reinforced goggles |mat7cost = 100 |mat7itemnote = Only required after completion of A Porcine of Interest. |mat8 = Scroll of imbuing |mat8cost = No |mat8itemnote = When uncharged, the scroll of imbuing is returned. |output1 = Slayer helmet (i) |output1cost = no |output1subtxt = Total - Emir's Arena }} }} Imbuing first  Unlocked Malevolent masquerade from a Slayer master |members = Yes |ticks = 0 |mat1 = Black mask (i) |mat1cost = no |mat2 = Earmuffs |mat3 = Facemask |mat4 = Nose peg |mat5 = Spiny helmet |mat6 = Enchanted gem |mat6cost = 1 |mat7 = Reinforced goggles |mat7cost = 100 |mat7itemnote = Only required after completion of A Porcine of Interest. |output1 = Slayer helmet (i) |output1cost = No |output1subtxt = Crafting }} Assembling first  ) / 9500 |mat2itemnote = Value based on the expected average value of spending Nightmare Zone points on herb boxes. |output1 = Slayer helmet (i) |output1cost = no |output1subtxt = Nightmare Zone }} |text2 = Soul Wars |item2 =  / 30 |mat2itemnote = Value based on the expected average value of spending Soul Wars Zeal Tokens on spoils of war. |output1 = Slayer helmet (i) |output1cost = no |output1subtxt = Soul Wars }} |text3 = Emir's Arena |item3 =  }} Cosmetics With 1,000 Slayer reward points, the player can learn how to recolour the helmet (or its imbued variant) by using certain special items on it. Each recolour ability is purchased separately, meaning 8,000 Slayer reward points are required to fully access all the recolours, provided the player has the items readily available. Players can disassemble the recoloured helmet to retrieve the items back. All recolours have the same stats and bonuses as the original Slayer helmet and are thus purely cosmetic. With the elite tier or above of Combat Achievements claimed, players may recolour their helm with the visage of certain bosses by bringing a Slayer helmet to Ghommal. The list of recolours offered by Ghommal depends on the tier of rewards claimed. Players may freely change this recolour at any time by speaking with Ghommal, or remove the recolour by disassembling the helm, upon which the un-coloured version will be returned. The variants that can be created are as follows: {| class=\"wikitable lighttable align-center-1\" ! colspan=\"2\" rowspan=\"2\" |Upgraded item ! colspan=\"2\" |Requirements |- !Unlock !Item |- | |King black bonnet | |- | |Kalphite khat | |- | |Unholy helmet | |- | |Dark Mantle | |- | |Undead Head | |- | |Eye see you | |- | |Use more head | |- | |Twisted Vision | |- ! colspan=\"2\" |Upgraded item ! colspan=\"2\" |Tier claimed |- | | colspan=\"2\"| |- | | colspan=\"2\"| |- | | colspan=\"2\"| |} Products Treasure Trails |item2=dragon battleaxe|item3=dragon defender|cost3=0|variation=slayer helmet}} Used in recommended equipment Changes  and all versions of the black mask (i) now offer a 15% damage boost to Magic damage against your assigned Slayer target, like they already do for melee and Ranging. *The Slayer helm (i) now has a +3 mage attack bonus, a +3 range attack bonus and a +10 mage defence bonus. *The Slayer helm's chathead model has had a green tint removed from its jaw. }} Gallery (historical) {|style=\"text-align: center\" cellpadding=\"15\" |300x150px |300x150px |300x150px |- |center |center |center |- |6 January 2014 –<br/>26 May 2016 |26 May 2016 –<br/>14 July 2021 |14 July 2021 –<br/>present |} Category:Warm clothing"}, "originalContent": "{{Infobox Item\n|version1 = Nightmare Zone\n|version2 = Soul Wars\n|version3 = <PERSON><PERSON>'s Arena\n|name = Slayer helmet (i)\n|image = [[File:Slayer helmet (i).png]]\n|release1 = [[6 January]] [[2014]]\n|release2 = [[6 January]] [[2021]]\n|release3 = [[6 July]] [[2022]]\n|update1 = Second Slayer Batch: The Helm\n|update2 = Soul Wars And 20th Anniversary Event\n|update3 = PvP Arena: Soft Launch\n|aka = Slayer helm (i)\n|members = Yes\n|quest = No\n|tradeable = No\n|placeholder = Yes\n|equipable = Yes\n|stackable = No\n|noteable = No\n|options = Wear, Check, Disassemble, Drop\n|wornoptions = Check, Partner, Log\n|examine = You really don't want to wear it inside-out.\n|value = 90000\n|weight = 2.267\n|id1 = 11865\n|id2 = 25177\n|id3 = 26674\n}}\n[[File:Slayer helmet (i) detail.png|left|140px]]\n[[File:Slayer helmet (i) chathead.png|left]]\nThe '''Slayer helmet (i)''', requiring a [[Defence]] level of 10 to wear, is a piece of equipment that is worn in the helmet slot. It provides the bonuses of multiple pieces of protective and beneficial [[Slayer]] equipment. It functions in the same manner as its [[Slayer helmet|unimbued variant]] (with the 16.67% [[Melee]] bonus), except that it also provides an extra 15% boost to [[Ranged]] accuracy '''and''' Ranged damage, and a 15% boost to [[Magic]] accuracy '''and''' Magic damage, against monsters assigned as the player's Slayer task.\n\nIt has defensive bonuses equivalent to the [[rune full helm]], with the exception of providing +11 higher relative magic defence (+10 vs. -1) as well as positive [[Magic]] and [[Ranged]] attack bonuses. It will prevent infection from [[Nex]]'s virus attack, and can be used as [[warm clothing]] for [[Wintertodt]].\n\nPlayers can imbue the [[slayer helmet]] using either 1,250,000 [[Nightmare Zone]] reward points (625,000 when the [[Combat_Achievements/Hard|hard tier]] of [[Combat Achievements]] are complete), 500 [[Soul Wars]] [[Zeal Tokens]], or by using a [[scroll of imbuing]] purchased from the [[Emir's Arena]]. The scroll can be used on the [[black mask]] before assembling the Slayer helmet or on the Slayer helmet itself. \n\nIf an imbued slayer helmet is disassembled, then it will return an imbued black mask, along with the other components. The black mask (i) can be uncharged to return (depending on the original imbuing method) the scroll of imbuing, or a full refund of the Nightmare Zone reward points or Soul Wars zeal tokens used to purchase it.\n\nFor players with undead [[Slayer task]]s, the boosts given by the [[Salve amulet]] or [[Salve amulet (e)]] '''do not''' stack with the effects of a Slayer helmet (i), and the amulet's boosts will take priority over the helmet's.\n\nWhen using [[obsidian weapons]] the effect of a [[berserker necklace]] '''does''' stack with the effects of a slayer helmet (i).\n\nThe ranged damage bonus from [[crystal armour]] pieces is multiplied by the 15% damage boost from the helmet. Assuming players are wearing both the [[crystal body]] and [[Crystal legs|legs]], this results in a damage bonus of approximately 29.375% (1.15 * 1.125).\n\n==Combat stats==\n{{Infobox Bonuses\n|version1 = Nightmare Zone\n|version2 = Soul Wars\n|version3 = Emir's Arena\n|astab = 0\n|aslash = 0\n|acrush = 0\n|amagic = +3\n|arange = +3\n|dstab = +30\n|dslash = +32\n|dcrush = +27\n|dmagic = +10\n|drange = +30\n|str = 0\n|rstr = 0\n|mdmg = 0\n|prayer = 0\n|slot = head\n|image = [[File:Slayer helmet (i) equipped.png|130px]]\n|altimage = [[File:Slayer helmet (i) equipped female.png|130px]]\n}}\n\n==Slayer helmet and Konar tasks==\nThe [[Slayer master]] [[Konar quo Maten]] assigns monsters as well as specific areas they must be killed in.\n\nThe Slayer helmet's accuracy and damage bonuses do not apply to monsters outside of the designated area, even if the monster type matches the task.\n\n==Creation==\n===Whole process===\n{{Switch infobox\n|text1 = Nightmare Zone\n|item1 = {{Recipe\n|skill1 = Crafting\n|skill1lvl = 55\n|skill1boostable = yes\n|skill1exp = 0\n|notes = {{SCP|Slayer}} Unlocked [[Malevolent masquerade]] from a [[Slayer master]]\n|members = Yes\n|ticks = 1\n|facilities = Dom Onion's Reward Shop\n|mat1 = Black mask\n|mat2 = Earmuffs\n|mat3 = Facemask\n|mat4 = Nose peg\n|mat5 = Spiny helmet\n|mat6 = Enchanted gem\n|mat6cost = 1\n|mat7 = Reinforced goggles\n|mat7cost = 100\n|mat7itemnote = Only required after completion of [[A Porcine of Interest]].\n|mat8 = Nightmare Zone points\n|mat8quantity = 1250000\n|mat8pic = Minigame map icon\n|mat8cost = ({{Average drop value|mob=herb box|raw=y}}) / 9500\n|mat8itemnote = Value based on the expected average value of spending Nightmare Zone points on [[herb box]]es.\n|output1 = Slayer helmet (i)\n|output1cost = no\n|output1subtxt = Total - Nightmare Zone\n}}\n|text2 = Soul Wars\n|item2 = {{Recipe\n|skill1 = Crafting\n|skill1lvl = 55\n|skill1boostable = yes\n|skill1exp = 0\n|notes = {{SCP|Slayer}} Unlocked [[Malevolent masquerade]] from a [[Slayer master]]\n|members = Yes\n|ticks = 1\n|facilities = Soul Wars Reward Shop\n|mat1 = Black mask\n|mat2 = Earmuffs\n|mat3 = Facemask\n|mat4 = Nose peg\n|mat5 = Spiny helmet\n|mat6 = Enchanted gem\n|mat6cost = 1\n|mat7 = Reinforced goggles\n|mat7cost = 100\n|mat7itemnote = Only required after completion of [[A Porcine of Interest]].\n|mat8 = Zeal Tokens\n|mat8quantity = 500\n|mat8pic = Minigame map icon\n|mat8cost = {{Average drop value|mob=spoils of war|raw=y}} / 30\n|mat8itemnote = Value based on the expected average value of spending [[Soul Wars]] [[Zeal Tokens]] on [[spoils of war]].\n|output1 = Slayer helmet (i)\n|output1cost = no\n|output1subtxt = Total - Soul Wars\n}}\n|text3 = Emir's Arena\n|item3 = {{Recipe\n|skill1 = Crafting\n|skill1lvl = 55\n|skill1boostable = yes\n|skill1exp = 0\n|notes = {{SCP|Slayer}} Unlocked [[Malevolent masquerade]] from a [[Slayer master]]\n|members = Yes\n|ticks = 1\n|mat1 = Black mask\n|mat2 = Earmuffs\n|mat3 = Facemask\n|mat4 = Nose peg\n|mat5 = Spiny helmet\n|mat6 = Enchanted gem\n|mat6cost = 1\n|mat7 = Reinforced goggles\n|mat7cost = 100\n|mat7itemnote = Only required after completion of [[A Porcine of Interest]].\n|mat8 = Scroll of imbuing\n|mat8cost = No\n|mat8itemnote = When uncharged, the [[scroll of imbuing]] is returned.\n|output1 = Slayer helmet (i)\n|output1cost = no\n|output1subtxt = Total - Emir's Arena\n}}\n}}\n\n===Imbuing first===\n{{Recipe\n|skill1 = Crafting\n|skill1lvl = 55\n|skill1boostable = yes\n|skill1exp = 0\n|notes = {{SCP|Slayer}} Unlocked [[Malevolent masquerade]] from a [[Slayer master]]\n|members = Yes\n|ticks = 0\n|mat1 = Black mask (i)\n|mat1cost = no\n|mat2 = Earmuffs\n|mat3 = Facemask\n|mat4 = Nose peg\n|mat5 = Spiny helmet\n|mat6 = Enchanted gem\n|mat6cost = 1\n|mat7 = Reinforced goggles\n|mat7cost = 100\n|mat7itemnote = Only required after completion of [[A Porcine of Interest]].\n|output1 = Slayer helmet (i)\n|output1cost = No\n|output1subtxt = Crafting\n}}\n\n===Assembling first===\n{{Switch infobox\n|text1 = Nightmare Zone\n|item1 = {{Recipe\n|members = Yes\n|ticks = 1\n|facilities = Dom Onion's Reward Shop\n|mat1 = Slayer helmet\n|mat1cost = no\n|mat2 = Nightmare Zone points\n|mat2quantity = 1250000\n|mat2pic = Minigame map icon\n|mat2cost = ({{Average drop value|mob=herb box|raw=y}} ) / 9500\n|mat2itemnote = Value based on the expected average value of spending Nightmare Zone points on [[herb box]]es.\n|output1 = Slayer helmet (i)\n|output1cost = no\n|output1subtxt = Nightmare Zone\n}}\n|text2 = Soul Wars\n|item2 = {{Recipe\n|members = Yes\n|ticks = 1\n|facilities = Soul Wars Reward Shop\n|mat1 = Slayer helmet\n|mat1cost = no\n|mat2 = Zeal Tokens\n|mat2quantity = 500\n|mat2pic = Minigame map icon\n|mat2cost = {{Average drop value|mob=spoils of war|raw=y}} / 30\n|mat2itemnote = Value based on the expected average value of spending [[Soul Wars]] [[Zeal Tokens]] on [[spoils of war]].\n|output1 = Slayer helmet (i)\n|output1cost = no\n|output1subtxt = Soul Wars\n}}\n|text3 = Emir's Arena\n|item3 = {{Recipe\n|members = Yes\n|ticks = 1\n|mat1 = Slayer helmet\n|mat1cost = no\n|mat2 = Scroll of imbuing\n|mat2cost = No\n|mat2itemnote = When uncharged, the [[scroll of imbuing]] is returned.\n|output1 = Slayer helmet (i)\n|output1cost = no\n|output1subtxt = Emir's Arena\n}}\n}}\n\n==Cosmetics==\nWith 1,000 [[Slayer reward point]]s, the player can learn how to recolour the helmet (or its imbued variant) by using certain special items on it. Each recolour ability is purchased separately, meaning 8,000 Slayer reward points are required to fully access all the recolours, provided the player has the items readily available. Players can disassemble the recoloured helmet to retrieve the items back. All recolours have the same stats and bonuses as the original Slayer helmet and are thus purely cosmetic.\n\nWith the elite tier or above of [[Combat Achievements]] claimed, players may recolour their helm with the visage of certain bosses by bringing a Slayer helmet to [[Ghommal]]. The list of recolours offered by Ghommal depends on the tier of rewards claimed. Players may freely change this recolour at any time by speaking with Ghommal, or remove the recolour by disassembling the helm, upon which the un-coloured version will be returned.\n\nThe variants that can be created are as follows:\n{| class=\"wikitable lighttable align-center-1\"\n! colspan=\"2\" rowspan=\"2\" |Upgraded item\n! colspan=\"2\" |Requirements\n|-\n!Unlock\n!Item\n|-\n|{{plinkt|Black slayer helmet (i)}}\n|[[King black bonnet]]\n|{{plink|Kbd heads|txt=King Black Dragon heads}}\n|-\n|{{plinkt|Green slayer helmet (i)}}\n|[[Kalphite khat]]\n|{{plink|Kq head|txt=Kalphite Queen head}}\n|-\n|{{plinkt|Red slayer helmet (i)}}\n|[[Unholy helmet]]\n|{{plink|Abyssal head|txt=Abyssal demon head}}\n|-\n|{{plinkt|Purple slayer helmet (i)}}\n|[[Dark Mantle]]\n|{{plink|Dark claw}}\n|-\n|{{plinkt|Turquoise slayer helmet (i)}}\n|[[Undead Head]]\n|{{plink|Vorkath's head}}\n|-\n|{{plinkt|Araxyte slayer helmet (i)}}\n|[[Eye see you]]\n|{{plink|Araxyte head}}\n|-\n|{{plinkt|Hydra slayer helmet (i)}}\n|[[Use more head]]\n|{{plink|Alchemical hydra heads}}\n|-\n|{{plinkt|Twisted slayer helmet (i)}}\n|[[Twisted Vision]]\n|{{plink|Twisted horns}}\n|-\n! colspan=\"2\" |Upgraded item\n! colspan=\"2\" |Tier claimed\n|-\n|{{plinkt|Tztok slayer helmet (i)}}\n| colspan=\"2\"|{{plink|Elite Combat Achievements|pic=Ghommal's hilt 4}}\n|-\n|{{plinkt|Vampyric slayer helmet (i)}}\n| colspan=\"2\"|{{plink|Master Combat Achievements|pic=Ghommal's hilt 5}}\n|-\n|{{plinkt|Tzkal slayer helmet (i)}}\n| colspan=\"2\"|{{plink|Grandmaster Combat Achievements|pic=Ghommal's hilt 6}}\n|}\n\n==Products==\n{{Uses material list|Slayer helmet (i)}}\n\n==Treasure Trails==\n{{EmoteClueHeader}}\n{{EmoteClue|tier=master|emote=Raspberry|location=[[Warriors' Guild]] bank|item1=Slayer helmet (i)|cost={{GEP|Black mask}}|item2=dragon battleaxe|item3=dragon defender|cost3=0|variation=slayer helmet}}\n{{EmoteClueBottom}}\n\n==Used in recommended equipment==\n{{Used in recommended equipment|Slayer helmet (i)}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 15 January 2025\n|update = Leagues V: Raging Echos Rewards Are Here\n|change = The Slayer helmet can now be used as [[warm clothing]] for [[Wintertodt]].\n}}\n{{Subject changes\n|date = 24 April 2024\n|update = Varlamore Tweaks, GameJam V Commences & More!\n|change = Fixed a bug where the Imbued Slayer Helmet wasn't refunding the points/scroll used to imbue it in some instances when lost on death.\n}}\n{{Subject changes\n|date = 10 April 2024\n|update = Undead Pirates, Colosseum Changes & more!\n|poll = Undead Pirates Rewards & More\n|change =\n* Manually uncharging the slayer helmet (i) will refund 100% of the points/zeal, up from 80%\n* Dying with the slayer helmet (i) that is lost on death will now refund either the points or the Imbue Scroll used to create it.\n}}\n{{Subject changes\n|date = 21 June 2023\n|update = More Poll 79 Changes\n|poll = Old School Content Poll 79: Quality of Life\n|change = Wearing a slayer helmet will now prevent players from becoming infected by a player infected from Nex's choke attack.\n}}\n{{Subject changes\n|date = 14 July 2021\n|update = Slayer Improvements - Poll 75\n|poll = A Night At The Theatre and Old School Content Poll 75: PvM\n|change = The item was [[graphically updated]].\n}}\n{{Subject changes \n|date = 26 May 2016\n|update = Maniacal Monkey Hunting\n|change = The item's inventory sprite was adjusted with the release of recoloured helmets.\n}}\n{{Subject changes\n|date = 11 February 2016\n|update = Tournament Stats & Skilling QoL\n|change = The \"Check\" option was added, allowing players to see their current Slayer task.\n}}\n{{Subject changes\n|date = 20 February 2014\n|update = Happy Birthday!\n|poll = Old School feature poll 16\n|change = \n*The Slayer helm (i){{sic}} and all versions of the [[black mask (i)]] now offer a 15% damage boost to Magic damage against your assigned Slayer target, like they already do for melee and Ranging.\n*The Slayer helm (i){{sic}} now has a +3 mage attack bonus, a +3 range attack bonus and a +10 mage defence bonus.\n*The Slayer helm's{{sic}} chathead model has had a green tint removed from its jaw.\n}}\n{{Subject changes\n|date = 16 January 2014\n|update = Patch Notes (16 January 2014)\n|change = The item's value was increased from 1 to 90,000.\n}}\n{{Subject changes\n|change = The slayer helmet (i) is now kept on death as with other untradeable items. Previously, all components of the helmet would disappear and the black mask would appear on the ground.\n}}\n{{Subject changes footer}}\n\n===Gallery (historical)===\n{|style=\"text-align: center\" cellpadding=\"15\"\n|[[File:Slayer helmet (i) (v1) detail.png|center|300x150px]]\n|[[File:Slayer helmet (i) (v2) detail.png|center|300x150px]]\n|[[File:Slayer helmet (i) detail.png|center|300x150px]]\n|-\n|[[File:Slayer helmet (i) v1.png|center]]\n|[[File:Slayer helmet (i) v2.png|center]]\n|[[File:Slayer helmet (i).png|center]]\n|-\n|6 January 2014 –<br/>26 May 2016\n|26 May 2016 –<br/>14 July 2021\n|14 July 2021 –<br/>present\n|}\n\n{{Melee headgear}}\n{{Slayer equipment}}\n{{Nightmare Zone}}\n{{Soul Wars}}\n\n[[Category:Warm clothing]]"}