{"title": "Wood<PERSON><PERSON> (Forestry event)", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [], "relationships": [{"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "28 June", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "2023", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "Woodcutting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "Anima-infused bark", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "Forestry event", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "Woodcutting Leprechaun", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "End of Rainbow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "Woodcutting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "anima-infused bark", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "bank deposit box", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "log basket", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "Woodcutting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "RuneScape clock", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "Logout button", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "Forestry outfit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "Lumberjack outfit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Wood<PERSON><PERSON> (Forestry event)", "to": "Leprechaun charm", "type": "references", "confidence": 0.7, "source": "wiki_link"}], "embedding": [-0.007165126036852598, -0.060875341296195984, 0.0032205700408667326, 0.03793776035308838, -0.05764278769493103, -0.03685225546360016, -0.0636514201760292, -0.022327633574604988, -0.06958764791488647, -0.044497616589069366, 0.0236531812697649, 0.08097931742668152, 0.007818825542926788, -0.029755309224128723, -0.0037394114769995213, -0.07672379165887833, 0.0036828245501965284, 0.021873150020837784, -0.07384500652551651, -0.034155718982219696, -0.018515828996896744, 0.07163667678833008, 0.0023107673041522503, 0.021692413836717606, -0.032977811992168427, -0.05734281241893768, -0.024673854932188988, 0.03228408470749855, 0.0013743886956945062, 0.034562598913908005, -0.031223813071846962, -0.07205113023519516, -0.029743755236268044, -0.007241475395858288, 2.2600952434004284e-06, -0.0450003519654274, 0.006000105757266283, -0.00786276999861002, -0.049791861325502396, -0.008115606382489204, 0.03999917954206467, -0.051610056310892105, -0.002563644666224718, 0.011734076775610447, -0.001134786056354642, 0.08700299263000488, -0.006105909589678049, -0.044204507023096085, 0.03394920006394386, 0.04816708341240883, 0.023212404921650887, -0.07013969868421555, -0.009520253166556358, 0.0064011202193796635, 0.14434288442134857, 0.0725974440574646, -0.0030618973542004824, -0.07409810274839401, -0.008638373576104641, -0.05186348035931587, -0.01585487648844719, -0.057607050985097885, 0.0563778355717659, -0.0030277094338089228, -0.048371799290180206, 0.012239045463502407, -0.03422440588474274, -0.007126519922167063, 0.007675222121179104, -0.0313853845000267, -0.048368003219366074, -0.016237011179327965, -0.0035164884757250547, 0.03985659033060074, -0.004139904864132404, -0.010357750579714775, 0.007988263852894306, 0.04886532574892044, 0.0016295554814860225, -0.020158909261226654, 0.024605896323919296, 0.027145983651280403, -0.00098417978733778, 0.024074537679553032, 0.022472087293863297, -0.009965086355805397, 0.01382331270724535, 0.04208293929696083, -0.01852378621697426, -0.05365418642759323, -0.014399445615708828, -0.02470696158707142, -0.030389301478862762, 0.033243853598833084, -0.016200050711631775, 0.015301627106964588, -0.020184339955449104, 0.1588284820318222, 0.03409169614315033, 0.09571997076272964, -0.06646574288606644, 0.01997886784374714, -0.009429148398339748, 0.00253807264380157, -0.02912914752960205, -0.0268159918487072, 0.06792902946472168, -0.027529414743185043, 0.024900194257497787, 0.003184389555826783, 0.03213754668831825, -0.0006470083026215434, 0.04083309695124626, -0.04098624363541603, -0.02000683732330799, 0.03942127153277397, 0.010087517090141773, 0.01271191518753767, -0.009789611212909222, -0.010223938152194023, 0.0637359470129013, 0.004954105243086815, -0.034482259303331375, 0.00468331016600132, 0.01762489415705204, -0.04185007885098457, -0.01967533305287361, 0.016952883452177048, 0.012586131691932678, -0.031115565448999405, -0.0028107098769396544, 0.04315831884741783, -0.016009604558348656, -0.008619324304163456, 0.033574577420949936, 0.10397768020629883, 0.017873069271445274, 0.04174261540174484, -0.0337318554520607, -0.012848326936364174, 0.0005354423774406314, 0.0077554043382406235, -0.04767649993300438, -0.0390600748360157, -0.04372253641486168, -0.057202767580747604, -0.019245736300945282, 0.02786710299551487, 0.009167380630970001, -0.017287660390138626, 0.0016828228253871202, 0.029515620321035385, -0.03487827628850937, 0.005941278301179409, 0.016422735527157784, 0.03302549570798874, -0.06349889934062958, 0.04997909814119339, 0.020020445808768272, 0.11497312039136887, 0.020956100896000862, -0.0010901734931394458, -0.04283037409186363, -0.02355055883526802, -0.010165431536734104, -0.028211375698447227, -0.014093602076172829, -0.014867808669805527, -0.11208774894475937, -0.07373460382223129, 0.02908625826239586, -0.029747774824500084, -0.01778407022356987, -0.07059154659509659, -0.033673062920570374, 0.02546578086912632, 0.028371494263410568, 0.026717351749539375, 0.003231275826692581, 0.003066352801397443, -0.0740465596318245, 0.013881465420126915, -0.014462566003203392, -0.022509248927235603, -0.046346817165613174, 0.004716345574706793, 0.04006071016192436, -0.022511206567287445, -0.0073628718964755535, 0.0050890338607132435, 0.0037875850684940815, -0.002807408571243286, -0.013852287083864212, -0.018181737512350082, -0.01919935829937458, -0.007959803566336632, 0.02611662819981575, 0.011927287094295025, -0.02916923351585865, 0.01878507062792778, 0.06248967722058296, 0.01224821899086237, -0.05451585352420807, 0.03383541852235794, -0.010547574609518051, -0.0003731012693606317, 0.013223839923739433, -0.018922287970781326, -0.015154805965721607, -0.008601631969213486, -0.06558652967214584, -0.044661570340394974, 0.02442251890897751, 0.05166194960474968, 0.04198208823800087, 0.0045480504631996155, 0.008757347241044044, 0.017579760402441025, -0.0058206478133797646, 0.0493573434650898, -0.017867889255285263, 0.009104671888053417, -0.014760381542146206, 0.011897239834070206, -0.012977268546819687, -0.03351891040802002, -0.02415326051414013, -0.018816247582435608, 0.006757640279829502, -0.011500113643705845, -0.012301546521484852, -0.012127895839512348, 0.055877719074487686, -0.005408586468547583, -0.009227242320775986, 0.010416116565465927, 0.027867117896676064, -0.03890426829457283, 0.057770390063524246, -0.0735221803188324, -0.0070411269553005695, 0.05551758408546448, -0.015557665377855301, 0.0012849955819547176, 0.03695259988307953, 0.018697161227464676, 0.02678642049431801, 0.004986893851310015, -0.025760365650057793, 0.0009640324860811234, -0.04788810387253761, -0.011252405121922493, 0.04114184528589249, 0.01744377613067627, 0.0461871363222599, -0.06921971589326859, 0.03605245053768158, -0.039639912545681, -0.08284056931734085, 0.019678838551044464, 0.018285930156707764, -0.020584899932146072, -0.046296488493680954, -0.0423138253390789, 0.08008334040641785, 0.016248926520347595, 0.008256208151578903, -0.07973233610391617, 0.03225080668926239, -0.002520473673939705, 0.049630437046289444, 0.03925468400120735, -0.031148375943303108, -0.01699545606970787, 0.025248317047953606, -0.0012647855328395963, -0.08755547553300858, -0.014108192175626755, 0.048469625413417816, 0.000751584826502949, -0.02645554579794407, -0.013940637931227684, 0.039397530257701874, 0.0032712402753531933, 0.025242101401090622, 0.04654097557067871, 0.027545303106307983, -0.05209454521536827, -0.02923661656677723, -0.0012217542389407754, -0.07526171952486038, -0.019720936194062233, 0.023261109367012978, 0.007850452326238155, 0.01679609715938568, 0.04037114977836609, 0.1261719912290573, 0.05594882369041443, -0.0016044038347899914, 0.03818276524543762, -0.040965016931295395, 0.006990083493292332, 0.021658146753907204, -0.011628636159002781, 0.028984887525439262, 0.01817913167178631, 0.0028176424093544483, -0.055460792034864426, 0.026712486520409584, -0.04803595691919327, 0.00995243526995182, -0.0013865315122529864, 0.013782638125121593, 0.07955268770456314, 0.009429380297660828, -0.039707694202661514, 0.011257696896791458, -0.06129756569862366, -0.008881946094334126, -0.013293415307998657, -0.03162854537367821, -0.01175734680145979, -0.01439374964684248, 0.006471597123891115, -0.026979180052876472, -0.00013398427108768374, 0.008008461445569992, -0.00464172288775444, 0.08709505945444107, 0.0029678877908736467, 0.001772747840732336, -0.11861919611692429, -0.03615649789571762, 0.02217690646648407, 0.06428824365139008, 0.012948468327522278, -0.04295653849840164, 0.00451318733394146, -0.03455976024270058, 0.023530123755335808, -0.01667553372681141, 0.00047920571523718536, -0.011369246058166027, -0.05119756981730461, 0.01991104893386364, -0.025426987558603287, -0.013374761678278446, 0.043391626328229904, 0.030718181282281876, 0.013866652734577656, 0.015080619603395462, -0.020670948550105095, -0.010384357534348965, 0.0025834531988948584, 0.004677732475101948, 0.02589653991162777, -0.028655538335442543, -0.09430671483278275, 0.049929529428482056, 0.027323976159095764, 0.007630341220647097, -0.02395649626851082, 0.05168210715055466, 0.015935149043798447, 0.027359135448932648, 0.02183528244495392, -0.007636614143848419, -0.02625214122235775, -0.010130854323506355, 0.006303922738879919, 0.024209503084421158, 0.01402261946350336, 0.039641015231609344, 0.008907051756978035, 0.022655988112092018, 0.017122946679592133, 0.022848231717944145, -0.02216128073632717, 0.0289962999522686, -0.006044866982847452, 0.010110268369317055, -0.045589838176965714, -0.031158948317170143, 0.0045617385767400265, -0.027027100324630737, 0.021812913939356804, -0.029715951532125473, 0.0009551277034915984, 0.010505292564630508, -0.005327550228685141, 0.009019298478960991, -0.05423358455300331, -0.024038920179009438, 0.007271721493452787, 0.01254477072507143, -0.04101306572556496, -0.003328390186652541, -0.013698645867407322, -0.015209639444947243, 0.03496655076742172, 0.019799521192908287, -0.012911317870020866, -0.020134761929512024, -0.013756100088357925, 0.0195241067558527, 0.051458101719617844, 0.05406409874558449, -0.021462051197886467, 0.024165593087673187, 0.09732230752706528, 0.025215616449713707, 0.034217044711112976, -0.006505107507109642, 0.001961424248293042, -0.016440989449620247, 0.022084632888436317, 0.02380426786839962, 0.02471691370010376, -0.0015941897872835398, 0.011724336072802544, -0.016785938292741776, 0.06257569789886475, -0.013964918442070484, 0.003989735152572393, -0.012150472030043602, 0.0451207272708416, 0.004970485344529152, -0.005513637326657772, 0.028522951528429985, 0.006600099615752697, 0.0003818956611212343, -0.03359252214431763, -0.08221303671598434, -0.06031370535492897, 0.013430395163595676, -0.13468138873577118, 0.03496045619249344, 0.018065670505166054, 0.0004026695969514549, -0.06036244332790375, -0.002727557672187686, 0.04636348783969879, 0.0043527367524802685, -0.002300289226695895, 0.018794070929288864, -0.14203041791915894, -0.009184141643345356, 0.038545746356248856, 0.02789408527314663, -0.01841527223587036, -0.03152403607964516, 0.0029310202226042747, 0.07055781036615372, 0.012884634546935558, -0.005782559048384428, -0.028287142515182495, 0.04352648928761482, -0.01960832253098488, -0.04557673633098602, -0.007091151084750891, -0.0113113047555089, 0.030380116775631905, 0.009811799973249435, -0.009789206087589264, -0.0055945878848433495, 0.0062825120985507965, 0.024871205911040306, -0.04901324585080147, 0.020390132442116737, -0.02588440664112568, 0.05257900804281235, 0.011597715318202972, 0.0422857403755188, 0.012042165733873844, 0.06513837724924088, 0.01377947349101305, 0.02598121017217636, -0.025339743122458458, 0.008766556158661842, -0.04332220181822777, 0.019121581688523293, -0.0477416068315506, -0.008227737620472908, -0.025363214313983917, 0.02384447678923607, 0.006022048648446798, 0.00502919964492321, -0.009315469302237034, 0.03062996082007885, -0.03578532487154007, -0.05993624031543732, -0.009165317751467228, 0.02651309221982956, 0.014082630164921284, 0.05379367619752884, -0.0140353012830019, -0.035028669983148575, -0.05367636680603027, 0.03514435514807701, 0.006331693846732378, 0.0007994947372935712, 0.0427735410630703, -0.001422726083546877, 0.021078871563076973, -0.027962081134319305, 0.00247638626024127, -0.01913856901228428, -0.027235768735408783, 0.026899682357907295, 0.0012088848743587732, 0.01962997019290924, -0.026338648051023483, -0.005507201887667179, -0.009169558994472027, 0.04165318235754967, 0.018773166462779045, 0.02614004909992218, 0.016244687139987946, 0.035119205713272095, 0.007653882261365652, 0.005678481422364712, -0.01944858208298683, -0.005629749968647957, 0.008987965062260628, -0.04071813449263573, -0.03616044670343399, 0.003603944554924965, -0.02428813837468624, -0.059450726956129074, -0.016431080177426338, 0.027350036427378654, 0.002084458712488413, 0.03527400270104408, 0.03743496537208557, -5.4651638492941856e-05, -0.002059624530375004, 0.04753933101892471, -0.06676651537418365, 0.03961411863565445, -0.02556907944381237, -0.05536780506372452, 0.05233103036880493, 0.06991350650787354, -0.0349973626434803, 0.01769503764808178, 0.02804340049624443, -0.0015768486773595214, -0.006873719859868288, -0.02344757691025734, 0.014458462595939636, 0.027062632143497467, 0.01881357841193676, -0.049921728670597076, -0.023361070081591606, -0.0046305106952786446, -6.688426204960533e-33, -0.04063180461525917, -0.015601018443703651, 0.005387417506426573, 0.014958510175347328, -0.01957828924059868, 0.0032456722110509872, 0.04793832078576088, -0.03125426545739174, 0.020402727648615837, -0.02313440479338169, 0.01072313915938139, -0.008036699146032333, 0.01387944258749485, -0.03130345046520233, 0.015237793326377869, -0.01375673245638609, 0.024818504229187965, 0.010695935226976871, -0.006292132195085287, -0.018039286136627197, -0.051675572991371155, -0.032189805060625076, 0.00844009779393673, -0.0049864789471030235, 0.002868577605113387, -0.0010448290267959237, -0.01661778800189495, -0.011509586125612259, 0.02570476569235325, -0.053031690418720245, 0.006410839501768351, -0.012374252080917358, 0.03852391242980957, 0.010865174233913422, -0.013906247913837433, -0.04081575945019722, 0.014203495346009731, -0.027976324781775475, 0.018358362838625908, -0.019686177372932434, -0.11301033198833466, -0.03132561221718788, 0.031773217022418976, 0.020373161882162094, 0.03128586336970329, -0.01750931888818741, 0.003918561618775129, 0.02006753906607628, 0.0034581462386995554, 0.08218500018119812, 0.04454128444194794, 0.011936631985008717, 0.017618894577026367, -0.01801111549139023, -0.058636412024497986, 0.069345623254776, 0.004058578982949257, 0.004560909699648619, -0.012026268988847733, 0.028934374451637268, -0.023444900289177895, 0.011558056809008121, 0.018354875966906548, 0.005637391936033964, 0.01707690767943859, 0.05951905623078346, -0.009916787967085838, -0.008822877891361713, -0.0019231047481298447, -0.01933217979967594, -0.03145166486501694, 0.000254159327596426, 0.04351005330681801, 0.0007224297150969505, 0.029201935976743698, -0.009470577351748943, -0.03532286733388901, 0.03508501499891281, 0.07479685544967651, -0.03275272995233536, 0.005799904465675354, -0.006908066105097532, 0.01757967658340931, -0.012278338894248009, -0.012262451462447643, 0.07910964637994766, 0.012872256338596344, -0.03549519181251526, 0.005809757858514786, 0.0028741362038999796, 0.010424505919218063, 0.04668322578072548, -0.010379384271800518, 0.037730924785137177, -0.021870624274015427, 0.031367745250463486, 0.08618482202291489, -0.02545994520187378, 0.005074679851531982, -0.004422585014253855, 0.04498937353491783, 0.060971807688474655, 0.001800506841391325, -0.048576634377241135, 0.03448956459760666, -0.01900392584502697, -0.02472917176783085, 0.050812989473342896, -0.027828307822346687, 0.01444551907479763, 0.013771666213870049, 0.018812013790011406, 0.013273446820676327, 0.024998776614665985, 0.0718783363699913, -0.020330039784312248, 0.015331420116126537, -0.015051644295454025, 0.035575445741415024, -0.07593776285648346, 0.03426690027117729, -0.052867062389850616, -0.016696911305189133, 0.032091185450553894, -0.09160538762807846, 0.02178816869854927, 0.0018256480107083917, -0.024083927273750305, -0.0915154367685318, 0.0023923316039144993, -0.010828772559762001, -0.060866232961416245, 2.92479910513066e-07, -0.020713461562991142, 0.010485968552529812, 0.014428211376070976, 0.05954175442457199, 0.04729597270488739, -0.09353893995285034, -0.02466076798737049, 0.008689716458320618, -0.01013919711112976, -0.05726886913180351, -0.007032272405922413, -0.00655131321400404, 0.00929183792322874, 0.03210969269275665, -0.05891313776373863, -0.02737061120569706, -0.01932383142411709, 0.006417475175112486, -0.016079066321253777, -0.024280596524477005, 0.004091300070285797, -0.021496938541531563, 0.004164175596088171, -0.004519943613559008, -0.027285348623991013, -0.010964114218950272, -0.02894153818488121, -0.02677288092672825, 0.004891320131719112, -0.04712659865617752, -0.07029957324266434, -0.0020790505222976208, 0.013652713038027287, -0.033764105290174484, 0.026283761486411095, -0.003954914398491383, -0.01139411237090826, 0.011092077940702438, -0.03529864549636841, 0.06599500775337219, 0.0013066603569313884, 0.016990402713418007, 0.0005275088478811085, 0.0436963252723217, 0.05052416771650314, 0.01420750841498375, 0.004034914076328278, 0.014669914729893208, 0.041958700865507126, -0.00026626154431141913, -0.015139704570174217, 0.03328131139278412, -0.03664623945951462, 0.0393449105322361, 0.01825617626309395, 0.013875668868422508, 0.04355604574084282, -0.03135828301310539, -0.021978970617055893, -0.06121668592095375, -0.02159101516008377, 0.029943257570266724, 0.019863352179527283, 0.07278992980718613, -0.06808106601238251, -0.0573340468108654, -0.*****************, 3.0208065049072308e-34, -0.008082753978669643, 0.****************, -0.024103039875626564, -0.013074083253741264, 0.007306948304176331, 0.006083275657147169, 0.019935306161642075, -0.***************, 0.*****************, 0.*****************, -0.*****************], "parsedContent": {"infobox": {}, "categories": [], "links": ["28 June", "2023", "Woodcutting", "Anima-infused bark", "Forestry event", "Woodcutting Leprechaun", "End of Rainbow", "Woodcutting", "anima-infused bark", "bank deposit box", "log basket", "Woodcutting", "RuneScape clock", "Logout button", "Forestry outfit", "Lumberjack outfit", "Leprechaun charm"], "plainText": "Woodcutting Leprechaun is a Forestry event where a Leprechaun spawns end of rainbows to players whilst chopping down a tree. Overview The goal of this event is for players to stand under the end of rainbows, as each rainbow spawned will grant a buff called <PERSON><PERSON><PERSON><PERSON><PERSON>'s luck. Each stack of the buff used will grant the player extra Woodcutting experience and anima-infused bark when cutting down different trees. Players who were actively cutting trees near the event when he appeared can use his services as a bank deposit box for all items in the inventory and contents inside the log basket. If players were not actively chopping trees when the event spawns, he will only allow players to deposit Woodcutting resources instead. Players are encouraged to stand under as many rainbows as possible, as this will increase the amount of extra experience and bark with any type of tree (except normal, dead, or any tree that is available at level 1 Woodcutting). How to play The length of this event is one minute and 12 seconds. This event also consists of six end of rainbows that spawn one by one in 10-12 seconds (or 16-20 ticks) increments during the event. These rainbows spawn up to a 10-block radius of the Leprechaun. As players stand in more ends of rainbows, the more of the Lep<PERSON>chaun's luck they receive. The amount of luck is determined by how many ends of rainbows the player stood under during the event: {| class=\"wikitable\" |+ !Rainbows !Luck |- |1 |2-3 |- |2 |4-5 |- |3 |7-8 |- |4 |9-10 |- |5 |12-13 |- |6 |14-15 |} The Leprechaun's luck buff is stackable up to 51 if players were to come in contact with this event again whilst having leftover luck. The amount of Leprechaun luck is shown on both the desktop and mobile versions of the official client, with the mobile version being in increments of five with a maximum of 255; however, the PC version considers a Leprechaun's luck to be the product of one successful skilling roll. There is no time limit on spending this buff as it will last between sessions. The Leprechaun will give a warning saying, '''''', giving players 38 ticks (or 22.8 seconds) and spawning the last end of rainbow in the process. After the player collects the buff, players can chop any tree (except for normal, dead, or any Woodcutting Level 1 specific tree) and earn between 22 and 23 bark with a scaled amount of Woodcutting experience, according to the skill level of the tree. Below is the equation that models the Woodcutting experience based on the Woodcutting level and base experience of obtaining a log from a tree: <math>Bonus\\, experience\\, \\thickapprox (1.5 * Woodcutting\\, Level\\,) + Base\\, XP\\, of\\, the\\, tree</math>  Players can earn an increase in Woodcutting experience bonus with either the Forestry or Lumberjack outfit, as any visible (or invisible) Woodcutting boost can stack with the bonus experience effect from earning Leprechaun's luck. Changes"}, "originalContent": "{{Otheruses|the Forestry Event|the NPC variant|Woodcutting Leprechaun}}\n{{Infobox Activity\n|name = Woodcutting Leprechaun\n|image = [[File:Forestry - woodcutting leprechaun.png|250px]]\n|release = [[28 June]] [[2023]]\n|update = Forestry: The Way of the Forester - Part One\n|type = Forestry\n|members = Yes\n|location = Various\n|skills = [[Woodcutting]]\n|currency = [[Anima-infused bark]]\n|map = No\n|leagueRegion = No\n}}\n'''Woodcutting Leprechaun''' is a [[Forestry event]] where a [[Woodcutting Leprechaun|Leprechaun]] spawns [[End of Rainbow|end of rainbows]] to players whilst chopping down a tree.\n\n==Overview==\nThe goal of this event is for players to stand under the end of rainbows, as each rainbow spawned will grant a buff called <PERSON><PERSON><PERSON><PERSON><PERSON>'s luck. Each stack of the buff used will grant the player extra [[Woodcutting]] experience and [[anima-infused bark]] when cutting down different trees. Players who were actively cutting trees near the event when he appeared can use his services as a [[bank deposit box]] for all items in the inventory and contents inside the [[log basket]]. If players were not actively chopping trees when the event spawns, he will only allow players to deposit [[Woodcutting]] resources instead. Players are encouraged to stand under as many rainbows as possible, as this will increase the amount of extra experience and bark with any type of tree (except normal, dead, or any tree that is available at level 1 Woodcutting){{fact|Leprechaun's luck works with player-grown/FG trees, need confirmation if you can get these bonuses on Blisterwood trees}}.\n\n==How to play==\nThe length of this event is one minute and 12 seconds. This event also consists of six end of rainbows that spawn one by one in 10-12 seconds (or 16-20 [[RuneScape clock|ticks]]) increments during the event. These rainbows spawn up to a 10-block radius of the Leprechaun. As players stand in more ends of rainbows, the more of the Leprechaun's luck they receive. The amount of luck is determined by how many ends of rainbows the player stood under during the event:\n{| class=\"wikitable\"\n|+\n!Rainbows\n!Luck\n|-\n|1\n|2-3\n|-\n|2\n|4-5\n|-\n|3\n|7-8\n|-\n|4\n|9-10\n|-\n|5\n|12-13\n|-\n|6\n|14-15\n|}\n\nThe Leprechaun's luck buff is stackable up to 51 if players were to come in contact with this event again whilst having leftover luck. The amount of Leprechaun luck is shown on both the desktop and mobile versions of the official client, with the mobile version being in increments of five with a maximum of 255; however, the PC version considers a Leprechaun's luck to be the product of one successful skilling roll. There is no time limit on spending this buff as it will last between [[Logout button|sessions]].\n\nThe Leprechaun will give a warning saying, '''{{overhead|I'll have to go shortly...}}''', giving players 38 ticks (or 22.8 seconds) and spawning the last end of rainbow in the process. After the player collects the buff, players can chop any tree (except for normal, dead, or any Woodcutting Level 1 specific tree) and earn between 22 and 23 bark with a scaled amount of Woodcutting experience, according to the skill level of the tree. Below is the equation that models the Woodcutting experience based on the Woodcutting level and base experience of obtaining a log from a tree:\n\n<math>Bonus\\, experience\\, \\thickapprox (1.5 * Woodcutting\\, Level\\,) + Base\\, XP\\, of\\, the\\, tree</math> \n\nPlayers can earn an increase in Woodcutting experience bonus with either the [[Forestry outfit|Forestry]] or [[Lumberjack outfit]], as any visible (or invisible) Woodcutting boost can stack with the bonus experience effect from earning Leprechaun's luck.\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 14 February 2024\n|update = Barbarian Training, Forestry Changes & More\n|change =\n* [[Leprechaun charm]]s are no longer required to trigger the event.\n* Players who are not present when an event spawns can only use the Leprechaun to deposit Woodcutting resources.\n}}\n{{Subject changes\n|date = 5 July 2023\n|update = Forestry: Part One - Feedback Changes\n|change = The Woodcutting Leprechaun will now take Teak, Mahogany, and Arctic Pine Logs to the player's bank, if the player is eligible for the event.\n}}\n{{Subject changes footer}}\n\n{{Forestry}}"}