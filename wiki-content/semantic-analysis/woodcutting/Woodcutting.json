{"title": "Woodcutting", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [{"type": "category", "value": "Free-to-play skills", "confidence": 0.9, "source": "category"}], "relationships": [{"from": "Woodcutting", "to": "4 January", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "2001", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Skills", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Fletching", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Firemaking", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Construction", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "icon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry kit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry Shop", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Draynor Village", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Seers' Village", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Members", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Farming", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Woodcutting Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Bucket of sap", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "knife", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bucket", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Tree (Tirannwn)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Tirannwn", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Dying tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Wilderness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Varrock", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Lumber Yard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Dead tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Charc<PERSON>l", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Gielinor", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Fishing Hamlet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Lava Dragon Isle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Wilderness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "charcoal", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Burnt tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Charc<PERSON>l", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Fossil Island Volcano", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Evergreen tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Falador", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Dwarven Mine", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Barbarian Village", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Dragontooth Island", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Bucket of sap", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "knife", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bucket", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Jungle tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Feldip Hunter area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Kharazi Jungle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Ape Atoll", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "RuneScape Classic", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Achey tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Big Chompy Bird Hunting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Ogre bow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ogre arrow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Light Jungle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Machete (weapon)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>i Cleanup", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "broodoo victim", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "mosquito swarm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "tribesman", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bush snake", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "jungle spider", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Oak tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ticks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Medium Jungle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Machete (weapon)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>i Cleanup", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "broodoo victim", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "mosquito swarm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "tribesman", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bush snake", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "jungle spider", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Willow tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ticks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Port Sarim", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Draynor Village", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Seers' Village", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Champions' Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Teak tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ticks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "teak logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Construction", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Castle Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Ape Atoll", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Hardwood Grove", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Dense Jungle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Machete (weapon)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>i Cleanup", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "broodoo victim", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "mosquito swarm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "tribesman", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bush snake", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "jungle spider", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Mature juniper tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "charcoal furnace", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "juniper charcoal", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "dynamite", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "blast mine", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Lovakengj", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Maple tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ticks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Kandarin", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Fremennik Province", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Corsair Cove Resource Area", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Woodcutting Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Kandarin Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Hollow tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ticks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Haunted Woods", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bark", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Splitbark armour", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Mahogany tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ticks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "mahogany logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Construction", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Hardwood Grove", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Ape Atoll", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Kharazi Jungle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Farming Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Fossil Island", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Arctic pine tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ticks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON>not", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Firemaking", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Yew tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ticks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Blisterwood tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Arboretum (location)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Sulliuscep", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Mushroom", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "sulliuscep cap", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Mort myre fungus", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "unidentified fossil", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Tar Swamp", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Fossil Island", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Magic tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ticks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Mage Training Arena", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Sorcerer's Tower", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Vale totems", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Tree Gnome Stronghold", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Woodcutting Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Redwood tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ticks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Woodcutting Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Farming Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "yommi tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Legends' Quest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "magic logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "magic tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "swaying tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "branch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "The Fremennik Trials", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "dramen tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "dramen branch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Lost City", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Recipe for Disaster/Freeing Sir <PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Recipe for Disaster", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Dream tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "dream log", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Lunar Diplomacy", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Undead tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "undead twigs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Animal Magnetism", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "blessed axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "windswept tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "windswept logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>'s Quest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Magic beans", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Grim Tales", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "phren bark", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "The Gauntlet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Corrupt Phren Roots", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "The Gauntlet#Corrupted Gauntlet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Scrapey tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "scrapey tree logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Trouble Brewing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON>a roots", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bruma root", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Wintertodt", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "meat tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Chambers of Xeric", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON> (Chambers of Xeric)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Kindling (Chambers of Xeric)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ice demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Chambers of Xeric", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Tree roots (Forestry)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Rising roots", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Grand Exchange", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Grand Exchange", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Grand Exchange", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Grand Exchange", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Iron axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Undead tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Animal Magnetism", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Slayer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Special attacks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Special attacks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "crystal shard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Song of the Elves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forester's rations", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Iron felling axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Special attacks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Special attacks", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "crystal shard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Song of the Elves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Machete", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Light jungle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Medium jungle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Dense jungle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry Shop", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Temple Trekking", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Temple Trekking", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "In Aid of the Myreque", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Lumberjack outfit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry outfit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry Shop", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Kandarin Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Kandarin Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Kandarin Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Kandarin Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bird nest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry Shop", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bird's egg", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "crystal axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "celestial signet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Sulliuscep cap", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry kit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Log brace", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry Shop", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Anima-infused bark", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Oak logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Willow logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Animal magnetism", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "skilling success rate", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Attack", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "RuneScape clock", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Oak tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Willow tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Teak tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Maple tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Hollow tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Mahogany tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Arctic pine tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Yew tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Magic tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Redwood tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "beaver", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Gielinor", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bird nest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bird's egg", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "crushed nest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Saradomin brew", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Herblore", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "clue nest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "clue scroll", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Woodcutting Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "members", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Invisible boost", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Gielinor", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Yew tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "magic tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "sawmill", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "bank", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "redwood tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Farming Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>'s Chop-chop Shop", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Old School RuneScape", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "rune axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Ents", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry kit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "anima-infused bark", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "leaves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry Shop", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Forestry", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Woodcutting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Struggling sapling (Forestry event)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Farming", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Rising roots", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Friendly Ent (Forestry event)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Fletching", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Bird nest (egg)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Beehive (Forestry event)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Construction", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Sturdy beehive parts", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Flowering Tree (Forestry event)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Strange fruit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Pheasant Control", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Thieving", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Pheasant tail feathers", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Golden pheasant egg", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Poachers (Forestry event)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON> whistle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Enchantment Ritual", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Petal garland", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Wood<PERSON><PERSON> (Forestry event)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Canoes", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "River Lum", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Edgeville", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "amulet of glory", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Wilderness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Black chincho<PERSON> (Hunter)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "thatch spar", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "trading stick", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "gout tuber", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "proboscis", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "snake hide", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "uncut opal", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "uncut jade", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "uncut red topaz", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "tribal mask", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "teak tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "mahogany tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "yew tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "magic tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "crystal shard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Wilderness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Woodcutting Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Bank note", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Wilderness Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "tree patch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Special patches#Special tree patches", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Fossil Island", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Crystal tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "crystal tree patch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Celastrus tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "celastrus patch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "redwood tree patch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Farming Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "tick", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Tick manipulation#1.5-tick <PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Jungle bush", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "jungle tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Kharazi jungle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Dead tree (Mountain Daughter)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Shining pool", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Tendrils (Abyss)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Abyss", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Thick vines", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Tar Swamp", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Vines (Brimhaven Dungeon)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Brimhaven Dungeon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Maple tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Miscellania", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Throne of Miscellania", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "teak tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "mahogany tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Etceteria", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Royal Trouble", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "evergreen tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Kandarin Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Seers' Village", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "fruit tree patch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Woodcutting stump", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON>not", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "split log", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Neitiznot shield", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "arctic pine logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "beaver", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "pet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "ent", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON> root", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "The Ribbiting Tale of a Lily Pad Labour Dispute", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Animal Magnetism", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Lost City", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "The Fremennik Trials", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "The Eyes of Glouphrie", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>'s Quest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Legends' Quest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "A Kingdom Divided", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Lunar Diplomacy", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "The Fremennik Isles", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Sins of the Father", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Song of the Elves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Grim Tales", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Enlightened Journey", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Recipe for Disaster", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Recipe for Disaster/Freeing Skrach Uglogwee", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Heroes' Quest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON>'s Friend", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "The Ribbiting Tale of a Lily Pad Labour Dispute", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Animal Magnetism", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "The Eyes of Glouphrie", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "The Fremennik Trials", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON><PERSON><PERSON>'s <PERSON> Helper", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "The Fremennik Isles", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Song of the Elves", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Grim Tales", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "crystal shard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Woodcutting Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Farming", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "rune axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "dragon axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Skilling success rate", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "magic tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Woodcutting/Experience table", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Woodcutting/Level up table", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting", "to": "Free-to-play skills", "type": "belongs_to_category", "confidence": 0.9, "source": "category"}], "embedding": [-0.0026488492731004953, -0.09800516813993454, 4.968863868271001e-05, 0.07341083139181137, -0.058700080960989, 0.0046583013609051704, -0.05094129964709282, 0.029990389943122864, -0.10671154409646988, -0.02421087585389614, -0.013221652247011662, 0.04121916741132736, 0.03739989921450615, -0.0105885686352849, -0.01913897506892681, -0.06114547327160835, 0.007688114419579506, 0.002504294505342841, 0.027397917583584785, -0.02541864849627018, 0.006753707770258188, 0.021923109889030457, 0.012927733361721039, -0.00266494182869792, -0.0836983174085617, -0.07305970042943954, -0.0031132898293435574, 0.020514488220214844, 0.017270095646381378, 0.012585651129484177, -0.03637521341443062, -0.02361573837697506, -0.02678467147052288, 0.0005072057247161865, 2.2121696474641794e-06, -0.00948944129049778, -0.014942377805709839, -0.001468682661652565, -0.07514625042676926, -0.022134186699986458, 0.04701033979654312, 0.011175921186804771, 0.014087180607020855, 0.0049354503862559795, -0.04138221964240074, 0.025892077013850212, 0.018159164115786552, 0.008369632996618748, -0.012493961490690708, 0.00031597388442605734, 0.024423332884907722, -0.03844097629189491, -0.026774544268846512, -0.0209912471473217, 0.09893228113651276, 0.06879265606403351, 0.015662575140595436, -0.04070451855659485, 0.007844526320695877, 0.0481170117855072, -0.059560488909482956, -0.04813579469919205, 0.03453477472066879, 0.019081734120845795, 0.02900151163339615, 0.023086214438080788, -0.07450919598340988, -0.054351095110177994, 0.04743367061018944, -0.03962268680334091, -0.03149760141968727, -0.011067906394600868, 0.01951395906507969, -0.02860322967171669, -0.01738235354423523, -0.02707190066576004, -0.0011176065308973193, 0.04059913754463196, -0.016064463183283806, -0.007564592640846968, 0.03823598101735115, -0.007896282710134983, -0.005158904008567333, -0.0035239316057413816, 0.09888947755098343, -0.030382439494132996, -0.014304418116807938, 0.030582251027226448, 0.005664016120135784, -0.026548808440566063, -0.017426392063498497, -0.036481376737356186, -0.07071592658758163, 0.03471008688211441, -0.022944185882806778, 0.0033528227359056473, -0.021813953295350075, 0.12867213785648346, 0.030215326696634293, 0.07532566040754318, -0.0743778869509697, -0.008714097552001476, 0.029125457629561424, 0.030920976772904396, -0.024499284103512764, -0.08880315721035004, 0.061322469264268875, 0.0644901916384697, 0.007898329757153988, -0.015446596778929234, -0.01990536041557789, -0.024343524128198624, 0.0023962771520018578, 0.008557523600757122, 0.024264972656965256, -0.011891879141330719, 0.012410440482199192, -0.03231493756175041, -0.037278398871421814, -0.03842869773507118, 0.07482776045799255, -0.0006122431950643659, -0.024580342695116997, -0.017335331067442894, 0.036011770367622375, -0.033083830028772354, -0.018536312505602837, 0.005134416278451681, 0.012502926401793957, 0.028213180601596832, -0.027403749525547028, 0.0706145390868187, 0.009920833632349968, -0.009566019289195538, 0.04307805746793747, -0.008528021164238453, 0.0013278262922540307, 0.025080697610974312, -0.013020415790379047, -0.045240819454193115, 0.003981109708547592, 0.026011019945144653, -0.01895664818584919, -0.041649315506219864, -0.045181646943092346, -0.05312633141875267, -0.013429277576506138, -0.01470614317804575, 0.00246964069083333, 0.02079690247774124, -0.0774768516421318, 0.005150842014700174, 0.002159241819754243, 0.00896964780986309, 0.00721858162432909, 0.003390869125723839, -0.03259919956326485, 0.024659566581249237, 0.017776669934391975, 0.06705532968044281, 0.005029805935919285, 0.009045653976500034, 0.0006229700520634651, -0.022103069350123405, -0.012510586529970169, -0.04291929677128792, 0.0059500024653971195, -0.015912897884845734, -0.07963396608829498, -0.06722816079854965, 0.04466794431209564, -0.035118862986564636, -0.038233399391174316, -0.0031042268965393305, -0.00983747374266386, 0.08789753168821335, 0.11802682280540466, 0.044210031628608704, 0.007879440672695637, -0.0013472199207171798, -0.05930382385849953, -0.06455705314874649, 0.009287036955356598, 0.016526460647583008, -0.026199499145150185, 0.022925999015569687, -0.06528956443071365, 0.005254463292658329, -0.02756348066031933, -0.01656564138829708, -0.008619333617389202, 0.01771208457648754, -0.0408458337187767, -0.01444800291210413, 0.008191267028450966, 0.0012809298932552338, -0.012646729126572609, -0.035063181072473526, -0.03578832745552063, 0.027331329882144928, 0.0339234322309494, -0.020645789802074432, -0.07315093278884888, -0.033992525190114975, -0.029886528849601746, -0.027954744175076485, 0.02539811097085476, 0.04627241939306259, -0.037994254380464554, -0.0023797405883669853, -0.042058419436216354, -0.020064078271389008, -0.00383290508762002, 0.052171848714351654, -0.016795184463262558, 0.015383530408143997, -0.013239490799605846, 0.02566552348434925, -0.019509093835949898, 0.034326277673244476, -0.001265172497369349, -0.08706602454185486, -0.00031111916177906096, -0.028302663937211037, -0.03567763790488243, -0.0011199411237612367, -0.007521667052060366, 0.01774311065673828, -0.05086195096373558, -0.02797022834420204, -0.015455941669642925, -0.00024056568508967757, 0.0419631227850914, 0.027048354968428612, -0.005013220012187958, 0.049129582941532135, 0.050013117492198944, 0.07270423322916031, 0.015686582773923874, -0.041119735687971115, -0.0532984659075737, 0.049875807017087936, 0.008330817334353924, -0.010811748914420605, -0.008506743237376213, -0.005302202422171831, 0.05012422427535057, -0.005303062032908201, -0.02983139641582966, -0.007612444460391998, -0.02598893642425537, 0.008302192203700542, 0.05854109302163124, 0.01887333206832409, 0.0034767957404255867, -0.03583022952079773, -0.08662650734186172, -0.004781841300427914, -0.05928056687116623, 0.031216861680150032, 0.0638674944639206, -0.05955775827169418, -0.03156738355755806, -0.03820709511637688, 0.10237661749124527, -0.0292684193700552, 0.024032363668084145, 0.028423743322491646, 0.024963434785604477, -0.007939895614981651, 0.0677548199892044, 0.0499429889023304, -0.006268898956477642, -0.04249634966254234, 0.005495695397257805, 0.03652123361825943, -0.004457393195480108, -0.001832996727898717, 0.017970332875847816, -0.0007141707465052605, -0.011705459095537663, 0.026951123028993607, 0.007499310653656721, 0.023895077407360077, 0.022420570254325867, -0.013482666574418545, 0.0014937027590349317, -0.020517738536000252, -0.02566852793097496, 0.056722983717918396, -0.021486926823854446, -0.008283044211566448, 0.029254071414470673, -0.006123425904661417, -0.020980115979909897, 0.028506439179182053, 0.05271609127521515, 0.029158631339669228, -0.04646248742938042, -0.021401168778538704, -0.013187306001782417, 0.018103227019309998, 0.0444040484726429, 0.0025069648399949074, 0.037879958748817444, 0.04956280067563057, 0.021661221981048584, -0.07725968956947327, 0.031836479902267456, -0.022663136944174767, 0.03466596454381943, 0.0069654714316129684, 0.02628697268664837, 0.030046461150050163, 0.026347367092967033, -0.01969415321946144, -0.004988101776689291, -0.02556767500936985, 0.002864653477445245, -0.013496940955519676, -0.027157466858625412, -0.0025673655327409506, -0.010808365419507027, -0.010075698606669903, -0.021889491006731987, 0.008947296999394894, 0.00493540009483695, 0.029490677639842033, 0.08565042167901993, 0.03050350211560726, 0.014002537354826927, -0.05789269506931305, -0.023462001234292984, 0.0522456094622612, 0.005929951556026936, 0.09130781143903732, -0.022505903616547585, -0.11914686858654022, -0.025225648656487465, 0.00045619963202625513, -0.027674661949276924, 0.005660072900354862, 0.02049943432211876, -0.05214456096291542, 0.009849128313362598, -0.0004992781323380768, 0.033287372440099716, 0.013047141954302788, 0.04530235007405281, 0.00862505380064249, 0.05528174713253975, 0.01477662194520235, -0.028076019138097763, -0.007726465817540884, 0.007750398013740778, 0.048891741782426834, 0.018473580479621887, -0.04708756133913994, 0.03372049331665039, 0.007307644933462143, 0.05607748404145241, 0.020689304918050766, 0.034906815737485886, -0.02714213915169239, 0.04440495744347572, -0.01649356074631214, 0.0021810289472341537, 0.002911694347858429, -0.02187320403754711, 0.007840011268854141, 0.0205118115991354, 0.0355047881603241, -0.03657820075750351, 0.022061960771679878, 0.03246965631842613, 0.002184175653383136, -0.006923749577254057, -0.07014959305524826, 0.026525046676397324, 0.020228726789355278, 0.03665725886821747, -0.04741978272795677, -0.0028598816134035587, -0.011627554893493652, 0.0293441079556942, 0.03459794819355011, -0.01614384539425373, 0.028333017602562904, -0.048794858157634735, 0.049793411046266556, 0.005685335025191307, -0.07203815132379532, -0.07729901373386383, 0.01114464458078146, -0.01148657314479351, -0.011657049879431725, -0.05452696233987808, -0.04425965994596481, -0.032707322388887405, 0.060489069670438766, 0.03043551929295063, -0.06656695157289505, 0.004327150993049145, 0.011579776182770729, 0.017991360276937485, 0.031042758375406265, 0.029169974848628044, 0.022955644875764847, 0.03478562831878662, 0.06637628376483917, 0.036827459931373596, 0.05838529020547867, -0.0005637098802253604, -0.06938721984624863, 0.008396160788834095, 0.014699889346957207, 0.018902622163295746, 0.019096776843070984, 0.017054308205842972, 0.029358934611082077, -0.06867317855358124, 0.010074048303067684, 0.06306522339582443, 0.0005422901012934744, 0.025569789111614227, 0.02449588105082512, -0.010534493252635002, 0.03366541489958763, 0.0141525873914361, -0.01217903383076191, 0.01555375475436449, -0.037262264639139175, -0.04087510704994202, 0.004122268874198198, 0.014630168676376343, -0.08879289031028748, 0.03986980766057968, -0.0266323983669281, -0.02201399765908718, -0.02042369544506073, -0.040732767432928085, 0.07184569537639618, -0.027689332142472267, -0.03689950332045555, 0.007458187174052, -0.07674669474363327, -0.0021026059985160828, 0.0009239012142643332, -0.0502140037715435, -0.00896786991506815, -0.0093431007117033, -0.005177677609026432, 0.035977840423583984, 0.04854165017604828, -0.031133709475398064, -0.01939035952091217, -0.04483431950211525, 0.004514198750257492, -0.0035615924280136824, -0.041820231825113297, 0.053081512451171875, 0.021372411400079727, 0.009303187020123005, 0.005918588954955339, -0.017442984506487846, -0.05973821505904198, -0.010345911607146263, -0.011305298656225204, 0.02294531837105751, -0.00913578923791647, -0.0458420030772686, 0.009934001602232456, 0.03919628635048866, 0.029644664376974106, 0.045354049652814865, 0.057367630302906036, 0.009049776941537857, -0.051457133144140244, 0.055875398218631744, -0.03442054241895676, 0.01104725245386362, -0.0003436623082961887, 0.013963288627564907, -0.00014060421381145716, 0.00011477771477075294, 0.011342561803758144, 0.03472662344574928, -0.0449589379131794, 0.04117768257856369, 0.004577914718538523, 0.005905922502279282, -0.041401274502277374, 0.005566008388996124, 0.05437009409070015, 0.06935524195432663, 0.027554012835025787, -0.04880828782916069, -0.003599219024181366, 0.054743919521570206, -0.03322257846593857, -0.03813045099377632, 0.0357639342546463, 0.034020282328128815, -0.02676444500684738, -0.0011327130487188697, 0.04686454311013222, -0.002275022678077221, 0.012455740012228489, -0.013205738738179207, -0.03009103797376156, 0.007058828603476286, -0.04214974865317345, 0.015042147599160671, -0.13686764240264893, 0.04809126257896423, 0.021676495671272278, 0.05693642422556877, -0.048901017755270004, -0.034207746386528015, -0.008702307939529419, 0.0006520651513710618, -0.008736998774111271, -0.0015685232356190681, 0.009840160608291626, -0.03771921619772911, -0.06234032288193703, 0.009515836834907532, -0.0007903210935182869, -0.0329461470246315, 0.01166940201073885, 0.0192884411662817, 0.037412095814943314, 0.0204192865639925, -0.03326835110783577, -0.03518250957131386, 0.05476168170571327, 0.0632733628153801, 0.0137418108060956, 0.013889461755752563, -0.018138719722628593, -0.023364631459116936, 0.01750556193292141, 0.05325408652424812, 0.017622746527194977, 0.0029235761612653732, 0.010792343877255917, -0.027326302602887154, 0.005765767302364111, -0.056608766317367554, 0.03177423030138016, -0.009631525725126266, 0.0005849794251844287, -0.024688929319381714, 0.05202455073595047, 0.00391394691541791, -6.0065094424210464e-33, -0.008943067863583565, 0.00061215297318995, 0.0056414916180074215, 0.06525330245494843, 0.009964670985937119, 0.0017259999876841903, 0.04841865226626396, 0.015271808952093124, -0.011224250309169292, -0.010120145976543427, 0.0026027271524071693, 0.00719788484275341, -0.0024440574925392866, -0.0012019956484436989, 0.002890596864745021, 0.0030242549255490303, 0.013637163676321507, -0.0073558371514081955, 0.001358226640149951, -0.012234954163432121, -0.0482870452105999, -0.006953998003154993, 0.07616005092859268, 0.07065602391958237, -0.02444954216480255, 0.01381867378950119, -0.0017505636205896735, -0.00907151959836483, -0.004012718331068754, -0.040201906114816666, 0.021750709041953087, -0.04005620628595352, -0.002874853787943721, -0.034098222851753235, -0.020023642107844353, -0.002419454511255026, -0.02572530135512352, -0.02669825218617916, -0.02342086099088192, 0.024229831993579865, -0.019459128379821777, -0.002250413876026869, 0.0057191248051822186, 0.06950840353965759, 0.05037708580493927, -0.027556413784623146, 0.023996323347091675, 0.0036765742115676403, -0.01957576721906662, 0.07171453535556793, 0.03015967831015587, 0.016703976318240166, 0.0012891181977465749, -0.07948826253414154, -0.028144819661974907, 0.038699693977832794, -0.01213887520134449, -0.016319872811436653, -0.07638011872768402, 0.04860679432749748, 0.012451110407710075, -0.011624488979578018, 0.005344163626432419, 0.04737359285354614, -0.018256735056638718, 0.00795701052993536, -0.05523621290922165, -0.034167833626270294, 0.08950808644294739, -0.04134238511323929, -0.021604737266898155, 0.07276048511266708, 0.007612486835569143, 0.013326495885848999, 0.05415160208940506, -0.002790507860481739, -0.025778699666261673, 0.04596727713942528, 0.00672860536724329, 0.05652318894863129, 0.005904474761337042, 0.025031231343746185, 0.031573861837387085, 0.0011599176796153188, -0.022458547726273537, 0.019738681614398956, -0.010138020850718021, 0.035580236464738846, -0.01613839901983738, 0.025124233216047287, -0.010117626748979092, 0.02906760200858116, -0.01848478429019451, 0.022340213879942894, 0.009221791289746761, 0.0727124810218811, 0.04278217628598213, -0.0016080262139439583, -0.023379158228635788, -0.05337165296077728, 0.02115372195839882, 0.040201716125011444, -0.03173433616757393, -0.02924247644841671, -0.0080832215026021, -0.033483561128377914, -0.015414386987686157, 0.01346778217703104, -0.040605995804071426, 0.01701119914650917, 0.014210603199899197, 0.007530319504439831, -0.032905105501413345, 0.002309567993506789, 0.08001510053873062, -0.015382839366793633, -0.0013495665043592453, -0.01184065081179142, 0.008076504804193974, -0.06755132228136063, -0.008082532323896885, -0.015659283846616745, -0.03550955653190613, -0.025481531396508217, 0.0005592696252278984, -0.007932964712381363, 0.016181593760848045, -0.03880604729056358, -0.05498631298542023, 0.0037734482903033495, 0.010547138750553131, -0.0141092948615551, 2.8381705874380714e-07, 0.020802443847060204, 0.029559900984168053, -0.0023628368508070707, -0.024800891056656837, 0.033654749393463135, -0.03417418152093887, -0.007099922280758619, 0.007989143952727318, 0.018221741542220116, -0.03930564224720001, 0.04102364555001259, 0.02984524331986904, -0.010343984700739384, -0.027254361659288406, 0.014129036106169224, 0.01279478706419468, -0.048454705625772476, -0.01661105640232563, -0.014553251676261425, -0.03830369561910629, -0.05290332809090614, -0.03498158976435661, -0.036794263869524, 0.008076420985162258, -0.0294540636241436, 0.01654268614947796, -0.004607112612575293, -0.056881338357925415, 0.06624085456132889, -0.1050126701593399, -0.09142538160085678, -0.011475820094347, 0.02209005132317543, -0.06520234048366547, 0.007255082484334707, -0.008538639172911644, -0.0070165167562663555, 0.00704589206725359, -0.03863322362303734, 0.09587707370519638, -0.034254271537065506, 0.058514755219221115, -0.03173927590250969, 0.040609169751405716, 0.029929719865322113, -0.050120677798986435, -0.011761574074625969, -0.053016290068626404, 0.020260155200958252, 0.027017660439014435, 0.00545153021812439, 0.029644818976521492, -0.04046403616666794, 0.010658267885446548, 0.02020150236785412, -0.01101066917181015, 0.011680429801344872, 0.028926298022270203, 0.020542388781905174, -0.01768507994711399, -0.03174292668700218, 0.004695458337664604, -0.003179675666615367, 0.01998913660645485, -0.018840383738279343, 0.03650185838341713, -0.09573148190975189, 2.417042989377393e-34, -0.02120380848646164, 0.055827319622039795, -0.031963348388671875, -0.005311060231178999, 0.010926028713583946, 0.01153222844004631, 0.04839583858847618, -0.04857506975531578, -0.014917852357029915, 0.041301436722278595, -0.027088604867458344], "parsedContent": {"infobox": {}, "categories": ["Free-to-play skills"], "links": ["4 January", "2001", "Skills", "Fletching", "Firemaking", "Construction", "axe", "icon", "Forestry", "Forestry kit", "Forestry Shop", "Draynor Village", "Seers' Village", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Members", "Farming", "Woodcutting Guild", "Tree", "Bucket of sap", "knife", "bucket", "Tree (Tirannwn)", "Tirannwn", "Dying tree", "Wilderness", "Varrock", "Lumber Yard", "Dead tree", "Logs", "Charc<PERSON>l", "Gielinor", "Fishing Hamlet", "Lava Dragon Isle", "Wilderness", "charcoal", "Burnt tree", "Charc<PERSON>l", "Fossil Island Volcano", "Evergreen tree", "Logs", "Falador", "Dwarven Mine", "Barbarian Village", "<PERSON><PERSON>", "Dragontooth Island", "Bucket of sap", "knife", "bucket", "Jungle tree", "Feldip Hunter area", "Kharazi Jungle", "Ape Atoll", "RuneScape Classic", "Achey tree", "Big Chompy Bird Hunting", "Ogre bow", "ogre arrow", "Light Jungle", "Machete (weapon)", "<PERSON>", "<PERSON>i Cleanup", "broodoo victim", "mosquito swarm", "tribesman", "bush snake", "jungle spider", "Oak tree", "ticks", "Medium Jungle", "Machete (weapon)", "<PERSON>", "<PERSON>i Cleanup", "broodoo victim", "mosquito swarm", "tribesman", "bush snake", "jungle spider", "Willow tree", "ticks", "Port Sarim", "Draynor Village", "Seers' Village", "Champions' Guild", "<PERSON>", "Teak tree", "ticks", "teak logs", "Construction", "Castle Wars", "<PERSON><PERSON>", "Ape Atoll", "Hardwood Grove", "Dense Jungle", "Machete (weapon)", "<PERSON>", "<PERSON>i Cleanup", "broodoo victim", "mosquito swarm", "tribesman", "bush snake", "jungle spider", "Mature juniper tree", "<PERSON><PERSON><PERSON>", "charcoal furnace", "juniper charcoal", "dynamite", "blast mine", "Lovakengj", "Maple tree", "ticks", "Kandarin", "Fremennik Province", "Corsair Cove Resource Area", "Woodcutting Guild", "Kandarin Diary", "Hollow tree", "ticks", "<PERSON><PERSON><PERSON>", "Haunted Woods", "bark", "Splitbark armour", "Mahogany tree", "ticks", "mahogany logs", "Construction", "Hardwood Grove", "Ape Atoll", "Kharazi Jungle", "Farming Guild", "Fossil Island", "Arctic pine tree", "ticks", "<PERSON><PERSON><PERSON>not", "<PERSON><PERSON><PERSON>", "Firemaking", "Yew tree", "ticks", "Blisterwood tree", "Arboretum (location)", "<PERSON><PERSON>", "Sulliuscep", "Mushroom", "sulliuscep cap", "Mort myre fungus", "unidentified fossil", "Tar Swamp", "Fossil Island", "Magic tree", "ticks", "Mage Training Arena", "Sorcerer's Tower", "<PERSON><PERSON><PERSON>", "Vale totems", "<PERSON><PERSON>", "Tree Gnome Stronghold", "Woodcutting Guild", "<PERSON><PERSON><PERSON>", "Redwood tree", "ticks", "Woodcutting Guild", "Farming Guild", "yommi tree", "Legends' Quest", "magic logs", "magic tree", "swaying tree", "branch", "The Fremennik Trials", "dramen tree", "dramen branch", "Lost City", "Recipe for Disaster/Freeing Sir <PERSON><PERSON>", "Recipe for Disaster", "Dream tree", "dream log", "Lunar Diplomacy", "Undead tree", "undead twigs", "Animal Magnetism", "blessed axe", "windswept tree", "windswept logs", "<PERSON>'s Quest", "Magic beans", "Grim Tales", "<PERSON><PERSON>", "phren bark", "The Gauntlet", "Corrupt Phren Roots", "The Gauntlet#Corrupted Gauntlet", "Scrapey tree", "scrapey tree logs", "Trouble Brewing", "<PERSON><PERSON>a roots", "bruma root", "Wintertodt", "meat tree", "<PERSON><PERSON><PERSON><PERSON>", "Chambers of Xeric", "<PERSON><PERSON> (Chambers of Xeric)", "Kindling (Chambers of Xeric)", "ice demon", "Chambers of Xeric", "Tree roots (Forestry)", "Rising roots", "Grand Exchange", "Grand Exchange", "Grand Exchange", "Grand Exchange", "Iron axe", "Undead tree", "Animal Magnetism", "Slayer", "Special attacks", "Special attacks", "crystal shard", "Song of the Elves", "Forester's rations", "Iron felling axe", "Special attacks", "Special attacks", "crystal shard", "Song of the Elves", "Machete", "Light jungle", "Medium jungle", "Dense jungle", "<PERSON><PERSON><PERSON>", "Forestry Shop", "Temple Trekking", "Temple Trekking", "In Aid of the Myreque", "Lumberjack outfit", "Forestry outfit", "Forestry Shop", "Kandarin Diary", "Kandarin Diary", "Kandarin Diary", "Kandarin Diary", "bird nest", "Forestry Shop", "bird's egg", "crystal axe", "celestial signet", "Sulliuscep cap", "Forestry kit", "Log brace", "Forestry Shop", "Anima-infused bark", "Oak logs", "Willow logs", "Animal magnetism", "skilling success rate", "Attack", "RuneScape clock", "Oak tree", "Willow tree", "Teak tree", "Maple tree", "Hollow tree", "Mahogany tree", "Arctic pine tree", "Yew tree", "Magic tree", "Redwood tree", "beaver", "Gielinor", "bird nest", "bird's egg", "crushed nest", "Saradomin brew", "Herblore", "clue nest", "clue scroll", "Woodcutting Guild", "<PERSON><PERSON><PERSON>", "members", "Invisible boost", "Gielinor", "Yew tree", "magic tree", "sawmill", "bank", "redwood tree", "Farming Guild", "<PERSON>'s Chop-chop Shop", "Old School RuneScape", "rune axe", "Ents", "Forestry", "Forestry kit", "anima-infused bark", "leaves", "Forestry Shop", "Forestry", "Woodcutting", "Struggling sapling (Forestry event)", "Farming", "Rising roots", "Friendly Ent (Forestry event)", "Fletching", "Bird nest (egg)", "Beehive (Forestry event)", "Construction", "Sturdy beehive parts", "Flowering Tree (Forestry event)", "Strange fruit", "Pheasant Control", "Thieving", "Pheasant tail feathers", "Golden pheasant egg", "Poachers (Forestry event)", "<PERSON>", "<PERSON> whistle", "Enchantment Ritual", "Petal garland", "Wood<PERSON><PERSON> (Forestry event)", "Canoes", "River Lum", "axe", "Edgeville", "amulet of glory", "Wilderness", "Black chincho<PERSON> (Hunter)", "<PERSON>", "thatch spar", "trading stick", "gout tuber", "proboscis", "snake hide", "uncut opal", "uncut jade", "uncut red topaz", "tribal mask", "Tree", "teak tree", "mahogany tree", "yew tree", "magic tree", "crystal shard", "<PERSON><PERSON>", "Wilderness", "Woodcutting Guild", "Bank note", "Wilderness Diary", "tree patch", "Special patches#Special tree patches", "Fossil Island", "Crystal tree", "crystal tree patch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Celastrus tree", "celastrus patch", "redwood tree patch", "Farming Guild", "tick", "Tick manipulation#1.5-tick <PERSON><PERSON><PERSON>", "Jungle bush", "jungle tree", "Kharazi jungle", "logs", "Dead tree (Mountain Daughter)", "Shining pool", "<PERSON><PERSON>", "Tendrils (Abyss)", "Abyss", "Thick vines", "Tar Swamp", "Vines (Brimhaven Dungeon)", "Brimhaven Dungeon", "Maple tree", "Miscellania", "Throne of Miscellania", "teak tree", "mahogany tree", "Etceteria", "Royal Trouble", "evergreen tree", "Kandarin Diary", "Seers' Village", "fruit tree patch", "Woodcutting stump", "<PERSON><PERSON><PERSON>not", "split log", "Neitiznot shield", "arctic pine logs", "beaver", "pet", "ent", "<PERSON><PERSON><PERSON> root", "The Ribbiting Tale of a Lily Pad Labour Dispute", "Animal Magnetism", "Lost City", "The Fremennik Trials", "The Eyes of Glouphrie", "<PERSON>'s Quest", "Legends' Quest", "A Kingdom Divided", "Lunar Diplomacy", "The Fremennik Isles", "Sins of the Father", "Song of the Elves", "Grim Tales", "Enlightened Journey", "Recipe for Disaster", "Recipe for Disaster/Freeing Skrach Uglogwee", "Heroes' Quest", "<PERSON>'s Friend", "The Ribbiting Tale of a Lily Pad Labour Dispute", "Animal Magnetism", "The Eyes of Glouphrie", "The Fremennik Trials", "<PERSON><PERSON><PERSON><PERSON><PERSON>'s <PERSON> Helper", "The Fremennik Isles", "Song of the Elves", "Grim Tales", "crystal shard", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tree", "Woodcutting Guild", "Farming", "rune axe", "dragon axe", "Skilling success rate", "magic tree", "Woodcutting/Experience table", "Woodcutting/Level up table"], "plainText": "left Woodcutting is a gathering skill that primarily involves chopping trees to obtain logs used in the Fletching, Firemaking, and Construction skills. Players with a higher Woodcutting level can chop down new types of trees and cut down trees quicker, especially as higher levels of axes become available. Woodcutting is a useful skill for a low-levelled player to make money early in the game. On the map, locations of rare trees can be located with a tree File:Rare_trees_icon.png icon. Players looking for a more social Woodcutting experience can partake in Forestry. To begin, grab a Forestry kit from the File:Forestry shop icon.png Forestry Shop in Draynor, Seers' Village, or Prifddinas. Members can also grow their own trees to chop using the Farming skill. Additionally, at level 60 Woodcutting and above (which can be boosted), members can gain access to the Woodcutting Guild, containing a wide variety of useful features. Types of trees {| class=\"wikitable sortable align-center-1 align-center-2 align-center-4\" |- ! Level ! colspan=2 |Tree ! colspan=2 |Resource !Experience !Chop Down time ! class=\"unsortable\" |Notes !Members |- |1 |x50px |Tree | |25 |One Log |The most common tree in Gielinor, these trees are how players start off training Woodcutting. Both evergreen and dead trees (both small and big) also yield logs. Each tree only yields one log, after which they are cut down and respawn in a short time.<br />Buckets of sap can be acquired by using a knife on the conical variants whilst having a bucket in one's inventory. | |- |1 |x50px |Tree (Tirannwn) | |25 |One Log |Found throughout Tirannwn. Equivalent to common trees, but all variants produce sap. | |- |1 |x50px |Dying tree | |25 |One Log |Can be found in the Wilderness, Varrock, and the Lumber Yard. | |- |1 |x50px |Dead tree |||Logs or Charcoal |25 |One Log |Dead trees can be found all around Gielinor. Dead trees found around the Fishing Hamlet, and east of the Lava Dragon Isle (in the Wilderness) give charcoal when cut. | |- |1 |x50px |Burnt tree |||Charcoal |25 |One Log |Found around the Fossil Island Volcano. | |- |1 |x50px |Evergreen tree |||Logs |25 |One Log |Evergreen trees can be found near Falador, near the entrance to the Dwarven Mines, north-west of Barbarian Village, east of Camelot Castle and on Dragontooth Island.<br/>Buckets of sap can be acquired by using a knife on an evergreen tree whilst having a bucket in your inventory. | |- |1 |x50px |Jungle tree | |25 |One Log |They are only found in the Feldip Hunter area, the Kharazi Jungle, and on Ape Atoll.<br />Several variations of models for the tree and stump exist, likely due to the trees in the Kharazi Jungle being introduced in RuneScape Classic. | |- |1 |x50px |Achey tree | |25 |One Log |These trees are used for the members-only quest Big Chompy Bird Hunting, to make ogre bows and arrows. Like normal trees, achey trees only yield one log. | |- |10 |x50px |Light Jungle | |32 |Varies |Light jungle can only be cut with a machete, and is found near Tai Bwo Wannai. Used in the Tai Bwo Wannai Cleanup minigame. When cutting these, many dangerous things may pop out, such as broodoo victims, mosquito swarms, tribesman, bush snakes and jungle spiders. | |- |15 |x50px |Oak tree | |37.5 |45 ticks (27 seconds) |These trees are fairly easy to find around wooded areas. Unlike normal trees, oak trees can yield multiple logs, and are used for training once the player has the ability to cut them. | |- |20 |x50px |Medium Jungle | |55 |Varies |Medium jungle can only be cut with a machete, and is found near Tai Bwo Wannai. Used in the Tai Bwo Wannai Cleanup minigame. When cutting these, many dangerous things may pop out, such as broodoo victims, mosquito swarms, tribesman, bush snakes and jungle spiders. | |- |30 |x50px |Willow tree | |67.5 |50 ticks (30 seconds) |These trees are found near water. Players can find them in and around Port Sarim, Draynor Village, Seers' Village, and Champions' Guild. A usually unoccupied area is also at the lake west of Fred the Farmer's hut. | |- |35 |x50px |Teak tree | |85 |50 ticks (30 seconds) |These trees yield teak logs when cut, which are used for Construction training. Teak trees are rather rare, with several lone trees around Castle Wars, Uzer and Ape Atoll. A large cluster can be found in the Hardwood Grove. | |- |35 |x50px |Dense Jungle | |80 |Varies |Dense jungle can only be cut with a machete, and is found near Tai Bwo Wannai. Used in the Tai Bwo Wannai Cleanup minigame. When cutting these, many dangerous things may pop out, such as broodoo victims, mosquito swarms, tribesman, bush snakes and jungle spiders. | |- |42 |x50px |Mature juniper tree | |35 |1 in 16 chance per log |These trees can only be found in Hosidius, north-west of the charcoal furnaces.<br />They are primarily cut to obtain juniper charcoal, a component in making dynamite for the blast mine minigame in Lovakengj.<br />Juniper logs cannot be burned. 1 in 16 chance of depletion when a log is received. | |- |45 |x50px |Maple tree | |100 |100 ticks (60 seconds) |Maple trees are special trees which can be found only in northern Kandarin, Fremennik Province, Corsair Cove Resource Area, behind Seers Village Bank and the Woodcutting Guild. Experience per log increases to 110 after completing the Kandarin Medium Diary. | |- |45 |x50px |Hollow tree | |82.5 |60 ticks (36 seconds) |These are found in Slepe and the Haunted Woods, and yield bark, which is used to create Splitbark armour. | |- |50 |x50px |Mahogany tree | |125 |100 ticks (60 seconds) |Like teak trees, mahogany trees are quite rare, and yield mahogany logs when cut, which are used for Construction training. Mahogany trees can be found in the Hardwood Grove, Ape Atoll, Kharazi Jungle, north of the Farming Guild, and grown on Fossil Island. | |- |54 |x50px |Arctic pine tree | |40 |140 ticks (84 seconds) |These are found on the islands of Neitiznot and south-west of Rellekka. These logs are better used for Firemaking experience rather than Woodcutting. | |- |60 |x50px |Yew tree | |175 |190 ticks (114 seconds) |The highest levelled tree for Free-to-Play players to cut, yew trees are uncommon throughout RuneScape. | |- |62 |x50px |Blisterwood tree | |76 |1/10 chance per log |A single tree can be found in the Arboretum in Darkmeyer. | |- |65 |x50px |Sulliuscep |||Mushrooms, sulliuscep caps, Mort Myre fungi and unidentified fossils |127 |1/16 chance per successful chop |Found in the Tar Swamp on Fossil Island. They do not respawn like trees, but rather each of the six sulliusceps found throughout the swamp respawn in a fixed rotation once the previous sulliuscep is depleted. | |- |75 |x50px |Magic tree | |250 |390 ticks (3 minutes and 54 seconds) |Cutting these trees takes time as they give logs at a very slow rate. You can find these trees at the Mage Training Arena, Sorcerer's Tower, south of Nemus Retreat near a totem, and south-west of Camelot. There are also a few just west of the south ladder to the bank in the Tree Gnome Stronghold, as well as inside the Woodcutting Guild. Two trees can be found just south of the main bank in Hosidius. | |- |90 |x50px |Redwood tree | |380 |440 ticks (4 minutes and 24 seconds) |These trees are found in the Woodcutting Guild or grown in the Farming Guild. Players do not chop the entire tree but rather sections of the trunk that are reached by climbing ladders | |} Trees chopped in quests and minigames * Dead yommi trees that may turn up during the Legends' Quest can be chopped for a random log. Notably, this allows players to obtain magic logs without the level requirement to chop magic trees. * The swaying tree is chopped for a branch during The Fremennik Trials. * The dramen tree is chopped for dramen branches during the Lost City quest and the Freeing Sir Amik Varze subquest of Recipe for Disaster. * Dream trees are chopped for dream logs during the Lunar Diplomacy quest. * Undead trees are chopped for undead twigs during Animal Magnetism. A blessed axe is required. * The windswept tree is chopped for windswept logs during Olaf's Quest. * A shrunk beanstalk is chopped during Grim Tales. The axe must be used on the beanstalk. * Phren roots are chopped for phren bark in The Gauntlet. A variant is chopped in the Corrupted Gauntlet. * Scrapey trees are chopped for scrapey tree logs in the Trouble Brewing minigame. * Bruma roots are chopped for bruma root whilst fighting the Wintertodt. * The meat tree is chopped when fighting the Muttadile in the Chambers of Xeric. * Saplings are chopped for kindling when fighting the ice demon in the Chambers of Xeric. * Regular and anima-infused roots are chopped during the Rising Roots Forestry Event. Logs Normal logs {| class=\"wikitable sortable align-left-2 align-right-3 align-right-4\" style=\"text-align:center;\" ! rowspan=\"2\" colspan=\"2\" |Item ! colspan=\"2\" style=\"white-space:nowrap;\" | ! rowspan=\"2\" |GE Price ! rowspan=\"2\" |Members |- !Level !Exp |- | |1||25 |}} | |- | |15||37.5 |}} | |- | |30||67.5 |}} | |- | |45||100 |}} | |- | |60||175 |}} | |- | |75||250 |}} | |- | |90||380 |}} | |} Construction logs {| class=\"wikitable sortable align-left-2 align-right-3 align-right-4\" style=\"text-align:center;\" ! rowspan=\"2\" colspan=\"2\" |Item ! colspan=\"2\" style=\"white-space:nowrap;\" | ! rowspan=\"2\" |GE Price ! rowspan=\"2\" |Members |- !Level !Exp |- | |35||85 |}} | |- | |50||125 |}} | |} Other logs {| class=\"wikitable sortable align-left-2 align-right-3 align-right-4\" style=\"text-align:center;\" ! rowspan=\"2\" colspan=\"2\" |Item ! colspan=\"2\" style=\"white-space:nowrap;\" | ! rowspan=\"2\" |GE Price ! rowspan=\"2\" |Members |- !Level !Exp |- | |1||25 |}} | |- | |54||40 |}} | |} Special logs {| class=\"wikitable sortable align-left-2 align-right-3 align-right-4\" style=\"text-align:center;\" ! rowspan=\"2\" colspan=\"2\" |Item ! colspan=\"2\" style=\"white-space:nowrap;\" | ! rowspan=\"2\" |GE Price ! rowspan=\"2\" |Members |- !Level !Exp |- | |0||25 | | |- | |42||35 | | |- | |45||82.5 |}} | |- | |62||76 | | |} Equipment Axes Various equipment may be used to interact with trees. Most trees require an axe to chop. {| class=\"wikitable align-center-1 align-center-2\" ! Level ! colspan=2 |Item !Description !Members |- |style=\"min-width:3em;\" rowspan=\"2\"|1 | |rowspan=\"6\" |Can be used to cut most trees. |rowspan=\"6\" | |- | |style=\"min-width:6em;text-align:left;\"|Iron axe |- |6 | |- |11 | |- |21 | |- |31 | |- |35 | | The only axe capable of cutting Undead trees, however unable to chop any other type. Used in the Animal Magnetism quest. Undead trees are NPCs and have a 18 Slayer level requirement to cut. | |- |41 | |rowspan=\"2\" |Can be used to cut most trees. The best axes available in Free-to-Play. |rowspan=\"2\" | |- |41 | |- |61 | |rowspan=\"2\" |Special attack: Lumber Up, provides a visible +3 boost to Woodcutting. |rowspan=\"4\" | |- |61 | |- |61 &<br/>85 | |Provides the same benefits as a dragon axe, including the special attack, however burns 1/3 of logs cut. This grants 1/2 the Firemaking experience that would normally be gained from doing so. Grants no additional benefit to trees that provide resources that cannot be burned. When depleted of charges, it behaves as a dragon axe. |- |71 | |Special attack: Lumber Up, provides a visible +3 boost to Woodcutting. Requires crystal shards to operate; as such, chopping very low level trees (such as oak and below) with the crystal axe is not recommended as players with 71 Woodcutting likely have maxed out the chance to receive a log. When depleted of charges, it behaves as a dragon axe. Requires completion of Song of the Elves to use. |} Felling axes Similar to their regular counterpart, when used with Forester's rations, felling axes provide an extra 10% Woodcutting experience with a 20% chance to not receive a log when chopping trees. {| class=\"wikitable align-center-1 align-center-2\" ! Level ! colspan=2 |Item !Description !Members |- |style=\"min-width:3em;\" rowspan=\"2\"|1 | |rowspan=\"7\" |Can be used to cut most trees. |rowspan=\"10\" | |- | |style=\"min-width:6em;text-align:left;\"|Iron felling axe |- |6 | |- |11 | |- |21 | |- |31 | |- |41 | |- |61 | |rowspan=\"2\" |Special attack: Lumber Up, provides a visible +3 boost to Woodcutting. |- |61 | |- |71 | |Special attack: Lumber Up, provides a visible +3 boost to Woodcutting. Requires crystal shards to operate; as such, chopping very low level trees (such as oak and below) with the crystal felling axe is not recommended as players with 71 Woodcutting likely have maxed out the chance to receive a log. When depleted of charges, it behaves as a dragon felling axe. Requires completion of Song of the Elves to use. |} Other Besides axes, other equipment is useful for the Woodcutting skill. {| class=\"wikitable align-center-1 align-center-2\" style=\"td:nth-child(2){width:15%}\" !colspan=\"2\"|Item !Benefit !Notes |- | |style=\"min-width:10em;\"|Machete |rowspan=\"4\"| Can be used to cut Light jungle, Medium jungle and, Dense jungle. |rowspan=\"4\"| Used in the Karamja jungle. |- | |- | |- | |- | |0.4% bonus exp. |rowspan=\"4\" |Can be purchased from the Forestry Shop or obtained during the Temple Trekking minigame. <ref name=Lumberjack-temple-trekking group=a>Obtaining via Temple Trekking requires the completion of In Aid of the Myreque.</ref><ref name=Lumberjack-set-bonus group=a>Wearing the whole set gives an additional 0.5% bonus, totaling to 2.5% bonus experience.</ref> |- | |0.8% bonus exp. |- | |0.6% bonus exp. |- | |0.2% bonus exp. |- | |0.4% bonus exp. |rowspan=\"4\" |Each piece of the Lumberjack outfit can be changed to the corresponding piece of the Forestry outfit from the Forestry Shop. <ref name=Lumberjack-set-bonus group=a>Wearing the whole set gives an additional 0.5% bonus, totaling to 2.5% bonus experience.</ref> |- | |0.8% bonus exp. |- | |0.6% bonus exp. |- | |0.2% bonus exp. |- | | rowspan=\"4\" | Chance to double logs from normal trees. |Requires completion of the easy Kandarin Diary. |- | | Requires completion of the medium Kandarin Diary. |- | | Requires completion of the hard Kandarin Diary. |- | | Requires completion of the elite Kandarin Diary. |- | |Gives 20% increased chance to receive a specific type of bird nest dropped from trees whilst Woodcutting. |Can be purchased from the Forestry Shop. |- | |Slightly increases bird's eggs and rings when receiving a bird nest. |Does not increase the drop rate of bird nests. |- | |Doubles the chance to receive easy-elite clue nests when in the wilderness. |Does not increase the drop rate of beginner clue nests. |- | |Provides a 10% chance to not use a charge when using a crystal axe. |The celestial signet provides the same benefit. |- | |Requiring level 99 in the Woodcutting skill, grants a 10% increase in chance to receive a bird nest. Temporary +1 visible boost to Woodcutting. |The temporary boost is applied when the cape is worn, and can be removed and reequipped to reapply the boost; however, visible boosts above 99 have no effect. |- | |Same effect as the Woodcutting cape, except for the visible boost. | |- | |Stores a variety of Forestry related items. | |- | |Can carry an additional 28 logs of any type, increasing the number of logs a player can carry to a total of 56.  |Cannot carry Sulliuscep caps. Can be attached to the Forestry kit with the Log brace purchased from the Forestry Shop for 5000 Anima-infused bark, 300 Oak logs and 300 Willow logs. |- | |Doubles the amount of leaves obtained from Forestry events. | |- | |Offers a 60-80% chance of receiving an additional log. | |- | |Used during and after the Animal magnetism quest to chop twigs from undead trees. | |} Mechanics Both Woodcutting level and tier of axe determines players' skilling success rate of chopping logs. Players with high enough Attack levels can equip the axes that match the respective metals, but having an axe equipped is not necessary in order to chop a tree; having one in the player's inventory also works. Most trees follow the same mechanics when cut, rolling to chop a log every 4 game ticks. All trees have different chances to chop a log depending on the tree being cut, the player's Woodcutting level, and the axe being used. With a high Woodcutting level and a good axe, a 100% success rate can be achieved when cutting certain lower-level trees. Trees despawn on a timer that activates after the first cut and regenerates if players stop cutting the tree before it is fully chopped. The regeneration rate is equal to the chopping time. So chopping for 10 seconds, then stopping for 10 seconds will let the tree recover to full. After a tree depletes, a timer counts down to its respawn.   {| class=\"wikitable\" |- ! Tree !! Despawn time !Respawn time |- | Oak tree || 27 seconds |8.4 seconds |- | Willow tree || 30 seconds |8.4 seconds |- | Teak tree || 30 seconds |9 seconds |- | Maple tree || 1 minute |35.4 seconds |- | Hollow tree || 36 seconds |25.8 seconds |- | Mahogany tree || 1 minute |8.4 seconds |- | Arctic pine tree || 1 minute, 24 seconds |8.4 seconds |- | Yew tree || 1 minute, 54 seconds |59.4 seconds |- | Magic tree || 3 minutes, 54 seconds |1 minute, 59.4 seconds |- | Redwood tree || 4 minutes, 24 seconds |1 minute, 59.4 seconds |} Members have a chance to receive the beaver Woodcutting pet whilst chopping trees around Gielinor. Bird Nests Whilst chopping most standard trees, players have a 1/256 chance to receive bird nests, which fall to the floor and can be picked up. These may be empty, or contain various rings, tree seeds, and rarely a bird's egg. The nests themselves are valuable in their crushed form for use in making Saradomin Brews with the Herblore skill. The player may also obtain clue nests in a similar fashion, which contain a clue scroll between beginner and elite tiers. Whilst regular bird nests aren't obtainable through chopping redwood trees, clue nests are. Unlike normal nests, these rates are affected by the type of tree and Woodcutting level. Special Woodcutting locations and activities Several locations have unique mechanics that related to Woodcutting. Woodcutting Guild The Woodcutting guild is located in the south-western corner of Hosidius, providing various Woodcutting features for members. The guild requires at least level 60 Woodcutting to enter. Within the guild, players receive an invisible +7 boost to their Woodcutting skill, allowing them to chop trees significantly faster, but not trees that require a Woodcutting level above their visible level. The guild contains a wide variety of trees found across Gielinor, including higher level yew and magic trees; all found in close proximity to both a sawmill and bank. It is the only location of redwood trees in the game, except for those grown by the player within the Farming guild. Other notable features include: * Perry's Chop-chop Shop, the only shop in Old School RuneScape that sells rune axes. * A dungeon containing many Ents Forestry A player cuts a tree with a felling axe, which is obtained from participating in forestry. Forestry is a social Woodcutting activity in which players carry a Forestry kit for a chance to spawn special events upon felling trees. These special events can spawn more frequently when players are either cutting the same tree, cutting higher-tiered trees, or by doing both. Eligible players can earn anima-infused bark, various types of leaves, and other rare event items. The anima-infused bark can then be exchanged at Forestry shops for a variety of Woodcutting-related items. {| class=\"wikitable sortable\" |+Forestry events ! !Members? !Group/Individual event?<ref group=\"g\">If the NPCs, objects, or actions are influenced by how many players are near the tree that spawned the event.</ref> !Skills Trained  (Other than Woodcutting) !Unique Rewards !Event length |- !Struggling sapling | |Group |Farming | |3 minutes, 30 seconds |- !Rising roots | |Independent | | |1 minute, 33 seconds |- !Friendly Ent | |Group |Fletching |Bird nest (egg) |2 minutes |- !Beehive | |Group |Construction |Sturdy beehive parts |2 minutes, 30 seconds |- !Flowering Tree | |Independent | |Strange fruit and other seeds |3 minutes |- !Pheasant Control | |Independent |Thieving |Pheasant tail feathers and Golden pheasant egg |1 minute, 36 seconds |- !Poachers | |Group |Hunter |Fox whistle |1 minute, 50 seconds |- !Enchantment Ritual | |Group | |Petal garland |2 minutes |- !Woodcutting Leprechaun | |Independent | | |1 minute, 12 seconds |} Canoes Canoes are a way of travelling along the River Lum. All that is required to use them is an axe and a minimum of level 12 Woodcutting for the lowest level canoe. There are five canoe stations and six stops. Canoes are a very handy way of getting to Edgeville without an amulet of glory. Whilst the Wilderness is the last stop, there are no suitable trees to make a canoe, so it is a one-way trip. It drops players off just north of the black chinchompa hunting grounds. {| class=\"wikitable\" ! rowspan=\"2\" |Image ! rowspan=\"2\" |Canoe ! colspan=\"2\" | ! rowspan=\"2\" |Notes |- !Level !Exp |- |200px |A Log |12 |30 |It will take you one stop along the River Lum. Cannot be used to travel to the Wilderness. |- |200px |A Dugout |27 |60 |It can take you up to two stops along the River Lum. Cannot be used to travel to the Wilderness. |- |200px |A Stable Dugout |42 |90 |It can take you up to three stops along the River Lum. Cannot be used to travel to the Wilderness. |- |200px |A Waka |57 |150 |It can take you unlimited stops along the River Lum. The only canoe that can travel into the Wilderness. |} Tai Bwo Wannai Cleanup Tai Bwo Wannai Cleanup is a Woodcutting minigame that revolves around hacking jungle bushes around Tai Bwo Wannai with a machete for thatch spars and using them to repair the village fence. Both actions grant favour with the locals, which can be exchanged for trading sticks. Furthermore, gout tubers, proboscides, snake hide, uncut opals, uncut jades, uncut red topazes and tribal masks may be obtained from creatures and rocks that occasionally spawn upon successfully clearing a jungle bush. Prifddinas The regular trees, teak tree, mahogany trees, yew trees and magic trees found within the walls of Prifddinas have a 1/40 chance of producing a crystal shard in addition to the main product. Despite their models also having embedded crystals, the trees in Gwenith do not yield crystal shards. Ents Ents are monsters found in the Wilderness and at the Woodcutting guild. They become ent trunks when killed, which can be chopped for random noted logs until it despawns. Ents in the wilderness produce logs at twice the rate of logs from ents at the Woodcutting Guild. Completing the medium tier of the Wilderness Diary further increases the rate. Farmable trees Oak trees, willow trees, maple trees, yew trees, and magic trees can be planted in any of six tree patches found throughout Gielinor. Teak trees and mahogany trees can be planted in any of three hardwood patches on Fossil Island. Crystal trees can be planted in the crystal tree patch in Prifddinas. Celastrus trees can be planted in the dedicated celastrus patch and redwood trees in the dedicated redwood tree patch, both found in the advanced tier of the Farming Guild. Crystal trees and celastrus do not grant Woodcutting experience when chopped. Farmable trees offer the benefit of being visible only to the player who has planted it, thus removing competition. Attempting to chop a tree in a Farming patch causes the player to stall for one tick, which allows for 1.5-tick woodcutting. Obstacles Several obstacles can be traversed by chopping them with an axe or machete. These include: * Jungle bushes and jungle trees blocking the entrance to the Kharazi jungle. In addition to being crossed when chopped, they yield logs. * Dead trees found north of the Shining Pool block the Kendal's cave. Only two of the trees can be chopped, the others produce the message \"There is nothing to gain from chopping down this tree\". These grant no logs and no experience. * Tendrils blocking access to the inner ring of the Abyss. * Thick vines blocking various pathways in the Tar Swamp. They grant 2 experience when chopped. * Vines blocking certain passageways in the Brimhaven Dungeon. They grant no experience when chopped. Other * The maples found on Miscellania can only be chopped after getting to a certain point during Throne of Miscellania. The player won't receive any logs upon cutting these specific trees, instead the kingdom keeps them, and grants you some favour in gratitude for woodcutting for them. Similarly, the teak trees and mahogany trees found on Etceteria can only be chopped after Royal Trouble has been completed, immediately granting the logs to the kingdom and earning some favour as a player. The evergreen trees found on both islands are not considered part of the kingdom, thus behave normally. * After completion of the medium tier of the Kandarin Diary, the maples in Seers' Village grant 10% extra experience. * Fruit trees planted in fruit tree patches can be chopped down at any level, granting no experience. * The Woodcutting stump in Neitiznot can be used to make split logs and Neitiznot shields from arctic pine logs, the former granting 5 Woodcutting experience. Woodcutting pet 175px The beaver is a skilling pet that can be obtained whilst training Woodcutting. The chances of getting it are dependent on the player's Woodcutting level and the time it takes to gather a resource. Chopping ent trunks and bruma roots will not give players a chance to receive this pet. Temporary boosts Quests Quests requiring Woodcutting {| class=\"wikitable sortable qc-active lighttable mw-collapsible\" style=\"text-align:center;\" !Quest !Woodcutting<br/>requirement !class=\"unsortable\"|Other skill requirements |- |The Ribbiting Tale of a Lily Pad Labour Dispute||||- |- |Animal Magnetism||||, ,  |- |Lost City|||| |- |The Fremennik Trials||||,  if crafting your own lyre |- |The Eyes of Glouphrie|| ||,  |- |Olaf's Quest|||| |- |Legends' Quest||||, , , , , , , ,  |- |A Kingdom Divided | |,,,,, |- |Lunar Diplomacy||||, , , ,  |- |The Fremennik Isles|| || ,   |- |Sins of the Father||||, , , , ,  |- |Song of the Elves||||, , , , , ,  |- |Grim Tales||||, , ,  |} Quests rewarding Woodcutting experience {| class=\"wikitable sortable lighttable sticky-header mw-collapsible qc-active oqg-table\" style=\"text-align:center;\" !Quest !Experience<br/>reward !Woodcutting<br/>requirement !class=\"unsortable\"|Other requirements |- |Enlightened Journey||1,500||-||, , ,  |- |Recipe for Disaster<br/>(Skrach Uglogwee subquest)||1,500||-||,  |- |Heroes' Quest||1,575||-||, , , ,  |- |Monk's Friend||2,000||-||- |- |The Ribbiting Tale of a Lily Pad Labour Dispute||2,000||||- |- |Animal Magnetism||2,500||||, ,  |- |The Eyes of Glouphrie||2,500|| ||,  |- |The Fremennik Trials||2,812||||,  if crafting your own lyre |- |Icthlarin's Little Helper||4,000||-||- |- |The Fremennik Isles||10,000|| ||, ,  |- |Song of the Elves||40,000||||, , , , , ,  |- |Grim Tales||60,000||||, , ,  |- !Total !128,387 !colspan=\"2\"| |} Skill choice Upon completing any of the following quests, players may choose to allocate experience to Woodcutting. These rewards usually come in the form of items, such as lamps or books, and are independent of any experience rewards directly received for completing the quest. Level up messages Changes Trivia *At level 82 and 89 woodcutting, both the rune axe and the dragon axe have the same chance to successfully chop magic trees. See also * Woodcutting/Experience table, a list of all Woodcutting experience sources * Woodcutting/Level up table, a list of all requirements and unlockables within the Woodcutting skill References  Category:Free-to-play skills"}, "originalContent": "{{External|rs|rsc|dw}}\n{{Otheruses|def=no|a list of all requirements and unlockables within the Woodcutting skill|Woodcutting/Level up table}}\n{{Has calculator|Calculator:Woodcutting}}\n{{Has skill guide|members=Pay-to-play Woodcutting training|free=Free-to-play Woodcutting training|iron=Ironman Guide/Woodcutting|UIM=Ultimate Ironman Guide/Woodcutting}}\n{{Infobox Skill\n|name = Woodcutting\n|image = [[File:Woodcutting icon.png]]\n|release = [[4 January]] [[2001]]\n|update = Runescape beta is now online!\n|aka = WC\n|members = No\n|type = Gathering\n}}\n[[File:Woodcutting icon (detail).png|left]]\n'''Woodcutting''' is a gathering [[Skills|skill]] that primarily involves chopping trees to obtain logs used in the [[Fletching]], [[Firemaking]], and [[Construction]] skills. Players with a higher Woodcutting level can chop down new types of trees and cut down trees quicker, especially as higher levels of [[axe]]s become available.\n\nWoodcutting is a useful skill for a low-levelled player to make money early in the game. On the map, locations of rare trees can be located with a tree [[File:Rare_trees_icon.png]] [[icon]].\n\nPlayers looking for a more social Woodcutting experience can partake in [[Forestry]]. To begin, grab a [[Forestry kit]] from the [[File:Forestry shop icon.png]] [[Forestry Shop]] in [[Draynor Village|Draynor]], [[Seers' Village]], or [[Prifddinas]].\n\n[[Members]] can also grow their own trees to chop using the [[Farming]] skill. Additionally, at level 60 Woodcutting and above (which can be boosted), members can gain access to the [[Woodcutting Guild]], containing a wide variety of useful features.\n\n{{Listen\n|align = right\n|title = Woodcutting level up - normal\n|filename = Woodcutting Level Up!.ogg\n|desc = The music that plays when levelling up.}}\n\n{{Listen\n|align = right\n|title = Woodcutting level up - with unlocks\n|filename = Woodcutting Level Up! (Unlocks).ogg\n|desc = The music that plays when levelling up and unlocking new content.}}\n\n==Types of trees==\n{| class=\"wikitable sortable align-center-1 align-center-2 align-center-4\"\n|-\n!{{SCP|Woodcutting}} Level\n! colspan=2 |Tree\n! colspan=2 |Resource\n!Experience\n!Chop Down time\n! class=\"unsortable\" |Notes\n!Members\n|-\n|1\n|[[File:Tree.png|x50px]]\n|[[Tree]]\n|{{plinkt|Logs}}\n|25\n|One Log\n|The most common tree in Gielinor, these trees are how players start off training Woodcutting. Both evergreen and dead trees (both small and big) also yield logs. Each tree only yields one log, after which they are cut down and respawn in a short time.<br />[[Bucket of sap|Buckets of sap]] can be acquired by using a [[knife]] on the conical variants whilst having a [[bucket]] in one's inventory.\n|{{members|no}}\n|-\n|1\n|[[File:Tree_(Tirannwn,_1).png|x50px]]\n|[[Tree (Tirannwn)]]\n|{{plinkt|Logs}}\n|25\n|One Log\n|Found throughout [[Tirannwn]]. Equivalent to common trees, but all variants produce sap.\n|{{members|yes}}\n|-\n|1\n|[[File:Dying tree.png|x50px]]\n|[[Dying tree]]\n|{{plinkt|Logs}}\n|25\n|One Log\n|Can be found in the [[Wilderness]], [[Varrock]], and the [[Lumber Yard]].\n|{{members|no}}\n|-\n|1\n|[[File:Dead tree.png|x50px]]\n|[[Dead tree]]\n|{{plinkp|Logs}}{{plinkp|Charcoal}}||[[Logs]] or [[Charcoal]]\n|25\n|One Log\n|Dead trees can be found all around [[Gielinor]]. Dead trees found around the [[Fishing Hamlet]], and east of the [[Lava Dragon Isle]] (in the [[Wilderness]]) give [[charcoal]] when cut.\n|{{members|no}}\n|-\n|1\n|[[File:Burnt tree (big).png|x50px]]\n|[[Burnt tree]]\n|{{plinkp|Charcoal}}||[[Charcoal]]\n|25\n|One Log\n|Found around the [[Fossil Island Volcano]].\n|{{members|yes}}\n|-\n|1\n|[[File:Evergreen tree.png|x50px]]\n|[[Evergreen tree]]\n|{{plinkp|Logs}}||[[Logs]]\n|25\n|One Log\n|Evergreen trees can be found near [[Falador]], near the entrance to the [[Dwarven Mine|Dwarven Mines]], north-west of [[Barbarian Village]], east of [[Camelot|Camelot Castle]] and on [[Dragontooth Island]].<br/>[[Bucket of sap|Buckets of sap]] can be acquired by using a [[knife]] on an evergreen tree whilst having a [[bucket]] in your inventory.\n|{{members|no}}\n|-\n|1\n|[[File:Jungle tree (dark).png|x50px]]\n|[[Jungle tree]]\n|{{plinkt|Logs}}\n|25\n|One Log\n|They are only found in the [[Feldip Hunter area]], the [[Kharazi Jungle]], and on [[Ape Atoll]].<br />Several variations of models for the tree and stump exist, likely due to the trees in the Kharazi Jungle being introduced in ''[[RuneScape Classic]]''.\n|{{members|no}}\n|-\n|1\n|[[File:Achey Tree.png|x50px]]\n|[[Achey tree]]\n|{{plinkt|Achey tree logs}}\n|25\n|One Log\n|These trees are used for the members-only quest [[Big Chompy Bird Hunting]], to make [[Ogre bow|ogre bows]] and [[ogre arrow|arrows]]. Like normal trees, achey trees only yield one log.\n|{{members|yes}}\n|-\n|10\n|[[File:Light Jungle.png|x50px]]\n|[[Light Jungle]]\n|{{plinkt|Thatch spar light}}\n|32\n|Varies\n|Light jungle can only be cut with a [[Machete (weapon)|machete]], and is found near [[Tai Bwo Wannai]]. Used in the [[Tai Bwo Wannai Cleanup]] minigame. When cutting these, many dangerous things may pop out, such as [[broodoo victim]]s, [[mosquito swarm]]s, [[tribesman]], [[bush snake]]s and [[jungle spider]]s.\n|{{members|yes}}\n|-\n|15\n|[[File:Oak tree.png|x50px]]\n|[[Oak tree]]\n|{{plinkt|Oak logs}}\n|37.5\n|45 [[ticks]] (27 seconds)\n|These trees are fairly easy to find around wooded areas. Unlike normal trees, oak trees can yield multiple logs, and are used for training once the player has the ability to cut them.\n|{{members|no}}\n|-\n|20\n|[[File:Medium Jungle.png|x50px]]\n|[[Medium Jungle]]\n|{{plinkt|Thatch spar med}}\n|55\n|Varies\n|Medium jungle can only be cut with a [[Machete (weapon)|machete]], and is found near [[Tai Bwo Wannai]]. Used in the [[Tai Bwo Wannai Cleanup]] minigame. When cutting these, many dangerous things may pop out, such as [[broodoo victim]]s, [[mosquito swarm]]s, [[tribesman]], [[bush snake]]s and [[jungle spider]]s.\n|{{members|yes}}\n|-\n|30\n|[[File:Willow tree.png|x50px]]\n|[[Willow tree]]\n|{{plinkt|Willow logs}}\n|67.5\n|50 [[ticks]] (30 seconds)\n|These trees are found near water. Players can find them in and around [[Port Sarim]], [[Draynor Village]], [[Seers' Village]], and [[Champions' Guild]]. A usually unoccupied area is also at the lake west of [[Fred the Farmer|Fred the Farmer's]] hut.\n|{{members|no}}\n|-\n|35\n|[[File:Teak tree.png|x50px]]\n|[[Teak tree]]\n|{{plinkt|Teak logs}}\n|85\n|50 [[ticks]] (30 seconds)\n|These trees yield [[teak logs]] when cut, which are used for [[Construction]] training. Teak trees are rather rare, with several lone trees around [[Castle Wars]], [[Uzer]] and [[Ape Atoll]]. A large cluster can be found in the [[Hardwood Grove]].\n|{{members|yes}}\n|-\n|35\n|[[File:Dense Jungle.png|x50px]]\n|[[Dense Jungle]]\n|{{plinkt|Thatch spar dense}}\n|80\n|Varies\n|Dense jungle can only be cut with a [[Machete (weapon)|machete]], and is found near [[Tai Bwo Wannai]]. Used in the [[Tai Bwo Wannai Cleanup]] minigame. When cutting these, many dangerous things may pop out, such as [[broodoo victim]]s, [[mosquito swarm]]s, [[tribesman]], [[bush snake]]s and [[jungle spider]]s.\n|{{members|yes}}\n|-\n|42\n|[[File:Mature juniper tree.png|x50px]]\n|[[Mature juniper tree]]\n|{{plinkt|Juniper logs}}\n|35\n|1 in 16 chance per log\n|These trees can only be found in [[Hosidius]], north-west of the [[charcoal furnace]]s.<br />They are primarily cut to obtain [[juniper charcoal]], a component in making [[dynamite]] for the [[blast mine]] minigame in [[Lovakengj]].<br />Juniper logs cannot be burned. 1 in 16 chance of depletion when a log is received.\n|{{members|yes}}\n|-\n|45\n|[[File:Maple tree.png|x50px]]\n|[[Maple tree]]\n|{{plinkt|Maple logs}}\n|100\n|100 [[ticks]] (60 seconds)\n|Maple trees are special trees which can be found only in northern [[Kandarin]], [[Fremennik Province]], [[Corsair Cove Resource Area]], behind Seers Village Bank and the [[Woodcutting Guild]]. Experience per log increases to 110 after completing the [[Kandarin Diary|Kandarin Medium Diary]].\n|{{members|no}}\n|-\n|45\n|[[File:Hollow tree (1).png|x50px]]\n|[[Hollow tree]]\n|{{plinkt|Bark}}\n|82.5\n|60 [[ticks]] (36 seconds)\n|These are found in [[Slepe]] and the [[Haunted Woods]], and yield [[bark]], which is used to create [[Splitbark armour]].\n|{{members|yes}}\n|-\n|50\n|[[File:Mahogany tree.png|x50px]]\n|[[Mahogany tree]]\n|{{plinkt|Mahogany logs}}\n|125\n|100 [[ticks]] (60 seconds)\n|Like teak trees, mahogany trees are quite rare, and yield [[mahogany logs]] when cut, which are used for [[Construction]] training. Mahogany trees can be found in the [[Hardwood Grove]], [[Ape Atoll]], [[Kharazi Jungle]], north of the [[Farming Guild]], and grown on [[Fossil Island]].\n|{{members|yes}}\n|-\n|54\n|[[File:Arctic pine tree.png|x50px]]\n|[[Arctic pine tree]]\n|{{plinkt|Arctic pine logs}}\n|40\n|140 [[ticks]] (84 seconds)\n|These are found on the islands of [[Neitiznot]] and south-west of [[Rellekka]]. These logs are better used for [[Firemaking]] experience rather than Woodcutting.\n|{{members|yes}}\n|-\n|60\n|[[File:Yew tree.png|x50px]]\n|[[Yew tree]]\n|{{plinkt|Yew logs}}\n|175\n|190 [[ticks]] (114 seconds)\n|The highest levelled tree for Free-to-Play players to cut, yew trees are uncommon throughout ''RuneScape''.\n|{{members|no}}\n|-\n|62\n|[[File:Blisterwood tree.png|x50px]]\n|[[Blisterwood tree]]\n|{{plinkt|Blisterwood logs}}\n|76\n|1/10 chance per log\n|A single tree can be found in the [[Arboretum (location)|Arboretum]] in [[Darkmeyer]].\n|{{members|yes}}\n|-\n|65\n|[[File:Sulliuscep.png|x50px]]\n|[[Sulliuscep]]\n|{{plinkp|Mushroom}}{{plinkp|Sulliuscep cap}}{{plinkp|Mort myre fungus}}{{plinkp|Numulite}}{{plinkp|Unidentified small fossil}}{{plinkp|Unidentified medium fossil}}{{plinkp|Unidentified large fossil}}{{plinkp|Unidentified rare fossil}}||[[Mushroom]]s, [[sulliuscep cap]]s, [[Mort myre fungus|Mort Myre fungi]] and [[unidentified fossil]]s\n|127\n|1/16 chance per successful chop\n|Found in the [[Tar Swamp]] on [[Fossil Island]]. They do not respawn like trees, but rather each of the six sulliusceps found throughout the swamp respawn in a fixed rotation once the previous sulliuscep is depleted.\n|{{members|yes}}\n|-\n|75\n|[[File:Magic tree.png|x50px]]\n|[[Magic tree]]\n|{{plinkt|Magic logs}}\n|250\n|390 [[ticks]] (3 minutes and 54 seconds)\n|Cutting these trees takes time as they give logs at a very slow rate. You can find these trees at the [[Mage Training Arena]], [[Sorcerer's Tower]], south of [[Nemus Retreat]] near a [[Vale totems|totem]], and south-west of [[Camelot]]. There are also a few just west of the south ladder to the bank in the [[Tree Gnome Stronghold]], as well as inside the [[Woodcutting Guild]]. Two trees can be found just south of the main bank in [[Hosidius]].\n|{{members|yes}}\n|-\n|90\n|[[File:Redwood tree.png|x50px]]\n|[[Redwood tree]]\n|{{plinkt|Redwood logs}}\n|380\n|440 [[ticks]] (4 minutes and 24 seconds)\n|These trees are found in the [[Woodcutting Guild]] or grown in the [[Farming Guild]]. Players do not chop the entire tree but rather sections of the trunk that are reached by climbing ladders\n|{{members|yes}}\n|}\n\n===Trees chopped in quests and minigames===\n* Dead [[yommi tree]]s that may turn up during the [[Legends' Quest]] can be chopped for a random log. Notably, this allows players to obtain [[magic logs]] without the level requirement to chop [[magic tree]]s.\n* The [[swaying tree]] is chopped for a [[branch]] during [[The Fremennik Trials]].\n* The [[dramen tree]] is chopped for [[dramen branch]]es during the [[Lost City]] quest and the [[Recipe for Disaster/Freeing Sir Amik Varze|Freeing Sir Amik Varze]] subquest of [[Recipe for Disaster]].\n* [[Dream tree]]s are chopped for [[dream log]]s during the [[Lunar Diplomacy]] quest.\n* [[Undead tree]]s are chopped for [[undead twigs]] during [[Animal Magnetism]]. A [[blessed axe]] is required.\n* The [[windswept tree]] is chopped for [[windswept logs]] during [[Olaf's Quest]].\n* A shrunk [[Magic beans|beanstalk]] is chopped during [[Grim Tales]]. The axe must be used on the beanstalk.\n* [[Phren Roots|Phren roots]] are chopped for [[phren bark]] in [[The Gauntlet]]. [[Corrupt Phren Roots|A variant]] is chopped in the [[The Gauntlet#Corrupted Gauntlet|Corrupted Gauntlet]].\n* [[Scrapey tree]]s are chopped for [[scrapey tree logs]] in the [[Trouble Brewing]] minigame.\n* [[Bruma roots]] are chopped for [[bruma root]] whilst fighting the [[Wintertodt]].\n* The [[meat tree]] is chopped when fighting the [[Muttadile]] in the [[Chambers of Xeric]].\n* [[Sapling (Chambers of Xeric)|Saplings]] are chopped for [[Kindling (Chambers of Xeric)|kindling]] when fighting the [[ice demon]] in the [[Chambers of Xeric]].\n* Regular and anima-infused [[Tree roots (Forestry)|roots]] are chopped during the [[Rising roots|Rising Roots Forestry Event]].\n\n==Logs==\n===Normal logs===\n{| class=\"wikitable sortable align-left-2 align-right-3 align-right-4\" style=\"text-align:center;\"\n! rowspan=\"2\" colspan=\"2\" |Item\n! colspan=\"2\" style=\"white-space:nowrap;\" |{{SCP|Woodcutting|link=y}}\n! rowspan=\"2\" |[[Grand Exchange|GE]] Price\n! rowspan=\"2\" |Members\n|-\n!Level\n!Exp\n|-\n|{{plinkt|Logs}}\n|1||25\n|{{Coins|{{GEP|Logs}}}}\n|{{Members|No}}\n|-\n|{{plinkt|Oak logs}}\n|15||37.5\n|{{Coins|{{GEP|Oak logs}}}}\n|{{Members|No}}\n|-\n|{{plinkt|Willow logs}}\n|30||67.5\n|{{Coins|{{GEP|Willow logs}}}}\n|{{Members|No}}\n|-\n|{{plinkt|Maple logs}}\n|45||100\n|{{Coins|{{GEP|Maple logs}}}}\n|{{Members|No}}\n|-\n|{{plinkt|Yew logs}}\n|60||175\n|{{Coins|{{GEP|Yew logs}}}}\n|{{Members|No}}\n|-\n|{{plinkt|Magic logs}}\n|75||250\n|{{Coins|{{GEP|Magic logs}}}}\n|{{Members|Yes}}\n|-\n|{{plinkt|Redwood logs}}\n|90||380\n|{{Coins|{{GEP|Redwood logs}}}}\n|{{Members|Yes}}\n|}\n\n===Construction logs===\n{| class=\"wikitable sortable align-left-2 align-right-3 align-right-4\" style=\"text-align:center;\"\n! rowspan=\"2\" colspan=\"2\" |Item\n! colspan=\"2\" style=\"white-space:nowrap;\" |{{SCP|Woodcutting|link=y}}\n! rowspan=\"2\" |[[Grand Exchange|GE]] Price\n! rowspan=\"2\" |Members\n|-\n!Level\n!Exp\n|-\n|{{plinkt|Teak logs}}\n|35||85\n|{{Coins|{{GEP|Teak logs}}}}\n|{{Members|Yes}}\n|-\n|{{plinkt|Mahogany logs}}\n|50||125\n|{{Coins|{{GEP|Mahogany logs}}}}\n|{{Members|Yes}}\n|}\n\n===Other logs===\n{| class=\"wikitable sortable align-left-2 align-right-3 align-right-4\" style=\"text-align:center;\"\n! rowspan=\"2\" colspan=\"2\" |Item\n! colspan=\"2\" style=\"white-space:nowrap;\" |{{SCP|Woodcutting|link=y}}\n! rowspan=\"2\" |[[Grand Exchange|GE]] Price\n! rowspan=\"2\" |Members\n|-\n!Level\n!Exp\n|-\n|{{plinkt|Achey tree logs}}\n|1||25\n|{{Coins|{{GEP|Achey tree logs}}}}\n|{{Members|Yes}}\n|-\n|{{plinkt|Arctic pine logs}}\n|54||40\n|{{Coins|{{GEP|Arctic pine logs}}}}\n|{{Members|Yes}}\n|}\n\n===Special logs===\n{| class=\"wikitable sortable align-left-2 align-right-3 align-right-4\" style=\"text-align:center;\"\n! rowspan=\"2\" colspan=\"2\" |Item\n! colspan=\"2\" style=\"white-space:nowrap;\" |{{SCP|Woodcutting|link=y}}\n! rowspan=\"2\" |[[Grand Exchange|GE]] Price\n! rowspan=\"2\" |Members\n|-\n!Level\n!Exp\n|-\n|{{plinkt|Scrapey tree logs}}\n|0||25\n|{{NA}}\n|{{Members|Yes}}\n|-\n|{{plinkt|Juniper logs}}\n|42||35\n|{{NA}}\n|{{Members|Yes}}\n|-\n|{{plinkt|Bark}}\n|45||82.5\n|{{Coins|{{GEP|Bark}}}}\n|{{Members|Yes}}\n|-\n|{{plinkt|Blisterwood logs}}\n|62||76\n|{{NA}}\n|{{Members|Yes}}\n|}\n\n==Equipment==\n===Axes===\nVarious equipment may be used to interact with trees. Most trees require an axe to chop.\n{| class=\"wikitable align-center-1 align-center-2\"\n!{{SCP|Woodcutting}} Level\n! colspan=2 |Item\n!Description\n!Members\n|-\n|style=\"min-width:3em;\" rowspan=\"2\"|1\n|{{plinkt|Bronze axe}}\n|rowspan=\"6\" |Can be used to cut most trees.\n|rowspan=\"6\" |{{members|no}}\n|-\n|{{plinkp|Iron axe}}\n|style=\"min-width:6em;text-align:left;\"|[[Iron axe]]\n|-\n|6\n|{{plinkt|Steel axe}}\n|-\n|11\n|{{plinkt|Black axe}}\n|-\n|21\n|{{plinkt|Mithril axe}}\n|-\n|31\n|{{plinkt|Adamant axe}}\n|-\n|35\n|{{plinkt|Blessed axe}}\n| The only axe capable of cutting [[Undead tree]]s, however unable to chop any other type. Used in the [[Animal Magnetism]] quest. Undead trees are NPCs and have a 18 [[Slayer]] level requirement to cut.\n|{{members|yes}}\n|-\n|41\n|{{plinkt|Rune axe}}\n|rowspan=\"2\" |Can be used to cut most trees. The best axes available in Free-to-Play.\n|rowspan=\"2\" |{{members|no}}\n|-\n|41\n|{{plinkt|Gilded axe}}\n|-\n|61\n|{{plinkt|Dragon axe}}\n|rowspan=\"2\" |[[Special attacks|Special attack]]: ''Lumber Up'', provides a visible +3 boost to Woodcutting.\n|rowspan=\"4\" |{{members|yes}}\n|-\n|61\n|{{Plinkt|3rd age axe}}\n|-\n|61 &<br/>{{SCP|Firemaking}}85\n|{{plinkt|Infernal axe}}\n|Provides the same benefits as a dragon axe, including the special attack, however burns 1/3 of logs cut. This grants 1/2 the Firemaking experience that would normally be gained from doing so. Grants no additional benefit to trees that provide resources that cannot be burned. When depleted of charges, it behaves as a dragon axe.\n|-\n|71\n|{{plinkt|Crystal axe}}\n|[[Special attacks|Special attack]]: ''Lumber Up'', provides a visible +3 boost to Woodcutting. Requires [[crystal shard]]s to operate; as such, chopping very low level trees (such as oak and below) with the crystal axe is not recommended as players with 71 Woodcutting likely have maxed out the chance to receive a log. When depleted of charges, it behaves as a dragon axe. Requires completion of [[Song of the Elves]] to use.\n|}\n\n===Felling axes===\nSimilar to their regular counterpart, when used with [[Forester's rations]], felling axes provide an extra 10% Woodcutting experience with a 20% chance to not receive a log when chopping trees.\n{| class=\"wikitable align-center-1 align-center-2\"\n!{{SCP|Woodcutting}} Level\n! colspan=2 |Item\n!Description\n!Members\n|-\n|style=\"min-width:3em;\" rowspan=\"2\"|1\n|{{plinkt|Bronze felling axe}}\n|rowspan=\"7\" |Can be used to cut most trees.\n|rowspan=\"10\" |{{members|yes}}\n|-\n|{{plinkp|Iron felling axe}}\n|style=\"min-width:6em;text-align:left;\"|[[Iron felling axe]]\n|-\n|6\n|{{plinkt|Steel felling axe}}\n|-\n|11\n|{{plinkt|Black felling axe}}\n|-\n|21\n|{{plinkt|Mithril felling axe}}\n|-\n|31\n|{{plinkt|Adamant felling axe}}\n|-\n|41\n|{{plinkt|Rune felling axe}}\n|-\n|61\n|{{plinkt|Dragon felling axe}}\n|rowspan=\"2\" |[[Special attacks|Special attack]]: ''Lumber Up'', provides a visible +3 boost to Woodcutting.\n|-\n|61\n|{{Plinkt|3rd age felling axe}}\n|-\n|71\n|{{plinkt|Crystal felling axe}}\n|[[Special attacks|Special attack]]: ''Lumber Up'', provides a visible +3 boost to Woodcutting. Requires [[crystal shard]]s to operate; as such, chopping very low level trees (such as oak and below) with the crystal felling axe is not recommended as players with 71 Woodcutting likely have maxed out the chance to receive a log. When depleted of charges, it behaves as a dragon felling axe. Requires completion of [[Song of the Elves]] to use.\n|}\n\n===Other===\nBesides axes, other equipment is useful for the Woodcutting skill.\n{| class=\"wikitable align-center-1 align-center-2\" style=\"td:nth-child(2){width:15%}\"\n!colspan=\"2\"|Item\n!Benefit\n!Notes\n|-\n|{{plinkp|Machete}}\n|style=\"min-width:10em;\"|[[Machete]]\n|rowspan=\"4\"| Can be used to cut [[Light jungle]], [[Medium jungle]] and, [[Dense jungle]].\n|rowspan=\"4\"| Used in the [[Karamja]] jungle.\n|-\n|{{plinkt|Opal machete}}\n|-\n|{{plinkt|Jade machete}}\n|-\n|{{plinkt|Red topaz machete}}\n|-\n|{{plinkt|Lumberjack hat}}\n|0.4% bonus exp.\n|rowspan=\"4\" |Can be purchased from the [[Forestry Shop]] or obtained during the [[Temple Trekking]] minigame. <ref name=Lumberjack-temple-trekking group=a>Obtaining via [[Temple Trekking]] requires the completion of [[In Aid of the Myreque]].</ref><ref name=Lumberjack-set-bonus group=a>Wearing the whole set gives an additional 0.5% bonus, totaling to 2.5% bonus experience.</ref>\n|-\n|{{plinkt|Lumberjack top}}\n|0.8% bonus exp.\n|-\n|{{plinkt|Lumberjack legs}}\n|0.6% bonus exp.\n|-\n|{{plinkt|Lumberjack boots}}\n|0.2% bonus exp.\n|-\n|{{plinkt|Forestry hat}}\n|0.4% bonus exp.\n|rowspan=\"4\" |Each piece of the [[Lumberjack outfit]] can be changed to the corresponding piece of the [[Forestry outfit]] from the [[Forestry Shop]]. <ref name=Lumberjack-set-bonus group=a>Wearing the whole set gives an additional 0.5% bonus, totaling to 2.5% bonus experience.</ref>\n|-\n|{{plinkt|Forestry top}}\n|0.8% bonus exp.\n|-\n|{{plinkt|Forestry legs}}\n|0.6% bonus exp.\n|-\n|{{plinkt|Forestry boots}}\n|0.2% bonus exp.\n|-\n|{{plinkt|Kandarin headgear 1}}\n| rowspan=\"4\" | Chance to double logs from normal trees.\n|Requires completion of the easy [[Kandarin Diary]].\n|-\n|{{plinkt|Kandarin headgear 2}}\n| Requires completion of the medium [[Kandarin Diary]].\n|-\n|{{plinkt|Kandarin headgear 3}}\n| Requires completion of the hard [[Kandarin Diary]].\n|-\n|{{plinkt|Kandarin headgear 4}}\n| Requires completion of the elite [[Kandarin Diary]].\n|-\n|{{plinkt|Twitcher's gloves}}\n|Gives 20% increased chance to receive a specific type of [[bird nest]] dropped from trees whilst Woodcutting.\n|Can be purchased from the [[Forestry Shop]].\n|-\n|{{plinkt|Strung rabbit foot}}\n|Slightly increases [[bird's egg]]s and rings when receiving a bird nest.\n|Does not increase the drop rate of bird nests.\n|-\n|{{plinkt|Ring of wealth (i)}}\n|Doubles the chance to receive easy-elite clue nests when in the wilderness.\n|Does not increase the drop rate of beginner clue nests.\n|-\n|{{plinkt|Elven signet}}\n|Provides a 10% chance to not use a charge when using a [[crystal axe]].\n|The [[celestial signet]] provides the same benefit.\n|-\n|{{plinkt|Woodcutting cape}}\n|Requiring level 99 in the Woodcutting skill, grants a 10% increase in chance to receive a bird nest. Temporary +1 visible boost to Woodcutting.\n|The temporary boost is applied when the cape is worn, and can be removed and reequipped to reapply the boost; however, visible boosts above 99 have no effect.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1375844352098975753|date=27 March 2021|archiveurl=https://archive.is/5piyY|archivedate=29 March 2021|quote=I did a little more trawling of the code, and can confirm that the visible stat is now treated as 99 if it's above 99 - that is a recent change. It seems to have been applied to Mining, Fishing and Woodcutting.}}\n|-\n|{{plinkt|Max cape}}\n|Same effect as the Woodcutting cape, except for the visible boost.\n|\n|-\n|{{plinkt|Forestry kit}}\n|Stores a variety of Forestry related items.\n|\n|-\n|{{plinkt|Log basket}}\n|Can carry an additional 28 logs of any type, increasing the number of logs a player can carry to a total of 56. \n|Cannot carry [[Sulliuscep cap]]s. Can be attached to the [[Forestry kit]] with the [[Log brace]] purchased from the [[Forestry Shop]] for 5000 [[Anima-infused bark]], 300 [[Oak logs]] and 300 [[Willow logs]].\n|-\n|{{plinkt|Secateurs attachment}}\n|Doubles the amount of leaves obtained from Forestry events.\n|\n|-\n|{{plinkt|Nature offerings}}\n|Offers a 60-80% chance of receiving an additional log.\n|\n|-\n|{{plinkt|Blessed axe}}\n|Used during and after the [[Animal magnetism]] quest to chop twigs from undead trees.\n|\n|}\n{{reflist|group=a}}\n\n==Mechanics==\nBoth Woodcutting level and tier of axe determines players' [[skilling success rate]] of chopping logs. Players with high enough [[Attack]] levels can equip the axes that match the respective metals, but having an axe equipped is not necessary in order to chop a tree; having one in the player's inventory also works.\n\nMost trees follow the same mechanics when cut, rolling to chop a log every 4 game [[RuneScape clock|ticks]]. All trees have different chances to chop a log depending on the tree being cut, the player's Woodcutting level, and the axe being used. With a high Woodcutting level and a good axe, a 100% success rate can be achieved when cutting certain lower-level trees.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1003748614546522117|date=4 June 2018|archiveurl=https://archive.is/FnPDT|archivedate=28 May 2020|quote=For oaks and below, a high-level player can get 100% success per roll with an adamant axe, so a rune axe would not help. This is not the case for willows or above.}}\n\nTrees despawn on a timer that activates after the first cut and regenerates if players stop cutting the tree before it is fully chopped. The regeneration rate is equal to the chopping time. So chopping for 10 seconds, then stopping for 10 seconds will let the tree recover to full. After a tree depletes, a timer counts down to its respawn.  \n{| class=\"wikitable\"\n|-\n! Tree !! Despawn time\n!Respawn time\n|-\n| [[Oak tree]] || 27 seconds\n|8.4 seconds\n|-\n| [[Willow tree]] || 30 seconds\n|8.4 seconds\n|-\n| [[Teak tree]] || 30 seconds\n|9 seconds\n|-\n| [[Maple tree]] || 1 minute\n|35.4 seconds\n|-\n| [[Hollow tree]] || 36 seconds\n|25.8 seconds\n|-\n| [[Mahogany tree]] || 1 minute\n|8.4 seconds\n|-\n| [[Arctic pine tree]] || 1 minute, 24 seconds\n|8.4 seconds\n|-\n| [[Yew tree]] || 1 minute, 54 seconds\n|59.4 seconds\n|-\n| [[Magic tree]] || 3 minutes, 54 seconds\n|1 minute, 59.4 seconds\n|-\n| [[Redwood tree]] || 4 minutes, 24 seconds\n|1 minute, 59.4 seconds\n|}\n\nMembers have a chance to receive the [[beaver]] Woodcutting pet whilst chopping trees around [[Gielinor]].\n\n===Bird Nests===\nWhilst chopping most standard trees, players have a 1/256 chance to receive [[bird nest]]s, which fall to the floor and can be picked up. These may be empty, or contain various rings, tree seeds, and rarely a [[bird's egg]]. The nests themselves are valuable in their [[crushed nest|crushed form]] for use in making [[Saradomin brew|Saradomin Brews]] with the [[Herblore]] skill.\n\nThe player may also obtain [[clue nest]]s in a similar fashion, which contain a [[clue scroll]] between beginner and elite tiers. Whilst regular bird nests aren't obtainable through chopping redwood trees, clue nests are. Unlike normal nests, these rates are affected by the type of tree and Woodcutting level.\n\n==Special Woodcutting locations and activities==\nSeveral locations have unique mechanics that related to Woodcutting.\n\n===Woodcutting Guild===\n{{Main|Woodcutting Guild}}\nThe [[Woodcutting Guild|Woodcutting guild]] is located in the south-western corner of [[Hosidius]], providing various Woodcutting features for [[members]]. The guild requires at least level 60 Woodcutting to enter. Within the guild, players receive an [[Invisible boost|invisible +7 boost]] to their Woodcutting skill, allowing them to chop trees significantly faster, but not trees that require a Woodcutting level above their visible level.\n\nThe guild contains a wide variety of trees found across [[Gielinor]], including higher level [[Yew tree|yew]] and [[magic tree]]s; all found in close proximity to both a [[sawmill]] and [[bank]]. It is the only location of [[redwood tree]]s in the game, except for those grown by the player within the [[Farming Guild|Farming guild]]. Other notable features include:\n* [[Perry's Chop-chop Shop]], the only shop in ''[[Old School RuneScape]]'' that sells [[rune axe]]s.\n* A dungeon containing many [[Ents]]\n\n===Forestry===\n{{Main|Forestry}}\n[[File:Felling axe.gif|thumb|200px|A player cuts a tree with a [[felling axe]], which is obtained from participating in forestry.]]\n[[Forestry]] is a social Woodcutting activity in which players carry a [[Forestry kit]] for a chance to spawn special events upon felling trees. These special events can spawn more frequently when players are either cutting the same tree, cutting higher-tiered trees, or by doing both. Eligible players can earn [[anima-infused bark]], various types of [[leaves]], and other rare event items. The anima-infused bark can then be exchanged at [[Forestry Shop|Forestry shops]] for a variety of Woodcutting-related items.\n\n{| class=\"wikitable sortable\"\n|+[[Forestry]] events\n!\n!Members?\n!Group/Individual event?<ref group=\"g\">If the NPCs, objects, or actions are influenced by how many players are near the tree that spawned the event.</ref>\n!Skills Trained \n(Other than [[Woodcutting]])\n!Unique Rewards\n!Event length\n|-\n![[Struggling sapling (Forestry event)|Struggling sapling]]\n|{{Members|No}}\n|Group\n|[[Farming]]\n|{{NA}}\n|3 minutes, 30 seconds\n|-\n![[Rising roots]]\n|{{Members|No}}\n|Independent\n|{{NA}}\n|{{NA}}\n|1 minute, 33 seconds\n|-\n![[Friendly Ent (Forestry event)|Friendly Ent]]\n|{{Members|No}}\n|Group\n|[[Fletching]]\n|[[Bird nest (egg)]]\n|2 minutes\n|-\n![[Beehive (Forestry event)|Beehive]]\n|{{Members|Yes}}\n|Group\n|[[Construction]]\n|[[Sturdy beehive parts]]\n|2 minutes, 30 seconds\n|-\n![[Flowering Tree (Forestry event)|Flowering Tree]]\n|{{Members|Yes}}\n|Independent\n|{{NA}}\n|[[Strange fruit]] and other seeds\n|3 minutes\n|-\n![[Pheasant Control]]\n|{{Members|Yes}}\n|Independent\n|[[Thieving]]\n|[[Pheasant tail feathers]] and [[Golden pheasant egg]]\n|1 minute, 36 seconds\n|-\n![[Poachers (Forestry event)|Poachers]]\n|{{Members|Yes}}\n|Group\n|[[Hunter]]\n|[[Fox whistle]]\n|1 minute, 50 seconds\n|-\n![[Enchantment Ritual]]\n|{{Members|Yes}}\n|Group\n|{{NA}}\n|[[Petal garland]]\n|2 minutes\n|-\n![[Woodcutting Leprechaun (Forestry event)|Woodcutting Leprechaun]]\n|{{Members|Yes}}\n|Independent\n|{{NA}}\n|{{NA}}\n|1 minute, 12 seconds\n|}\n\n{{Reflist|group=\"g\"}}\n\n===Canoes===\n{{Main|Canoe}}\n[[Canoes]] are a way of travelling along the [[River Lum]]. All that is required to use them is an [[axe]] and a minimum of level 12 Woodcutting for the lowest level canoe. There are five canoe stations and six stops.\n\nCanoes are a very handy way of getting to [[Edgeville]] without an [[amulet of glory]].\n\nWhilst the [[Wilderness]] is the last stop, there are no suitable trees to make a canoe, so it is a one-way trip. It drops players off just north of the [[Black chinchompa (Hunter)|black chinchompa]] hunting grounds.\n{| class=\"wikitable\"\n! rowspan=\"2\" |Image\n! rowspan=\"2\" |Canoe\n! colspan=\"2\" |{{SCP|Woodcutting|link=y}}\n! rowspan=\"2\" |Notes\n|-\n!Level\n!Exp\n|-\n|[[File:Log canoe.png|200px]]\n|A Log\n|12\n|30\n|It will take you one stop along the River Lum. Cannot be used to travel to the Wilderness.\n|-\n|[[File:Dugout canoe.png|200px]]\n|A Dugout\n|27\n|60\n|It can take you up to two stops along the River Lum. Cannot be used to travel to the Wilderness.\n|-\n|[[File:Stable Dugout canoe.png|200px]]\n|A Stable Dugout\n|42\n|90\n|It can take you up to three stops along the River Lum. Cannot be used to travel to the Wilderness.\n|-\n|[[File:Waka canoe.png|200px]]\n|A Waka\n|57\n|150\n|It can take you unlimited stops along the River Lum. The only canoe that can travel into the Wilderness.\n|}\n\n===Tai Bwo Wannai Cleanup===\n{{main|Tai Bwo Wannai Cleanup}}\nTai Bwo Wannai Cleanup is a Woodcutting minigame that revolves around hacking jungle bushes around [[Tai Bwo Wannai]] with a machete for [[thatch spar]]s and using them to repair the village fence. Both actions grant favour with the locals, which can be exchanged for [[trading stick]]s. Furthermore, [[gout tuber]]s, [[proboscis|proboscides]], [[snake hide]], [[uncut opal]]s, [[uncut jade]]s, [[uncut red topaz]]es and [[tribal mask]]s may be obtained from creatures and rocks that occasionally spawn upon successfully clearing a jungle bush.\n\n===Prifddinas===\n{{main|Prifddinas}}\nThe [[Tree|regular trees]], [[teak tree]], [[mahogany tree]]s, [[yew tree]]s and [[magic tree]]s found within the walls of Prifddinas have a 1/40 chance of producing a [[crystal shard]] in addition to the main product. Despite their models also having embedded crystals, the trees in [[Gwenith]] do not yield crystal shards.\n\n===Ents===\n{{main|Ent}}\nEnts are monsters found in the [[Wilderness]] and at the [[Woodcutting Guild|Woodcutting guild]]. They become ent trunks when killed, which can be chopped for random [[Bank note|noted]] logs until it despawns. Ents in the wilderness produce logs at twice the rate of logs from ents at the Woodcutting Guild. Completing the medium tier of the [[Wilderness Diary]] further increases the rate.\n\n===Farmable trees===\n{{Main|Tree patch}}\nOak trees, willow trees, maple trees, yew trees, and magic trees can be planted in any of six [[tree patch]]es found throughout Gielinor. Teak trees and mahogany trees can be planted in any of three [[Special patches#Special tree patches|hardwood patches]] on [[Fossil Island]]. [[Crystal tree]]s can be planted in the [[crystal tree patch]] in [[Prifddinas]]. [[Celastrus tree]]s can be planted in the dedicated [[celastrus patch]] and redwood trees in the dedicated [[redwood tree patch]], both found in the advanced tier of the [[Farming Guild]]. Crystal trees and celastrus do not grant Woodcutting experience when chopped.\n\nFarmable trees offer the benefit of being visible only to the player who has planted it, thus removing competition. Attempting to chop a tree in a Farming patch causes the player to stall for one [[tick]], which allows for [[Tick manipulation#1.5-tick Woodcutting|1.5-tick woodcutting]].\n\n===Obstacles===\nSeveral obstacles can be traversed by chopping them with an axe or machete. These include:\n* [[Jungle bush]]es and [[jungle tree]]s blocking the entrance to the [[Kharazi jungle]]. In addition to being crossed when chopped, they yield [[logs]].\n* [[Dead tree (Mountain Daughter)|Dead trees]] found north of the [[Shining pool|Shining Pool]] block the [[Kendal]]'s cave. Only two of the trees can be chopped, the others produce the message \"There is nothing to gain from chopping down this tree\". These grant no logs and no experience.\n* [[Tendrils (Abyss)|Tendrils]] blocking access to the inner ring of the [[Abyss]].\n* [[Thick vines]] blocking various pathways in the [[Tar Swamp]]. They grant 2 experience when chopped.\n* [[Vines (Brimhaven Dungeon)|Vines]] blocking certain passageways in the [[Brimhaven Dungeon]]. They grant no experience when chopped.\n\n===Other===\n* The [[Maple tree|maples]] found on [[Miscellania]] can only be chopped after getting to a certain point during [[Throne of Miscellania]]. The player won't receive any logs upon cutting these specific trees, instead the kingdom keeps them, and grants you some favour in gratitude for woodcutting for them. Similarly, the [[teak tree]]s and [[mahogany tree]]s found on [[Etceteria]] can only be chopped after [[Royal Trouble]] has been completed, immediately granting the logs to the kingdom and earning some favour as a player. The [[evergreen tree]]s found on both islands are not considered part of the kingdom, thus behave normally.\n* After completion of the medium tier of the [[Kandarin Diary]], the maples in [[Seers' Village]] grant 10% extra experience.\n* Fruit trees planted in [[fruit tree patch]]es can be chopped down at any level, granting no experience.\n* The [[Woodcutting stump]] in [[Neitiznot]] can be used to make [[split log]]s and [[Neitiznot shield]]s from [[arctic pine logs]], the former granting 5 Woodcutting experience.\n\n==Woodcutting pet==\n[[File:Beaver (follower).png|right|175px]]\n{{Main|Beaver}}\nThe [[beaver]] is a skilling [[pet]] that can be obtained whilst training Woodcutting. The chances of getting it are dependent on the player's Woodcutting level and the time it takes to gather a resource. Chopping [[ent]] trunks and [[Bruma root|bruma roots]] '''will not''' give players a chance to receive this pet.\n\n==Temporary boosts==\n{{main|Temporary skill boost}}\n{{:Temporary skill boost/Woodcutting}}\n\n==Quests==\n{{Sync}}\n\n===Quests requiring Woodcutting===\n{| class=\"wikitable sortable qc-active lighttable mw-collapsible\" style=\"text-align:center;\"\n!Quest\n!Woodcutting<br/>requirement\n!class=\"unsortable\"|Other skill requirements\n|-\n|[[The Ribbiting Tale of a Lily Pad Labour Dispute]]||{{SCP|Woodcutting|15|sort}}||-\n|-\n|[[Animal Magnetism]]||{{SCP|Woodcutting|35|sort}}||{{SCP|Crafting|19}}, {{SCP|Slayer|18}}, {{SCP|Ranged|30}}\n|-\n|[[Lost City]]||{{SCP|Woodcutting|36|sort}}||{{SCP|Crafting|31}}\n|-\n|[[The Fremennik Trials]]||{{SCP|Woodcutting|40|sort}}||{{SCP|Fletching|25}}, {{SCP|Crafting|40}} if crafting your own lyre\n|-\n|[[The Eyes of Glouphrie]]||{{SCP|Woodcutting|45}} {{Ironman|icon}}||{{SCP|Construction|5}}, {{SCP|Magic|46}}\n|-\n|[[Olaf's Quest]]||{{SCP|Woodcutting|50|sort}}||{{SCP|Firemaking|40}}\n|-\n|[[Legends' Quest]]||{{SCP|Woodcutting|50|sort}}||{{SCP|Agility|50}}, {{SCP|Crafting|50}}, {{SCP|Herblore|45}}, {{SCP|Magic|56}}, {{SCP|Mining|52}}, {{SCP|Prayer|42}}, {{SCP|Smithing|50}}, {{SCP|Strength|50}}, {{SCP|Thieving|50}}\n|-\n|[[A Kingdom Divided]]\n|{{SCP|Woodcutting|52|sort}}\n|{{SCP|Agility|54|sort}},{{SCP|Thieving|52|sort}},{{SCP|Herblore|50|sort}},{{SCP|Mining|42|sort}},{{SCP|Crafting|38|sort}},{{SCP|Magic|35|sort}}\n|-\n|[[Lunar Diplomacy]]||{{SCP|Woodcutting|55|sort}}||{{SCP|Magic|65}}, {{SCP|Crafting|61}}, {{SCP|Mining|60}}, {{SCP|Firemaking|49}}, {{SCP|Defence|40}}\n|-\n|[[The Fremennik Isles]]||{{SCP|Woodcutting|56|sort}} {{Ironman|icon}}|| {{SCP|Construction|20}}, {{SCP|Crafting|46}} {{Ironman|icon}}\n|-\n|[[Sins of the Father]]||{{SCP|Woodcutting|62|sort}}||{{SCP|Fletching|60}}, {{SCP|Crafting|56}}, {{SCP|Agility|52}}, {{SCP|Attack|50}}, {{SCP|Slayer|50}}, {{SCP|Magic|49}}\n|-\n|[[Song of the Elves]]||{{SCP|Woodcutting|70|sort}}||{{SCP|Agility|70}}, {{SCP|construction|70}}, {{SCP|Farming|70}}, {{SCP|Herblore|70}}, {{SCP|Hunter|70}}, {{SCP|Mining|70}}, {{SCP|Smithing|70}}\n|-\n|[[Grim Tales]]||{{SCP|Woodcutting|71|sort}}||{{SCP|Farming|45}}, {{SCP|Herblore|52}}, {{SCP|Thieving|58}}, {{SCP|Agility|59}}\n|}\n\n===Quests rewarding Woodcutting experience===\n{{Main|Quest experience rewards}}\n\n{| class=\"wikitable sortable lighttable sticky-header mw-collapsible qc-active oqg-table\" style=\"text-align:center;\"\n!Quest\n!Experience<br/>reward\n!Woodcutting<br/>requirement\n!class=\"unsortable\"|Other requirements\n|-\n|[[Enlightened Journey]]||1,500||-||{{SCP|Quest|20}}, {{SCP|Firemaking|20}}, {{SCP|Farming|30}}, {{SCP|Crafting|36}}\n|-\n|[[Recipe for Disaster]]<br/>([[Recipe for Disaster/Freeing Skrach Uglogwee|Skrach Uglogwee subquest]])||1,500||-||{{SCP|Cooking|41}}, {{SCP|Firemaking|20}}\n|-\n|[[Heroes' Quest]]||1,575||-||{{SCP|Quest|55}}, {{SCP|Cooking|53}}, {{SCP|Fishing|53}}, {{SCP|Herblore|25}}, {{SCP|Mining|50}}\n|-\n|[[Monk's Friend]]||2,000||-||-\n|-\n|[[The Ribbiting Tale of a Lily Pad Labour Dispute]]||2,000||{{SCP|Woodcutting|15}}||-\n|-\n|[[Animal Magnetism]]||2,500||{{SCP|Woodcutting|35}}||{{SCP|Slayer|18}}, {{SCP|Crafting|19}}, {{SCP|Ranged|30}}\n|-\n|[[The Eyes of Glouphrie]]||2,500||{{SCP|Woodcutting|45}} {{Ironman|icon}}||{{SCP|Construction|5}}, {{SCP|Magic|46}}\n|-\n|[[The Fremennik Trials]]||2,812||{{SCP|Woodcutting|40}}||{{SCP|Fletching|25}}, {{SCP|Crafting|40}} if crafting your own lyre\n|-\n|[[Icthlarin's Little Helper]]||4,000||-||-\n|-\n|[[The Fremennik Isles]]||10,000||{{SCP|Woodcutting|56}} {{Ironman|icon}}||{{SCP|Construction|20}}, {{SCP|Agility|40}}, {{SCP|Crafting|46}}\n|-\n|[[Song of the Elves]]||40,000||{{SCP|Woodcutting|70}}||{{SCP|Agility|70}}, {{SCP|Construction|70}}, {{SCP|Farming|70}}, {{SCP|Herblore|70}}, {{SCP|Hunter|70}}, {{SCP|Mining|70}}, {{SCP|Smithing|70}}\n|-\n|[[Grim Tales]]||60,000||{{SCP|Woodcutting|71}}||{{SCP|Farming|45}}, {{SCP|Herblore|52}}, {{SCP|Thieving|58}}, {{SCP|Agility|59}}\n|-\n!Total\n!128,387\n!colspan=\"2\"|\n|}\n\n===Skill choice===\nUpon completing any of the following quests, players may choose to allocate experience to Woodcutting. These rewards usually come in the form of items, such as lamps or books, and are independent of any experience rewards directly received for completing the quest.\n{{SkillChoice|Woodcutting}}\n\n==Level up messages==\n{{hastranscript|skill}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 11 September 2024\n|update = Araxxor CAs, Poll 82 Updates & More!\n|change = The chance of receiving [[crystal shard]]s obtained from cutting non-regular trees in [[Prifddinas]] has increased from 1/80 to 1/40.\n}}\n{{Subject changes\n|date = 28 June 2023\n|update = Forestry: The Way of the Forester - Part One\n|change = \n* Trees will now despawn on a timer that activates after the first cut and regenerates if players stop cutting the tree before it is fully chopped.\n* Players chopping the same tree will be given an invisible +1 Woodcutting bonus for each person at the tree, up to a maximum of +10 for 10 players.\n** This bonus does not apply to normal [[tree]]s, any tree found in the [[Woodcutting Guild]], or any tree grown through [[Farming]].\n}}\n{{Subject changes\n|date = 15 December 2015\n|update = Christmas 2015\n|poll = \n|change = Level up messages relating to the canoes no longer reference them being members content as they are available in F2P.\n}}\n{{Subject changes\n|date = 24 September 2015\n|update = New Animations & Barbarian Assault Changes\n|poll = \n|change = Canoes are now listed correctly as F2P content in the Woodcutting skillguide.\n}}\n{{Subject changes footer}}\n\n==Trivia==\n*At level 82 and 89 woodcutting, both the [[rune axe]] and the [[dragon axe]] have the same [[Skilling success rate|chance to successfully]] chop [[magic tree]]s.\n\n==See also==\n* [[Woodcutting/Experience table]], a list of all Woodcutting experience sources\n* [[Woodcutting/Level up table]], a list of all requirements and unlockables within the Woodcutting skill\n\n==References== \n{{Reflist}}\n\n{{Skills}}\n[[Category:Free-to-play skills]]"}