{"title": "Woodcutting Guild", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [{"type": "category", "value": "Temporary skill boost", "confidence": 0.9, "source": "category"}], "relationships": [{"from": "Woodcutting Guild", "to": "2 June", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "2016", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "members", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Woodcutting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Temporary skill boost#List of temporary boosts", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "dragon axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "special attack", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "forestry", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "hard Kourend & Kebos Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "skills necklace", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON>'s blessing", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON>d <PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Lovakengj Minecart Network", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON>d <PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Tithe Farm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Grouping", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Lovakengj Minecart Network", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Veos", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Port Sarim", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Land's End", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Teleport to House", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "POH", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Teleport to house", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON><PERSON> teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "scroll of redirection", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Fairy rings", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON>'s talisman", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Xeric's Lookout", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "spirit tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Farming", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Kourend Castle Teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Client of Kourend", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Pest Control", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Grouping", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Pest control teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Port Sarim", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Land's End", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Veos", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "bank chest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Redwood logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "magic logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Oak tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Willow tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Maple tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Yew tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Magic tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Redwood tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "cave (Woodcutting Guild)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Ents", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "bank chest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Wilderness", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "sawmill", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Lumber Yard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Varrock", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON>'s Chop-chop Shop", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Old School RuneScape", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "rune axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "bank deposit box", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "shrine", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "bird egg", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Prayer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "bird nest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "fruit tree", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "evil chicken outfit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Berry (Woodcutting Guild)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Guildmaster <PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Imp", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Berry (Woodcutting Guild)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "<PERSON>'s Chop-chop Shop", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "bank deposit box", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "bank", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Woodcutting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Country Jig", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "The Forlorn Homestead", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "A Walk in the Woods", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting Guild", "to": "Temporary skill boost", "type": "belongs_to_category", "confidence": 0.9, "source": "category"}], "embedding": [-0.020223168656229973, -0.0015807687304913998, 0.*****************, 0.****************, -0.050439536571502686, 0.0015477464767172933, -0.0037854232359677553, 0.046332117170095444, -0.025744659826159477, 0.010753724724054337, 0.012681975029408932, 0.****************, 0.052505724132061005, -0.013221515342593193, 0.03075653873383999, -0.010573451407253742, 0.04631735384464264, 0.009633068926632404, -0.0562274307012558, -0.046216122806072235, -0.039282239973545074, 0.013321957550942898, -0.04921228811144829, 0.0302431620657444, -0.05493350327014923, -0.05739213526248932, -0.00957611482590437, 0.012416655197739601, -0.0008391985902562737, 0.12952077388763428, -0.02966379001736641, 0.001984167844057083, 8.780819916864857e-05, 0.05749495327472687, 2.462650627421681e-06, -0.03615611419081688, -0.010188065469264984, -0.004434221889823675, -0.05996926128864288, -0.024457573890686035, -0.02380187250673771, -0.038805194199085236, 0.001020916854031384, 0.002345714718103409, -0.015369873493909836, 0.03802485391497612, 0.010679373517632484, -0.01726268045604229, 0.05065156891942024, -0.006267291959375143, 0.0003655869804788381, -0.0483926422894001, -0.059311434626579285, -0.03296511247754097, 0.012984827160835266, 0.02372067980468273, -0.006441527046263218, -0.03452550619840622, 0.007180825807154179, 0.027280837297439575, -0.018878484144806862, 0.020323097705841064, 0.006905697286128998, 0.007476077880710363, 0.05165155604481697, -0.02359207160770893, -0.030609866604208946, -0.009256409481167793, 0.03462141007184982, -0.06368102133274078, 0.008136977441608906, 0.008957230485975742, -0.043364230543375015, 0.002278461353853345, 0.006210751831531525, -0.01466275006532669, -0.007708878722041845, 0.00201620371080935, -0.01822327822446823, 0.005667821504175663, -0.0028289060574024916, 0.011920594610273838, -0.007666714955121279, -0.004529265221208334, 0.05907084420323372, 0.034704338759183884, -0.011929471045732498, 0.03688982501626015, -0.012580528855323792, 0.007601739373058081, -0.0014669839292764664, -0.01590651273727417, -0.07283828407526016, 0.041000936180353165, -0.0214355681091547, 0.00687072379514575, 0.03272341191768646, 0.1587161421775818, 0.07562465965747833, 0.07508724182844162, -0.09412893652915955, 0.005845495034009218, 0.05709552764892578, 0.04483615979552269, -0.03132318705320358, -0.04870457947254181, 0.061561428010463715, 3.434861355344765e-05, 0.015384786762297153, -0.011147897690534592, -0.02579265460371971, -0.030482981353998184, -0.04929304122924805, -0.0344102680683136, -0.0004511160950642079, 0.004715658258646727, 0.00023574325314257294, -0.04242780804634094, -0.014715764671564102, 0.016309604048728943, 0.10460218787193298, 0.013361649587750435, -0.00911383330821991, -0.023129286244511604, 0.02825312502682209, -0.045951101928949356, -0.02201126143336296, 0.053176116198301315, -0.004463861230760813, 0.02320222184062004, 0.0098061328753829, -0.008855711668729782, 0.02655297890305519, 0.010583539493381977, 0.03191790357232094, 0.03558342158794403, -0.05044303834438324, 0.03678885102272034, 0.049163807183504105, -0.01401466690003872, 0.020972903817892075, 0.03924613445997238, -0.02647099643945694, -0.01649179868400097, -0.007744842674583197, -0.019100166857242584, 0.011493884958326817, 0.0017313077114522457, -0.023909108713269234, 0.014407357200980186, -0.022460857406258583, 0.010997830890119076, -0.06356071680784225, -0.009864605963230133, 0.03624957427382469, 0.04254709556698799, -0.10312431305646896, 0.020046068355441093, 0.019246719777584076, 0.053473226726055145, 0.00903115700930357, 0.012921106070280075, -0.023309651762247086, 0.022218303754925728, -0.017501644790172577, -0.0174872986972332, 0.018927477300167084, -0.006037939339876175, -0.08570948988199234, -0.04234194755554199, -0.015308459289371967, 0.00101686199195683, -0.00611597066745162, -0.04559765383601189, 0.026623116806149483, 0.04702828824520111, 0.10732206702232361, 0.01697097346186638, -0.05331944674253464, -0.0033188113011419773, -0.025883527472615242, -0.10631083697080612, 0.02419346198439598, -0.05403145030140877, 0.023233436048030853, 0.04054725542664528, 0.0010220138356089592, 0.007973423227667809, 0.01374860294163227, -0.009633145295083523, -0.026305019855499268, 0.02545306459069252, 0.004633799195289612, -0.014206960797309875, 0.033609986305236816, 0.007134207524359226, -0.038535770028829575, -0.090681292116642, 0.025815332308411598, -0.026855919510126114, 0.031195390969514847, -0.03712525963783264, -0.05097658187150955, 0.024539560079574585, 0.01370952371507883, 0.014099324122071266, 0.037195999175310135, -0.001384396688081324, -0.03496759757399559, 0.018282394856214523, -0.03943467512726784, -0.034651827067136765, 0.05814410373568535, 0.037176840007305145, -0.0018468591151759028, 0.007780933286994696, -0.009004630148410797, 0.007383868098258972, -0.002281403401866555, -0.036241237074136734, -0.03306135535240173, -0.07600719481706619, -0.0037838148418813944, 0.009258341044187546, 0.02708691917359829, 0.0020046213176101446, -0.029204191640019417, 0.0078410804271698, -0.017302362248301506, -0.030540334060788155, -0.005709254182875156, 0.002589957322925329, 0.040424320846796036, -0.045079898089170456, -0.07150035351514816, 0.008655375801026821, 0.0035568983294069767, 0.005586183164268732, 0.014566382393240929, -0.009944482706487179, -0.0161078292876482, -0.015349219553172588, 0.02182827889919281, 0.0011898619122803211, 0.017564894631505013, -0.008060559630393982, 0.0315629243850708, 0.018393035978078842, -0.07488631457090378, -0.004688553512096405, -0.01984989084303379, -0.017846552655100822, 0.02546350285410881, 0.048609551042318344, -0.009283951483666897, 0.003881539450958371, -0.03842172399163246, -0.007725561037659645, -0.02529885247349739, 0.04540174826979637, 0.07065805792808533, -0.04716387763619423, 0.01878291741013527, -0.038472145795822144, 0.038387201726436615, -0.008676384575664997, 0.021408826112747192, -0.01801907829940319, 0.014107206836342812, 0.013436836190521717, 0.023867355659604073, 0.01908198557794094, -0.056984130293130875, -0.04391389340162277, -0.027257265523076057, -0.024289358407258987, 0.024766625836491585, 0.036207396537065506, -0.03329930081963539, -0.02545374445617199, -0.0023555553052574396, 0.05662349611520767, -0.0006820471026003361, 0.015838757157325745, 0.0212235189974308, 0.022347474470734596, 0.05339401215314865, -0.09313707053661346, 0.0002017552760662511, -0.006142083089798689, -0.046216752380132675, 0.003237321740016341, 0.012665304355323315, -0.03342197835445404, -0.0031642841640859842, 0.033404771238565445, 0.049488410353660583, 0.0265674889087677, -0.03929739445447922, -0.010314813815057278, 0.006304597016423941, -0.017452247440814972, 0.029726849868893623, -0.019590165466070175, 0.009790098294615746, 0.05631781369447708, 0.018297776579856873, -0.03443126007914543, 0.05503995344042778, -0.011527160182595253, 0.06608883291482925, 0.02653518319129944, 0.008796833455562592, 0.1005973219871521, -0.007710612379014492, 0.0163876935839653, -0.013671423308551311, -0.025473223999142647, -0.005831012036651373, -0.04379120096564293, 0.043821100145578384, -0.015689250081777573, -0.02263476327061653, -0.03712565451860428, -0.03712969645857811, 0.012049311771988869, -0.0072675831615924835, 0.03128259256482124, 0.01839522458612919, 0.03131788969039917, 0.045966167002916336, -0.03440679609775543, -0.046189695596694946, 0.015802772715687752, 0.04210462421178818, 0.06961610913276672, -0.02456524595618248, -0.0864812508225441, -0.0055493856780231, 0.026824111118912697, -0.0293979924172163, -0.0038145920261740685, -0.020203150808811188, 0.011634365655481815, -0.04388132318854332, 0.06632763147354126, 0.021790215745568275, -0.009422779083251953, 0.051467668265104294, 0.03944597393274307, -0.01566113904118538, 0.07168953120708466, -0.039847616106271744, 0.012923302128911018, -0.03470221534371376, 0.021469755098223686, -0.03447607904672623, -0.02906295284628868, -0.022655609995126724, -0.021814845502376556, -0.0038536370266228914, 0.023325087502598763, -0.001025250181555748, -0.030655458569526672, 0.027225958183407784, -0.040109582245349884, 0.004581037908792496, -0.015261182561516762, -0.004922323394566774, -0.04606538638472557, -0.014971551485359669, 0.03859460726380348, 0.007771545555442572, -0.01767899841070175, 0.031986795365810394, -0.03593188524246216, 0.024975823238492012, 0.004364436957985163, -0.007860302925109863, -0.024056771770119667, -0.011726352386176586, 0.0007600350072607398, -0.00771139282733202, 0.060121819376945496, 0.047581542283296585, -0.030365342274308205, 0.02082032337784767, 0.012001500464975834, 0.03010985627770424, 0.07414956390857697, -0.0033594914712011814, -0.03217832371592522, -0.04865655675530434, 0.0261940099298954, 0.014531940221786499, 0.015505410730838776, -0.04110870137810707, -0.02331014722585678, -0.039410125464200974, 0.03335931524634361, 0.024889031425118446, -0.08180621266365051, -0.03571697697043419, 0.009865090250968933, 0.022079525515437126, 0.043618928641080856, 0.02862553671002388, 0.006883985362946987, 0.029927214607596397, 0.02096385695040226, 0.035971757024526596, 0.0475630909204483, -0.005745673086494207, -0.04491014406085014, 0.052436742931604385, -0.011214773170650005, -0.005274893715977669, -0.0007037095492705703, -0.0028560885693877935, 0.01763872057199478, -0.05107326805591583, 0.03122279793024063, -0.03922506049275398, 0.01921154372394085, 0.04415832459926605, 0.05601547658443451, 0.03334863856434822, -0.0058572920970618725, 0.019242992624640465, -0.0034645169507712126, -0.0022292055655270815, 0.03475113585591316, -0.06762804090976715, 0.02538033202290535, 0.05983256548643112, -0.07926268875598907, 0.019713686779141426, 0.035836536437273026, 0.09955323487520218, -0.05083895102143288, 0.008637412451207638, 0.048884883522987366, 0.0014492215123027563, -0.049429792910814285, 0.019294003024697304, -0.09441989660263062, 0.00886639766395092, -0.013072210364043713, -0.05102205649018288, -0.050113022327423096, 0.019497964531183243, -0.012220579199492931, 0.014754429459571838, -0.008400307036936283, -0.0063085095025599, -0.03264098986983299, -0.018274398520588875, -0.02414671704173088, -0.019649237394332886, -0.06527569890022278, 0.03080577775835991, 0.03891143947839737, 0.0054561672732234, -0.00736598577350378, -0.009293907321989536, -0.013826602138578892, -0.030895516276359558, -0.006176748313009739, 0.050805967301130295, -0.03472679480910301, -0.013908248394727707, -0.010190400294959545, 0.01837640069425106, 0.038980256766080856, 0.007014518138021231, 0.04310936480760574, -0.0001631990453461185, 0.01616220735013485, 0.08507555723190308, -0.03612608090043068, 0.015899255871772766, -0.1318867802619934, -0.01685585267841816, 0.04538905993103981, -0.01829966902732849, 0.02765355072915554, 0.010568590834736824, -0.005127165000885725, -0.005014474969357252, 0.006535990629345179, -0.024328945204615593, -0.0426131896674633, 0.02544190175831318, 0.03860201686620712, 0.04359140247106552, 0.0031173627357929945, -0.10607044398784637, -0.005625101737678051, -0.026546940207481384, -0.04378737509250641, 0.02958032488822937, 0.023717954754829407, -0.005216418765485287, -0.07896871119737625, -0.041671715676784515, 0.009692002087831497, -0.026743775233626366, 0.010233626700937748, 0.002446521306410432, -0.054929524660110474, -0.012666919268667698, 0.003969709388911724, -0.005105387419462204, -0.10168404877185822, 0.03011230379343033, 0.03863878920674324, -0.004551698919385672, 0.009323157370090485, 0.031086569651961327, -0.009660822339355946, -0.0014288929523900151, 0.015300936996936798, 0.0015487304190173745, -0.033170752227306366, -0.021249230951070786, -0.04751532897353172, 0.026341650635004044, 0.0005395332118496299, 0.016943160444498062, 0.02204877696931362, 0.050406262278556824, -0.0010810871608555317, 0.0242016538977623, -0.009580405429005623, -0.038578592240810394, 0.03786662966012955, 0.07961440086364746, 0.024476604536175728, -0.02745574526488781, -0.00024227522953879088, -0.010529347695410252, 0.0005169078940525651, -0.049420520663261414, 0.0021650069393217564, 0.010777999646961689, -0.01908504217863083, -0.019636448472738266, 0.010644782334566116, -0.01572624035179615, -0.009124076925218105, -0.005675834603607655, 7.634906069142744e-05, 0.009146199561655521, 0.0716564878821373, 0.01755739375948906, -7.297936923593182e-33, -0.016962099820375443, 0.000755565008148551, 0.015562841668725014, 0.005369447637349367, -0.01783728040754795, -0.005308841355144978, 0.02648662030696869, -0.02312672883272171, -0.016300765797495842, -0.009013154543936253, -0.012821541167795658, 0.016291050240397453, 0.004772792104631662, 0.0356379970908165, -0.05056567117571831, 0.03898776322603226, 0.049881789833307266, -0.012976160272955894, -0.007818160578608513, -0.027389509603381157, -0.06922762840986252, -0.010318368673324585, 0.05903109163045883, 0.03957560285925865, 0.0264014583081007, 0.0018264636164531112, -0.06267733126878738, -0.013542654924094677, -0.0038602547720074654, -0.07319741696119308, 0.009265112690627575, -0.003179071005433798, 0.00013929794658906758, -0.01558569073677063, 0.01859579235315323, -0.03110603243112564, -0.022850994020700455, 0.007071956526488066, -0.044264987111091614, 0.06519962847232819, -0.015123097226023674, 0.031965937465429306, -0.03802704066038132, 0.03680939972400665, 0.053585439920425415, -0.06442297995090485, -0.007355926558375359, 0.006733024027198553, -0.0017325110966339707, 0.0934676006436348, -0.014641925692558289, -0.008031854405999184, 0.027314485982060432, -0.070304736495018, -0.01971810683608055, 0.010130112059414387, -0.006980269681662321, -0.05045296251773834, -0.050534408539533615, 0.005229328293353319, 0.08268663287162781, -0.0458420105278492, 0.035242754966020584, 0.006425012368708849, -0.019834615290164948, 0.0057098702527582645, -0.03169233351945877, -0.0008649665396660566, 0.037076834589242935, 0.007566226180642843, -0.010758391581475735, 0.05658664554357529, 0.03817788138985634, -0.062002722173929214, 0.034703679382801056, 0.03628719598054886, -0.05239594355225563, -0.012917710468173027, 0.037774909287691116, 0.006379830185323954, 0.032731685787439346, -0.013257795944809914, 0.03865458443760872, -0.04742980748414993, -0.02937409095466137, -0.057566605508327484, 0.02285701036453247, 0.0252689216285944, -0.015926213935017586, 0.012177803553640842, 0.0491066612303257, -0.002215086715295911, -0.05003678798675537, 0.013103794306516647, 0.04316720366477966, 0.0863054022192955, 0.06248685345053673, -0.035009175539016724, -0.013966558501124382, -0.056115977466106415, 0.016234340146183968, 0.05874045938253403, -0.020580047741532326, -0.005720467772334814, 0.016811922192573547, -0.035063840448856354, -0.03210160508751869, 0.0054265474900603294, -0.04792483150959015, 0.0462653748691082, 0.002438158029690385, 0.01563541404902935, -0.011877105571329594, 0.03481360152363777, 0.08687041699886322, -0.048630766570568085, 0.024585625156760216, 0.006196142174303532, -0.015777284279465675, -0.037670668214559555, 0.03893675655126572, 0.013760221190750599, -0.03930360823869705, -0.024941718205809593, -0.03742462769150734, 0.02267496846616268, 0.0718180313706398, -0.037025000900030136, 0.01934184692800045, 0.015742948278784752, 0.016239680349826813, -0.1134871393442154, 3.215091624042543e-07, -0.006817071698606014, 0.061372432857751846, -0.016595540568232536, -0.017781183123588562, 0.02119865082204342, -0.026977024972438812, -0.012849650345742702, 0.02227511815726757, 0.022609911859035492, 0.01733299344778061, -0.00035069187288172543, 0.015150762163102627, -0.02852349542081356, 0.008201231248676777, -0.0215078704059124, 0.02418728731572628, -0.021477678790688515, 0.0005351454601623118, 0.014129046350717545, -0.010830148123204708, -0.026373611763119698, -0.011086859740316868, -0.009461422450840473, -0.02991148643195629, -0.012782330624759197, -0.05587637424468994, -0.003715229220688343, -0.0011554992524906993, 0.022896599024534225, -0.0408138632774353, -0.0406508594751358, -0.0678931176662445, 0.05591253936290741, 0.008369958028197289, 0.013765484094619751, 0.03491531312465668, 0.010961005464196205, -0.007166131399571896, -0.050644733011722565, 0.13314278423786163, -0.03831278905272484, -0.0005221070605330169, 0.03103966824710369, 0.030921686440706253, 0.016619907692074776, -0.07044785469770432, -0.03505992516875267, -0.03148757666349411, 0.022557802498340607, -0.014509830623865128, -0.014280689880251884, 0.010222584009170532, -0.015520112589001656, -0.005029682070016861, 0.023154241964221, 0.003050669329240918, 0.03873799741268158, -0.0011501159751787782, 0.013559327460825443, -0.06234973669052124, -0.05018364265561104, -0.03976016864180565, 0.0410265289247036, 0.048733748495578766, -0.04599009081721306, 0.054556820541620255, -0.10149621963500977, 2.9983316039324856e-34, -0.018852846696972847, 0.025353018194437027, -0.016967898234725, 0.027449393644928932, 0.019424598664045334, -0.026870612055063248, 0.017382679507136345, -0.027987750247120857, 0.008996614255011082, -0.0004805061616934836, -0.03357423469424248], "parsedContent": {"infobox": {}, "categories": ["Temporary skill boost"], "links": ["2 June", "2016", "<PERSON><PERSON><PERSON>", "members", "guild", "<PERSON><PERSON><PERSON>", "Woodcutting", "Temporary skill boost#List of temporary boosts", "dragon axe", "special attack", "forestry", "hard Kourend & Kebos Diary", "skills necklace", "<PERSON><PERSON>'s blessing", "<PERSON><PERSON>d <PERSON>", "Lovakengj Minecart Network", "<PERSON><PERSON>d <PERSON>", "Tithe Farm", "Grouping", "Lovakengj Minecart Network", "Veos", "Port Sarim", "Land's End", "Teleport to House", "POH", "<PERSON><PERSON><PERSON>", "Teleport to house", "<PERSON><PERSON><PERSON> teleport", "scroll of redirection", "Fairy rings", "<PERSON><PERSON>'s talisman", "Xeric's Lookout", "spirit tree", "Farming", "Kourend Castle Teleport", "Client of Kourend", "Pest Control", "Grouping", "Pest control teleport", "Port Sarim", "Land's End", "Veos", "bank chest", "Redwood logs", "magic logs", "Tree", "Oak tree", "Willow tree", "Maple tree", "Yew tree", "Magic tree", "Redwood tree", "cave (Woodcutting Guild)", "Ents", "bank chest", "Wilderness", "sawmill", "Lumber Yard", "Varrock", "<PERSON>'s Chop-chop Shop", "Old School RuneScape", "rune axe", "bank deposit box", "shrine", "bird egg", "Prayer", "bird nest", "tree", "fruit tree", "evil chicken outfit", "Berry (Woodcutting Guild)", "Guildmaster <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Imp", "Berry (Woodcutting Guild)", "<PERSON><PERSON><PERSON>", "<PERSON>'s Chop-chop Shop", "bank deposit box", "bank", "Woodcutting", "Country Jig", "The Forlorn Homestead", "A Walk in the Woods"], "plainText": "|map =  |type = guild |leagueRegion = Kourend }} The Woodcutting Guild is a members-only guild located in the south-western corner of Hosidius. In order to enter the guild, players must have at least level 60 in Woodcutting (which can be boosted). While inside the guild, players will receive an invisible +7 level boost to their Woodcutting. This boost will stack with any existing boosts the player already has, such as the visible +3 boost from using the dragon axe's special attack, though visible boosts are capped to level 99. However, the invisible boost will not allow a player to perform actions above their base level. In addition, forestry events will not occur while inside the Woodcutting Guild. There is no group Woodcutting bonus whilst cutting trees inside the guild. Entering the Woodcutting Guild is a task in the hard Kourend & Kebos Diary. Transportation *Use a skills necklace to teleport directly outside the front gate. *Use <PERSON><PERSON>'s blessing to teleport to Kourend Woodland and run north-east to enter through the back. *Lovakengj Minecart Network to Hosidius West and run south, or to Kourend Woodland and run west then north. *Tithe Farm teleport via the Grouping interface and use the Lovakengj Minecart Network, or run West. *Right click V<PERSON><PERSON>, who is located on the northern dock of Port Sarim, to travel to Land's End and head north-east. *Teleport to House while the player's POH is in Hosidius. Redirecting a Teleport to house tablet to a Hosidius teleport tablet via the use of a scroll of redirection works as well. *Take the Fairy Ring to Hosidius Vinery  () and then run west. *Use Xeric's talisman to teleport to Xeric's Lookout and run south. *Use a fully grown spirit tree to teleport north-east of the guild entrance. Requires 83 Farming. *Casting Kourend Castle Teleport and running south. Requires completion of Client of Kourend. *Pest Control teleport via the Grouping interface (or a Pest control teleport), take the boat back to Port Sarim, then to Land's End via Veos and run north-east upon arrival. Features  Bank chest  The guild features a bank chest near in the western section. This is useful for players looking to bank their redwood or magic logs. Trees A group of players chopping redwood. The entrance to the upper level can be seen on the right tree. Within the guild players can find the following types of trees: *11 Trees *4 Oak trees *7 Willow trees *13 Maple trees *17 Yew trees *8 Magic trees *2 Redwood trees (24 walls to chop) Ent dungeon The ent habitat found underneath the Woodcutting Guild. The Woodcutting Guild dungeon, accessible through the cave entrance near the sawmill, contains 22 Ents (combat level 86) and a bank chest. It is the only place outside the Wilderness where players can kill and woodcut ent remains. Unlike the wilderness ents, the ents in the dungeon will only reward one noted log instead of two for every successful chop. If the player is using a boost to enter the guild, level 60 Woodcutting (not boostable) is also required to enter the dungeon.  Bank Chest  Another bank chest can be found in the back of the ent dungeon. The chest is one of the most-unused banks in the game, making it a potential location for players looking to train bankable skills quietly. Sawmill There is a sawmill available for use, which is much closer to the bank compared to the main sawmill in the Lumber Yard north-east of Varrock. Perry's Chop-chop Shop Perry's Chop-chop Shop can be found by the entrance inside the guild. This is the only shop in Old School RuneScape that sells rune axes. Within the shop, there is a bank deposit box that is not shown on the minimap, which is convenient for the 3 yew trees directly north of the shop and the 1 yew tree and several maple trees west of the shop. Shrine A player offers bird's eggs to the shrine. The shrine is a large bird's nest located outside the entrance to the Ent dungeon. It is marked by the File:Altar icon.png on the minimap. Players can use the shrine to recharge their prayer, and offer bird eggs to this shrine to receive 100 Prayer experience and a bird nest containing tree or fruit tree seeds. There is also a 1/300 chance of receiving a piece of the evil chicken outfit for each egg offered. Personalities *Berry *Guildmaster Lars *Murfet *Nesty *Kai *Forester *Perry *Imps may be encountered inside the guild. Changes Soundtrack The music played in the Woodcutting Guild has changed several times throughout its history. In 2016, Country Jig was played, but after complaints that it didn't suit the guild, the theme was changed to The Forlorn Homestead. In 2019, the theme changed again to A Walk in the Woods. References Category:Temporary skill boost"}, "originalContent": "{{Infobox Location\n|name = Woodcutting Guild\n|image = [[File:Woodcutting Guild.png|300px]]\n|release = [[2 June]] [[2016]]\n|update = The Woodcutting Guild\n|members = Yes\n|location = [[Ho<PERSON>dius]]\n|requirement = {{SCP|Woodcutting|60}}\n|map = {{Map|name=Woodcutting Guild|height=200|zoom=1|mtype=polygon|1565,3499|1563,3497|1563,3478|1564,3477|1582,3477|1587,3472|1595,3472|1596,3473|1596,3480|1601,3485|1601,3497|1607,3497|1607,3492|1612,3487|1617,3487|1623,3493|1632,3493|1633,3492|1633,3490|1634,3489|1648,3489|1656,3497|1656,3502|1657,3502|1658,3503|1658,3507|1655,3510|1655,3516|1654,3517|1633,3517|1631,3519|1624,3519|1621,3516|1612,3516|1610,3514|1608,3514|1607,3513|1607,3511|1604,3511|1604,3506|1607,3506|1607,3501|1601,3501|1601,3503|1600,3504|1582,3504|1577,3499}}\n|type = guild\n|leagueRegion = Kourend\n}}\nThe '''Woodcutting Guild''' is a [[members]]-only [[guild]] located in the south-western corner of [[Hosidius]]. In order to enter the guild, players must have at least level 60 in [[Woodcutting]] (which can be [[Temporary skill boost#List of temporary boosts|boosted]]).\n\nWhile inside the guild, players will receive an invisible +7 level boost to their Woodcutting. This boost will stack with any existing boosts the player already has,{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/740931209543487488|date=9 June 2016|archiveurl=https://archive.is/G7ewB|archivedate=30 May 2020|quote=[With the +7 boost in the WC guild, does it make the dragon axe special pointless? Or would that increase it to +10?] They stack.}} such as the visible +3 boost from using the [[dragon axe]]'s [[special attack]], though visible boosts are capped to level 99.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1375844352098975753|date=27 March 2021|archiveurl=https://archive.is/5piyY|archivedate=29 March 2021|quote=I did a little more trawling of the code, and can confirm that the visible stat is now treated as 99 if it's above 99 - that is a recent change. It seems to have been applied to Mining, Fishing and Woodcutting.}} However, the invisible boost '''will not''' allow a player to perform actions above their base level. In addition, [[forestry]] events will not occur while inside the Woodcutting Guild. There is no group Woodcutting bonus whilst cutting trees inside the guild.\n\nEntering the Woodcutting Guild is a task in the [[hard Kourend & Kebos Diary]].\n\n==Transportation==\n*Use a [[skills necklace]] to teleport directly outside the front gate.\n*Use [[Rada's blessing]] to teleport to [[Kourend Woodland]] and run north-east to enter through the back.\n*[[Lovakengj Minecart Network]] to Hosidius West and run south, or to [[Kourend Woodland]] and run west then north.\n*[[Tithe Farm]] teleport via the [[Grouping]] interface and use the [[Lovakengj Minecart Network]], or run West.\n*Right click [[Veos]], who is located on the northern dock of [[Port Sarim]], to travel to [[Land's End]] and head north-east.\n*[[Teleport to House]] while the player's [[POH]] is in [[Hosidius]]. Redirecting a [[Teleport to house]] tablet to a [[Hosidius teleport]] tablet via the use of a [[scroll of redirection]] works as well.\n*Take the [[Fairy rings|Fairy Ring]] to Hosidius Vinery  ({{Fairycode|AKR}}) and then run west.\n*Use [[Xeric's talisman]] to teleport to [[Xeric's Lookout]] and run south.\n*Use a fully grown [[spirit tree]] to teleport north-east of the guild entrance. Requires 83 [[Farming]].\n*Casting [[Kourend Castle Teleport]] and running south. Requires completion of [[Client of Kourend]].\n*[[Pest Control]] teleport via the [[Grouping]] interface (or a [[Pest control teleport]]), take the boat back to [[Port Sarim]], then to [[Land's End]] via [[Veos]] and run north-east upon arrival.\n\n==Features==\n\n=== Bank chest ===\nThe guild features a [[bank chest]] near in the western section. This is useful for players looking to bank their [[Redwood logs|redwood]] or [[magic logs]].\n\n===Trees===\n[[File:Chopping redwood.png|thumb|280px|A group of players chopping redwood. The entrance to the upper level can be seen on the right tree.]]\nWithin the guild players can find the following types of trees:\n*11 [[Tree]]s\n*4 [[Oak tree]]s\n*7 [[Willow tree]]s\n*13 [[Maple tree]]s\n*17 [[Yew tree]]s\n*8 [[Magic tree]]s\n*2 [[Redwood tree]]s (24 walls to chop)\n\n===Ent dungeon===\n{{Map|1572,9888|mapID=32|align=right|width=250|height=250|caption=A map of the Woodcutting Guild dungeon.}}\n[[File:Woodcutting Guild ent area.png|thumb|left|The ent habitat found underneath the Woodcutting Guild.]]\nThe Woodcutting Guild dungeon, accessible through the [[cave (Woodcutting Guild)|cave entrance]] near the sawmill, contains 22 [[Ents]] (combat level 86) and a [[bank chest]]. It is the only place outside the [[Wilderness]] where players can kill and woodcut ent remains. Unlike the wilderness ents, the ents in the dungeon will only reward one noted log instead of two for every successful chop. If the player is using a boost to enter the guild, level 60 Woodcutting (not boostable) is also required to enter the dungeon.\n\n==== Bank Chest ====\nAnother bank chest can be found in the back of the ent dungeon. The chest is one of the most-unused banks in the game, making it a potential location for players looking to train bankable skills quietly.\n\n===Sawmill===\nThere is a [[sawmill]] available for use, which is much closer to the bank compared to the main sawmill in the [[Lumber Yard]] north-east of [[Varrock]].\n\n===Perry's Chop-chop Shop===\n[[Perry's Chop-chop Shop]] can be found by the entrance inside the guild. This is the only shop in ''[[Old School RuneScape]]'' that sells [[rune axe]]s.\n\nWithin the shop, there is a [[bank deposit box]] that is not shown on the minimap, which is convenient for the 3 yew trees directly north of the shop and the 1 yew tree and several maple trees west of the shop.\n\n===Shrine===\n[[File:Offering god eggs.gif|thumb|180px|A player offers [[bird's egg]]s to the shrine.]]\n\nThe [[shrine]] is a large bird's nest located outside the entrance to the Ent dungeon. It is marked by the [[File:Altar icon.png]] on the minimap. Players can use the shrine to recharge their prayer, and offer [[bird egg]]s to this shrine to receive 100 [[Prayer]] experience and a [[bird nest]] containing [[tree]] or [[fruit tree]] seeds. There is also a 1/300 chance of receiving a piece of the [[evil chicken outfit]] for each egg offered.\n\n==Personalities==\n*[[Berry (Woodcutting Guild)|Berry]]\n*[[Guildmaster Lars]]\n*[[Murfet]]\n*[[Nesty]]\n*[[Kai]]\n*[[Forester]]\n*[[Perry]]\n*[[Imp]]s may be encountered inside the guild.\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 24 January 2024\n|update = Scurrius, the Rat King\n|change = [[Berry (Woodcutting Guild)|Berry]] will no longer interrupt players with a chat box every time they enter the Woodcutting Guild.\n}}\n{{Subject changes\n|date = 10 January 2024\n|update = Children of the Sun\n|poll = Varlamore Activities & Kourend Changes\n|change = As [[Kourend Favour]] has been removed, accessing the guild no longer requires it.\n}}\n{{Subject changes\n|date = 9 June 2016\n|update = The Catacombs of Kourend\n|change =\n* [[Perry's Chop-chop Shop]] has been added to the Woodcutting Guild.\n* A [[bank deposit box]] has been added to the Woodcutting Guild [[bank]].\n* The Woodcutting Guild now provides a hidden +7 [[Woodcutting]] boost.\n}}\n{{Subject changes\n|date = 2 June 2016\n|update = The Woodcutting Guild\n|poll = Old School Content Poll 43: Great Kourend Content\n|change = The Woodcutting Guild is added.\n}}\n{{Subject changes footer}}\n\n==Soundtrack==\nThe music played in the Woodcutting Guild has changed several times throughout its history. In 2016, [[Country Jig]] was played, but after complaints that it didn't suit the guild, the theme was changed to [[The Forlorn Homestead]]. In 2019, the theme changed again to [[A Walk in the Woods]].\n\n==References==\n{{Reflist}}\n\n{{Woodcutting Guild}}\n{{Guilds}}\n{{Hosidius}}\n[[Category:Temporary skill boost]]"}