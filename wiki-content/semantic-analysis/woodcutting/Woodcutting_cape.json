{"title": "Woodcutting cape", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [{"type": "category", "value": "Temporary skill boost", "confidence": 0.9, "source": "category"}, {"type": "category", "value": "Woodcutting boosting items", "confidence": 0.9, "source": "category"}], "relationships": [{"from": "Woodcutting cape", "to": "18 October", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "2006", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "Lumbridge", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "coins", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "Woodcutting hood", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "Woodcutting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "Cape of Accomplishment", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "bird nest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "clue nests", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "ring nest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "egg nest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "seed nest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "forestry kit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "Forestry basket", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "cape pouch", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "Dragon axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "infernal axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "Woodcutting hood", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "Woodcutting cape", "to": "Temporary skill boost", "type": "belongs_to_category", "confidence": 0.9, "source": "category"}, {"from": "Woodcutting cape", "to": "Woodcutting boosting items", "type": "belongs_to_category", "confidence": 0.9, "source": "category"}], "embedding": [0.01376413181424141, -0.04321713373064995, 0.019662724807858467, 0.016758743673563004, -0.061059001833200455, -0.02283136546611786, 0.016826940700411797, 0.0075143007561564445, -0.11609454452991486, 0.008007780648767948, -0.004183770157396793, 0.07707515358924866, 0.06427625566720963, -0.020299246534705162, 0.014178772456943989, -0.03782273456454277, -0.0066178725101053715, -0.016559161245822906, 0.004246924072504044, -0.00961899384856224, 0.033941660076379776, 0.016718696802854538, -0.03416302055120468, -0.0027534596156328917, -0.06504086405038834, -0.021916307508945465, -0.005278958939015865, -0.015540392138063908, 0.016115011647343636, 0.0370887890458107, 0.024729404598474503, -0.002364533022046089, -0.03782353550195694, -0.016672983765602112, 2.271042831125669e-06, -0.039892036467790604, 0.020214136689901352, 0.006924379616975784, -0.05698645859956741, -0.030497325584292412, 0.0396585650742054, 0.006393678952008486, 0.02750297822058201, 0.038583870977163315, 0.00351046328432858, 0.045946430414915085, -0.03059312328696251, -0.0020573993679136038, 0.015494883060455322, -0.00554853118956089, -0.012923356145620346, -0.028597688302397728, 0.01604117639362812, -0.023258714005351067, 0.030966799706220627, 0.027392886579036713, -0.017710665240883827, -0.01729595847427845, 0.032663241028785706, -0.014279802329838276, 0.021397296339273453, -0.02840612269937992, 0.002439191099256277, 0.023083288222551346, -0.0070460159331560135, -0.026947837322950363, -0.0032579367980360985, 0.001484400941990316, 0.04171469807624817, -0.008569899015128613, -0.006418346427381039, 0.014487355947494507, -0.02653641439974308, 0.031038347631692886, 0.06724323332309723, 0.00692058727145195, -0.00665522925555706, -0.0008735184092074633, -0.006104299332946539, -0.008597028441727161, -0.02350596897304058, -0.011150680482387543, -0.02063646726310253, 0.0047617945820093155, 0.02576892264187336, 0.012800253927707672, -0.03373795002698898, 0.03191886842250824, -0.017889637500047684, -0.03135645389556885, -0.04092780128121376, -0.009970434941351414, -0.05803109332919121, 0.02148900181055069, 0.01410555001348257, 0.032822977751493454, -0.02663005329668522, 0.07418248802423477, 0.04744688794016838, 0.0946490615606308, -0.05491591989994049, 0.005111127160489559, 0.026123331859707832, 0.029494112357497215, -0.04455442726612091, -0.10389838367700577, 0.021902399137616158, -0.02802778221666813, 0.0026633422821760178, -0.004659537225961685, 0.014902444556355476, -0.03041766583919525, 0.005921334493905306, -0.006943713407963514, -0.046350520104169846, 0.005507317371666431, 0.04680589959025383, -0.0604410246014595, -0.014383500441908836, 0.05065208300948143, 0.02749369665980339, -0.038426052778959274, -0.013581856153905392, -0.022296514362096786, 0.03379502892494202, -0.05617682635784149, -0.007836380042135715, 0.07843007147312164, -0.0067890482023358345, 0.04358462616801262, 0.0016237442614510655, -0.03242618218064308, 0.01614227145910263, -0.0475919172167778, 0.03356771171092987, 0.02159269154071808, -0.05759096518158913, 0.03806248679757118, 0.0302532110363245, -0.044618066400289536, -0.03828984126448631, 0.04847574606537819, -0.018211545422673225, -0.023095671087503433, -0.04801680147647858, -0.012928620912134647, -0.008567680604755878, 0.004649657756090164, -0.00726630724966526, -0.0012289343867450953, -0.005763644818216562, 0.010336787439882755, 0.0021612574346363544, -0.0171052198857069, 0.027317583560943604, 0.023103779181838036, -0.07903532683849335, 0.026957383379340172, -0.013269410468637943, 0.052316807210445404, -0.012940943241119385, 0.006823521573096514, -0.09470370411872864, 0.01537852268666029, -0.0014670129166916013, -0.01895521767437458, -0.10587422549724579, -0.016747118905186653, -0.011339961551129818, -0.023391837254166603, 0.013239715248346329, -0.060163941234350204, -0.033746231347322464, -0.0322103425860405, 0.011914750561118126, 0.015408492647111416, 0.07848425954580307, 0.04219450429081917, -0.0635567456483841, 0.058075036853551865, 0.009776541031897068, -0.08286269754171371, 0.03210873529314995, -0.051447853446006775, -0.0757889673113823, -0.0032311303075402975, -0.03820855915546417, -0.016715308651328087, -0.03367982804775238, -0.030065882951021194, 0.007879303768277168, 0.009958752430975437, -0.09262653440237045, -0.009743536822497845, 0.0013325260952115059, -0.01884194277226925, 3.313008710392751e-05, -0.09726597368717194, -0.02143264003098011, -0.028223536908626556, 0.036742839962244034, -0.018410174176096916, -0.06652629375457764, 0.024540243670344353, -0.03543645516037941, 0.011113698594272137, 0.005888889078050852, 0.009152728132903576, -0.034279681742191315, -0.03231097012758255, -0.04639747738838196, -0.029454853385686874, 0.011547759175300598, 0.04567187279462814, -0.0009480041917413473, -0.025340083986520767, -0.014601347967982292, -0.003727698465809226, -0.0021660812199115753, 0.0002284831425640732, -0.045129697769880295, -0.07334808260202408, 0.08916590362787247, 0.02509821392595768, 0.06719569116830826, -0.0034770455677062273, -0.04923166334629059, 0.008547970093786716, 0.04082942754030228, -0.030414117500185966, -0.014742560684680939, -0.009771320968866348, 0.03849029913544655, -0.008263003081083298, -0.06791221350431442, 0.07147286087274551, 0.0020591970533132553, -0.01693050190806389, 0.035245779901742935, -0.0566023625433445, -0.012975865975022316, 0.03710656985640526, 0.015052816830575466, -0.012908414006233215, -0.021272825077176094, -0.04516081139445305, 0.03803509473800659, 0.03559282794594765, -0.051390718668699265, 0.0033487174659967422, -0.008852166123688221, 0.01845325529575348, 0.02716817334294319, 0.022130051627755165, -0.02862740494310856, 0.05703899636864662, 0.05236349254846573, -0.019202563911676407, 0.01753951422870159, 0.0027610838878899813, -0.005465899594128132, -0.009288253262639046, 0.05212649703025818, -0.03632703423500061, 0.021791616454720497, -0.003385405521839857, -0.019380517303943634, -0.01883137784898281, -0.004223315976560116, 0.03527142480015755, -0.010552734136581421, -0.015648163855075836, -0.030255993828177452, -0.054078225046396255, -0.020909758284687996, -0.00034649422741495073, -0.03037356398999691, 0.010255757719278336, -0.023794760927557945, -0.037013210356235504, 0.0021472740918397903, 0.0321502685546875, 0.028225203976035118, 0.003483224892988801, 0.022643106058239937, 0.04386686906218529, 0.0073554180562496185, -0.03544618561863899, -0.00905096996575594, 0.09316007792949677, 0.006107581313699484, 0.025871122255921364, 0.003051015315577388, -0.0032701557502150536, 0.001792325172573328, 0.017531903460621834, 0.042662255465984344, 0.02323293313384056, -0.002120727440342307, -0.017312128096818924, -0.014258972369134426, 0.02387707121670246, 0.0013874720316380262, 0.0033917368855327368, 0.01050540804862976, -0.03092068061232567, 0.011460362002253532, -0.006273801438510418, 0.060094013810157776, -0.03312225267291069, 0.03464588150382042, 0.013936500996351242, 0.029501017183065414, 0.03678524121642113, 0.04092509299516678, -0.05904978886246681, -0.025180505588650703, 0.02397460862994194, -0.01008373498916626, 0.016972234472632408, 0.024855248630046844, -0.0012006291653960943, -0.0203548613935709, -0.01850164867937565, 0.008542484603822231, 0.011661586351692677, 0.05080186203122139, 0.04385692626237869, 0.07157985121011734, 0.025543835014104843, 0.015032477676868439, -0.09100370109081268, -0.026770928874611855, 0.03594447299838066, 0.0008245000499300659, 0.05658341199159622, -0.016053851693868637, -0.14074397087097168, -0.04616422578692436, 0.03092386946082115, 0.002523437375202775, -0.03709877282381058, -0.014655254781246185, -0.012235144153237343, -0.041448649019002914, 0.0480448380112648, -0.02585883066058159, 0.006048304960131645, 0.028355780988931656, 0.015144072473049164, 0.030588215216994286, 0.04869190976023674, -0.006138366647064686, 0.0035209967754781246, -0.02406666800379753, 0.01575518399477005, -0.02301039919257164, -0.05105511099100113, 0.012077477760612965, 0.017955688759684563, 0.005978403612971306, 0.0029805651865899563, -0.025359880179166794, 0.0053642368875443935, 0.04234011843800545, 0.0034190684091299772, -0.008108288049697876, -0.008717839606106281, 0.016767390072345734, -0.016003889963030815, 0.057447999715805054, 0.0352478101849556, 0.021784847602248192, 0.019016645848751068, -0.0018998314626514912, -0.013971740379929543, -0.0052939183078706264, -0.035706114023923874, 0.013707274571061134, -0.016019288450479507, -0.015041187405586243, 0.04587381333112717, 0.00657778512686491, 0.014610501937568188, 0.021746525540947914, 0.029299885034561157, 0.038702744990587234, 0.04498736187815666, -0.049537330865859985, 0.02666635625064373, -0.008461594581604004, 0.04019661620259285, 0.01568981260061264, 0.028417129069566727, 0.04072658717632294, 0.017800791189074516, -0.041457753628492355, 0.004039057530462742, -0.0117109389975667, 0.0332050547003746, 0.03469356894493103, -0.05516921728849411, -0.015358077362179756, -0.00811455026268959, 0.04087391868233681, -0.009215554222464561, -0.02285158261656761, -0.00674120057374239, -0.013570892624557018, 0.027065299451351166, -0.023962708190083504, -0.019514117389917374, -0.02347986400127411, -0.010154258459806442, 0.035326406359672546, 0.0184121485799551, 0.010332359001040459, 0.00950162298977375, 0.04831979051232338, 0.016233112663030624, -0.0622984953224659, 0.07964257895946503, -0.02076314389705658, 0.017772983759641647, 0.09961238503456116, 0.026379864662885666, 0.004533323924988508, -0.10125095397233963, 0.01776537299156189, -0.0073979683220386505, -0.023029442876577377, 0.021895630285143852, -0.028750566765666008, -0.01416966412216425, -0.011108585633337498, -0.0451345257461071, 0.043116603046655655, 0.04486854001879692, 0.08911935985088348, -0.03075660765171051, 0.013411861844360828, 0.04869421198964119, 0.023296386003494263, -0.01730751059949398, 0.0026777840685099363, -0.1620570868253708, 0.042378317564725876, -0.07001025974750519, -0.045328810811042786, -0.03044026531279087, -0.031651075929403305, 0.007838299497961998, 0.07495112717151642, 0.029054760932922363, -0.04160432144999504, -0.04200321435928345, -0.06336469203233719, 0.043303508311510086, -0.03330577537417412, -0.0658518522977829, 0.06959298998117447, 0.029742639511823654, 0.001826880732551217, -0.010872174054384232, -0.03438599035143852, -0.03440845012664795, -0.04772314056754112, -0.03342525660991669, -0.0013684104196727276, -0.013527191244065762, 0.006082911044359207, 0.010119856335222721, 0.005049281287938356, 0.051948945969343185, 0.029674390330910683, 0.025746243074536324, 0.014515331946313381, 0.04213147610425949, -0.007051847875118256, 0.010458741337060928, -0.03588268160820007, -0.10164793580770493, 0.014382238499820232, 0.03950238972902298, -0.014912165701389313, 0.042103637009859085, 0.05229763314127922, -0.028484871610999107, 0.03965228796005249, 0.005423650145530701, -0.03967827185988426, -0.005588743835687637, 0.01772543229162693, 0.023191792890429497, 0.015076199546456337, 0.01706654205918312, -0.04636283963918686, -0.0323936864733696, 0.0011633675312623382, 0.025967871770262718, 0.0054590655490756035, 0.005917455069720745, 0.007876472547650337, -0.0847276970744133, -0.04847698658704758, -0.006646560039371252, -0.02327602170407772, -0.015521478839218616, -0.021333828568458557, 0.011331461369991302, -0.026890061795711517, 0.031544942408800125, 0.009600188583135605, -0.11207471042871475, 0.015795649960637093, 0.038983967155218124, -0.023601138964295387, -0.028193971142172813, 0.024651119485497475, -0.028595173731446266, -0.0019807214848697186, -0.00470998790115118, 0.04452948272228241, -0.04486868530511856, 0.000689332140609622, 0.010305877774953842, 0.001114457263611257, -0.03252637758851051, -0.013061915524303913, 0.0018732990138232708, 0.0573461577296257, 0.024244463071227074, 0.028703156858682632, -0.03342660889029503, -0.04293356463313103, -0.014336819760501385, 0.050622496753931046, -0.04999950900673866, 0.028593869879841805, -0.039328206330537796, -0.010830620303750038, -0.010829471983015537, 0.029728611931204796, -0.05837228521704674, 0.027111981064081192, -0.0011029932647943497, -0.009847894310951233, 0.031490106135606766, -0.019275618717074394, 0.01396573893725872, 0.01225184928625822, 0.03724803775548935, 0.0463971309363842, 0.02801910974085331, 0.032978471368551254, -6.533044953565027e-33, 0.027283672243356705, 0.048123519867658615, 0.017692163586616516, 0.009908025152981281, -0.009777775965631008, 0.035358529537916183, 0.03868670389056206, 0.023805316537618637, 0.023852135986089706, -0.03232947364449501, -0.023394359275698662, -0.005764840170741081, -0.004283285699784756, 0.016871333122253418, -0.03690081089735031, 0.017647169530391693, 0.0673774927854538, 0.002744378987699747, 0.01676838845014572, -0.025748977437615395, -0.006012925412505865, 0.006522811483591795, 0.042230602353811264, 0.039228249341249466, 0.01337661687284708, -0.02772137150168419, -0.018643256276845932, -0.04359794035553932, 0.0384223610162735, -0.04947059601545334, -0.0014733687276020646, -0.0359685942530632, -0.02296834997832775, 0.0746595710515976, 0.002061493694782257, -0.0024520549923181534, -0.011585040017962456, 0.0037580605130642653, -0.01700010895729065, 0.019955245777964592, 0.022173328325152397, -0.015853099524974823, -0.021069549024105072, 0.038783036172389984, 0.019445523619651794, -0.025431370362639427, 0.017652390524744987, -0.006323921959847212, -0.027454646304249763, -0.012321650981903076, 0.013948557898402214, 0.004511550068855286, 0.009754043072462082, -0.08149857074022293, 0.005489993374794722, 0.043886102735996246, -0.012957103550434113, -0.11583825945854187, 0.003881326876580715, 0.040142741054296494, 0.05980682373046875, 0.010622543282806873, 0.01526495348662138, -0.02015623077750206, -0.021652275696396828, -0.002708730287849903, -0.06035872548818588, -0.005178162828087807, 0.06481948494911194, 0.017465123906731606, -0.009795245714485645, 0.07123589515686035, 0.06655583530664444, 0.0018414193764328957, 0.03785772994160652, 0.04365001991391182, -0.05228579416871071, 0.03460432216525078, 0.00909367948770523, 0.09497483819723129, 0.035404883325099945, 0.016979681327939034, 0.03597942367196083, -0.006071666721254587, -0.09207691997289658, -0.04804503917694092, 0.006727244704961777, 0.02491666004061699, 0.011204336769878864, -0.016045527532696724, 0.024323072284460068, -0.012995989061892033, -6.481754098786041e-05, 0.040692318230867386, 0.03003280609846115, -0.004717149306088686, 0.05411621555685997, 0.0019729742780327797, 0.014915745705366135, -0.04524777829647064, 0.012246055528521538, 0.03941332548856735, -0.002178899710997939, 0.028883634135127068, 0.0056804087944328785, -0.050731681287288666, 0.04015520587563515, 0.021127009764313698, -0.019999176263809204, 0.017629848793148994, -0.01653665117919445, 0.009340479038655758, -0.04359656199812889, 0.04694787412881851, 0.054855745285749435, -0.014424756169319153, 0.004940189886838198, -0.028183668851852417, -0.02538318932056427, -0.095896415412426, 0.0068548452109098434, -0.0026655406691133976, -0.001203431049361825, -0.01155927311629057, -0.07034749537706375, -0.006256079766899347, 0.04514402896165848, -0.02899123914539814, 0.008007915690541267, -0.028040152043104172, 0.013270998373627663, -0.04359937086701393, 2.9222371722426033e-07, 0.005994991399347782, -0.009452584199607372, 0.0249925646930933, -0.053385429084300995, -0.0013463431969285011, -0.019887138158082962, 0.003044940996915102, 0.004666046239435673, 0.004483090713620186, -0.021669194102287292, 0.0021865605376660824, 0.0044363150373101234, 0.004005723167210817, 0.018866484984755516, 0.031153814867138863, -0.1210903450846672, 0.06888492405414581, 0.029068157076835632, 0.013854335062205791, -0.01069457083940506, -0.036873284727334976, -0.04628276452422142, 0.00033626265940256417, 0.006163094658404589, 0.005707260221242905, 0.004839311819523573, 0.028892524540424347, -0.04448084533214569, 0.016693511977791786, -0.1079510748386383, -0.03641294315457344, -0.024797946214675903, 0.03855391964316368, -0.050889600068330765, 0.028142599388957024, 0.028741544112563133, 0.011446770280599594, 0.0769733414053917, 0.03097403049468994, 0.12367842346429825, -0.028508691117167473, 0.07023832947015762, 0.005388181656599045, 0.039695627987384796, 0.01927344501018524, -0.017196210101246834, -0.028597598895430565, 0.0073770275339484215, -0.011820419691503048, 0.025183754041790962, 0.006590921897441149, -0.0036713615991175175, -0.039860185235738754, 0.03447921574115753, 0.020103050395846367, 0.022795645520091057, 0.024502461776137352, -0.029358942061662674, -0.015589778311550617, -0.020601516589522362, -0.05895761772990227, -0.009575573727488518, 0.0323672778904438, 0.07174354046583176, -0.06131953373551369, 0.013422851450741291, -0.04640096426010132, 2.6845643814604293e-34, -0.01797378621995449, 0.03167402744293213, -0.014195282012224197, 0.006916312035173178, 0.002437601098790765, -0.003750160103663802, 0.08765753358602524, 0.0040527586825191975, 0.007991558872163296, -0.01877974532544613, -0.032034531235694885], "parsedContent": {"infobox": {}, "categories": ["Temporary skill boost", "Woodcutting boosting items"], "links": ["18 October", "2006", "<PERSON><PERSON><PERSON>", "Lumbridge", "coins", "Woodcutting hood", "Woodcutting", "Cape of Accomplishment", "bird nest", "clue nests", "ring nest", "egg nest", "seed nest", "forestry kit", "Forestry basket", "cape pouch", "Dragon axe", "infernal axe", "Woodcutting hood"], "plainText": "A Woodcutting cape can be purchased from <PERSON><PERSON><PERSON> (found south of the furnace in Lumbridge) for 99,000 coins and comes with the Woodcutting hood only for players who have achieved level 99 Woodcutting. A player performing the Woodcutting cape's emote. Like all skill capes, the Woodcutting cape gives +9 to all defensive stats, and also a +4 bonus to prayer if it is trimmed, which requires a separate skill at level 99. The cape is automatically trimmed if this condition is met. When a Woodcutting cape is equipped, players will have an additional 10% chance of a bird nest falling while chopping trees. This boost does not apply to clue nests, only ring, egg, and seed nests. The Woodcutting cape can be stored in a forestry kit or basket that has been expanded by way of a cape pouch, allowing you to receive its bird nest boosting effects while wearing either of those instead. Merely carrying a forestry kit or basket is not sufficient to benefit from the effects of a Woodcutting cape stored inside; it must be equipped. It should be noted that boosting past level 99 Woodcutting does not have an effect on the rate at which players receive logs. Therefore, the +1 boost a player gains when equipping the skillcape - as well as any other boosts, such as the dragon/infernal axe special attack - aren't necessary when woodcutting.  Combat stats Shop locations Used in recommended equipment Changes References Category:Temporary skill boost Category:Woodcutting boosting items"}, "originalContent": "{{External|rs}}\n{{Infobox Item\n|version1 = Untrimmed\n|version2 = Trimmed\n|name1 = Woodcutting cape\n|name2 = Woodcut. cape(t)\n|image1 = [[File:Woodcutting cape.png]]\n|image2 = [[File:Woodcut. cape(t).png]]\n|release = [[18 October]] [[2006]]\n|update = Capes of Achievement... and Goats!\n|members = Yes\n|quest = No\n|tradeable = No\n|placeholder = Yes\n|equipable = Yes\n|stackable = No\n|noteable = No\n|options = Wear, Drop\n|wornoptions = Boost\n|examine = The cape worn by master woodcutters.\n|value = 99000\n|weight = 0.453\n|leagueRegion = Misthalin\n|id1 = 9807\n|id2 = 9808\n}}\n{{Synced switch\n|version1 = [[File:Woodcutting cape detail.png|left|120px]]\n|version2 = [[File:Woodcut. cape(t) detail.png|left|120px]]\n}}\nA '''Woodcutting cape''' can be purchased from [[Wilfred]] (found south of the furnace in [[Lumbridge]]) for 99,000 [[coins]] and comes with the [[Woodcutting hood]] only for players who have achieved level 99 [[Woodcutting]].\n[[File:Woodcutting cape emote.gif|160px|thumb|A player performing the Woodcutting cape's emote.]]\nLike all [[Cape of Accomplishment|skill capes]], the Woodcutting cape gives +9 to all defensive stats, and also a +4 bonus to prayer if it is trimmed, which requires a separate skill at level 99. The cape is automatically trimmed if this condition is met.\n\nWhen a Woodcutting cape is equipped, players will have an additional 10% chance of a [[bird nest]] falling while chopping trees.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1529473963029569536|date=25 May 2022|archiveurl=https://archive.is/UPM6w|archivedate=13 April 2023|quote=[Hey @JagexAsh, I was wondering if the woodcutting skill cape perk of having an additional 10% chance to receive a bird nest is calculated over the 1/256 itself (1/256 * 1.1) or whether there is an independent extra 10% chance to receive one (1/256 + 1/10). Thanks in advance!] It modifies the 256 rather than doing a separate roll.}} This boost does not apply to [[clue nests]], only [[ring nest|ring]], [[egg nest|egg]], and [[seed nest]]s.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1606695428988321793|date=24 December 2022|archiveurl=https://archive.is/VSbQD|archivedate=25 December 2023|quote=[The OSRS Wiki's Redwoods page states the WC cape's 10% bird nest drop buff doesn't apply to the clue nests, but everything else I can find never specifies what kind of nests it does applies to. Can you please confirm if the cape does anything at Redwoods? Thank you!] I don't see any references to it in the code there, including for the clue nests. It'd have helped with the non-clue nests, but those are not offered there. (...)}}\n\nThe Woodcutting cape can be stored in a [[forestry kit]] or [[Forestry basket|basket]] that has been expanded by way of a [[cape pouch]], allowing you to receive its bird nest boosting effects while wearing either of those instead. Merely carrying a forestry kit or basket is not sufficient to benefit from the effects of a Woodcutting cape stored inside; it must be equipped.\n\nIt should be noted that boosting past level 99 Woodcutting does not have an effect on the rate at which players receive logs.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/7779280128473210881|date=19 September 2016|archiveurl=https://web.archive.org/web/20181004144650/https://twitter.com/JagexAsh/status/777928012847321088|archivedate=29 October 2018|quote=No; according to the documentation the standard skill-random method ignores boosts over 99.}} Therefore, the +1 boost a player gains when equipping the skillcape - as well as any other boosts, such as the [[Dragon axe|dragon]]/[[infernal axe]] special attack - aren't necessary when woodcutting. \n\n{{Costume storage|cape rack|Woodcutting cape set, along with the [[Woodcutting hood|hood]]|set=true}}\n\n{{Skill 99s|Woodcutting}}\n\n==Combat stats==\n{{Infobox Bonuses\n|version1 = Untrimmed\n|version2 = Trimmed\n|astab = 0\n|aslash = 0\n|acrush = 0\n|amagic = 0\n|arange = 0\n|dstab = +9\n|dslash = +9\n|dcrush = +9\n|dmagic = +9\n|drange = +9\n|str = 0\n|rstr = 0\n|mdmg = 0\n|prayer1 = 0\n|prayer2 = +4\n|slot = cape\n|image1 = [[File:Woodcutting cape equipped.png]]\n|image2 = [[File:Woodcut. cape(t) equipped.png]]\n|altimage1 = [[File:Woodcutting cape equipped female.png|150px]]\n|altimage2 = [[File:Woodcut. cape(t) equipped female.png|150px]]\n}}\n\n==Shop locations==\n{{Store locations list|Woodcutting cape}}\n\n==Used in recommended equipment==\n{{main|Cape of Accomplishment#Used in recommended equipment}}\n{{Used in recommended equipment|Woodcutting cape,Woodcut. cape(t)}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 3 December 2015\n|update = Skill Cape Perks\n|poll = Old School Content Poll 37: Skillcape Perks\n|change = When equipped, the Woodcutting cape grants a 10% increased chance of finding a bird's nest.\n}}\n{{Subject changes\n|date = 6 November 2006\n|update = Enlightened Journey\n|change = The item no longer recolours depending on the colour of the player's leggings.\n}}\n{{Subject changes footer}}\n\n==References==\n{{Reflist}}\n\n{{Skillcape}}\n{{Skilling outfits}}\n{{Cape}}\n[[Category:Temporary skill boost]]\n[[Category:Woodcutting boosting items]]"}