{"title": "(hard)", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [], "relationships": [{"from": "(hard)", "to": "suffixes", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Treasure Trails", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Temple Trekking", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Nightmare Zone", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "antique lamp", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Achievement Diary", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Puzzle box", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Challenge scroll", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Agrith-Na-Na", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Arianwyn", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Arrg", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Barrelchest (Nightmare Zone)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Black demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Black Knight Titan", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Chronozon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Corsair Traitor", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON> (Nightmare Zone)#Hard Mode", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Cow (Nightmare Zone)#Hard Mode", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Culinaroman<PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Dad", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON><PERSON> mother", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Essyllt", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Evil Chicken (Nightmare Zone)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Flambeed", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Giant Roc", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Giant scarab", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Glod", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Headless Beast", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Ice Troll King (Nightmare Zone)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Jungle Demon", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Khazard warlord", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "King <PERSON><PERSON>ld", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Me", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON> (Nightmare Zone)#Hard Mode", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Nazastarool", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Sand Snake", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Skeleton Hellhound", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "The Everlasting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "The Inadequacy", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "The Kendal", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "The Untouchable", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Trapped Soul", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Tree spirit", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "<PERSON><PERSON>th", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "Witch's experiment", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "(hard)", "to": "STASH", "type": "references", "confidence": 0.7, "source": "wiki_link"}], "embedding": [0.05825790762901306, -0.1001395583152771, -0.022615492343902588, 0.02583601139485836, -0.031491316854953766, 0.0733131393790245, -0.011612966656684875, 0.010770849883556366, -0.022535182535648346, 0.019855575636029243, 0.05622576177120209, 0.01564127765595913, 0.0025662072002887726, -0.04812084138393402, 0.008681805804371834, -0.0030264591332525015, 0.03736947849392891, 0.01132060494273901, 0.03379759564995766, -0.004291266202926636, -9.4788265414536e-05, 0.027110880240797997, -0.014343233779072762, 0.01819203607738018, -0.006309820804744959, -0.01891767978668213, -0.008488588966429234, 0.0007364756893366575, -0.03201475739479065, -0.03912316635251045, 0.09068500995635986, -0.00358460727147758, -0.04028981924057007, 0.027107514441013336, 2.179133844038006e-06, -0.04416368529200554, 0.018379172310233116, -0.014516817405819893, -0.08733159303665161, 0.010242138989269733, -0.02302088402211666, -0.04500811547040939, 0.0425158329308033, -0.021168755367398262, 0.043884750455617905, -0.04451914131641388, 0.043559931218624115, 0.03517352417111397, -0.023889975622296333, -0.028883198276162148, 0.014202317222952843, 0.035323336720466614, -0.013163000345230103, -0.0663779228925705, 0.107167087495327, -0.07210039347410202, -0.016976524144411087, -0.002493589650839567, 0.029487881809473038, 0.07769375294446945, -0.0077200280502438545, -0.05111221224069595, 0.005425274837762117, 0.05803292244672775, -0.03771345317363739, -0.028782639652490616, -0.03937434405088425, -0.030394377186894417, 0.01016882061958313, 0.034974876791238785, -0.014493666589260101, 0.006411280483007431, 0.027669090777635574, 0.019467802718281746, 0.0026648282073438168, -0.004207314923405647, -0.0030426171142607927, -0.017259815707802773, -0.02875456213951111, -0.018163418397307396, -0.0011181666050106287, 0.03467116132378578, 0.005665284115821123, 0.02642359584569931, 0.02779850736260414, 0.002673451090231538, -0.008564808405935764, -0.00900784321129322, 0.060539230704307556, 0.005166199523955584, -0.027667928487062454, 0.026374513283371925, -0.0076116714626550674, -0.011797181330621243, -0.033298566937446594, -0.04340818151831627, -0.03477475047111511, 0.034768834710121155, 0.07046965509653091, -0.025693535804748535, 0.034493304789066315, 0.0009074372937902808, -0.06425821036100388, 0.04486631974577904, -0.05034707859158516, -0.03490643575787544, 0.08385643362998962, -0.04222958907485008, 0.028243305161595345, -0.03494463115930557, 0.08292283862829208, -0.007683729752898216, -0.05736268311738968, -0.013586574234068394, 0.04695833474397659, -0.0027455815579742193, -0.003084478434175253, -0.04622378945350647, -0.06639141589403152, -0.021731678396463394, 0.011049202643334866, -0.026615440845489502, -0.043513160198926926, 0.03532160818576813, -0.03832666575908661, -0.07704387605190277, -0.11452128738164902, 0.05243426561355591, 0.04021519050002098, 0.023792138323187828, 0.004642748739570379, -0.05415062978863716, 0.08253820985555649, -0.04688526690006256, 0.02564065344631672, -0.0012614884180948138, 0.01908484473824501, 0.027953319251537323, 0.022622888907790184, -0.003003685735166073, 0.02734566479921341, -5.1273807912366465e-05, -0.01624908298254013, -0.015934348106384277, -0.016528069972991943, 0.00445055915042758, 0.028543107211589813, -0.04721445590257645, -0.012361290864646435, -0.012333547696471214, -0.02256733365356922, -0.018380625173449516, 0.00868914183229208, -0.03822101280093193, 0.031843192875385284, 0.027663161978125572, -0.007504184730350971, -0.03958512097597122, -0.03651178628206253, 0.061865609139204025, 0.009460811503231525, -0.03502510115504265, 0.02904285490512848, -0.02072824537754059, -0.019846748560667038, -0.037441156804561615, -0.02083439752459526, 0.0063987853936851025, -0.04256134852766991, -0.03355560079216957, -0.0532686747610569, 0.03665994107723236, -0.03178449720144272, -0.014268509112298489, 0.03227265179157257, -0.026968251913785934, 0.06653010100126266, 0.014627324417233467, -0.05991928651928902, -0.03845452889800072, 0.026649730280041695, -0.03222784772515297, 0.023024246096611023, -0.003420656779780984, 0.012231752276420593, -0.021634289994835854, -0.008757009170949459, -0.007832496426999569, 0.018793627619743347, -0.07783202081918716, -0.005942505318671465, 0.012679146602749825, 0.10652054846286774, -0.01727459207177162, 0.034076206386089325, 0.023356234654784203, -0.02776237204670906, -0.015029722824692726, 0.01931566745042801, 0.029412562027573586, -0.005963790696114302, -0.029814518988132477, 0.031647440046072006, -0.024890325963497162, 0.007127969991415739, 0.017343157902359962, -0.056914713233709335, 0.02259240113198757, -0.011676324531435966, 0.022653935477137566, -0.010443009436130524, 0.019219649955630302, -0.0330146960914135, -0.044207487255334854, -0.024268802255392075, 0.05394982174038887, 0.03400837630033493, 0.00020598663832060993, -0.03612791746854782, -0.03408745303750038, 0.010744266211986542, 0.04741336405277252, -0.04065605252981186, -0.02314189448952675, 0.019191469997167587, -0.057044096291065216, -0.030265582725405693, 0.03415588289499283, -0.0268726609647274, -0.012447192333638668, 0.007495496887713671, -0.023006413131952286, -0.003817587858065963, 0.0024622667115181684, -0.02092854492366314, -0.08832870423793793, 0.0010336849372833967, 0.11447318643331528, 0.00469061778858304, -0.019691109657287598, 0.011205150745809078, 0.014858215115964413, 0.04450146108865738, -0.03187450394034386, 0.06503615528345108, -0.045299362391233444, -0.035611528903245926, 0.005904085468500853, -0.022742366418242455, -0.02308592200279236, 0.07388907670974731, 0.004078551661223173, 0.05408241227269173, -0.014790674671530724, 0.05473307520151138, 0.03110903687775135, -0.07110299915075302, -0.02497044950723648, 0.011158357374370098, -0.052308399230241776, 0.02261432446539402, -0.017326101660728455, 0.015358918346464634, 0.003307164181023836, -0.0084535488858819, 0.0019526544492691755, -0.04966891556978226, 0.04787971079349518, -0.008095399476587772, 0.019341031089425087, 0.026376239955425262, -0.0037046405486762524, 0.02091486006975174, -0.042201340198516846, 0.014894561842083931, 0.04543738812208176, 0.07353488355875015, -0.05161704123020172, 0.013115492649376392, 0.024733833968639374, 0.08577753603458405, -0.032621707767248154, 0.0295023825019598, -0.007815569639205933, 0.013130651786923409, 0.009414691478013992, -0.04973891004920006, 0.07273708283901215, -0.026723159477114677, -0.02238808013498783, -0.06501737982034683, -0.02904713898897171, 0.033103253692388535, -0.05590856075286865, 0.029006917029619217, 0.01734096184372902, 0.061630260199308395, 0.004862168338149786, 0.009732769802212715, 0.033861588686704636, -0.052742112427949905, -0.11210023611783981, 0.008898654021322727, 0.010405522771179676, -0.03379083797335625, -0.04873233661055565, 0.006260179914534092, -0.06384546309709549, -0.021982811391353607, 0.05504322052001953, 0.05000470578670502, -0.014057752676308155, -0.020977629348635674, -0.025905665010213852, 0.03130864351987839, -0.005492060910910368, -0.008513064123690128, 0.008852857165038586, 0.003962559159845114, 0.05338355898857117, -0.02420154958963394, 0.03546648100018501, 0.003938725218176842, -0.06018652394413948, 0.004569855984300375, 0.01860036887228489, -0.061522118747234344, 0.011331697925925255, -0.009050511755049229, 0.010262846015393734, 0.005975062493234873, 0.02650359459221363, -0.024880966171622276, 0.031383614987134933, 0.0021075180266052485, 0.030157268047332764, -0.05074509605765343, -0.03297223150730133, 0.03469381853938103, -0.05073811858892441, -0.010368910618126392, 0.049442440271377563, 0.06939514726400375, -0.009406805969774723, 0.028716299682855606, -0.022771814838051796, 0.016376489773392677, 0.017466628924012184, 0.06517349183559418, 0.01962960883975029, -0.04142221435904503, 0.028205132111907005, -0.0314968042075634, 0.009254124946892262, -0.05071798339486122, -0.06907937675714493, 0.07458613812923431, 0.024244582280516624, 0.03500795736908913, -0.005651275627315044, -0.03859321400523186, -0.021072348579764366, -0.01976567693054676, 0.00303977495059371, 0.016573196277022362, -0.013261282816529274, 0.0033750806469470263, 0.015365300700068474, -0.037066008895635605, 0.0035438821651041508, -0.05698578804731369, -0.0126935550943017, -0.013281440362334251, 0.0001564532722113654, -0.022668397054076195, -0.02952186018228531, 0.026328101754188538, -0.02654622122645378, 0.06816647201776505, 0.04137144982814789, -0.010332153178751469, -0.030573949217796326, 0.0031171657610684633, 0.026486296206712723, -0.01258764322847128, 0.0024592664558440447, -0.04024621099233627, 0.06757770478725433, 0.012075773440301418, -0.0208735391497612, 0.004666774999350309, 0.0003594515728764236, -0.050313081592321396, 0.026545075699687004, 0.008063962683081627, 0.015154155902564526, 0.024742286652326584, -0.02177579514682293, 0.04010172560811043, 0.015942217782139778, 0.055831946432590485, 0.06223735213279724, 0.04035006836056709, 0.030680781230330467, -0.006108911707997322, 0.05640546604990959, 0.023139577358961105, -0.0030656466260552406, -0.0495334230363369, 0.002492221537977457, 0.10934345424175262, -0.020930316299200058, -0.02512427791953087, 0.0028313167858868837, -0.07208418846130371, 0.04850442335009575, -0.025107964873313904, 0.004232601262629032, -0.023499688133597374, 0.020086411386728287, 0.013337654992938042, -0.032620083540678024, 0.07596027106046677, 0.016643760725855827, 0.017870362848043442, -0.021773992106318474, -0.02838997170329094, 0.011647824198007584, 0.025439241901040077, 0.010588637553155422, -0.03444044291973114, -0.016103699803352356, -0.03647204488515854, 0.016512727364897728, 0.07429833710193634, -0.10866372287273407, -0.005258974153548479, -0.057235777378082275, 0.08510483801364899, -0.00922947283834219, -0.03820177540183067, 0.05428358167409897, 0.04526006430387497, -0.01669069565832615, -0.02167489007115364, -0.05780359357595444, 0.0015886567998677492, -0.026456784456968307, 0.014246382750570774, 0.04103447124361992, -0.004767298232764006, -0.05167654529213905, -0.00020544248400256038, -0.010537335649132729, -0.04092467948794365, -0.025133877992630005, 0.025592053309082985, 0.01452355831861496, 0.03958781808614731, 0.01728208363056183, -0.0002564854803495109, 0.032489754259586334, -0.06886540353298187, -0.027401184663176537, -0.031315114349126816, -0.051657844334840775, -0.024311861023306847, -0.004441916476935148, -0.028546690940856934, 0.009080309420824051, -0.04930748790502548, 0.006203545723110437, -0.007544339634478092, -0.005826747976243496, 0.00046001269947737455, -0.025777362287044525, 0.021526509895920753, -0.009887459687888622, -0.03834034875035286, 0.007590354420244694, -0.05206269398331642, 0.004970880225300789, -0.026420868933200836, -0.07266596704721451, 0.03348422795534134, 0.015422621741890907, 0.0421108603477478, -0.006823214702308178, 0.03157017007470131, -0.022399546578526497, 0.005598331801593304, -0.001693860162049532, 0.00548995565623045, 0.038935285061597824, 0.12740303575992584, 0.07185757160186768, -0.0932496190071106, -0.031430281698703766, 0.0013256979873403907, 0.02548317424952984, 0.01418610755354166, 0.0171846691519022, 0.006885502953082323, 0.027041587978601456, -0.03295284882187843, 0.06445498019456863, -0.00439544627442956, 0.0433306023478508, -0.0025030765682458878, 0.03177964314818382, -0.017576638609170914, -0.04962724447250366, 0.041982006281614304, 0.05503770709037781, -0.013065257109701633, -0.011934676207602024, -0.015484076924622059, -0.08265494555234909, 0.027143441140651703, -0.006715719122439623, -0.012092547491192818, -0.013486356474459171, -0.012422378174960613, -0.016984639689326286, -0.03244752809405327, -0.030486444011330605, 0.0053380513563752174, 0.02764299139380455, -0.06899487972259521, 0.045853141695261, 0.014116009697318077, 0.06675750762224197, 0.010868992656469345, -0.012240063399076462, -0.006012067664414644, 0.047573257237672806, 0.04607531800866127, -0.031493742018938065, 0.02880246378481388, -0.024619076400995255, 0.013363083824515343, -0.019401727244257927, 0.010872429236769676, 0.031139308586716652, 0.03166920319199562, -0.02366979978978634, -0.021299460902810097, 0.06765134632587433, -0.040094226598739624, -0.016049906611442566, 0.01785370521247387, -0.02420775406062603, 0.02090064249932766, -0.012262208387255669, 0.020299561321735382, -6.678754825189142e-33, 9.859669808065519e-05, 0.004036456812173128, -0.003292693989351392, -0.049620043486356735, -0.012837554328143597, 0.062148336321115494, -0.0029541349504143, 0.003315983572974801, 0.019137907773256302, -0.006383066065609455, 0.003399608191102743, -0.03290361911058426, 0.0059020789340138435, -0.005852579139173031, 0.023969409987330437, -0.008326686918735504, 0.0025617589708417654, -0.026918599382042885, 0.012739434838294983, -0.051206931471824646, -0.00011207097122678533, -0.00011505320435389876, 0.05124327540397644, -0.028469033539295197, 0.04035501182079315, -0.00931929238140583, -0.01951802708208561, -0.007459632586687803, 0.046523287892341614, -0.010804967023432255, -0.01639195904135704, -0.08217711001634598, 0.0024982010945677757, -0.05176868289709091, 0.0008958250982686877, -0.009855997748672962, -0.06988663226366043, 0.012959741987287998, 0.013544506393373013, 0.017949068918824196, 0.017683299258351326, -0.03327859565615654, -0.020838413387537003, 0.03844195231795311, 0.00453313160687685, 0.028529271483421326, -0.020840998739004135, -0.026927070692181587, 0.013383015990257263, 0.0034767845645546913, 0.0250820554792881, -0.018552938476204872, -0.008729089051485062, -0.03853541240096092, -0.0480094775557518, -0.054888997226953506, -0.004750889725983143, 0.0328398123383522, -0.02805413119494915, -0.021010233089327812, 0.0030871666967868805, 0.04811932519078255, 0.027060307562351227, 0.025054598227143288, -0.017137598246335983, 0.0012787355808541179, 0.020549334585666656, -0.001614428125321865, 0.011144651100039482, 0.06872108578681946, 0.025510231032967567, 0.06869513541460037, 0.058677177876234055, 0.04704368859529495, 0.08401471376419067, -0.004845154006034136, 0.008751966059207916, 0.01866779290139675, 0.01718139834702015, -0.019647512584924698, 0.06303735077381134, -0.0011789040872827172, -0.03500745818018913, 0.0077866725623607635, -0.005735647398978472, 0.09265806525945663, -0.0014520789263769984, -0.023180844262242317, -0.008944150060415268, 0.024411560967564583, -0.007943027652800083, 0.04768452048301697, 0.011202294379472733, 0.026005113497376442, -0.02843691222369671, -0.008618518710136414, 0.051419273018836975, 0.0027531450614333153, -0.05323537811636925, -0.0581253357231617, 0.049357350915670395, -0.010056886821985245, 0.006238101050257683, -0.04009535163640976, 0.042672786861658096, 0.009473860263824463, 0.013985627330839634, 0.01318745780736208, -0.03378007188439369, -0.02173597738146782, 0.014740998856723309, 0.01329452358186245, -0.011652596294879913, 0.034611959010362625, 0.01292987447232008, -0.067875437438488, 0.01551976427435875, -0.06214969605207443, 0.02683977782726288, -0.03016894869506359, 0.01812652312219143, 0.04294817894697189, -0.02895525097846985, -0.024139180779457092, -0.004839815199375153, -0.008525082841515541, -0.05363309755921364, -0.049147795885801315, 0.0029062358662486076, -0.020450875163078308, 0.012438394129276276, -0.011970646679401398, 2.846018674063089e-07, -0.06446978449821472, 0.10256398469209671, 0.002741097239777446, 0.022412236779928207, 0.011940570548176765, -0.0070631070993840694, 0.011744587682187557, -0.01067593228071928, -0.05338415130972862, -0.025531968101859093, 0.021172568202018738, 0.008930951356887817, 0.0050336625427007675, 0.020184146240353584, -0.007265112362802029, -0.03679152950644493, -0.028260447084903717, -0.044851090759038925, -0.036780864000320435, -0.012473909184336662, 0.028156349435448647, 0.017663048580288887, -0.003195280907675624, 0.03402511402964592, -0.02038131095468998, -0.058343857526779175, -0.04940557852387428, -0.05956929922103882, 0.04184291139245033, 0.02050834149122238, 0.04190394654870033, -0.03251079097390175, 0.05293577164411545, 0.004862855188548565, 0.03194383159279823, -0.023870164528489113, 0.06983966380357742, -0.018035074695944786, 0.03042958304286003, 0.004914696794003248, -0.0075719538144767284, -0.024766001850366592, 0.0059112017042934895, 0.0032146014273166656, 0.00393329281359911, 0.11881912499666214, -0.008787359111011028, 0.015012913383543491, -0.033674456179142, -0.015602926723659039, 0.006915230769664049, -0.021895594894886017, 0.03783116489648819, -0.03312014043331146, 0.02771843411028385, 0.016429873183369637, 0.004962940234690905, -0.04348190128803253, 0.023291705176234245, 0.07228914648294449, -0.029114188626408577, -0.006444175262004137, -0.009426973760128021, -0.01730559580028057, -0.016646523028612137, -0.07041830569505692, -0.022398965433239937, 2.619240645099548e-34, -0.0030623681377619505, 0.019372545182704926, 0.010750852525234222, -0.015320181846618652, 0.061041709035634995, -0.01600046269595623, 0.037743497639894485, -0.010880439542233944, -0.03408848121762276, -0.0003200148930773139, -0.015440283343195915], "parsedContent": {"infobox": {}, "categories": [], "links": ["suffixes", "Treasure Trails", "Temple Trekking", "Nightmare Zone", "antique lamp", "Achievement Diary", "Puzzle box", "Challenge scroll", "Agrith-Na-Na", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Arianwyn", "Arrg", "Barrelchest (Nightmare Zone)", "Black demon", "Black Knight Titan", "<PERSON><PERSON><PERSON>", "Chronozon", "<PERSON><PERSON><PERSON><PERSON>", "Corsair Traitor", "<PERSON> (Nightmare Zone)#Hard Mode", "Cow (Nightmare Zone)#Hard Mode", "Culinaroman<PERSON>", "Dad", "<PERSON><PERSON><PERSON><PERSON> mother", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Essyllt", "Evil Chicken (Nightmare Zone)", "<PERSON><PERSON>", "Flambeed", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Giant Roc", "Giant scarab", "Glod", "Headless Beast", "Ice Troll King (Nightmare Zone)", "Jungle Demon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Khazard warlord", "King <PERSON><PERSON>ld", "Me", "<PERSON> (Nightmare Zone)#Hard Mode", "Nazastarool", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sand Snake", "Skeleton Hellhound", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "The Everlasting", "The Inadequacy", "The Kendal", "The Untouchable", "Trapped Soul", "Tree spirit", "<PERSON><PERSON>th", "Witch's experiment", "STASH"], "plainText": "(hard) is a suffix that denotes an item as hard. Used by Treasure Trails, Temple Trekking, Nightmare Zone, and by antique lamps rewarded from Achievement Diaries. Items *Puzzle box (hard) *Challenge scroll (hard) NPCs Nightmare Zone hard mode *Agrith-Na-Na (hard) *<PERSON><PERSON>rith-Naar (hard) *<PERSON><PERSON><PERSON> (hard) *Arrg (hard) *<PERSON>el<PERSON><PERSON> (hard) *Black demon (hard) *Black Knight Titan (hard) *Bouncer (hard) *Chronozon (hard) *<PERSON><PERSON><PERSON><PERSON> (hard) *Corsair Traitor (hard) *<PERSON> (hard) *Cow (hard) *Culinaromancer (hard) *<PERSON> (hard) *<PERSON><PERSON><PERSON><PERSON> mother (hard) *<PERSON><PERSON> (hard) *<PERSON><PERSON>urt (hard) *<PERSON><PERSON>us (hard) *Elvarg (hard) *Essyllt (hard) *Evil Chicken (hard) *Fareed (hard) *Flambeed (hard) *Gelatinnoth Mother (hard) *Giant Roc (hard) *Giant scarab (hard) *Glod (hard) *Headless Beast (hard) *Ice Troll King (hard) *Jungle Demon (hard) *<PERSON><PERSON><PERSON> (hard) *<PERSON><PERSON> (hard) *Khazard warlord (hard) *<PERSON>ld (hard) *<PERSON> (hard) *Moss Guardian (hard) *Nazastarool (hard) *<PERSON><PERSON>ik<PERSON><PERSON> (hard) *Sand Snake (hard) *Skeleton Hellhound (hard) *Slagilith (hard) *Tanglefoot (hard) *The Everlasting (hard) *The Inadequacy (hard) *The Kendal (hard) *The Untouchable (hard) *Trapped Soul (hard) *Tree spirit (hard) *Treus Dayth (hard) *Witch's experiment (hard) Objects *STASH objects **STASH (hard) **Inconspicuous bush (hard) **Inconspicuous hole (hard) **Inconspicuous rocks (hard) **Inconspicuous crate (hard)"}, "originalContent": "'''(hard)''' is a [[suffixes|suffix]] that denotes an item as hard. Used by [[Treasure Trails]], [[Temple Trekking]], [[Nightmare Zone]], and by [[antique lamp]]s rewarded from [[Achievement Diary|Achievement Diaries]].\n\n==Items==\n{{#dpl:\n| namespace =|\n| category = items\n| titlematch = %(hard)\n| ordermethod = title\n}}\n*[[Puzzle box|Puzzle box (hard)]]\n*[[Challenge scroll|Challenge scroll (hard)]]\n\n==NPCs==\n{{#dpl:\n| namespace =|\n| category = non-player characters\n| titlematch = %(hard)\n| ordermethod = title\n}}\n\n===Nightmare Zone hard mode===\n*[[Agrith-Na-Na|Agrith-Na-Na (hard)]]\n*[[Agrith-Naar|Agrith-Naar (hard)]]\n*[[Arianwyn|Arianwyn (hard)]]\n*[[Arrg|Arrg (hard)]]\n*[[Barrelchest (Nightmare Zone)|Barrelchest (hard)]]\n*[[Black demon|Black demon (hard)]]\n*[[Black Knight Titan|Black Knight Titan (hard)]]\n*[[<PERSON><PERSON><PERSON>|<PERSON>un<PERSON> (hard)]]\n*[[Chronozon|Chronozon (hard)]]\n*[[Corrupt Lizardman|Corrupt Lizardman (hard)]]\n*[[Corsair Traitor|Corsair Traitor (hard)]]\n*[[Count Draynor (Nightmare Zone)#Hard Mode|Count Draynor (hard)]]\n*[[Cow (Nightmare Zone)#Hard Mode|Cow (hard)]]\n*[[Culinaromancer|Culinaromancer (hard)]]\n*[[Dad|Dad (hard)]]\n*[[Dagannoth mother|Dagannoth mother (hard)]]\n*[[Damis|Damis (hard)]]\n*[[Dessourt|Dessourt (hard)]]\n*[[Dessous|Dessous (hard)]]\n*[[Elvarg|Elvarg (hard)]]\n*[[Essyllt|Essyllt (hard)]]\n*[[Evil Chicken (Nightmare Zone)|Evil Chicken (hard)]]\n*[[Fareed|Fareed (hard)]]\n*[[Flambeed|Flambeed (hard)]]\n*[[Gelatinnoth Mother|Gelatinnoth Mother (hard)]]\n*[[Giant Roc|Giant Roc (hard)]]\n*[[Giant scarab|Giant scarab (hard)]]\n*[[Glod|Glod (hard)]]\n*[[Headless Beast|Headless Beast (hard)]]\n*[[Ice Troll King (Nightmare Zone)|Ice Troll King (hard)]]\n*[[Jungle Demon|Jungle Demon (hard)]]\n*[[Kamil|Kamil (hard)]]\n*[[Karamel|Karamel (hard)]]\n*[[Khazard warlord|Khazard warlord (hard)]]\n*[[King Roald|King Roald (hard)]]\n*[[Me|Me (hard)]]\n*[[Moss Guardian (Nightmare Zone)#Hard Mode|Moss Guardian (hard)]]\n*[[Nazastarool|Nazastarool (hard)]]\n*[[Nezikchened|Nezikchened (hard)]]\n*[[Sand Snake|Sand Snake (hard)]]\n*[[Skeleton Hellhound|Skeleton Hellhound (hard)]]\n*[[Slagilith|Slagilith (hard)]]\n*[[Tanglefoot|Tanglefoot (hard)]]\n*[[The Everlasting|The Everlasting (hard)]]\n*[[The Inadequacy|The Inadequacy (hard)]]\n*[[The Kendal|The Kendal (hard)]]\n*[[The Untouchable|The Untouchable (hard)]]\n*[[Trapped Soul|Trapped Soul (hard)]]\n*[[Tree spirit|Tree spirit (hard)]]\n*[[Treus Dayth|Treus Dayth (hard)]]\n*[[Witch's experiment|Witch's experiment (hard)]]\n\n==Objects==\n*[[STASH]] objects\n**STASH (hard)\n**Inconspicuous bush (hard)\n**Inconspicuous hole (hard)\n**Inconspicuous rocks (hard)\n**Inconspicuous crate (hard)\n\n{{Suffixes}}\n{{Parentitle override}}"}