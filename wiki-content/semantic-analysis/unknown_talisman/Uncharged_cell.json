{"title": "Uncharged cell", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [], "relationships": [], "embedding": [], "parsedContent": {"infobox": {}, "categories": [], "links": ["23 March", "2022", "Guardians of the Rift", "minigame", "Runic altar"], "plainText": "120px Uncharged cells are an item obtained during the Guardians of the Rift minigame. They are charged by imbuing them at a runic altar, accessible directly by entering an active Portal Guardian. The type of charge it holds is dependent on the runic altar it is charged at. When inspected, the chatbox will state \"An uncharged magical cell built to hold runic energy.\" Products Changes"}, "originalContent": "{{Infobox Item\n|name = Uncharged cell\n|image = [[File:Uncharged cell.png]]\n|release = [[23 March]] [[2022]]\n|update = Guardians of the Rift Launch\n|members = Yes\n|quest = No\n|tradeable = No\n|bankable = No\n|placeholder = \n|equipable = No\n|stackable = Yes\n|noteable = \n|options = Inspect, Drop\n|destroy = ''The Uncharged cell is destroyed as you drop it.''\n|examine = Can potentially hold runic energy, but it currently holds nothing.\n|value = 30\n|alchable = No\n|weight = 0.000\n|id = 26882\n}}\n[[File:Uncharged cell detail.png|left|120px]]\n'''Uncharged cells''' are an item obtained during the [[Guardians of the Rift]] [[minigame]]. They are charged by imbuing them at a [[Runic altar|runic altar]], accessible directly by entering an active Portal Guardian. The type of charge it holds is dependent on the runic altar it is charged at.\n\nWhen inspected, the chatbox will state \"An uncharged magical cell built to hold runic energy.\"\n\n==Products==\n{{Uses material list|Uncharged cell}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 30 March 2022\n|update = Guardians of the Rift Improvements\n|change = The item now has a \"Drop\" option instead of \"Destroy\". Previously, the destroy text was \"You can get another in the Temple of the Eye.\"\n}}\n{{Subject changes footer}}\n\n{{Guardians of the Rift}}"}