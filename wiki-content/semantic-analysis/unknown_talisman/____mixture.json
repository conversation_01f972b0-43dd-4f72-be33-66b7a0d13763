{"title": "??? mixture", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [], "relationships": [{"from": "??? mixture", "to": "27 June", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "2005", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "Recruitment Drive", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "quest items", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "Recruitment Drive", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "Miss <PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "cupric sulfate", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "vial of liquid", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "gypsum", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "Recruitment Drive", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "Recruitment Drive", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "Recruitment Drive", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "Recruitment Drive", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "Recruitment Drive", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "Recruitment Drive", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "??? mixture", "to": "Recruitment Drive", "type": "references", "confidence": 0.7, "source": "wiki_link"}], "embedding": [0.05652661994099617, -0.07519567757844925, 0.005214910488575697, -0.02703685872256756, -0.04049871116876602, 0.047734979540109634, -0.04990813881158829, 0.03103642538189888, 0.00470515014603734, 0.007252084091305733, 0.06169693544507027, -0.015986600890755653, 0.04774652048945427, 0.027012541890144348, 0.015911515802145004, -0.008783838711678982, 0.0030282705556601286, 0.0026050882879644632, -0.05323008447885513, 0.00784298311918974, -0.011877775192260742, -0.02003602311015129, 0.0027336955536156893, -0.01144740916788578, 0.018726006150245667, -0.050507474690675735, -0.037047047168016434, -0.004569449461996555, -0.030591383576393127, -0.029322078451514244, 0.03589246794581413, 0.04822876304388046, 0.00312841241247952, 0.0005105719319544733, 1.9150611478835344e-06, -0.03344651311635971, 0.009667475707828999, 0.010430164635181427, -0.09731131047010422, -0.0405941903591156, 0.00042859272798523307, 0.02024794928729534, 0.003959768917411566, 0.03355036675930023, -0.008272902108728886, 0.04890109598636627, 0.015160441398620605, 0.05846156179904938, 0.0145369041711092, 0.019513264298439026, 0.003914418164640665, -0.0011225267080590129, -0.05208249390125275, 0.001536269672214985, 0.11485499143600464, 0.018055636435747147, 0.01029707957059145, -0.030097372829914093, 0.08815822750329971, 0.0003743129491340369, 0.005467552226036787, -0.008298839442431927, -0.01398544106632471, 0.043734360486269, -0.007260899059474468, 0.010286102071404457, -0.01946345902979374, -0.0753508135676384, 0.021598782390356064, 0.023875674232840538, -0.008574789389967918, 0.01813170686364174, 0.03183520585298538, 0.06835485994815826, -0.012689716182649136, -0.02067560702562332, -0.015499105677008629, -0.050731781870126724, -0.04740317538380623, -0.005965199787169695, -0.021030697971582413, 0.010664946399629116, -0.0009424285381101072, 0.013335618190467358, 0.014087029732763767, 0.10859441757202148, -0.011976264417171478, 0.019029583781957626, 0.04561327025294304, 0.008800479583442211, 0.031215133145451546, -0.04466560110449791, -0.02385665476322174, 0.01167356874793768, -0.054142728447914124, -0.05580431595444679, 0.029572660103440285, -0.002672462025657296, 0.08244030922651291, -0.05569014698266983, -0.018058212473988533, 0.000529020035173744, 0.013394160196185112, 0.04824085161089897, 0.006990858353674412, 0.017205743119120598, 0.05425485596060753, -0.06190621480345726, -0.03487388417124748, -0.02577909454703331, -0.0076339347288012505, -0.006613044999539852, -0.040449146181344986, 0.0029447730630636215, -0.01247925590723753, -0.022646943107247353, -0.030621465295553207, -0.013157586567103863, -0.07819602638483047, 0.061820708215236664, 0.005304648075252771, 0.027542714029550552, -0.009477398358285427, 0.03297506272792816, -0.0194858405739069, -0.025667915120720863, -0.041646312922239304, 0.03547156974673271, 0.04084872454404831, -0.028657961636781693, -0.011322412639856339, 0.019741106778383255, -0.029283033683896065, 0.018808891996741295, 0.012621396221220493, 0.07603787630796432, -0.03120367042720318, -0.02346660941839218, -0.040265440940856934, 0.011011055670678616, 0.026550481095910072, -0.0465666763484478, 0.013685516081750393, -0.0082399258390069, 0.0404595322906971, -0.00256602605804801, -0.003687551012262702, 0.012756512500345707, 0.005826892331242561, -0.021257761865854263, -0.0009216811158694327, 0.031024957075715065, 0.007905866019427776, -0.002597483806312084, 0.031263139098882675, 0.03904994577169418, -0.08716866374015808, 0.04620986804366112, -0.01828642748296261, 0.06748173385858536, -0.012349475175142288, -0.00045865194988436997, 0.03790631145238876, -0.05891694501042366, -0.0008662608452141285, 0.062289826571941376, -0.05735257267951965, 0.033260222524404526, -0.03661244735121727, 0.006987520027905703, -0.018342573195695877, 0.036723554134368896, -0.03439515456557274, -0.0012387940660119057, 0.0036556259728968143, 0.01251937635242939, -0.0097856055945158, -0.039014823734760284, -0.02757045440375805, 0.0046930378302931786, -0.014536351896822453, 0.02203117124736309, 0.032978978008031845, -0.03899215906858444, -0.11666988581418991, -0.006927477195858955, 0.08206024765968323, 0.06960287690162659, -0.057345908135175705, -0.02674192562699318, 0.009399943053722382, -0.0006042474415153265, 0.03569473698735237, 0.005785619840025902, 0.031087197363376617, -0.016427431255578995, 0.033056095242500305, -0.036784395575523376, -0.014248481951653957, -0.027994411066174507, 0.053097475320100784, 0.011883684433996677, 0.11437229067087173, 0.07166872173547745, -0.04584461450576782, -0.01008215919137001, -0.04647339507937431, -0.017293786630034447, -0.001963509013876319, -0.008829866535961628, -0.030377980321645737, 0.031049620360136032, 0.06413187086582184, -0.018123487010598183, -0.024139810353517532, 0.014941278845071793, 0.026155857369303703, -0.01192709244787693, -0.03397258743643761, -0.005626576021313667, 0.012309570796787739, -0.003997004125267267, 0.09657515585422516, -0.01687808521091938, -0.018193300813436508, -0.03454213589429855, 0.005598296411335468, -0.026723135262727737, 0.01794716902077198, 0.007272514048963785, 0.026857873424887657, 0.02857957035303116, 0.028329452499747276, -0.02104366570711136, -0.07301536947488785, -0.03193534165620804, 0.08224371075630188, 0.02121901512145996, -0.02763865515589714, -0.012217032723128796, -0.01894991472363472, -0.11544134467840195, 0.0028120570350438356, -0.010064858943223953, 0.04936365783214569, 0.04686799272894859, -0.02783614583313465, 0.0028210675809532404, -0.006365959066897631, 0.014005363918840885, 0.022898541763424873, 0.0024682071525603533, 0.018197625875473022, -0.0009820658015087247, 0.02591000124812126, 0.04663900285959244, -0.051946427673101425, -0.01827126368880272, -0.011908493936061859, 0.06427875906229019, -0.0016921061323955655, 0.004384295549243689, 0.050167232751846313, -0.04650268331170082, -0.06227242201566696, -0.041264019906520844, -0.06593627482652664, 0.048195164650678635, 0.04348248988389969, -0.011641109362244606, 0.011597326025366783, -0.01564871333539486, 0.04418785870075226, -0.019154999405145645, 0.028117585927248, -0.03034493327140808, -0.02355918101966381, -0.0034692639019340277, -0.03502996265888214, -0.014872142113745213, 0.041738979518413544, 0.06369034200906754, -0.0043575577437877655, -0.00369417411275208, 0.04853708669543266, 0.055729351937770844, -0.03861169517040253, 0.055934540927410126, -0.047203224152326584, -0.04237598553299904, -0.035055648535490036, 0.005965039134025574, 0.004941743332892656, -0.012695166282355785, 0.001210935297422111, -0.008468225598335266, 0.08494263887405396, 0.03422720357775688, 0.05910681560635567, -0.006798514164984226, -0.028285948559641838, -0.07992756366729736, 0.0030431507620960474, -0.0026136799715459347, -0.03085106797516346, 0.02063724957406521, 0.03239353373646736, -0.028276393190026283, 0.0036163581535220146, -0.05272845923900604, -0.0032381003256887197, 0.013600862585008144, -0.04067298397421837, 0.08108771592378616, -0.01004465576261282, -0.010022259317338467, -0.056530993431806564, -0.028366900980472565, 0.013947468250989914, 0.0017430868465453386, -0.03268326446413994, -0.009723407216370106, -0.006367347668856382, -0.037513408809900284, 0.016829287633299828, -0.03724975883960724, -0.04085210710763931, 0.036710962653160095, -0.0565461702644825, -0.04016797989606857, -0.014539961703121662, -0.0419609472155571, -0.01117001287639141, 0.0024441140703856945, 0.005479154177010059, -0.0553983673453331, -0.09523983299732208, -0.055121928453445435, -0.01139797456562519, -0.023691652342677116, 0.00529902521520853, 0.007426931988447905, 0.0013807174982503057, -0.03423766419291496, 0.061760272830724716, 0.02736443094909191, 0.016930678859353065, 0.04433448612689972, -0.00894666463136673, -0.0007370616658590734, 0.03617298975586891, 0.011505062691867352, -0.012473423965275288, 0.018658798187971115, -0.026357175782322884, -0.017165591940283775, 0.00727328285574913, 0.049865927547216415, 0.0746392011642456, -0.035869862884283066, 0.030066197738051414, 0.021117404103279114, 0.11930611729621887, -0.08355172723531723, -0.021482344716787338, 0.02984972670674324, -0.007344936951994896, -0.0044575962238013744, -0.03796897828578949, -0.021424127742648125, 0.03603065386414528, 0.01656189374625683, 0.033075813204050064, 0.04187638685107231, -0.014560958370566368, 0.005288409069180489, 0.0024365894496440887, 0.020455097779631615, 0.019442008808255196, -0.009826438501477242, 0.0007687922916375101, -0.09141387045383453, 0.061043642461299896, 0.030421268194913864, -0.041350625455379486, -0.017828678712248802, -0.04252006486058235, -0.0562392883002758, 0.011365246027708054, 0.020760338753461838, 0.03476100042462349, -0.06613531708717346, -0.06852016597986221, -0.025626983493566513, 0.030389152467250824, 0.0003107904049102217, 0.05066573992371559, 0.01066509447991848, 0.01449901144951582, 0.017630526795983315, 0.033574752509593964, 0.06248456984758377, -0.08735699951648712, -0.007425950840115547, -0.033990368247032166, 0.021709155291318893, 0.008279324509203434, -0.02517889067530632, 0.018484821543097496, -0.007365281227976084, 0.01962370052933693, -0.0996226817369461, -0.008051732555031776, 0.03439309448003769, -0.008481355383992195, -0.0017453316831961274, -0.013304582796990871, 0.003564997809007764, 0.004944298416376114, -0.06798934191465378, -0.03943893685936928, -0.03920963406562805, 0.02348123863339424, -0.013833779841661453, 0.0075855436734855175, -0.0008156535914167762, -0.0019591774325817823, 0.04935397207736969, -0.004142913967370987, 0.02694418467581272, -0.009036420844495296, 0.02599990926682949, -0.04094133898615837, 0.042635779827833176, 0.04742443934082985, 0.07915648072957993, 0.0022150559816509485, -0.02169802226126194, 0.030050966888666153, 0.01914209872484207, -0.02466946840286255, 0.07340981811285019, 0.060316819697618484, -0.029385417699813843, 0.034372247755527496, -0.06082482635974884, 0.004345035180449486, 0.021015087142586708, 0.006554862949997187, 0.033282019197940826, 0.023715415969491005, -0.05388334020972252, -0.010056021623313427, -0.06795790046453476, 0.013541135005652905, -0.018645232543349266, -0.02155323326587677, -0.08848938345909119, -0.04611217603087425, 0.0014931525802239776, 0.031783536076545715, 0.037684544920921326, -0.010114779695868492, -0.019280144944787025, -0.009071506559848785, -0.0060114674270153046, 0.0013998004142194986, -0.014646565541625023, 0.022860508412122726, -0.0325198657810688, -0.022948874160647392, 0.02649412862956524, -0.031441062688827515, -0.01673482358455658, 0.02386556938290596, -0.048729293048381805, -0.005847358610481024, -0.010551203973591328, -0.020799241960048676, 0.032221704721450806, -0.049093134701251984, 0.0027183869387954473, 0.010453573428094387, 0.06975536793470383, -0.008917671628296375, 0.03118712268769741, 0.08755425363779068, -0.04086688160896301, 0.020601069554686546, -0.03192394971847534, -0.026647012680768967, -0.028298914432525635, 0.00026984961004927754, -0.028942784294486046, 0.007292803842574358, 0.07619357109069824, 0.032170649617910385, -0.029986467212438583, 0.06291020661592484, 0.015085920691490173, 0.04007672891020775, 0.028403986245393753, 0.028991669416427612, 0.06776349991559982, -0.05492379143834114, 0.006389059592038393, -0.04875575751066208, 0.049470845609903336, 0.014181217178702354, 0.029310103505849838, 0.010430836118757725, 0.011920375749468803, -0.048195451498031616, -0.06866929680109024, 0.00968116708099842, -0.01020599715411663, -0.0029597138054668903, -0.10117603838443756, 0.007313116919249296, 0.024842286482453346, 0.017828362062573433, -0.05214301124215126, 0.007998613640666008, -0.05950784683227539, -0.01057063601911068, -0.047889336943626404, 0.026575133204460144, 0.025522135198116302, -0.03724835067987442, 0.08599309623241425, -0.0527123361825943, 0.01142203900963068, 0.028244202956557274, 0.06387188285589218, -0.04026985913515091, 0.039852503687143326, 0.06182580068707466, 0.05661448836326599, 0.024022825062274933, -0.024990245699882507, -0.012310911901295185, 0.0030606042128056288, 0.04426605626940727, 0.001742466352880001, 0.04241163283586502, 0.02218940295279026, -0.017856411635875702, 0.04151393473148346, -0.012164951302111149, -0.022075247019529343, 0.009406210854649544, 0.048247452825307846, 0.05607886239886284, -0.03244771063327789, 0.0650184229016304, -6.2115384324075e-33, -0.021358110010623932, -0.02145661786198616, 0.006226921919733286, -0.08468130230903625, 0.0005476358346641064, 0.027216220274567604, -0.014897129498422146, -0.018552623689174652, 0.010146982967853546, -0.0035045011900365353, -0.0010875342413783073, -0.005354839377105236, 0.02369227632880211, 0.0013403368648141623, 0.03442038968205452, -0.007196709048002958, 0.0031651046592742205, 0.01552159059792757, -0.0050857351161539555, -0.0010479323100298643, 0.016384297981858253, -0.03574618697166443, -0.03607090562582016, 0.014104976318776608, -0.008162029087543488, 0.014174988493323326, 0.015048593282699585, 0.000807667151093483, -0.007295873016119003, 0.04518090561032295, -0.013320881873369217, -0.030312372371554375, 0.050265055149793625, 0.04184316471219063, 0.0003143710200674832, 0.014998589642345905, 0.021464254707098007, -0.05700499191880226, -0.02330883778631687, -0.01450100727379322, -0.10134909301996231, -0.05891158804297447, 0.011535423807799816, -0.027528846636414528, 0.032547350972890854, -0.011219453997910023, -0.03790557384490967, 0.00965460017323494, 0.0008787707192823291, -0.021793941035866737, -0.009973826818168163, 0.003021234180778265, -0.008493742905557156, -0.017131906002759933, 0.00596530782058835, -0.0072836680337786674, -0.011070835404098034, -0.012376775033771992, 0.05039772391319275, 0.02828010730445385, 0.007059980183839798, 0.04639856517314911, -0.01614474318921566, 0.03560536727309227, -0.021427279338240623, 0.020215017721056938, -0.0020712155383080244, 0.016321726143360138, -0.04861318692564964, -0.004925928544253111, -0.022454585880041122, 0.039109181612730026, 0.05930769816040993, -0.005970702972263098, 0.04355636239051819, -0.05001719668507576, 0.014283557422459126, -0.028144557029008865, 0.0285008754581213, -0.0690121278166771, -0.05070717632770538, 0.0061766644939780235, 0.011531936004757881, -0.014066524803638458, -0.054505616426467896, 0.02882103994488716, -0.024881230667233467, 0.005903702229261398, 0.02428284101188183, 0.024959122762084007, -0.008795687928795815, -0.017443260177969933, -0.022034091874957085, 0.05582951381802559, 0.05833710357546806, 0.008344005793333054, 0.0685417428612709, -0.021446509286761284, 0.03434785082936287, 0.0036386691499501467, -0.0916169136762619, -0.001138447318226099, 0.02363816648721695, -0.05051904171705246, -0.03015139512717724, 0.05566719174385071, -0.051323603838682175, 0.036726124584674835, -0.02944033406674862, 0.0029240604490041733, 0.0018862646538764238, 0.024020209908485413, 0.03539130836725235, -0.012964933179318905, -0.010630493052303791, -0.012274757958948612, 0.028469447046518326, -0.043041571974754333, -0.020377451553940773, -0.039538949728012085, 0.028881946578621864, 0.035275302827358246, 0.008905950002372265, 0.013858924619853497, 0.01603275164961815, 0.024474220350384712, -0.009952392429113388, 0.01764063909649849, 0.039854276925325394, 0.0023477214854210615, 0.010289526544511318, -0.02808985486626625, 2.7593839035944256e-07, 0.015928048640489578, 0.05551620200276375, -0.02109626680612564, -0.03922262787818909, -0.028895139694213867, -0.04983972758054733, -0.0038133845664560795, 0.012560765258967876, -0.04760601371526718, -0.044977132230997086, 0.019965041428804398, 0.024398447945713997, -0.01628439500927925, 0.051439426839351654, -0.01984853856265545, 0.014727422967553139, 0.015741296112537384, -0.062310054898262024, -0.0536879226565361, -0.02247445285320282, 0.02158944308757782, 0.04398593306541443, 0.00930505245923996, -0.029342571273446083, -0.021965809166431427, 0.025573261082172394, -0.011566055938601494, -0.052459731698036194, 0.04827483743429184, 0.03937968984246254, 0.035364508628845215, 0.020454127341508865, 0.05364556238055229, 0.029029397293925285, 0.0178468469530344, -0.019511962309479713, 0.03490307182073593, -0.031456515192985535, -0.015140158124268055, -0.01976153999567032, -0.007254438009113073, -0.04811325669288635, -0.04203991964459419, -0.0013179280795156956, -0.027330776676535606, 0.04700523987412453, 0.01407571230083704, -0.05910991504788399, -0.05376986786723137, -0.022552605718374252, 0.01979677937924862, 0.028787095099687576, -0.002609317423775792, 0.04411428049206734, -0.00994902104139328, 0.012688121758401394, 0.018804945051670074, -0.001079703215509653, 0.01939103752374649, -0.027767037972807884, -0.030019467696547508, -0.008942401967942715, 0.006658220198005438, 0.012615490704774857, 0.0017175133107230067, -0.045518841594457626, -0.03017275035381317, 2.318181159408354e-34, 0.041171714663505554, -0.037843409925699234, -0.026545612141489983, 0.008163091726601124, 0.00956706888973713, -0.0009738585795275867, -0.06127823516726494, 0.001312588108703494, -0.02364693582057953, -0.045587532222270966, -0.005588930565863848], "parsedContent": {"infobox": {}, "categories": [], "links": ["27 June", "2005", "Recruitment Drive", "quest items", "Recruitment Drive", "Miss <PERSON><PERSON><PERSON>", "cupric sulfate", "vial of liquid", "gypsum", "Recruitment Drive", "Recruitment Drive", "Recruitment Drive", "Recruitment Drive", "Recruitment Drive", "Recruitment Drive", "Recruitment Drive"], "plainText": "The ??? mixture is a quest item used only in Recruitment Drive. It is made by incorrectly mixing ingredients together during <PERSON>'s trial. There are three ??? mixtures obtainable during the quest, none of which are useful in completing the challenge. Because many of the ingredients cannot be retrieved after use, it is likely that the challenge must be restarted if a ??? mixture is created. The hot mixture is made by using cupric sulfate on the vial of liquid. The warm mixture is made by using gypsum on a vial of liquid. The horrible mixture is made by adding cupric sulfate to anything else, or to another hot mixture. Creation Hot Using any filled vial on an existing hot ??? mixture will turn it into a horrible ??? mixture.  Partial completion of Recruitment Drive |members = Yes |ticks =  |mat1 = Cupric sulfate |mat1cost = No |mat2 = Vial of liquid |mat2cost = No |output1 = ??? mixture#Hot |output1txt = ??? mixture |output1pic = ??? mixture (hot) |output1cost = No |output2 = Vial }} Warm  Partial completion of Recruitment Drive |members = Yes |ticks =  |mat1 = Gypsum |mat1cost = No |mat2 = Vial of liquid |mat2cost = No |output1 = ??? mixture#Warm |output1txt = ??? mixture |output1pic = ??? mixture (warm) |output1cost = No |output2 = Vial }} Horrible Using any filled vial (except for nitrous oxide and warm ??? mixture) on an existing horrible ??? mixture will empty the contents into that mixture, resulting in no changes.  <tabber> Sodium chloride=  Partial completion of Recruitment Drive |members = Yes |ticks =  |mat1 = Cupric sulfate |mat1cost = No |mat2 = Sodium chloride |mat2cost = No |output1 = ??? mixture#Horrible |output1txt = ??? mixture |output1pic = ??? mixture (horrible) |output1cost = No |output2 = Vial }} |-| Gypsum=  Partial completion of Recruitment Drive |members = Yes |ticks =  |mat1 = Cupric sulfate |mat1cost = No |mat2 = Gypsum |mat2cost = No |output1 = ??? mixture#Horrible |output1txt = ??? mixture |output1pic = ??? mixture (horrible) |output1cost = No |output2 = Vial }} |-| Acetic acid=  Partial completion of Recruitment Drive |members = Yes |ticks =  |mat1 = Cupric sulfate |mat1cost = No |mat2 = Acetic acid |mat2cost = No |output1 = ??? mixture#Horrible |output1txt = ??? mixture |output1pic = ??? mixture (horrible) |output1cost = No |output2 = Vial }} |-| Cupric ore powder=  Partial completion of Recruitment Drive |members = Yes |ticks =  |mat1 = Cupric sulfate |mat1cost = No |mat2 = Cupric ore powder |mat2cost = No |output1 = ??? mixture#Horrible |output1txt = ??? mixture |output1pic = ??? mixture (horrible) |output1cost = No |output2 = Vial }} |-| Tin ore powder=  Partial completion of Recruitment Drive |members = Yes |ticks =  |mat1 = Cupric sulfate |mat1cost = No |mat2 = Tin ore powder |mat2cost = No |output1 = ??? mixture#Horrible |output1txt = ??? mixture |output1pic = ??? mixture (horrible) |output1cost = No |output2 = Vial }} </tabber>"}, "originalContent": "{{External|rs}}\n{{Infobox Item\n|version1 = Hot\n|version2 = Warm\n|version3 = Horrible\n|name = ??? mixture\n|image1 = [[File:??? mixture (hot).png]]\n|image2 = [[File:??? mixture (warm).png]]\n|image3 = [[File:??? mixture (horrible).png]]\n|release = [[27 June]] [[2005]]\n|update = Recruitment Drive\n|members = Yes\n|quest = [[Recruitment Drive]]\n|tradeable = No\n|bankable = No\n|placeholder = No\n|equipable = No\n|stackable = No\n|noteable = No\n|options = Drop\n|examine1 = A very hot vial of something or other. The label says 'Cupric Sulfate'.\n|examine2 = A very warm vial of something or other. It's a bit lumpy.\n|examine3 = It looks horrible. I think I messed something up.\n|value = 1\n|weight = 0.056\n|id1 = 5589\n|id2 = 5590\n|id3 = 5591\n}}\n{{Synced switch\n|version1 = [[File:??? mixture (hot) detail.png|left|120px]]\n|version2 = [[File:??? mixture (warm) detail.png|left|120px]]\n|version3 = [[File:??? mixture (horrible) detail.png|left|120px]]\n}}\nThe '''??? mixture''' is a [[quest items|quest item]] used only in [[Recruitment Drive]]. It is made by incorrectly mixing ingredients together during [[Miss Cheevers]]'s trial. There are three ??? mixtures obtainable during the quest, none of which are useful in completing the challenge. Because many of the ingredients cannot be retrieved after use, it is likely that the challenge must be restarted if a ??? mixture is created.\n\nThe hot mixture is made by using [[cupric sulfate]] on the [[vial of liquid]]. The warm mixture is made by using [[gypsum]] on a vial of liquid. The horrible mixture is made by adding cupric sulfate to anything else, or to another hot mixture.\n\n==Creation==\n===Hot===\nUsing any filled vial on an existing hot ??? mixture will turn it into a horrible ??? mixture.\n{{Recipe\n|notes = {{SCP|Quest}} Partial completion of [[Recruitment Drive]]\n|members = Yes\n|ticks = \n|mat1 = Cupric sulfate\n|mat1cost = No\n|mat2 = Vial of liquid\n|mat2cost = No\n|output1 = ??? mixture#Hot\n|output1txt = ??? mixture\n|output1pic = ??? mixture (hot)\n|output1cost = No\n|output2 = Vial\n}}\n\n===Warm===\n{{Recipe\n|notes = {{SCP|Quest}} Partial completion of [[Recruitment Drive]]\n|members = Yes\n|ticks = \n|mat1 = Gypsum\n|mat1cost = No\n|mat2 = Vial of liquid\n|mat2cost = No\n|output1 = ??? mixture#Warm\n|output1txt = ??? mixture\n|output1pic = ??? mixture (warm)\n|output1cost = No\n|output2 = Vial\n}}\n\n===Horrible===\nUsing any filled vial (except for nitrous oxide and warm ??? mixture) on an existing horrible ??? mixture will empty the contents into that mixture, resulting in no changes. \n<tabber>\nSodium chloride=\n{{Recipe\n|notes = {{SCP|Quest}} Partial completion of [[Recruitment Drive]]\n|members = Yes\n|ticks = \n|mat1 = Cupric sulfate\n|mat1cost = No\n|mat2 = Sodium chloride\n|mat2cost = No\n|output1 = ??? mixture#Horrible\n|output1txt = ??? mixture\n|output1pic = ??? mixture (horrible)\n|output1cost = No\n|output2 = Vial\n}}\n|-|\nGypsum=\n{{Recipe\n|notes = {{SCP|Quest}} Partial completion of [[Recruitment Drive]]\n|members = Yes\n|ticks = \n|mat1 = Cupric sulfate\n|mat1cost = No\n|mat2 = Gypsum\n|mat2cost = No\n|output1 = ??? mixture#Horrible\n|output1txt = ??? mixture\n|output1pic = ??? mixture (horrible)\n|output1cost = No\n|output2 = Vial\n}}\n|-|\nAcetic acid=\n{{Recipe\n|notes = {{SCP|Quest}} Partial completion of [[Recruitment Drive]]\n|members = Yes\n|ticks = \n|mat1 = Cupric sulfate\n|mat1cost = No\n|mat2 = Acetic acid\n|mat2cost = No\n|output1 = ??? mixture#Horrible\n|output1txt = ??? mixture\n|output1pic = ??? mixture (horrible)\n|output1cost = No\n|output2 = Vial\n}}\n|-|\nCupric ore powder=\n{{Recipe\n|notes = {{SCP|Quest}} Partial completion of [[Recruitment Drive]]\n|members = Yes\n|ticks = \n|mat1 = Cupric sulfate\n|mat1cost = No\n|mat2 = Cupric ore powder\n|mat2cost = No\n|output1 = ??? mixture#Horrible\n|output1txt = ??? mixture\n|output1pic = ??? mixture (horrible)\n|output1cost = No\n|output2 = Vial\n}}\n|-|\nTin ore powder=\n{{Recipe\n|notes = {{SCP|Quest}} Partial completion of [[Recruitment Drive]]\n|members = Yes\n|ticks = \n|mat1 = Cupric sulfate\n|mat1cost = No\n|mat2 = Tin ore powder\n|mat2cost = No\n|output1 = ??? mixture#Horrible\n|output1txt = ??? mixture\n|output1pic = ??? mixture (horrible)\n|output1cost = No\n|output2 = Vial\n}}\n</tabber>\n\n{{Recruitment Drive}}"}