{"title": "<PERSON><PERSON>re Flesh Eaters", "namespace": "Main", "type": "unknown", "confidence": 0.5, "entities": [{"type": "category", "value": "Feldip <PERSON>", "confidence": 0.9, "source": "category"}], "relationships": [{"from": "<PERSON><PERSON>re Flesh Eaters", "to": "17 May", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "2005", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Feldip <PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "quest", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Skogre", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Castle Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Big Chompy Bird Hunting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Jungle Potion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Druidic Ritual", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "comp ogre bow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Crumble Undead", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Wolf bones", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Achey logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Knife", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Axe", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Woodcutting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Bow string", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Brutal arrows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Crumble Undead", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Achey logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Knife", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Nails", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Hammer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "protection prayers", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Strength#Temporary boosts", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Comp ogre bow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Brutal arrows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Strength potion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Vial of water", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Rogue's purse", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Snake weed", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>'s balm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Sanfew serum", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>'s balm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Ava's accumulator", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "super restore", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "combat potion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "ranged potion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "magic potion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "restore potion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "ring of dueling", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Castle Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Zombie (<PERSON><PERSON><PERSON> Flesh Eaters)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Castle Wars", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "fairy ring", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Super restore potion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "cooked chompy", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Ogre guard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Disease", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "diseased", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Combat", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Skogre", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Super restore potion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Broken Lecturn", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "torn page", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "zombie (<PERSON><PERSON><PERSON> Flesh Eaters)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "ruined backpack", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Dragon inn tankard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "rotten food", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "knife", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON> (Zogre Flesh Eaters)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "black prism", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Nightmare Zone", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Ferox Enclave", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "ring of dueling", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Grouping", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Pool of Refreshment", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Black prism", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Dragon inn tankard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Torn page", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Yanille", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Minigame teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Watchtower teleport", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Watchtower", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Dragon Inn", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Dragon inn tankard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON> Vahn", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Zavistic Rarve", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Wizards' Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Wizards' Tower", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Black prism", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "torn page", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Sithik <PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Wizards' Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "drawers (<PERSON><PERSON><PERSON> Flesh Eaters)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "cupboard (<PERSON><PERSON><PERSON> Flesh Eaters)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "wardrobe (<PERSON><PERSON><PERSON>lesh Eaters)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Book of portraiture", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Necromancy book", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "papyrus", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "charcoal", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Book of 'h.a.m'", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Crafting", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Dragon inn tankard", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Zavistic Rarve", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "signed portrait", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Strange potion", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "cup of tea", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "ogre", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>'s balm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "brutal arrows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Black Prism", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Zavistic Rarve", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>'s balm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "brutal arrows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Crumble Undead", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "sample bottle", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Sithik <PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>'s balm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "rogue's purse", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "vial of water", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "snake weed", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Grand Exchange", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON> Nar", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "achey logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "bow string", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "cooked chompy", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "knives", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Brutal arrows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "composite ogre bow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Grand Exchange", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Achey tree logs", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "wolf bones", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "bow string", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Fletching", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "ogre arrow shaft", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "feathers", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "nails", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "metal", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "flighted ogre arrow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "hammer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>'s balm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "composite ogre bow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "brutal arrows", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Crumble Undead", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Ogre gate key", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "stand (<PERSON><PERSON><PERSON>lesh Eaters)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Crumble Undead", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Prayer", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "disease", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "ourg", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "safespot", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Protect from Missiles", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "ogre artefact", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "ourg bones", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON> and <PERSON> Man II", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "zogre bone", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "ourg bones", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "zogre bones", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>'s balm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "comp ogre bow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "brutal arrow", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "inoculation bracelet", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON><PERSON>'s balm", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "~ <PERSON><PERSON><PERSON>'s stuffsies ~", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "black prism", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Zavistic Rarve", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Wizards' Guild", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON>", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Shilo Village (location)", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Rum Deal", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "<PERSON><PERSON> and <PERSON> Man II", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "wikipedia:Zombi_2", "type": "references", "confidence": 0.7, "source": "wiki_link"}, {"from": "<PERSON><PERSON>re Flesh Eaters", "to": "Feldip <PERSON>", "type": "belongs_to_category", "confidence": 0.9, "source": "category"}], "embedding": [0.06508644670248032, 0.07875995337963104, 0.008088180795311928, -0.036429036408662796, -0.00836591050028801, 0.028794771060347557, -0.10177359730005264, 0.00927521102130413, 0.047815944999456406, -0.02485485188663006, -0.0385415181517601, 0.03557991608977318, 0.013978724367916584, -0.025643743574619293, 0.00675115967169404, 0.006745971739292145, 0.025379717350006104, 0.025649435818195343, -0.05273347720503807, -0.0071463692001998425, -0.023693252354860306, 0.024555210024118423, -0.008347460068762302, -0.0019201551331207156, 0.05380632355809212, -0.046322647482156754, -0.06439477950334549, 0.021186957135796547, -0.00971195474267006, -0.06277139484882355, -0.04252752289175987, -0.06516331434249878, 0.0078126797452569, 0.03491948917508125, 2.4935441160778282e-06, -0.0028456428553909063, -0.06110558286309242, 0.029300007969141006, -0.08766814321279526, 0.06681501865386963, -0.015571784228086472, -0.0037136045284569263, -0.024240665137767792, -0.0028789122588932514, 0.06010420247912407, -0.09456062316894531, 0.04732663556933403, 0.028851836919784546, -0.05295463278889656, 0.024063322693109512, -0.002243238501250744, -0.008792535401880741, -0.03227083757519722, -0.0067720259539783, 0.015565981157124043, 0.009973514825105667, -0.007759027648717165, -0.044989969581365585, 0.027868490666151047, 0.028695974498987198, -0.014613245613873005, -0.009115558117628098, -0.026234906166791916, 0.0175460372120142, 0.011044090613722801, -0.020346738398075104, 0.04912332817912102, -0.022496625781059265, -0.006333727855235338, 0.01428894978016615, -0.06236175447702408, -0.010209206491708755, 0.011775228194892406, 0.020643819123506546, -0.0058919996954500675, -0.04654216766357422, -0.0164224561303854, 0.03296629339456558, -0.047673337161540985, -0.016617635264992714, 0.005748636554926634, -0.02139745093882084, 0.0200898889452219, 0.018220262601971626, -0.003915961366146803, 0.021492457017302513, 0.021225901320576668, -0.03711879625916481, 0.08132311701774597, -0.023723307996988297, -0.052174072712659836, -0.023336227983236313, -0.009865544736385345, 0.06264867633581161, -0.07990534603595734, -0.024917569011449814, 0.018285144120454788, 0.034806232899427414, 0.14689858257770538, 0.007140112109482288, -0.006000051740556955, -0.013306881301105022, -0.0164585430175066, 0.011690959334373474, 0.020798439159989357, -0.010477876290678978, 0.05606083571910858, -0.037371676415205, -0.04136427119374275, 0.001799028948880732, 0.0010637014638632536, 0.008529001846909523, -0.04246419668197632, -0.015493620187044144, 0.028182748705148697, -0.013665701262652874, 0.06579632312059402, -0.0550827793776989, 0.028816521167755127, -0.009702020324766636, -0.0773741751909256, 0.016888998448848724, 0.0023930452298372984, 0.00859880167990923, -0.008810354396700859, -0.05477563664317131, 0.02667687088251114, 0.018231865018606186, -0.025538861751556396, 0.02538042701780796, -0.04971778765320778, 0.003003327641636133, 0.04542425647377968, -0.0015086379135027528, 0.02909567579627037, 0.0116934385150671, -0.052873365581035614, 0.009100832045078278, 0.015965167433023453, -0.005972689017653465, 0.026409003883600235, 0.029219480231404305, -0.06852982938289642, 0.041085384786129, 0.05616314709186554, -0.0327455960214138, -0.008221744559705257, 0.08632820099592209, -0.008266786113381386, 0.050808828324079514, 0.028037194162607193, 0.02240467444062233, 0.05159783735871315, 0.0014423681423068047, 0.010715216398239136, -0.0014480259269475937, 0.04844367131590843, -0.043292537331581116, -0.021146101877093315, 0.047754086554050446, -0.015512820333242416, 0.04444460570812225, -0.03672812506556511, -0.014287354424595833, -0.05287153646349907, -0.06323348730802536, 0.04687237739562988, 0.06036580726504326, -0.029068216681480408, -0.015163762494921684, -0.001606280216947198, 0.04293838515877724, 0.013244912028312683, -0.07224728912115097, -0.030088089406490326, 0.14575240015983582, 0.022304238751530647, -0.021683180704712868, -0.05927243456244469, -0.012323587201535702, 0.015989363193511963, -0.02155926637351513, -0.004086795728653669, -0.029338916763663292, 0.007786542642861605, 0.007163119036704302, -0.015819387510418892, 0.0363621860742569, 0.018888263031840324, -0.024563314393162727, -0.026959439739584923, -0.006196118891239166, 0.06210131198167801, -0.004164422862231731, 0.03545595705509186, 0.02009168267250061, -0.046548400074243546, -0.14209403097629547, 0.0026658624410629272, -0.055400460958480835, 0.015187068842351437, 0.03358585387468338, 0.06700220704078674, -0.05107329785823822, 0.04180816933512688, -0.022599929943680763, -0.060886044055223465, -0.0015597500605508685, -0.02471652626991272, 0.014744138345122337, -0.036347128450870514, -0.024793436750769615, 0.04810616746544838, 0.0022256721276789904, -0.044593971222639084, 0.0015723372343927622, -0.007202358916401863, -0.059200529009103775, -0.02387729287147522, 0.026786047965288162, 0.007551396265625954, 0.021082766354084015, -0.0038200276903808117, 0.037271324545145035, -0.04243509843945503, 0.06886283308267593, -0.02950959838926792, 0.02594689466059208, 0.026558808982372284, -0.0412740558385849, 0.023467686027288437, 0.04213712364435196, 0.022703520953655243, -0.04030860215425491, -0.04754731431603432, -0.019404789432883263, 0.03130157291889191, 0.02061869390308857, -0.010603601112961769, -0.03386770561337471, 0.03380017355084419, -0.026270003989338875, -0.007879246026277542, 0.016676926985383034, 0.043702512979507446, -0.05348041653633118, -0.020741350948810577, -0.014280065894126892, -0.01981966197490692, 0.027226153761148453, -0.014796305447816849, -0.02212226204574108, -0.002173104789108038, 0.023265060037374496, 0.01710423268377781, 0.023768113926053047, 0.028278257697820663, 0.009499113075435162, -0.026581887155771255, 0.05499745160341263, -0.00276407552883029, -0.045471154153347015, 0.005314813461154699, -0.022225063294172287, 0.03809230402112007, -0.006101994775235653, 0.0024994374252855778, -0.038046132773160934, 0.028722859919071198, 0.02289879508316517, 0.05280928686261177, 0.00656083133071661, 0.00540459668263793, 0.03741852566599846, -0.019648348912596703, -0.052020661532878876, 0.007278019562363625, -0.012763828970491886, -0.017733724787831306, -0.03396525979042053, 0.0036182620096951723, 0.049002837389707565, 0.009809096343815327, 0.08280377089977264, 0.013566594570875168, 0.03385256603360176, 0.00326549238525331, -0.05727509409189224, 0.021209899336099625, -0.06027085334062576, -0.0329565703868866, -0.001956705469638109, -0.01975279115140438, -0.06382400542497635, 0.009692061692476273, 0.03345716372132301, 0.007534531876444817, 0.044648922979831696, 0.018197717145085335, 0.018116161227226257, -0.028330011293292046, -0.0005157318664714694, 0.018095655366778374, 0.000589947565458715, -0.016599245369434357, -0.014615976251661777, 0.02399006113409996, -0.08963461965322495, 0.022989889606833458, 0.03143283352255821, 0.024444496259093285, 0.04698198288679123, 0.048792969435453415, -0.00995782669633627, 0.010057447478175163, -0.007307537365704775, 0.05483921989798546, -0.008527163416147232, 0.0333833247423172, -0.05577988177537918, -0.042693957686424255, 0.06548012793064117, -0.010618421249091625, 0.011710209771990776, -0.04718053713440895, 0.03670339286327362, -0.006490346044301987, -0.04000811651349068, -0.01782594807446003, 0.017715999856591225, 0.06935588270425797, 0.10394300520420074, -0.034678276628255844, 0.016431007534265518, 0.02553281933069229, 0.022844837978482246, -0.01327656302601099, -0.026426224038004875, 0.027909299358725548, 0.0055600376799702644, 0.026998233050107956, -0.03230675309896469, 0.003558787750080228, -0.0201194379478693, 0.031039102002978325, 0.007036268245428801, -0.02278769016265869, 0.015889177098870277, 0.0005467155715450644, 0.05648483708500862, -0.033158332109451294, 0.006203703116625547, -0.010783908888697624, -0.015648068860173225, -0.011393805965781212, -0.001325193326920271, -0.008405743166804314, 0.05875217914581299, -0.008582278154790401, -0.0030479831621050835, -0.001314435969106853, 0.07477733492851257, -0.014452188275754452, -0.031205100938677788, 0.046553462743759155, 0.023826856166124344, -0.021558796986937523, -0.016316315159201622, 0.020386947318911552, -0.09998530894517899, -0.022369466722011566, 0.005188487935811281, -0.014039801433682442, 0.012197855859994888, 0.027355261147022247, 0.01717265136539936, 0.03663007169961929, -0.029240811243653297, 0.0279092900454998, 0.028303220868110657, -0.007636493071913719, -0.05124398320913315, -0.02917889691889286, 0.029945900663733482, 0.016528360545635223, -0.08833236247301102, -0.032264046370983124, -0.021676557138562202, 0.019610483199357986, -0.0037702913396060467, 0.02898896113038063, 0.015094718895852566, 0.009526093490421772, -0.05821638181805611, 0.026086336001753807, -0.06841254979372025, -0.03177475929260254, -0.0013969269348308444, 0.0042663575150072575, -0.001438009669072926, 0.008064476773142815, 0.01698997989296913, 0.047873880714178085, 0.054064247757196426, 0.02460598386824131, 0.012012198567390442, 0.029587604105472565, 0.02155013009905815, 0.06342100352048874, 0.01119043119251728, 0.057666752487421036, -0.04157204553484917, -0.03841245546936989, 0.008151943795382977, 0.021690353751182556, 0.01860620640218258, 0.01567983627319336, -0.035914722830057144, -0.0034850467927753925, 0.01410954911261797, -0.027788355946540833, 0.004521889612078667, -0.019275600090622902, 0.027160560712218285, 0.03748320788145065, 0.020792605355381966, 0.0057714697904884815, 0.0016288454644382, 0.0364178828895092, 0.01819036342203617, 0.010279456153512001, -0.02545884996652603, -0.04162754863500595, -0.044667549431324005, 0.032264593988657, -0.015820108354091644, 0.07251153886318207, -0.05571916326880455, -0.03555332496762276, 0.010901598259806633, -0.009242843836545944, 0.037322115153074265, 0.022278914228081703, 0.01846519485116005, -0.020871920511126518, -0.0040148841217160225, 0.0024556992575526237, 0.005884803365916014, 0.03441532701253891, -0.0026446045376360416, -0.024022331461310387, -0.0437006801366806, 0.0077033210545778275, -0.008265395648777485, -0.03397398442029953, -0.012324330396950245, 0.10145840793848038, -0.05426982045173645, -0.04558772221207619, -0.06028830632567406, 0.026390479877591133, 0.023457249626517296, -0.010206542909145355, -0.049547743052244186, -0.0016028882237151265, -0.0714089423418045, -0.02722209319472313, -0.04711438715457916, 0.016136638820171356, -0.013780939392745495, 0.015254825353622437, 0.01358911581337452, 0.013880733400583267, 0.0029010612051934004, 0.03688949719071388, 0.013377746567130089, 0.01728718914091587, -0.026421407237648964, -0.059463020414114, 0.03441733494400978, 0.033792175352573395, -0.0349007286131382, 0.012081999331712723, -0.039304450154304504, -0.012423733249306679, -0.033790044486522675, 0.03711029142141342, 0.017548026517033577, -0.07277193665504456, -0.003695941064506769, 0.026601620018482208, 0.0353110134601593, 0.043017998337745667, 0.04928753525018692, -0.015784168615937233, 0.01419347245246172, -0.07431267201900482, -0.07330510020256042, 0.08859993517398834, -0.024662865325808525, 0.04908960312604904, 0.020259015262126923, -0.0030117351561784744, 0.02347753569483757, -0.009407897479832172, -0.03207680955529213, -0.027213826775550842, 0.017827108502388, 0.0015804617432877421, -0.011792849749326706, -0.014482315629720688, -0.008914586156606674, -0.007661224342882633, -0.03014633059501648, 0.008099513128399849, -0.08780114352703094, -0.05339993163943291, -0.014558964408934116, 0.045587968081235886, 0.061221107840538025, 0.010281432420015335, -0.018129315227270126, 0.09661164879798889, -0.020356617867946625, -0.03501530736684799, -0.030545461922883987, 0.03492572158575058, -0.000973094196524471, -0.020926639437675476, 0.0011336124734953046, 0.08089662343263626, -0.0008894751663319767, 0.0825560912489891, -0.0033548343926668167, -0.0019155977061018348, -0.002840045839548111, 0.07614018023014069, 0.007054051849991083, -0.001353242201730609, -0.005915407091379166, -0.021835679188370705, -0.04551178589463234, -0.028301255777478218, -0.06050775572657585, -0.03370843827724457, 0.02217785455286503, -0.032899416983127594, -0.02637040801346302, -0.026752643287181854, 0.03941773250699043, 0.04927220940589905, 0.010677138343453407, -0.013251311145722866, -0.07697483897209167, 0.05103365704417229, -7.042466736328974e-33, -0.002261803951114416, -0.011092672124505043, -0.03615327924489975, -0.05637633055448532, -0.06608101725578308, -0.01881951093673706, 0.013333158567547798, 0.002470456762239337, -0.0229218490421772, -0.030587399378418922, 0.018862634897232056, -0.05562443658709526, -0.0020033917389810085, -0.004270198754966259, -0.032873447984457016, 0.027719561010599136, -0.004005261231213808, 0.0016193637857213616, -0.020542796701192856, -0.04686928167939186, -0.0044803014025092125, 0.025119144469499588, 0.07596949487924576, 0.028031930327415466, -0.025719711557030678, 0.03229033946990967, 0.00949915498495102, -0.017496006563305855, 0.002531902864575386, 0.03097672387957573, -0.037227433174848557, 0.029073508456349373, 0.013346356339752674, -0.0012390471529215574, 0.014172754250466824, -0.01916373521089554, -0.06709220260381699, -0.024554690346121788, 0.007568359375, 0.03164592385292053, -0.022613856941461563, -0.009653392247855663, -0.0032596655655652285, -0.01854851469397545, 0.023059377446770668, 0.009852500632405281, 0.010482460260391235, -0.003045125398784876, -0.030730031430721283, 0.12061650305986404, 0.02047950215637684, -0.044358815997838974, -0.003277636133134365, -0.10019899904727936, 0.06744235008955002, 0.07389160245656967, 0.003590906737372279, 0.0001369951933156699, 0.07916789501905441, -0.03978404402732849, 0.0005933741922490299, -0.05179281532764435, -0.0009522987529635429, 0.02780444175004959, -0.0017439894145354629, 0.02327592484652996, 0.0008254893473349512, -0.06711200624704361, -0.019742364063858986, 0.016302907839417458, -0.08386167138814926, -0.014365028589963913, -0.04258125275373459, -0.009457222186028957, 0.05164980888366699, -0.01067140232771635, -0.05314447358250618, 0.020905926823616028, 0.01947028562426567, -0.04370437562465668, 0.005760824307799339, -0.04230879992246628, 0.059436798095703125, -0.017641998827457428, 0.02177189290523529, -0.007242689840495586, 0.006712734699249268, 0.0531587116420269, -0.015372633002698421, 0.03574325144290924, 0.012619227170944214, -0.0500631108880043, -0.026329562067985535, 0.028878508135676384, -0.007058671675622463, -0.0007834132993593812, 0.07124575972557068, -0.03897310048341751, -0.017199888825416565, -0.003973703365772963, 0.025877196341753006, 0.07269179821014404, 0.027865905314683914, -0.01352703757584095, 0.010186844505369663, -0.0005478957900777459, 0.02299341931939125, 0.017700769007205963, -0.005612317938357592, -0.007150717079639435, -0.04435105249285698, 0.006259806454181671, 0.008884659968316555, 0.09026659280061722, 0.017468523234128952, -0.049175240099430084, -0.021953212097287178, -0.01260783988982439, 0.01644560508430004, -0.03869171813130379, 0.021409671753644943, -0.005202969536185265, 0.03510545566678047, -0.07938401401042938, 0.001769790193066001, 0.004681949038058519, 0.06184171140193939, -0.03069191798567772, -0.05347687751054764, -0.03934413939714432, 0.002044102642685175, -0.047964908182621, 3.115308970791375e-07, -0.012269459664821625, 0.0044451188296079636, 0.0020527930464595556, 0.041041381657123566, -0.03742868825793266, 0.01122993603348732, 0.011991748586297035, 0.055279336869716644, -0.07581649720668793, -0.025426724925637245, -0.011050460860133171, -0.006830962374806404, -0.0012724922271445394, -0.008472087793052197, -0.02816472202539444, 0.004930291324853897, -0.004184406250715256, 0.007258004043251276, -0.012996392324566841, -0.012275905348360538, -0.016273196786642075, -0.027615852653980255, 0.0045988354831933975, -0.025038858875632286, -0.04548649862408638, -0.019659994170069695, -0.01418443862348795, -0.0063314069993793964, -0.02895193174481392, 0.028629571199417114, 0.013167435303330421, 0.04262314736843109, 0.024368667975068092, -0.001735630095936358, 0.04615800082683563, -0.045095279812812805, 0.05580361932516098, -0.006531331688165665, -0.024264970794320107, -0.0059868693351745605, -0.012211971916258335, -0.01678384654223919, 0.007088066078722477, -0.029399177059531212, 0.007866166532039642, 0.021620213985443115, -0.04249026998877525, 0.04122350737452507, -0.010744992643594742, 0.007048966363072395, 0.016021359711885452, 0.037050798535346985, 0.06146020069718361, -0.01909906417131424, 0.0010981064988300204, 0.0009359460673294961, 0.04752354696393013, -0.012770077213644981, 0.016692567616701126, -0.006447063758969307, 0.001815781812183559, -0.03762228786945343, 0.02442743256688118, 0.04982626065611839, -0.05194318667054176, -0.04903220757842064, -0.057594407349824905, 3.7684190753202825e-34, 0.002096193842589855, 0.011958087794482708, -0.022927800193428993, 0.02810216322541237, 0.038944363594055176, 0.0034235231578350067, 0.0444013848900795, -0.002215271582826972, 0.01874694600701332, -0.017348187044262886, -0.03301431983709335], "parsedContent": {"infobox": {}, "categories": ["Feldip <PERSON>"], "links": ["17 May", "2005", "Feldip <PERSON>", "quest", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Skogre", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Castle Wars", "Big Chompy Bird Hunting", "Jungle Potion", "Druidic Ritual", "comp ogre bow", "Crumble Undead", "Wolf bones", "Achey logs", "Knife", "Axe", "Woodcutting", "Bow string", "Brutal arrows", "Crumble Undead", "Achey logs", "Knife", "Nails", "Hammer", "<PERSON><PERSON>", "protection prayers", "Strength#Temporary boosts", "Comp ogre bow", "Brutal arrows", "Strength potion", "Vial of water", "Rogue's purse", "Snake weed", "<PERSON><PERSON><PERSON>'s balm", "Sanfew serum", "<PERSON><PERSON><PERSON>'s balm", "Ava's accumulator", "super restore", "combat potion", "ranged potion", "magic potion", "restore potion", "ring of dueling", "Castle Wars", "Ferox Enclave", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zombie (<PERSON><PERSON><PERSON> Flesh Eaters)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Castle Wars", "fairy ring", "<PERSON><PERSON><PERSON>", "Super restore potion", "cooked chompy", "Ogre guard", "<PERSON><PERSON><PERSON>", "Disease", "diseased", "Combat", "<PERSON><PERSON><PERSON>", "Skogre", "Super restore potion", "Broken Lecturn", "torn page", "zombie (<PERSON><PERSON><PERSON> Flesh Eaters)", "ruined backpack", "Dragon inn tankard", "rotten food", "knife", "<PERSON><PERSON><PERSON> (Zogre Flesh Eaters)", "black prism", "Nightmare Zone", "Ferox Enclave", "ring of dueling", "Grouping", "Pool of Refreshment", "Black prism", "Dragon inn tankard", "Torn page", "Yanille", "Minigame teleport", "Watchtower teleport", "Watchtower", "Dragon Inn", "Dragon inn tankard", "<PERSON><PERSON> Vahn", "Zavistic Rarve", "Wizards' Guild", "Wizards' Tower", "Black prism", "torn page", "Sithik <PERSON>", "Wizards' Guild", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "drawers (<PERSON><PERSON><PERSON> Flesh Eaters)", "cupboard (<PERSON><PERSON><PERSON> Flesh Eaters)", "wardrobe (<PERSON><PERSON><PERSON>lesh Eaters)", "Book of portraiture", "Necromancy book", "papyrus", "charcoal", "Book of 'h.a.m'", "Crafting", "Dragon inn tankard", "<PERSON><PERSON>", "Zavistic Rarve", "signed portrait", "Strange potion", "cup of tea", "ogre", "<PERSON><PERSON><PERSON>'s balm", "brutal arrows", "Black Prism", "Zavistic Rarve", "<PERSON><PERSON><PERSON>'s balm", "brutal arrows", "Crumble Undead", "sample bottle", "Sithik <PERSON>", "<PERSON><PERSON><PERSON>'s balm", "rogue's purse", "vial of water", "snake weed", "Grand Exchange", "<PERSON><PERSON><PERSON> Nar", "achey logs", "bow string", "cooked chompy", "knives", "Brutal arrows", "<PERSON><PERSON><PERSON>", "composite ogre bow", "Grand Exchange", "Achey tree logs", "wolf bones", "bow string", "Fletching", "ogre arrow shaft", "feathers", "nails", "metal", "flighted ogre arrow", "hammer", "<PERSON><PERSON><PERSON>'s balm", "composite ogre bow", "brutal arrows", "Crumble Undead", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ogre gate key", "stand (<PERSON><PERSON><PERSON>lesh Eaters)", "Crumble Undead", "Prayer", "disease", "ourg", "<PERSON><PERSON><PERSON>", "safespot", "Protect from Missiles", "ogre artefact", "ourg bones", "<PERSON><PERSON> and <PERSON> Man II", "zogre bone", "ourg bones", "zogre bones", "<PERSON><PERSON><PERSON>'s balm", "comp ogre bow", "brutal arrow", "inoculation bracelet", "<PERSON><PERSON><PERSON>'s balm", "~ <PERSON><PERSON><PERSON>'s stuffsies ~", "black prism", "Zavistic Rarve", "Wizards' Guild", "<PERSON><PERSON>", "Shilo Village (location)", "Rum Deal", "<PERSON><PERSON> and <PERSON> Man II", "wikipedia:Zombi_2"], "plainText": "Zogre Flesh Eaters is a Feldip Hills area quest in which you must venture into the crypts of Gu'Tanoth, an ogre resting ground, now crawling with undead zombie and skeleton ogres. Details    *    *    |items = * 5 free inventory spaces are needed to receive items near the start of the quest * A comp ogre bow (not needed if using Crumble Undead  in final battle) or: ** Wolf bones ** Achey logs (obtainable during quest) ** Knife (obtainable during quest) ** Axe of your Woodcutting level ** Bow string * Brutal arrows (not needed if using Crumble Undead in final battle) or: ** Achey logs (obtainable during quest) ** Knife (obtainable during quest) ** Nails (the best you can fletch into brutal arrows) ** Hammer ** Feathers (4 per 1 brutal arrow) |recommended = *  *  for protection prayers *  (otherwise it is very difficult to open the chest below the stairs in the north-west corner. Strength boosts are useful here) *   (is needed if you want to create your own Comp ogre bow and  is needed to fletch Brutal arrows.) * Strength potion if your strength is low. Opening the ogre tomb seems to require strength and lowers your level each time you fail. * Vial of water, Rogue's purse and Snake weed if you choose to learn the technique that allows you to mix <PERSON><PERSON><PERSON>'s balm (pretty much necessary if < 89 combat, unless you have sanfew serum). * A Sanfew serum would be helpful to prevent disease and restore stats. <PERSON><PERSON><PERSON>'s balm is a cheap alternative to the Sanfew serum * Ava's accumulator to conserve arrows * To save the super restore obtained during the quest to combat disease, combat, ranged, or magic potions can be used after normal restore potions. * A ring of dueling to teleport to Castle Wars and Ferox Enclave to restore your stats and cure disease with the pools.  * A Yanille Teleport |kills = * Slash Bash (level 111) * Zombie (level 39) |leagueRegion = <poem>  — location requirement Unlocking   will auto-complete the quest </poem> }} Walkthrough Items required: 5-10 free inventory spaces. 5 for three cooked chompy birds (heal 10) and two Super restore potions. An additional 5 free slots to collect some quest items. Recommended: A teleport out, a weapon and food, especially for lower level players. Speak to Grish, located at Jiggig (south after crossing the connecting bridge to Castle Wars) east of the fairy ring code . He will talk about Zogres (zombie ogres) coming from the ground and making people sick. Ask if you can help, and progress through the conversation. Grish will give you two Super restore potions and three cooked chompy birds. Talk to the Ogre guard to the east. After mentioning Grish, he will remove the barricade to allow you inside the Zogre-infested area. Run east to a set of stairs going downward and descend these stairs into the Jiggig Dungeon. The cave Fighting the zombie. Note: You may now (or have already) become diseased if you are under 89 Combat and a Zogre or Skogre (level 44) attacks you. From now on, make sure you carry Super restore potions with you, because the disease will temporarily reduce a random skill by up to 10 levels every minute or so. *Proceed north-west to an area with a blackened floor. There will be a short cutscene. *Search the Broken Lecturn. You will find a torn page that contained a spell. *Search the skeleton that is adjacent to the lecturn; a zombie will appear and attack. Kill it to obtain a ruined backpack. *Open the backpack to obtain a Dragon inn tankard, some rotten food and a knife. *Attempt to search the coffin nearest to the skeleton, and then use a knife on it. The lock should unlock. Try opening the coffin (it may take a couple of tries). When the coffin is open, search it to obtain a black prism. *Backtrack and exit the cave by running back to the barricade or simply teleporting out. *Note: If you became diseased in the previous part, you can enter a dream in the Nightmare Zone in practice mode and leave to cure disease. Alternatively, you can teleport to Ferox Enclave with a ring of dueling (equivalently, Minigame teleport to Clan Wars) and drink from the Pool of Refreshment to restore your stats and cure disease. Otherwise it will weaken and cure after 15 minutes. Note: Being Diseased can prevent teleporting with minigame teleport. Keep trying until you get it, but it might take more than once. B. Vahn Items required: Black prism, Dragon inn tankard, Torn page, and 5 free inventory spaces. *Proceed to Yanille. Using the Minigame teleport to the Nightmare Zone will place you next to Yanille. The Watchtower teleport can also be used to get to Yanille, assuming the Watchtower quest is completed. Fairy ring code  is also near Yanille. *Go to the Dragon Inn in south-western Yanille, and use the tankard on the bartender. The bartender will explain that the tankard belonged to Brentle Vahn, a not-so-moral customer who had been seen with a shady wizard. *Talk to Zavistic Rarve, found on the  of the Wizards' Guild (NOT the Wizards' Tower!). He'll be wearing white and baby blue robes, distinct from the other wizards. If you don't have the required level to enter the guild, ring the bell on the east side of the building, then Rarve will appear outside. After seeing the prism and torn page, Rarve will tell you to talk to Sithik Ints in the guest house north of the guild on the . '''''Note: Unlike the bartender, using the items on Rarve does not work. You must specifically click 'talk-to' and speak to Rarve or Sithiks will never be mentioned.''''' Incriminating Sithik Proceed to the guest house, which is directly north of the Wizards' Guild, and then go to the top floor. Sithik is in the western most room (in the bed, does not appear as yellow dot on the minimap). Talk to him, and he will deny any connection to Zogres. When you raise allegations, Sithik will challenge you to prove he is guilty. Search the drawers, the cupboard, and the wardrobe in Sithiks room. You will find a Book of portraiture, a Necromancy book, papyrus and charcoal, and a Book of 'h.a.m'. Read all three books. Do not drop the tankard as you will need it shortly after. Use the Book of Portraiture on Sithik. He will recommend trying portraiture for yourself. Draw a portrait of Sithik by using the papyrus on him. He will admire either the 'truth' or the 'honesty' of your completed portrait: If he likes the '''<nowiki/>'truth' of the portrait, you will need to drop it and make a new one. You can grab another papyrus from the drawers. If he likes the <nowiki/>'honesty'''' of the portrait, keep it. Having a higher Crafting level helps with making a successful sketch. The two portraits won't appear the same in the inventory and the correct one, when examined, will read  Sithik is turned into an ogre. Take the portrait and the tankard back to the bartender (First use the tankard on the bartender and then the portrait). The bartender will recognise Sithik as the wizard seen with Brentle, and will sign the portrait as an affidavit. Go back to the Wizards' Guild and talk to Zavistic Rarve or ring the bell outside the door. Use the Necromancy book, the Book of 'h.a.m.', the tankard, and the signed portrait on him, which will be enough evidence to convince Rarve that Sithik may be responsible for the murder. Rarve will give you a Strange potion, which he instructs you to use on Sithik. '''You need to talk to Zavistic Rarve and select the dialogue option to give him the evidence, otherwise he won't give you the potion.''' Use the potion on the cup of tea on top of Sithik's dresser. Go down the ladder and back up to Sithik and you will find that he has turned into an ogre. Talk to Sithik, and he will confess. Obtain answers to all three questions in order to make Relicym's balm and brutal arrows. At this point you can sell the Black Prism to Zavistic Rarve. This is not necessary to progress the quest. You now have a choice: Make Relicym's balm and brutal arrows, or skip ahead to 'Relocating the ceremonial grounds' and use Crumble Undead against Slash Bash.  Making or buying Relicym's balm After Sithik Ints has revealed the secret to making Relicym's balm, having it on hand in the final fight will help tremendously. It is your choice to obtain this now; however, the quest can be done without it. The balm is also tradeable. Relicym's balm(3) is made by using a rogue's purse on a vial of water and then adding snake weed, the herb order does not matter and will yield the same result. Due to both ingredients not being tradeable between players, it can be faster to purchase a Relicym's balm from the Grand Exchange. It is HIGHLY recommended to give a balm to Uglug Nar, who is near the area in the start of the quest. This will give access to a store selling 100 Relicym's balm (3), and he will give you enough money () to buy three of them back. If you give him a 2-dose balm, he will only give you , while if you give him a 4-dose balm, he will give you . The store also has 100 achey logs, 10 bow strings, 10 cooked chompy, and five knives in stock. Making brutal arrows Brutal arrows are the best way to kill Zogres, and shooting them from a composite ogre bow is the best strategy in the final fight. It is your choice to obtain them; however, the quest can be done without it. The easiest way to obtain them will be from the Grand Exchange, as both the brutal arrows and the composite ogre bow are tradeable. To make the composite ogre bow, use a knife on Achey tree logs with some wolf bones in the inventory. Use a bow string on the unstrung bow to complete the bow (requires 30 Fletching). To make the arrows, use a knife on Achey tree logs and fletch them into ogre arrow shafts (each log will give 2-6 shafts). Add feathers to the shafts (each shaft needs 4 feathers). Finally, use nails (made of any metal) on the flighted ogre arrow while having a hammer in your inventory. Relocating the ceremonial grounds ''Recommended: Food, armour (Ranged armour is preferred), Relicym's balm, and a composite ogre bow and brutal arrows. Crumble Undead can also be used instead.'' Return to Grish and tell him what Sithik told you. Go through all available conversation options. (The bow CANNOT be equipped until this step has been completed!) After this he will give you a key which will give access to the ceremonial chamber to look for some 'old' items for him. Return to the cave beyond the barricade and head west. There will be two successive locked doors, proceed through both of these and descend the stairs. Continue to the end of this long, wide room. Searching the stand will spawn the boss. Slash Bash is weak to brutal arrows and the Crumble Undead spell. Other attacks, such as melee, are not effective as they will only hit up to 25% of the normal max hit. He also drains Prayer rapidly. Prepare accordingly, and don't forget to bring at least three Relicym's balm(3) to remove disease. Slash Bash Attacking Slash Bash from a distance. A level 111 undead ourg named Slash Bash will attack you. The boss can be safespotted, however you can still be attacked by his range attacks. Use the safespot (pictured right) and kill the Zogre while having on Protect from Missiles. It is possible to use melee on the boss, however it will require using more resources and time since the damage is reduced against him. It is possible to take too long to kill him, in which case he says something about growing weary of the fight and that he is awaiting a real challenge. You will have to start the fight over if this happens. After Slash Bash is defeated, collect the bones and the ogre artefact that he drops. The three ourg bones that Slash Bash drops are worth about |c}} each. ''Note: While you're here, if you've started Rag and Bone Man II, it is recommended to take advantage of your supplies and kill Zogres until they drop the zogre bone.'' Return to Grish with the ogre artefact for your reward. Congratulations, quest complete! Rewards  experience * experience * experience *Ability to make Relicym's balm *Ability to fletch comp ogre bows and brutal arrows *Ability to wear inoculation bracelets *Give a 2-, 3-, or 4-dose Relicym's balm to Uglug to gain access to ~ Uglug's stuffsies ~ }} Bonus: Take the black prism to either Zavistic Rarve in the Wizards' Guild for  or to Yanni Salika in Shilo Village for . Required for completing Completion of Zogre Flesh Eaters is required for the following: *Rum Deal *Rag and Bone Man II (Partial completion of Zogre Flesh Eaters recommended but not strictly necessary) Transcript Trivia * The quest's title is a reference to the movie Zombi 2, which was released in some countries under the title Zombie Flesh Eaters. * Even though the quest has a smithing requirement, it can be completed without boosts at level 1 smithing, since nails for brutal arrows are easily bought at shops. Category:Feldip Hills"}, "originalContent": "{{External|rs}}\n{{Has quick guide}}\n{{Infobox Quest\n|name = Zogre Flesh Eaters\n|number = 80\n|image = [[File:Zogre Flesh Eaters.png|300px]]\n|release = [[17 May]] [[2005]]\n|update = Zogre Flesh Eaters\n|members = Yes\n|series = None\n|developer = Tytn H\n}}\n'''Zogre Flesh Eaters''' is a [[Feldip Hills]] area [[quest]] in which you must venture into the crypts of [[Gu'Tanoth]], an ogre resting ground, now crawling with undead [[Zogre|zombie]] and [[Skogre|skeleton]] ogres.\n\n==Details==\n{{Quest details\n|start = Speak to [[Grish]] in [[Jiggig]], just south of [[Castle Wars]].\n|startmap = 2444,3051\n|difficulty = Intermediate\n|description = Run for your lives...the zombie ogres are here for your flesh! An ogre ceremonial dance area, used to communicate with ancient ogrish ancestors, has been overrun with diseased undead ogres.\n\nHow could something like this have happened? And what sort of rewards could an enterprising adventurer gain from exploring the tombs of such a place?\n|length = Short\n|requirements = * Completion of the following quests:\n** [[Big Chompy Bird Hunting]]\n** [[Jungle Potion]]\n*** [[Druidic Ritual]]\n* You must be able to defeat a level 111 enemy.\n* {{SCP|Smithing|4|link=yes}} {{Boostable|yes}} {{Questreqstart|}}\n* {{SCP|Herblore|8|link=yes}} {{Boostable|yes}} {{Questreqstart|}}\n* {{SCP|Ranged|30|link=yes}} {{Boostable|no}} {{Questreqstart|}}\n|items = * 5 free inventory spaces are needed to receive items near the start of the quest\n* A [[comp ogre bow]] (not needed if using [[Crumble Undead]]  in final battle) '''or''':\n** [[Wolf bones]]\n** [[Achey logs]] (obtainable during quest)\n** [[Knife]] (obtainable during quest)\n** [[Axe]] of your [[Woodcutting]] level\n** [[Bow string]]\n* [[Brutal arrows]] (not needed if using [[Crumble Undead]] in final battle) '''or''':\n** [[Achey logs]] (obtainable during quest)\n** [[Knife]] (obtainable during quest)\n** [[Nails]] (the best you can fletch into brutal arrows)\n** [[Hammer]]\n** [[Feather]]s (4 per 1 brutal arrow)\n|recommended = * {{SCP|Combat|45|link=yes}}\n* {{SCP|Prayer|43|link=yes}} for [[protection prayers]]\n* {{SCP|Strength|10+|link=yes}} (otherwise it is very difficult to open the chest below the stairs in the north-west corner. [[Strength#Temporary boosts|Strength boosts]] are useful here)\n* {{SCP|Fletching|30|link=yes}} {{Boostable|yes}} (is needed if you want to create your own [[Comp ogre bow]] and {{SCP|Fletching|33|link=yes}} is needed to fletch [[Brutal arrows]].)\n* [[Strength potion]] if your strength is low. Opening the ogre tomb seems to require strength and lowers your level each time you fail.\n* [[Vial of water]], [[Rogue's purse]] and [[Snake weed]] if you choose to learn the technique that allows you to mix [[Relicym's balm]] (pretty much necessary if < 89 combat, unless you have sanfew serum).\n* A [[Sanfew serum]] would be helpful to prevent disease and restore stats. [[Relicym's balm]] is a cheap alternative to the Sanfew serum\n* [[Ava's accumulator]] to conserve arrows\n* To save the [[super restore]] obtained during the quest to combat disease, [[combat potion|combat]], [[ranged potion|ranged]], or [[magic potion|magic potion]]s can be used after normal [[restore potion]]s.\n* A [[ring of dueling]] to teleport to [[Castle Wars]] and [[Ferox Enclave]] to restore your stats and cure disease with the pools. \n* A [[Yanille Teleport]]\n|kills = * [[Slash Bash]] ''(level 111)''\n* [[Zombie (Zogre Flesh Eaters)|Zombie]] ''(level 39)''\n|leagueRegion = <poem>\n{{RE|Kandarin}} — location requirement\nUnlocking {{RE|Morytania}} {{okay}} will auto-complete the quest\n</poem>\n}}\n\n==Walkthrough==\n'''''Items required:''''' 5-10 ''free inventory spaces. 5 for three cooked chompy birds (heal 10) and two Super restore potions. An additional 5 free slots to collect some quest items.''\n\n'''''Recommended: '''A teleport out, a weapon and food, especially for lower level players.''\n\nSpeak to [[Grish]], located at [[Jiggig]] (south after crossing the connecting bridge to [[Castle Wars]]) east of the [[fairy ring]] code {{Fairycode|BKP}}. He will talk about [[Zogres]] (zombie ogres) coming from the ground and making people sick. Ask if you can help, and progress through the conversation. Grish will give you two [[Super restore potion]]s and three [[cooked chompy]] birds.\n\nTalk to the [[Ogre guard]] to the east. After mentioning Grish, he will remove the barricade to allow you inside the Zogre-infested area.\n\nRun east to a set of stairs going downward and descend these stairs into the [[Jiggig Dungeon]].\n\n{{UIMnote|You need enough space to receive the super restore potions and cooked chompy birds, but they are not required to complete the quest. The super restore potions may help negate the effects of [[Disease]] during the quest, however.}}\n\n===The cave===\n[[File:Zogre Flesh Eaters skeleton.png|thumb|220px|Fighting the zombie.]]\n'''Note:''' You may now (or have already) become [[diseased]] if you are under 89 [[Combat]] and a [[Zogre]] or [[Skogre]] (level 44) attacks you. From now on, make sure you carry [[Super restore potion]]s with you, because the disease will temporarily reduce a random skill by up to 10 levels every minute or so.\n\n*Proceed north-west to an area with a blackened floor. There will be a short cutscene.\n*'''Search the [[Broken Lecturn]]'''. You will find a [[torn page]] that contained a spell.\n*'''Search the skeleton''' that is adjacent to the lecturn; a [[zombie (Zogre Flesh Eaters)|zombie]] will appear and attack. Kill it to obtain a [[ruined backpack]].\n*'''Open the backpack''' to obtain a [[Dragon inn tankard]], some [[rotten food]] and a [[knife]].\n*Attempt to '''search the [[Ogre Coffin (Zogre Flesh Eaters)|coffin]] nearest to the skeleton''', and then use a knife on it. The lock should unlock. Try opening the coffin (it may take a couple of tries). When the coffin is open, '''search''' it to obtain a [[black prism]].\n*Backtrack and exit the cave by running back to the barricade or simply teleporting out.\n*'''Note:''' If you became diseased in the previous part, you can enter a dream in the [[Nightmare Zone]] in practice mode and leave to cure disease. Alternatively, you can teleport to [[Ferox Enclave]] with a [[ring of dueling]] (equivalently, [[Grouping|Minigame teleport]] to Clan Wars) and drink from the [[Pool of Refreshment]] to restore your stats and cure disease. Otherwise it will weaken and cure after 15 minutes. '''''Note: Being Diseased can prevent teleporting with minigame teleport. Keep trying until you get it, but it might take more than once.'''''\n\n{{UIMnote|You may drop the rotten food immediately, and the knife after unlocking the coffin.}}\n\n===B. Vahn===\n'''''Items required: '''[[Black prism]], [[Dragon inn tankard]], [[Torn page]], and 5 free inventory spaces.''\n\n*Proceed to [[Yanille]]. Using the [[Minigame teleport]] to the Nightmare Zone will place you next to Yanille. The [[Watchtower teleport]] can also be used to get to Yanille, assuming the [[Watchtower]] quest is completed. Fairy ring code {{Fairycode|ciq}} is also near Yanille.\n*Go to the [[Dragon Inn]] in south-western Yanille, and use the [[Dragon inn tankard|tankard]] on the bartender. The bartender will explain that the tankard belonged to [[Brentle Vahn]], a not-so-moral customer who had been seen with a shady wizard.\n*Talk to [[Zavistic Rarve]], found on the {{FloorNumber|uk=0}} of the [[Wizards' Guild]] (NOT the [[Wizards' Tower]]!). He'll be wearing white and baby blue robes, distinct from the other wizards. If you don't have the required level to enter the guild, ring the bell on the east side of the building, then Rarve will appear outside. After seeing the [[Black prism|prism]] and [[torn page]], Rarve will tell you to talk to [[Sithik Ints]] in the guest house north of the guild on the {{FloorNumber|uk=1}}. '''''Note: Unlike the bartender, using the items on Rarve does not work. You must specifically click 'talk-to' and speak to Rarve or Sithiks will never be mentioned.'''''\n\n===Incriminating Sithik===\nProceed to the guest house, which is directly north of the [[Wizards' Guild]], and then go to the top floor. [[Sithik]] is in the western most room (in the bed, does not appear as yellow dot on the minimap).\n\nTalk to him, and he will deny any connection to [[Zogre]]s. When you raise allegations, Sithik will challenge you to prove he is guilty.\n\nSearch the [[drawers (Zogre Flesh Eaters)|drawers]], the [[cupboard (Zogre Flesh Eaters)|cupboard]], and the [[wardrobe (Zogre Flesh Eaters)|wardrobe]] in Sithiks room. You will find a [[Book of portraiture]], a [[Necromancy book]], [[papyrus]] and [[charcoal]], and a [[Book of 'h.a.m']]. Read all three books. Do not drop the tankard as you will need it shortly after.\n\nUse the Book of Portraiture on Sithik. He will recommend trying portraiture for yourself. Draw a portrait of Sithik by using the papyrus on him. He will admire either the 'truth' or the 'honesty' of your completed portrait:\n\nIf he likes the '''<nowiki/>'truth'''' of the portrait, you will need to drop it and make a new one. You can grab another papyrus from the drawers. If he likes the '''<nowiki/>'honesty'''' of the portrait, keep it. Having a higher [[Crafting]] level helps with making a successful sketch.\n\nThe two portraits won't appear the same in the inventory and the correct one, when examined, will read {{mes|A classic realist charcoal portrait of Sithik.}}\n\n[[File:Zogre Flesh Eaters interrogating Sithik.png|thumb|220px|Sithik is turned into an ogre.]]\nTake the portrait and the [[Dragon inn tankard|tankard]] back to the bartender (First use the tankard on the bartender and then the portrait). The bartender will recognise Sithik as the wizard seen with [[Brentle]], and will sign the portrait as an affidavit. Go back to the Wizards' Guild and talk to [[Zavistic Rarve]] or ring the bell outside the door. Use the Necromancy book, the Book of 'h.a.m.', the tankard, and the [[signed portrait]] on him, which will be enough evidence to convince Rarve that Sithik may be responsible for the murder. Rarve will give you a [[Strange potion]], which he instructs you to use on Sithik. '''You need to talk to Zavistic Rarve and select the dialogue option to give him the evidence, otherwise he won't give you the potion.'''\n\nUse the potion on the [[cup of tea]] on top of Sithik's dresser. Go down the ladder and back up to Sithik and you will find that he has turned into an [[ogre]].\n\nTalk to Sithik, and he will confess. Obtain answers to all three questions in order to make [[Relicym's balm]] and [[brutal arrows]].\n\nAt this point you can sell the [[Black Prism]] to [[Zavistic Rarve]]. This is not necessary to progress the quest.\n\nYou now have a choice: Make [[Relicym's balm]] and [[brutal arrows]], or skip ahead to 'Relocating the ceremonial grounds' and use [[Crumble Undead]] against Slash Bash. \n\n{{UIMnote|Upon giving you the Strange potion, Zavistic Rarve will take the Necromancy book, Book of H.A.M., tankard, and portrait from you. After drawing the correct portrait, you no longer need the charcoal, and after poisoning the cup of tea, you can drop the [[sample bottle]].}}\n\n===Making or buying Relicym's balm===\nAfter [[Sithik Ints]] has revealed the secret to making [[Relicym's balm]], having it on hand in the final fight will help tremendously. It is your choice to obtain this now; however, the quest can be done without it. The balm is also tradeable.\n\nRelicym's balm(3) is made by using a [[rogue's purse]] on a [[vial of water]] and then adding [[snake weed]], the herb order does not matter and will yield the same result. Due to both ingredients not being tradeable between players, it can be faster to purchase a Relicym's balm from the [[Grand Exchange]].\n\nIt is '''HIGHLY''' recommended to give a balm to [[Uglug Nar]], who is near the area in the start of the quest. This will give access to a store selling 100 Relicym's balm (3), and he will give you enough money ({{NoCoins|650|c}}) to buy three of them back. If you give him a 2-dose balm, he will only give you {{NoCoins|300|c}}, while if you give him a 4-dose balm, he will give you {{NoCoins|1000|c}}. The store also has 100 [[achey logs]], 10 [[bow string]]s, 10 [[cooked chompy]], and five [[knives]] in stock.\n\n===Making brutal arrows===\n[[Brutal arrows]] are the best way to kill [[Zogre]]s, and shooting them from a [[composite ogre bow]] is the best strategy in the final fight. It is your choice to obtain them; however, the quest can be done without it. The easiest way to obtain them will be from the [[Grand Exchange]], as both the brutal arrows and the composite ogre bow are tradeable.\n\nTo make the composite ogre bow, use a knife on [[Achey tree logs]] with some [[wolf bones]] in the inventory. Use a [[bow string]] on the unstrung bow to complete the bow (requires 30 [[Fletching]]).\n\nTo make the arrows, use a knife on Achey tree logs and fletch them into [[ogre arrow shaft]]s (each log will give 2-6 shafts). Add [[feathers]] to the shafts (each shaft needs 4 feathers). Finally, use [[nails]] (made of any [[metal]]) on the [[flighted ogre arrow]] while having a [[hammer]] in your inventory.\n\n===Relocating the ceremonial grounds===\n'''''Recommended: '''Food, armour (Ranged armour is preferred), [[Relicym's balm]], and a [[composite ogre bow]] and [[brutal arrows]]. [[Crumble Undead]] can also be used instead.''\n\nReturn to [[Grish]] and tell him what [[Sithik]] told you. Go through all available conversation options. '''(The bow CANNOT be equipped until this step has been completed!)''' After this he will give you a [[Ogre gate key|key]] which will give access to the ceremonial chamber to look for some 'old' items for him.\n\nReturn to the cave beyond the barricade and head west. There will be two successive locked doors, proceed through both of these and descend the stairs. Continue to the end of this long, wide room. Searching the [[stand (Zogre Flesh Eaters)|stand]] will spawn the boss.\n\nSlash Bash is weak to brutal arrows and the [[Crumble Undead]] spell. Other attacks, such as melee, are not effective as they will only hit up to 25% of the normal max hit. He also drains [[Prayer]] rapidly. Prepare accordingly, and don't forget to bring at least three Relicym's balm(3) to remove [[disease]].\n\n===Slash Bash===\n[[File:Fighting Slash Bash.png|thumb|220px|Attacking Slash Bash from a distance.]]\nA level 111 undead [[ourg]] named [[Slash Bash]] will attack you. The boss can be safespotted, however you can still be attacked by his range attacks. Use the [[safespot]] (pictured right) and kill the Zogre while having on [[Protect from Missiles]]. It is possible to use melee on the boss, however it will require using more resources and time since the damage is reduced against him.\n\nIt is possible to take too long to kill him, in which case he says something about growing weary of the fight and that he is awaiting a real challenge. You will have to start the fight over if this happens.\n\nAfter Slash Bash is defeated, collect the bones and the [[ogre artefact]] that he drops. The three [[ourg bones]] that Slash Bash drops are worth about {{NoCoins|{{GEP|ourg bones}}|c}} each.\n\n''Note: While you're here, if you've started [[Rag and Bone Man II]], it is recommended to take advantage of your supplies and kill Zogres until they drop the [[zogre bone]].''\n\nReturn to Grish with the ogre artefact for your reward.\n\n'''Congratulations, quest complete!'''\n\n==Rewards==\n{{Quest rewards\n|name = Zogre Flesh Eaters\n|image = [[File:Zogre Flesh Eaters reward scroll.png|centre]]\n|qp = 1\n|rewards = \n*3 [[ourg bones]]\n*2 [[zogre bones]]\n*{{SCP|Fletching|2,000|link=yes}} experience\n*{{SCP|Ranged|2,000|link=yes}} experience\n*{{SCP|Herblore|2,000|link=yes}} experience\n*Ability to make [[Relicym's balm]]\n*Ability to fletch [[comp ogre bow]]s and [[brutal arrow]]s\n*Ability to wear [[inoculation bracelet]]s\n*Give a 2-, 3-, or 4-dose [[Relicym's balm]] to Uglug to gain access to [[~ Uglug's stuffsies ~]]\n}}\n\n'''Bonus:''' Take the [[black prism]] to either [[Zavistic Rarve]] in the [[Wizards' Guild]] for {{NoCoins|2000|c}} or to [[Yanni Salika]] in [[Shilo Village (location)|Shilo Village]] for {{NoCoins|5000|c}}.\n\n==Required for completing==\nCompletion of Zogre Flesh Eaters is required for the following:\n*[[Rum Deal]]\n*[[Rag and Bone Man II]] (Partial completion of Zogre Flesh Eaters recommended but not strictly necessary)\n\n==Transcript==\n{{hastranscript|Quest}}\n\n==Trivia==\n* The quest's title is a reference to the movie ''[[wikipedia:Zombi_2|Zombi 2]],'' which was released in some countries under the title ''Zombie Flesh Eaters''.\n* Even though the quest has a smithing requirement, it can be completed without boosts at level 1 smithing, since nails for brutal arrows are easily bought at shops.\n\n{{Zogre Flesh Eaters}}\n[[Category:Feldip Hills]]"}